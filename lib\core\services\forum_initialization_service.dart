import 'package:cloud_firestore/cloud_firestore.dart';

import '../../domain/models/forum/badge_model.dart';
import '../../domain/models/forum/achievement_model.dart';

/// خدمة تهيئة المنتدى
class ForumInitializationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// تهيئة المنتدى بالكامل
  Future<void> initializeForum() async {
    try {
      await Future.wait([
        _createDefaultCategories(),
        _createPropertyCategories(),
        _initializeBadges(),
        _initializeAchievements(),
        _setupDefaultSettings(),
        _initializeForumStatistics(),
      ]);
    } catch (e) {
      // خطأ في تهيئة المنتدى
    }
  }

  /// إنشاء الفئات الافتراضية
  Future<void> _createDefaultCategories() async {
    try {
      final categories = [
        {
          'id': 'general_discussion',
          'name': 'النقاش العام',
          'description': 'مناقشات عامة حول العقارات والاستثمار',
          'icon': 'forum',
          'color': '#2196F3',
          'order': 1,
          'isActive': true,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'property_sales',
          'name': 'بيع العقارات',
          'description': 'مناقشات حول بيع العقارات والاستثمارات',
          'icon': 'home',
          'color': '#4CAF50',
          'order': 2,
          'isActive': true,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'property_rentals',
          'name': 'إيجار العقارات',
          'description': 'مناقشات حول إيجار العقارات والشقق',
          'icon': 'key',
          'color': '#FF9800',
          'order': 3,
          'isActive': true,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'cars',
          'name': 'السيارات',
          'description': 'مناقشات حول السيارات والمركبات',
          'icon': 'directions_car',
          'color': '#9C27B0',
          'order': 4,
          'isActive': true,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'jobs',
          'name': 'الوظائف والفرص',
          'description': 'فرص العمل والوظائف المتاحة',
          'icon': 'work',
          'color': '#607D8B',
          'order': 5,
          'isActive': true,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'services',
          'name': 'الخدمات والعروض',
          'description': 'خدمات متنوعة وعروض خاصة',
          'icon': 'business',
          'color': '#795548',
          'order': 6,
          'isActive': true,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
      ];

      for (final categoryData in categories) {
        await _firestore
            .collection('forum_categories')
            .doc(categoryData['id'] as String)
            .set(categoryData, SetOptions(merge: true));
      }
    } catch (e) {
      // خطأ في إنشاء الفئات الافتراضية
    }
  }

  /// إنشاء فئات العقارات
  Future<void> _createPropertyCategories() async {
    try {
      final propertyCategories = [
        {
          'id': 'qa',
          'name': 'أسئلة وأجوبة',
          'description': 'أسئلة وأجوبة حول العقارات والاستثمار',
          'icon': 'help',
          'color': '#E91E63',
          'order': 7,
          'isActive': true,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'general_chat',
          'name': 'نقاشات عامة',
          'description': 'نقاشات عامة ومتنوعة',
          'icon': 'chat',
          'color': '#00BCD4',
          'order': 8,
          'isActive': true,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'news_updates',
          'name': 'الأخبار والتحديثات',
          'description': 'آخر الأخبار والتحديثات في سوق العقارات',
          'icon': 'newspaper',
          'color': '#FF5722',
          'order': 9,
          'isActive': true,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
      ];

      for (final categoryData in propertyCategories) {
        await _firestore
            .collection('forum_categories')
            .doc(categoryData['id'] as String)
            .set(categoryData, SetOptions(merge: true));
      }
    } catch (e) {
      // خطأ في إنشاء فئات العقارات
    }
  }

  /// تهيئة الشارات
  Future<void> _initializeBadges() async {
    try {
      final badges = BadgeModel.getAllBadges();
      
      for (final badge in badges) {
        await _firestore
            .collection('forum_badges')
            .doc(badge.id)
            .set(badge.toMap(), SetOptions(merge: true));
      }
    } catch (e) {
      // خطأ في تهيئة الشارات
    }
  }

  /// تهيئة الإنجازات
  Future<void> _initializeAchievements() async {
    try {
      final achievements = AchievementModel.getAllAchievements();
      
      for (final achievement in achievements) {
        await _firestore
            .collection('forum_achievements')
            .doc(achievement.id)
            .set(achievement.toMap(), SetOptions(merge: true));
      }
    } catch (e) {
      // خطأ في تهيئة الإنجازات
    }
  }

  /// إعداد الإعدادات الافتراضية
  Future<void> _setupDefaultSettings() async {
    try {
      final defaultSettings = {
        'forum_name': 'منتدى كريا',
        'forum_description': 'منتدى للنقاش حول العقارات والاستثمار',
        'max_posts_per_day': 50,
        'max_topics_per_day': 10,
        'min_post_length': 10,
        'max_post_length': 5000,
        'allow_anonymous_posts': false,
        'require_email_verification': true,
        'auto_approve_posts': true,
        'enable_post_reactions': true,
        'enable_user_mentions': true,
        'enable_topic_subscriptions': true,
        'enable_email_notifications': true,
        'enable_push_notifications': true,
        'points_per_topic': 10,
        'points_per_post': 5,
        'points_per_like_received': 2,
        'points_per_like_given': 1,
        'achievement_notifications_enabled': true,
        'badge_notifications_enabled': true,
        'level_up_notifications_enabled': true,
        'created_at': Timestamp.now(),
        'updated_at': Timestamp.now(),
      };

      await _firestore
          .collection('forum_settings')
          .doc('default')
          .set(defaultSettings, SetOptions(merge: true));
    } catch (e) {
      // خطأ في إعداد الإعدادات الافتراضية
    }
  }

  /// إنشاء موضوع ترحيبي
  Future<void> createWelcomeTopic() async {
    try {
      final welcomeTopicData = {
        'id': 'welcome_topic',
        'title': 'مرحباً بكم في منتدى كريا',
        'content': '''
مرحباً بكم في منتدى كريا للعقارات!

هذا المنتدى مخصص لمناقشة كل ما يتعلق بالعقارات والاستثمار العقاري في الكويت.

## قوانين المنتدى:
1. احترام الآخرين وآرائهم
2. عدم نشر محتوى مسيء أو غير لائق
3. التأكد من صحة المعلومات قبل نشرها
4. عدم الإعلان المباشر دون إذن
5. استخدام الفئات المناسبة للمواضيع

## الفئات المتاحة:
- **النقاش العام**: للمناقشات العامة حول العقارات
- **بيع العقارات**: لمناقشة بيع العقارات والاستثمارات
- **إيجار العقارات**: لمناقشة إيجار العقارات والشقق
- **السيارات**: لمناقشة السيارات والمركبات
- **الوظائف والفرص**: لنشر فرص العمل والوظائف
- **الخدمات والعروض**: للخدمات المتنوعة والعروض الخاصة
- **أسئلة وأجوبة**: للأسئلة والاستفسارات
- **نقاشات عامة**: للنقاشات المتنوعة
- **الأخبار والتحديثات**: لآخر الأخبار في سوق العقارات

نتمنى لكم تجربة ممتعة ومفيدة في المنتدى!

فريق كريا
        ''',
        'categoryId': 'general_discussion',
        'authorId': 'system',
        'authorName': 'فريق كريا',
        'isPinned': true,
        'isFeatured': true,
        'isLocked': false,
        'viewsCount': 0,
        'likesCount': 0,
        'repliesCount': 0,
        'lastActivityAt': Timestamp.now(),
        'createdAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
        'tags': ['ترحيب', 'قوانين', 'إرشادات'],
        'status': 'published',
      };

      await _firestore
          .collection('forum_topics')
          .doc('welcome_topic')
          .set(welcomeTopicData, SetOptions(merge: true));
    } catch (e) {
      // خطأ في إنشاء الموضوع الترحيبي
    }
  }

  /// تهيئة إحصائيات المنتدى
  Future<void> _initializeForumStatistics() async {
    try {
      final statsDoc = await _firestore.collection('forum_statistics').doc('global').get();

      if (!statsDoc.exists) {
        // إنشاء إحصائيات أولية
        final initialStats = {
          'categoriesCount': 0,
          'topicsCount': 0,
          'postsCount': 0,
          'activeUsersCount': 0,
          'latestTopics': [],
          'mostViewedTopics': [],
          'mostActiveTopics': [],
          'updatedAt': Timestamp.now(),
          'activityData': {
            'الأحد': 0,
            'الإثنين': 0,
            'الثلاثاء': 0,
            'الأربعاء': 0,
            'الخميس': 0,
            'الجمعة': 0,
            'السبت': 0,
          },
          'categoriesData': {
            'النقاش العام': 0,
            'بيع العقارات': 0,
            'إيجار العقارات': 0,
            'السيارات': 0,
            'الوظائف والفرص': 0,
            'الخدمات والعروض': 0,
            'أسئلة وأجوبة': 0,
            'نقاشات عامة': 0,
            'الأخبار والتحديثات': 0,
          },
        };

        await _firestore.collection('forum_statistics').doc('global').set(initialStats);
      }
    } catch (e) {
      // خطأ في تهيئة إحصائيات المنتدى
    }
  }
}
