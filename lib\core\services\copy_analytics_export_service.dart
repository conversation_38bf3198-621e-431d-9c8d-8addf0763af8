import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' as xlsio;
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';
import 'package:intl/intl.dart';

/// خدمة تصدير تحليلات النسخ إلى Excel
class CopyAnalyticsExportService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// تصدير تحليلات النسخ الشاملة
  static Future<void> exportCopyAnalytics() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('المستخدم غير مسجل الدخول');
    }

    try {
      // الحصول على البيانات
      final copiedPropertiesData = await _getCopiedPropertiesData(currentUser.uid);
      final overviewData = await _getOverviewData(copiedPropertiesData);
      final performanceData = await _getPerformanceData(copiedPropertiesData);
      final revenueData = await _getRevenueData(copiedPropertiesData);
      final monthlyData = await _getMonthlyAnalytics(copiedPropertiesData);

      // إنشاء ملف Excel
      final xlsio.Workbook workbook = xlsio.Workbook();

      // حذف الورقة الافتراضية
      workbook.worksheets.clear();

      // إنشاء الأوراق المختلفة
      _createOverviewSheet(workbook, overviewData);
      _createPropertiesListSheet(workbook, copiedPropertiesData);
      _createPerformanceSheet(workbook, performanceData);
      _createRevenueSheet(workbook, revenueData);
      _createMonthlyAnalyticsSheet(workbook, monthlyData);

      // حفظ ومشاركة الملف
      await _saveAndShareWorkbook(workbook, 'تحليلات_النسخ');

    } catch (e) {
      throw Exception('خطأ في تصدير التحليلات: $e');
    }
  }

  /// الحصول على بيانات العقارات المنسوخة
  static Future<List<Map<String, dynamic>>> _getCopiedPropertiesData(String userId) async {
    final snapshot = await _firestore
        .collection('estates')
        .where('ownerId', isEqualTo: userId)
        .where('isCopied', isEqualTo: true)
        .orderBy('copiedAt', descending: true)
        .get();

    return snapshot.docs.map((doc) {
      final data = doc.data();
      return {
        'id': doc.id,
        'title': data['title'] ?? 'غير محدد',
        'originalEstateId': data['originalEstateId'] ?? '',
        'price': (data['price'] ?? 0).toDouble(),
        'location': data['location'] ?? 'غير محدد',
        'subCategory': data['subCategory'] ?? 'غير محدد',
        'isPaid': data['isPaid'] ?? false,
        'isActive': data['isActive'] ?? false,
        'views': data['views'] ?? 0,
        'inquiries': data['inquiries'] ?? 0,
        'copiedAt': data['copiedAt'] as Timestamp?,
        'createdAt': data['createdAt'] as Timestamp?,
        'adType': data['adType'] ?? 'basic',
        'planType': data['planType'] ?? 'free',
      };
    }).toList();
  }

  /// الحصول على بيانات النظرة العامة
  static Future<Map<String, dynamic>> _getOverviewData(List<Map<String, dynamic>> properties) async {
    final totalCopied = properties.length;
    final activeCopies = properties.where((p) => p['isPaid'] == true && p['isActive'] == true).length;
    final pendingPayment = properties.where((p) => p['isPaid'] == false).length;
    final totalViews = properties.fold<int>(0, (sum, p) => sum + (p['views'] as int));
    final totalInquiries = properties.fold<int>(0, (sum, p) => sum + (p['inquiries'] as int));

    return {
      'totalCopied': totalCopied,
      'activeCopies': activeCopies,
      'pendingPayment': pendingPayment,
      'totalViews': totalViews,
      'totalInquiries': totalInquiries,
      'successRate': totalCopied > 0 ? ((activeCopies / totalCopied) * 100).round() : 0,
      'avgViewsPerProperty': totalCopied > 0 ? (totalViews / totalCopied).round() : 0,
    };
  }

  /// الحصول على بيانات الأداء
  static Future<Map<String, dynamic>> _getPerformanceData(List<Map<String, dynamic>> properties) async {
    final typeStats = <String, Map<String, int>>{};

    for (final property in properties) {
      final type = property['subCategory'] as String;
      if (!typeStats.containsKey(type)) {
        typeStats[type] = {'total': 0, 'active': 0, 'views': 0, 'inquiries': 0};
      }

      typeStats[type]!['total'] = typeStats[type]!['total']! + 1;
      if (property['isPaid'] == true && property['isActive'] == true) {
        typeStats[type]!['active'] = typeStats[type]!['active']! + 1;
      }
      typeStats[type]!['views'] = typeStats[type]!['views']! + (property['views'] as int);
      typeStats[type]!['inquiries'] = typeStats[type]!['inquiries']! + (property['inquiries'] as int);
    }

    return {'typeStats': typeStats};
  }

  /// الحصول على بيانات الإيرادات
  static Future<Map<String, dynamic>> _getRevenueData(List<Map<String, dynamic>> properties) async {
    const double pricePerProperty = 50.0; // افتراض 50 د.ك لكل عقار

    final paidProperties = properties.where((p) => p['isPaid'] == true).length;
    final totalRevenue = paidProperties * pricePerProperty;
    final pendingRevenue = (properties.length - paidProperties) * pricePerProperty;

    return {
      'totalRevenue': totalRevenue,
      'pendingRevenue': pendingRevenue,
      'totalPotentialRevenue': totalRevenue + pendingRevenue,
      'paidProperties': paidProperties,
      'unpaidProperties': properties.length - paidProperties,
    };
  }

  /// الحصول على التحليلات الشهرية
  static Future<List<Map<String, dynamic>>> _getMonthlyAnalytics(List<Map<String, dynamic>> properties) async {
    final monthlyStats = <String, Map<String, int>>{};

    for (final property in properties) {
      final copiedAt = property['copiedAt'] as Timestamp?;
      if (copiedAt != null) {
        final date = copiedAt.toDate();
        final monthKey = DateFormat('yyyy-MM').format(date);
        final monthName = DateFormat('MMMM yyyy', 'ar').format(date);

        if (!monthlyStats.containsKey(monthKey)) {
          monthlyStats[monthKey] = {
            'total': 0,
            'paid': 0,
            'views': 0,
            'inquiries': 0,
          };
        }

        monthlyStats[monthKey]!['total'] = monthlyStats[monthKey]!['total']! + 1;
        if (property['isPaid'] == true) {
          monthlyStats[monthKey]!['paid'] = monthlyStats[monthKey]!['paid']! + 1;
        }
        monthlyStats[monthKey]!['views'] = monthlyStats[monthKey]!['views']! + (property['views'] as int);
        monthlyStats[monthKey]!['inquiries'] = monthlyStats[monthKey]!['inquiries']! + (property['inquiries'] as int);
      }
    }

    // تحويل إلى قائمة مرتبة
    final sortedEntries = monthlyStats.entries.toList()
      ..sort((a, b) => b.key.compareTo(a.key)); // ترتيب تنازلي

    return sortedEntries.map((entry) {
      final date = DateTime.parse('${entry.key}-01');
      return {
        'month': DateFormat('MMMM yyyy', 'ar').format(date),
        'monthKey': entry.key,
        'total': entry.value['total'],
        'paid': entry.value['paid'],
        'views': entry.value['views'],
        'inquiries': entry.value['inquiries'],
        'revenue': (entry.value['paid']! * 50.0), // 50 د.ك لكل عقار مدفوع
      };
    }).toList();
  }

  /// إنشاء ورقة النظرة العامة
  static void _createOverviewSheet(xlsio.Workbook workbook, Map<String, dynamic> data) {
    final worksheet = workbook.worksheets.add();
    worksheet.name = 'النظرة العامة';

    // تنسيق العنوان الرئيسي
    _setHeaderStyle(worksheet, 1, 1, 'تحليلات النسخ - النظرة العامة');
    worksheet.getRangeByIndex(1, 1, 1, 2).merge();

    // إضافة تاريخ التقرير
    worksheet.getRangeByIndex(2, 1).setText('تاريخ التقرير:');
    worksheet.getRangeByIndex(2, 2).setText(DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now()));

    // الإحصائيات الرئيسية
    int row = 4;
    _addStatRow(worksheet, row++, 'إجمالي العقارات المنسوخة', '${data['totalCopied']}');
    _addStatRow(worksheet, row++, 'العقارات النشطة', '${data['activeCopies']}');
    _addStatRow(worksheet, row++, 'في انتظار الدفع', '${data['pendingPayment']}');
    _addStatRow(worksheet, row++, 'إجمالي المشاهدات', '${data['totalViews']}');
    _addStatRow(worksheet, row++, 'إجمالي الاستفسارات', '${data['totalInquiries']}');
    _addStatRow(worksheet, row++, 'معدل النجاح', '${data['successRate']}%');
    _addStatRow(worksheet, row++, 'متوسط المشاهدات لكل عقار', '${data['avgViewsPerProperty']}');

    // ضبط عرض الأعمدة
    worksheet.autoFitColumn(1);
    worksheet.autoFitColumn(2);
  }

  /// إضافة صف إحصائية
  static void _addStatRow(xlsio.Worksheet worksheet, int row, String label, String value) {
    worksheet.getRangeByIndex(row, 1).setText(label);
    worksheet.getRangeByIndex(row, 2).setText(value);

    // تنسيق الخلايا
    worksheet.getRangeByIndex(row, 1).cellStyle.bold = true;
    worksheet.getRangeByIndex(row, 2).cellStyle.backColor = '#F5F5F5';
  }

  /// تنسيق العنوان
  static void _setHeaderStyle(xlsio.Worksheet worksheet, int row, int col, String text) {
    final cell = worksheet.getRangeByIndex(row, col);
    cell.setText(text);
    cell.cellStyle.backColor = '#2E7D32';
    cell.cellStyle.fontColor = '#FFFFFF';
    cell.cellStyle.bold = true;
    cell.cellStyle.fontSize = 14;
  }

  /// إنشاء ورقة قائمة العقارات
  static void _createPropertiesListSheet(xlsio.Workbook workbook, List<Map<String, dynamic>> properties) {
    final worksheet = workbook.worksheets.add();
    worksheet.name = 'قائمة العقارات';

    // العناوين
    final headers = [
      'العنوان', 'النوع', 'السعر (د.ك)', 'الموقع', 'الحالة',
      'المشاهدات', 'الاستفسارات', 'تاريخ النسخ', 'نوع الإعلان'
    ];

    for (int i = 0; i < headers.length; i++) {
      _setHeaderStyle(worksheet, 1, i + 1, headers[i]);
    }

    // البيانات
    for (int i = 0; i < properties.length; i++) {
      final property = properties[i];
      final row = i + 2;

      worksheet.getRangeByIndex(row, 1).setText(property['title']);
      worksheet.getRangeByIndex(row, 2).setText(property['subCategory']);
      worksheet.getRangeByIndex(row, 3).setText('${property['price']}');
      worksheet.getRangeByIndex(row, 4).setText(property['location']);

      String status = 'معلق';
      if (property['isPaid'] == true && property['isActive'] == true) {
        status = 'نشط';
      } else if (property['isPaid'] == true) {
        status = 'مدفوع';
      }
      worksheet.getRangeByIndex(row, 5).setText(status);

      worksheet.getRangeByIndex(row, 6).setText('${property['views']}');
      worksheet.getRangeByIndex(row, 7).setText('${property['inquiries']}');

      final copiedAt = property['copiedAt'] as Timestamp?;
      worksheet.getRangeByIndex(row, 8).setText(
        copiedAt != null ? DateFormat('yyyy-MM-dd').format(copiedAt.toDate()) : 'غير محدد'
      );

      worksheet.getRangeByIndex(row, 9).setText(property['adType']);
    }

    // ضبط عرض الأعمدة
    for (int i = 1; i <= headers.length; i++) {
      worksheet.autoFitColumn(i);
    }
  }

  /// إنشاء ورقة تحليل الأداء
  static void _createPerformanceSheet(xlsio.Workbook workbook, Map<String, dynamic> performanceData) {
    final worksheet = workbook.worksheets.add();
    worksheet.name = 'تحليل الأداء';

    _setHeaderStyle(worksheet, 1, 1, 'تحليل الأداء حسب نوع العقار');
    worksheet.getRangeByIndex(1, 1, 1, 5).merge();

    // العناوين
    final headers = ['نوع العقار', 'إجمالي المنسوخة', 'النشطة', 'المشاهدات', 'الاستفسارات'];
    for (int i = 0; i < headers.length; i++) {
      _setHeaderStyle(worksheet, 3, i + 1, headers[i]);
    }

    // البيانات
    final typeStats = performanceData['typeStats'] as Map<String, Map<String, int>>;
    int row = 4;

    for (final entry in typeStats.entries) {
      final type = entry.key;
      final stats = entry.value;

      worksheet.getRangeByIndex(row, 1).setText(type);
      worksheet.getRangeByIndex(row, 2).setText('${stats['total']}');
      worksheet.getRangeByIndex(row, 3).setText('${stats['active']}');
      worksheet.getRangeByIndex(row, 4).setText('${stats['views']}');
      worksheet.getRangeByIndex(row, 5).setText('${stats['inquiries']}');

      row++;
    }

    // ضبط عرض الأعمدة
    for (int i = 1; i <= headers.length; i++) {
      worksheet.autoFitColumn(i);
    }
  }

  /// إنشاء ورقة تحليل الإيرادات
  static void _createRevenueSheet(xlsio.Workbook workbook, Map<String, dynamic> revenueData) {
    final worksheet = workbook.worksheets.add();
    worksheet.name = 'تحليل الإيرادات';

    _setHeaderStyle(worksheet, 1, 1, 'تحليل الإيرادات');
    worksheet.getRangeByIndex(1, 1, 1, 2).merge();

    int row = 3;
    _addStatRow(worksheet, row++, 'إجمالي الإيرادات المحققة', '${revenueData['totalRevenue']} د.ك');
    _addStatRow(worksheet, row++, 'الإيرادات المعلقة', '${revenueData['pendingRevenue']} د.ك');
    _addStatRow(worksheet, row++, 'إجمالي الإيرادات المحتملة', '${revenueData['totalPotentialRevenue']} د.ك');
    _addStatRow(worksheet, row++, 'العقارات المدفوعة', '${revenueData['paidProperties']}');
    _addStatRow(worksheet, row++, 'العقارات غير المدفوعة', '${revenueData['unpaidProperties']}');

    // ضبط عرض الأعمدة
    worksheet.autoFitColumn(1);
    worksheet.autoFitColumn(2);
  }

  /// إنشاء ورقة التحليلات الشهرية
  static void _createMonthlyAnalyticsSheet(xlsio.Workbook workbook, List<Map<String, dynamic>> monthlyData) {
    final worksheet = workbook.worksheets.add();
    worksheet.name = 'التحليلات الشهرية';

    _setHeaderStyle(worksheet, 1, 1, 'التحليلات الشهرية');
    worksheet.getRangeByIndex(1, 1, 1, 6).merge();

    // العناوين
    final headers = ['الشهر', 'إجمالي المنسوخة', 'المدفوعة', 'المشاهدات', 'الاستفسارات', 'الإيرادات (د.ك)'];
    for (int i = 0; i < headers.length; i++) {
      _setHeaderStyle(worksheet, 3, i + 1, headers[i]);
    }

    // البيانات
    for (int i = 0; i < monthlyData.length; i++) {
      final data = monthlyData[i];
      final row = i + 4;

      worksheet.getRangeByIndex(row, 1).setText(data['month']);
      worksheet.getRangeByIndex(row, 2).setText('${data['total']}');
      worksheet.getRangeByIndex(row, 3).setText('${data['paid']}');
      worksheet.getRangeByIndex(row, 4).setText('${data['views']}');
      worksheet.getRangeByIndex(row, 5).setText('${data['inquiries']}');
      worksheet.getRangeByIndex(row, 6).setText('${data['revenue']}');
    }

    // ضبط عرض الأعمدة
    for (int i = 1; i <= headers.length; i++) {
      worksheet.autoFitColumn(i);
    }
  }

  /// حفظ ومشاركة ملف Excel
  static Future<void> _saveAndShareWorkbook(xlsio.Workbook workbook, String fileName) async {
    final List<int> bytes = workbook.saveAsStream();
    workbook.dispose();

    final directory = await getApplicationDocumentsDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final file = File('${directory.path}/${fileName}_$timestamp.xlsx');
    await file.writeAsBytes(bytes);

    await Share.shareXFiles(
      [XFile(file.path)],
      text: '$fileName - ${DateFormat('yyyy-MM-dd').format(DateTime.now())}',
    );
  }
}
