@echo off
echo ========================================
echo بناء إصدار الإنتاج لتطبيق Krea
echo ========================================
echo.

echo التحقق من متطلبات البناء...

:: التحقق من وجود Flutter
flutter --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Flutter غير مثبت أو غير موجود في PATH
    pause
    exit /b 1
)

:: التحقق من وجود ملف key.properties
if not exist "android\key.properties" (
    echo ❌ ملف key.properties غير موجود
    echo يرجى تشغيل scripts\create_keystore.bat أولاً
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo.

echo تنظيف المشروع...
flutter clean

echo تحديث التبعيات...
flutter pub get

echo تشغيل code generation...
flutter packages pub run build_runner build --delete-conflicting-outputs

echo.
echo اختر نوع البناء:
echo 1. APK (للاختبار)
echo 2. App Bundle (للنشر على Google Play)
echo 3. كلاهما
echo.

set /p choice="اختر (1/2/3): "

if "%choice%"=="1" goto build_apk
if "%choice%"=="2" goto build_aab
if "%choice%"=="3" goto build_both
echo خيار غير صحيح
pause
exit /b 1

:build_apk
echo.
echo بناء APK...
flutter build apk --release --split-per-abi
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء APK بنجاح!
    echo الملفات موجودة في: build\app\outputs\flutter-apk\
) else (
    echo ❌ فشل في بناء APK
)
goto end

:build_aab
echo.
echo بناء App Bundle...
flutter build appbundle --release
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء App Bundle بنجاح!
    echo الملف موجود في: build\app\outputs\bundle\release\app-release.aab
) else (
    echo ❌ فشل في بناء App Bundle
)
goto end

:build_both
echo.
echo بناء APK...
flutter build apk --release --split-per-abi
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء APK بنجاح!
) else (
    echo ❌ فشل في بناء APK
)

echo.
echo بناء App Bundle...
flutter build appbundle --release
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء App Bundle بنجاح!
    echo.
    echo الملفات جاهزة:
    echo - APK: build\app\outputs\flutter-apk\
    echo - AAB: build\app\outputs\bundle\release\app-release.aab
) else (
    echo ❌ فشل في بناء App Bundle
)

:end
echo.
echo انتهى البناء!
pause
