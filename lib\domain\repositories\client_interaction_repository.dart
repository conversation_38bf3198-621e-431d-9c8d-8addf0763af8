import 'dart:io';
import '../entities/client_interaction.dart';
import '../entities/appointment.dart';

/// واجهة مستودع التفاعلات مع العملاء
abstract class ClientInteractionRepository {
  // إدارة التفاعلات الأساسية
  
  /// إنشاء تفاعل جديد
  Future<String> createInteraction(ClientInteraction interaction);
  
  /// تحديث تفاعل موجود
  Future<void> updateInteraction(ClientInteraction interaction);
  
  /// حذف تفاعل
  Future<void> deleteInteraction(String interactionId);
  
  /// الحصول على تفاعل بواسطة المعرف
  Future<ClientInteraction?> getInteractionById(String interactionId);
  
  /// الحصول على تفاعلات العميل
  Future<List<ClientInteraction>> getClientInteractions(String clientId);
  
  /// الحصول على تفاعلات الوكيل
  Future<List<ClientInteraction>> getAgentInteractions(String agentId);
  
  /// الحصول على تفاعلات الوكيل بالتحميل المتدرج
  Future<Map<String, dynamic>> getAgentInteractionsPaginated({
    required String agentId,
    int limit = 20,
    String? lastInteractionId,
    InteractionType? type,
    InteractionStatus? status,
    DateTime? startDate,
    DateTime? endDate,
  });
  
  /// البحث عن التفاعلات
  Future<List<ClientInteraction>> searchInteractions({
    required String agentId,
    String? query,
    InteractionType? type,
    InteractionStatus? status,
    InteractionOutcome? outcome,
    DateTime? startDate,
    DateTime? endDate,
  });
  
  // إدارة المواعيد
  
  /// إنشاء موعد جديد
  Future<String> createAppointment(Appointment appointment);
  
  /// تحديث موعد موجود
  Future<void> updateAppointment(Appointment appointment);
  
  /// حذف موعد
  Future<void> deleteAppointment(String appointmentId);
  
  /// الحصول على موعد بواسطة المعرف
  Future<Appointment?> getAppointmentById(String appointmentId);
  
  /// الحصول على مواعيد العميل
  Future<List<Appointment>> getClientAppointments(String clientId);
  
  /// الحصول على مواعيد الوكيل
  Future<List<Appointment>> getAgentAppointments(String agentId);
  
  /// الحصول على مواعيد اليوم
  Future<List<Appointment>> getTodayAppointments(String agentId);
  
  /// الحصول على مواعيد الأسبوع
  Future<List<Appointment>> getWeekAppointments(String agentId);
  
  /// الحصول على مواعيد الشهر
  Future<List<Appointment>> getMonthAppointments(String agentId);
  
  /// الحصول على المواعيد القادمة
  Future<List<Appointment>> getUpcomingAppointments(String agentId, {int daysAhead = 7});
  
  /// الحصول على المواعيد المتأخرة
  Future<List<Appointment>> getOverdueAppointments(String agentId);
  
  // إدارة المتابعة
  
  /// إضافة تذكير متابعة
  Future<void> addFollowUpReminder(String clientId, DateTime followUpDate, String notes);
  
  /// الحصول على تذكيرات المتابعة
  Future<List<ClientInteraction>> getFollowUpReminders(String agentId);
  
  /// الحصول على تذكيرات المتابعة لليوم
  Future<List<ClientInteraction>> getTodayFollowUps(String agentId);
  
  /// تحديث حالة المتابعة
  Future<void> updateFollowUpStatus(String interactionId, InteractionStatus status);
  
  /// إضافة ملاحظة متابعة
  Future<void> addFollowUpNote(String interactionId, String note);
  
  // التقارير والإحصائيات
  
  /// الحصول على إحصائيات التفاعلات
  Future<Map<String, dynamic>> getInteractionStatistics(String agentId, {
    DateTime? startDate,
    DateTime? endDate,
  });
  
  /// الحصول على تقرير أداء الوكيل
  Future<Map<String, dynamic>> getAgentPerformanceReport(String agentId, {
    DateTime? startDate,
    DateTime? endDate,
  });
  
  /// الحصول على تقرير تفاعلات العميل
  Future<Map<String, dynamic>> getClientInteractionReport(String clientId);
  
  /// الحصول على إحصائيات المواعيد
  Future<Map<String, dynamic>> getAppointmentStatistics(String agentId, {
    DateTime? startDate,
    DateTime? endDate,
  });
  
  /// الحصول على تقرير معدل التحويل
  Future<Map<String, dynamic>> getConversionReport(String agentId, {
    DateTime? startDate,
    DateTime? endDate,
  });
  
  /// الحصول على تقرير العائد من التفاعلات
  Future<Map<String, dynamic>> getRevenueReport(String agentId, {
    DateTime? startDate,
    DateTime? endDate,
  });
  
  // إدارة المرفقات
  
  /// رفع مرفق للتفاعل
  Future<String> uploadInteractionAttachment(String interactionId, File file);
  
  /// حذف مرفق من التفاعل
  Future<void> deleteInteractionAttachment(String interactionId, String attachmentUrl);
  
  /// الحصول على مرفقات التفاعل
  Future<List<String>> getInteractionAttachments(String interactionId);
  
  // التذكيرات والإشعارات
  
  /// إرسال تذكير موعد
  Future<void> sendAppointmentReminder(String appointmentId);
  
  /// إرسال تذكير متابعة
  Future<void> sendFollowUpReminder(String interactionId);
  
  /// إرسال إشعار تفاعل جديد
  Future<void> sendInteractionNotification(String interactionId);
  
  /// جدولة تذكيرات تلقائية
  Future<void> scheduleAutomaticReminders(String agentId);
  
  // التصدير والاستيراد
  
  /// تصدير تفاعلات العميل
  Future<String> exportClientInteractions(String clientId, String format);
  
  /// تصدير تقرير التفاعلات
  Future<String> exportInteractionReport(String agentId, {
    DateTime? startDate,
    DateTime? endDate,
    String format = 'excel',
  });
  
  /// استيراد تفاعلات من ملف
  Future<int> importInteractionsFromFile(String agentId, File file);
  
  /// تصدير جدول المواعيد
  Future<String> exportAppointmentSchedule(String agentId, {
    DateTime? startDate,
    DateTime? endDate,
    String format = 'pdf',
  });
  
  // الاستماع للتغييرات
  
  /// الاستماع لتغييرات تفاعلات العميل
  Stream<List<ClientInteraction>> listenToClientInteractions(String clientId);
  
  /// الاستماع لتغييرات تفاعلات الوكيل
  Stream<List<ClientInteraction>> listenToAgentInteractions(String agentId);
  
  /// الاستماع لتغييرات مواعيد الوكيل
  Stream<List<Appointment>> listenToAgentAppointments(String agentId);
  
  /// الاستماع لتذكيرات المتابعة
  Stream<List<ClientInteraction>> listenToFollowUpReminders(String agentId);
  
  // التحليلات المتقدمة
  
  /// تحليل أنماط التفاعل
  Future<Map<String, dynamic>> analyzeInteractionPatterns(String agentId, {
    DateTime? startDate,
    DateTime? endDate,
  });
  
  /// تحليل فعالية قنوات التواصل
  Future<Map<String, dynamic>> analyzeCommunicationChannels(String agentId, {
    DateTime? startDate,
    DateTime? endDate,
  });
  
  /// تحليل أوقات الذروة
  Future<Map<String, dynamic>> analyzePeakTimes(String agentId, {
    DateTime? startDate,
    DateTime? endDate,
  });
  
  /// توقع احتياجات العميل
  Future<Map<String, dynamic>> predictClientNeeds(String clientId);
  
  /// اقتراح أفضل أوقات التواصل
  Future<Map<String, dynamic>> suggestOptimalContactTimes(String clientId);
}
