import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

/// حالة المهمة
enum TaskStatus {
  pending,    // معلقة
  inProgress, // قيد التنفيذ
  completed,  // مكتملة
  cancelled,  // ملغية
  onHold,     // متوقفة مؤقتاً
}

/// أولوية المهمة
enum TaskPriority {
  low,    // منخفضة
  medium, // متوسطة
  high,   // عالية
  urgent, // عاجلة
}

/// نوع المهمة
enum TaskType {
  design,      // تصميم
  construction, // إنشاء
  planning,    // تخطيط
  review,      // مراجعة
  approval,    // موافقة
  documentation, // توثيق
  meeting,     // اجتماع
  other,       // أخرى
}

/// نموذج مهمة المشروع
class ProjectTask extends Equatable {
  /// معرف المهمة
  final String id;

  /// معرف المشروع
  final String projectId;

  /// عنوان المهمة
  final String title;

  /// وصف المهمة
  final String description;

  /// نوع المهمة
  final TaskType type;

  /// حالة المهمة
  final TaskStatus status;

  /// أولوية المهمة
  final TaskPriority priority;

  /// معرف المسؤول عن المهمة
  final String? assignedToId;

  /// اسم المسؤول عن المهمة
  final String? assignedToName;

  /// معرف منشئ المهمة
  final String createdById;

  /// اسم منشئ المهمة
  final String createdByName;

  /// تاريخ البداية المتوقع
  final DateTime? startDate;

  /// تاريخ الانتهاء المتوقع
  final DateTime? dueDate;

  /// تاريخ الانتهاء الفعلي
  final DateTime? completedDate;

  /// نسبة الإنجاز (0-100)
  final double progress;

  /// الوقت المقدر للإنجاز (بالساعات)
  final double? estimatedHours;

  /// الوقت الفعلي المستغرق (بالساعات)
  final double? actualHours;

  /// التكلفة المقدرة
  final double? estimatedCost;

  /// التكلفة الفعلية
  final double? actualCost;

  /// قائمة المرفقات
  final List<String> attachments;

  /// قائمة التعليقات
  final List<String> comments;

  /// قائمة المهام الفرعية
  final List<String> subtasks;

  /// قائمة المهام المعتمدة عليها
  final List<String> dependencies;

  /// العلامات
  final List<String> tags;

  /// تاريخ الإنشاء
  final DateTime createdAt;

  /// تاريخ آخر تحديث
  final DateTime updatedAt;

  /// معلومات إضافية
  final Map<String, dynamic>? additionalInfo;

  const ProjectTask({
    required this.id,
    required this.projectId,
    required this.title,
    required this.description,
    required this.type,
    required this.status,
    required this.priority,
    this.assignedToId,
    this.assignedToName,
    required this.createdById,
    required this.createdByName,
    this.startDate,
    this.dueDate,
    this.completedDate,
    this.progress = 0.0,
    this.estimatedHours,
    this.actualHours,
    this.estimatedCost,
    this.actualCost,
    this.attachments = const [],
    this.comments = const [],
    this.subtasks = const [],
    this.dependencies = const [],
    this.tags = const [],
    required this.createdAt,
    required this.updatedAt,
    this.additionalInfo,
  });

  /// إنشاء نسخة معدلة من المهمة
  ProjectTask copyWith({
    String? id,
    String? projectId,
    String? title,
    String? description,
    TaskType? type,
    TaskStatus? status,
    TaskPriority? priority,
    String? assignedToId,
    String? assignedToName,
    String? createdById,
    String? createdByName,
    DateTime? startDate,
    DateTime? dueDate,
    DateTime? completedDate,
    double? progress,
    double? estimatedHours,
    double? actualHours,
    double? estimatedCost,
    double? actualCost,
    List<String>? attachments,
    List<String>? comments,
    List<String>? subtasks,
    List<String>? dependencies,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalInfo,
  }) {
    return ProjectTask(
      id: id ?? this.id,
      projectId: projectId ?? this.projectId,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      assignedToId: assignedToId ?? this.assignedToId,
      assignedToName: assignedToName ?? this.assignedToName,
      createdById: createdById ?? this.createdById,
      createdByName: createdByName ?? this.createdByName,
      startDate: startDate ?? this.startDate,
      dueDate: dueDate ?? this.dueDate,
      completedDate: completedDate ?? this.completedDate,
      progress: progress ?? this.progress,
      estimatedHours: estimatedHours ?? this.estimatedHours,
      actualHours: actualHours ?? this.actualHours,
      estimatedCost: estimatedCost ?? this.estimatedCost,
      actualCost: actualCost ?? this.actualCost,
      attachments: attachments ?? this.attachments,
      comments: comments ?? this.comments,
      subtasks: subtasks ?? this.subtasks,
      dependencies: dependencies ?? this.dependencies,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalInfo: additionalInfo ?? this.additionalInfo,
    );
  }

  /// تحويل المهمة إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'projectId': projectId,
      'title': title,
      'description': description,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'priority': priority.toString().split('.').last,
      'assignedToId': assignedToId,
      'assignedToName': assignedToName,
      'createdById': createdById,
      'createdByName': createdByName,
      'startDate': startDate != null ? Timestamp.fromDate(startDate!) : null,
      'dueDate': dueDate != null ? Timestamp.fromDate(dueDate!) : null,
      'completedDate': completedDate != null ? Timestamp.fromDate(completedDate!) : null,
      'progress': progress,
      'estimatedHours': estimatedHours,
      'actualHours': actualHours,
      'estimatedCost': estimatedCost,
      'actualCost': actualCost,
      'attachments': attachments,
      'comments': comments,
      'subtasks': subtasks,
      'dependencies': dependencies,
      'tags': tags,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'additionalInfo': additionalInfo,
    };
  }

  /// إنشاء مهمة من Map
  factory ProjectTask.fromMap(Map<String, dynamic> map) {
    return ProjectTask(
      id: map['id'] ?? '',
      projectId: map['projectId'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      type: _getTaskTypeFromString(map['type'] ?? 'other'),
      status: _getTaskStatusFromString(map['status'] ?? 'pending'),
      priority: _getTaskPriorityFromString(map['priority'] ?? 'medium'),
      assignedToId: map['assignedToId'],
      assignedToName: map['assignedToName'],
      createdById: map['createdById'] ?? '',
      createdByName: map['createdByName'] ?? '',
      startDate: map['startDate'] is Timestamp
          ? (map['startDate'] as Timestamp).toDate()
          : null,
      dueDate: map['dueDate'] is Timestamp
          ? (map['dueDate'] as Timestamp).toDate()
          : null,
      completedDate: map['completedDate'] is Timestamp
          ? (map['completedDate'] as Timestamp).toDate()
          : null,
      progress: (map['progress'] ?? 0.0).toDouble(),
      estimatedHours: map['estimatedHours']?.toDouble(),
      actualHours: map['actualHours']?.toDouble(),
      estimatedCost: map['estimatedCost']?.toDouble(),
      actualCost: map['actualCost']?.toDouble(),
      attachments: List<String>.from(map['attachments'] ?? []),
      comments: List<String>.from(map['comments'] ?? []),
      subtasks: List<String>.from(map['subtasks'] ?? []),
      dependencies: List<String>.from(map['dependencies'] ?? []),
      tags: List<String>.from(map['tags'] ?? []),
      createdAt: map['createdAt'] is Timestamp
          ? (map['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp
          ? (map['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
      additionalInfo: map['additionalInfo'],
    );
  }

  /// إنشاء مهمة من DocumentSnapshot
  factory ProjectTask.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return ProjectTask.fromMap(data);
  }

  /// التحقق من انتهاء موعد المهمة
  bool isOverdue() {
    if (dueDate == null || status == TaskStatus.completed) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  /// التحقق من اقتراب موعد المهمة
  bool isDueSoon({int daysThreshold = 3}) {
    if (dueDate == null || status == TaskStatus.completed) return false;
    final now = DateTime.now();
    final difference = dueDate!.difference(now).inDays;
    return difference <= daysThreshold && difference >= 0;
  }

  /// الحصول على لون الحالة
  static Color getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return Colors.orange;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.cancelled:
        return Colors.red;
      case TaskStatus.onHold:
        return Colors.grey;
    }
  }

  /// الحصول على لون الأولوية
  static Color getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return Colors.green;
      case TaskPriority.medium:
        return Colors.orange;
      case TaskPriority.high:
        return Colors.red;
      case TaskPriority.urgent:
        return Colors.purple;
    }
  }

  @override
  List<Object?> get props => [
    id,
    projectId,
    title,
    description,
    type,
    status,
    priority,
    assignedToId,
    assignedToName,
    createdById,
    createdByName,
    startDate,
    dueDate,
    completedDate,
    progress,
    estimatedHours,
    actualHours,
    estimatedCost,
    actualCost,
    attachments,
    comments,
    subtasks,
    dependencies,
    tags,
    createdAt,
    updatedAt,
    additionalInfo,
  ];
}

// Helper functions
TaskType _getTaskTypeFromString(String type) {
  switch (type) {
    case 'design':
      return TaskType.design;
    case 'construction':
      return TaskType.construction;
    case 'planning':
      return TaskType.planning;
    case 'review':
      return TaskType.review;
    case 'approval':
      return TaskType.approval;
    case 'documentation':
      return TaskType.documentation;
    case 'meeting':
      return TaskType.meeting;
    default:
      return TaskType.other;
  }
}

TaskStatus _getTaskStatusFromString(String status) {
  switch (status) {
    case 'pending':
      return TaskStatus.pending;
    case 'inProgress':
      return TaskStatus.inProgress;
    case 'completed':
      return TaskStatus.completed;
    case 'cancelled':
      return TaskStatus.cancelled;
    case 'onHold':
      return TaskStatus.onHold;
    default:
      return TaskStatus.pending;
  }
}

TaskPriority _getTaskPriorityFromString(String priority) {
  switch (priority) {
    case 'low':
      return TaskPriority.low;
    case 'medium':
      return TaskPriority.medium;
    case 'high':
      return TaskPriority.high;
    case 'urgent':
      return TaskPriority.urgent;
    default:
      return TaskPriority.medium;
  }
}
