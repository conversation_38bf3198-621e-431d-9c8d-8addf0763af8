// file: lib/presentation/widgets/estate_card.dart
import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';

import '../../domain/entities/estate.dart';
import '../../domain/services/favorites_service.dart';
// import '../../domain/services/property_comparison_service.dart'; // معلق مؤقتاً
import '../../domain/services/estate_copy_service.dart';
import '../../core/services/notification_service.dart';
import '../../domain/models/notification_model.dart' as domain;
import '../../domain/services/user_interface_customization_service.dart';
import '../../domain/entities/user.dart';
import '../../core/constants/user_types.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../bloc/estate_bloc.dart';
import '../bloc/estate_event.dart';
import 'estate_details_page.dart';

/// بطاقة عرض العقار
class EstateCard extends StatelessWidget {
  /// نموذج العقار
  final Estate estate;

  /// ما إذا كان هذا القسم هو قسم العقارات المميزة
  final bool isFeaturedSection;

  /// ما إذا كان هذا القسم هو قسم إدارة العقارات
  final bool isManageSection;

  /// دالة يتم استدعاؤها عند النقر على زر التعديل
  final VoidCallback? onEdit;

  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final VoidCallback? onTap;

  const EstateCard({
    super.key,
    required this.estate,
    this.isFeaturedSection = false,
    this.isManageSection = false,
    this.onEdit,
    this.onTap,
  });

  /// دالة مساعدة لعرض الصورة مع fallback
  Widget _buildEstateImage(String imageUrl) {
    if (imageUrl.startsWith('http')) {
      return CachedNetworkImage(
        imageUrl: imageUrl,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: Colors.grey.shade200,
          child: const Center(
            child: SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.grey))))),
        errorWidget: (context, url, error) => Container(
          color: Colors.grey.shade200,
          child: const Icon(Icons.error, color: Colors.grey)));
    } else if (imageUrl.isNotEmpty) {
      return Image.file(
        File(imageUrl),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => Container(
          color: Colors.grey.shade200,
          child: const Icon(Icons.error, color: Colors.grey)));
    } else {
      return Container(
        color: Colors.grey[300],
        alignment: Alignment.center,
        child: const Icon(Icons.image_not_supported));
    }
  }

  @override
  Widget build(BuildContext context) {
    // اختر أول رابط من القائمة إذا كانت غير فارغة
    final String? firstImageUrl =
        estate.photoUrls.isNotEmpty ? estate.photoUrls.first : null;

    return GestureDetector(
      onTap: () {
        if (onTap != null) {
          onTap!();
        } else {
          // عند الضغط، انتقل لصفحة تفاصيل الإعلان مع تطبيق تأثير Hero
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => EstateDetailsPage(estate: estate)));
        }
      },
      child: Container(
        // إزالة الارتفاع الثابت للسماح بالتمدد التلقائي
        // إضافة عرض محدد لمنع تجاوز الحدود
        width: double.infinity,
        decoration: BoxDecoration(
          color: AppColors.cardBackground,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
              spreadRadius: 0),
          ],
          border: Border.all(
            color: AppColors.border,
            width: 0.5)),
        clipBehavior: Clip.antiAlias,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // صورة العقار
            _buildEstateImageSection(context, firstImageUrl),

            // معلومات العقار الأساسية
            _buildSimpleInfoSection(),
          ])));
  }

  /// بناء قسم المعلومات البسيطة
  Widget _buildSimpleInfoSection() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // عنوان العقار
          Text(
            estate.title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 15,
              height: 1.2,
              color: AppColors.textPrimary,
              letterSpacing: 0.2),
            maxLines: 1,
            overflow: TextOverflow.ellipsis),

          const SizedBox(height: 8),

          // الموقع مع أيقونة
          Row(
            children: [
              Icon(
                Icons.location_on,
                size: 14,
                color: AppColors.primary),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  estate.location,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis)),
            ]),

          const SizedBox(height: 8),

          // السعر والمساحة في نفس السطر
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // السعر
              Flexible(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.attach_money,
                      size: 12,
                      color: AppColors.success),
                    const SizedBox(width: 2),
                    Flexible(
                      child: Text(
                        '${estate.price} د.ك',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: AppColors.success),
                        overflow: TextOverflow.ellipsis)),
                  ])),

              // المساحة (إذا كانت متوفرة)
              if (estate.area != null)
                Flexible(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.square_foot,
                        size: 12,
                        color: AppColors.info),
                      const SizedBox(width: 2),
                      Flexible(
                        child: Text(
                          '${estate.area} م²',
                          style: TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w500,
                            color: AppColors.info),
                          overflow: TextOverflow.ellipsis)),
                    ])),
            ]),
        ]));
  }

  /// بناء قسم صورة العقار
  Widget _buildEstateImageSection(BuildContext context, String? firstImageUrl) {
    return Stack(
      children: [
        // الصورة
        SizedBox(
          height: 120,
          width: double.infinity,
          child: firstImageUrl != null && firstImageUrl.isNotEmpty
              ? Hero(
                  tag: 'estate_image_${estate.id}',
                  child: _buildEstateImage(firstImageUrl))
              : Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primary.withAlpha(13),
                        AppColors.primary.withAlpha(38),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter)),
                  alignment: Alignment.center,
                  child: Icon(
                    Icons.home_outlined,
                    size: 36,
                    color: AppColors.primary.withAlpha(77)))),

        // طبقة تدرج شفافة في أسفل الصورة
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 40,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withAlpha(102),
                ])))),

        // السعر في أسفل الصورة
        Positioned(
          bottom: 8,
          right: 8,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(26),
                  blurRadius: 4,
                  offset: const Offset(0, 2)),
              ]),
            child: Text(
              '${estate.price} د.ك',
              style: TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
                fontSize: 12)))),

        // تم إزالة أيقونة العقار المميز - ستظهر فقط بعد موافقة الإدارة

        // أزرار المفضلة والمقارنة
        if (!isManageSection)
          Positioned(
            top: 8,
            right: 8,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // زر المفضلة (فقط للمستخدمين غير الشركات العقارية)
                FutureBuilder<bool>(
                  future: _canShowFavoriteButton(),
                  builder: (context, canShowSnapshot) {
                    final canShowFavorite = canShowSnapshot.data ?? false;
                    if (!canShowFavorite) return const SizedBox.shrink();

                    return Container(
                      margin: const EdgeInsets.only(left: 4),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(26),
                            blurRadius: 4,
                            offset: const Offset(0, 2)),
                        ]),
                      child: FutureBuilder<bool>(
                        future: FavoritesService().isFavorite(estate.id),
                        builder: (context, snapshot) {
                          final isFavorite = snapshot.data ?? false;
                          return IconButton(
                            icon: Icon(
                              isFavorite ? Icons.favorite : Icons.favorite_border,
                              color: isFavorite ? Colors.red : Colors.grey,
                              size: 16),
                            onPressed: () => _toggleFavorite(context),
                            padding: const EdgeInsets.all(6),
                            constraints: const BoxConstraints());
                        }));
                  }),
                // تم إزالة زر المقارنة

                // زر النسخ (للمستثمرين فقط)
                FutureBuilder<bool>(
                  future: _canShowCopyButton(),
                  builder: (context, snapshot) {
                    final canShowCopy = snapshot.data ?? false;
                    if (!canShowCopy) return const SizedBox.shrink();

                    return Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(26),
                            blurRadius: 4,
                            offset: const Offset(0, 2)),
                        ]),
                      child: IconButton(
                        icon: const Icon(
                          Icons.content_copy,
                          color: Colors.purple,
                          size: 16),
                        onPressed: () => _showCopyDialog(context),
                        padding: const EdgeInsets.all(6),
                        constraints: const BoxConstraints()));
                  }),
              ])),

        // أزرار الإدارة
        if (isManageSection)
          Positioned(
            top: 8,
            right: 8,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  margin: const EdgeInsets.only(left: 4),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(26),
                        blurRadius: 4,
                        offset: const Offset(0, 2)),
                    ]),
                  child: IconButton(
                    icon: const Icon(Icons.edit, color: Colors.blue, size: 16),
                    onPressed: onEdit,
                    padding: const EdgeInsets.all(6),
                    constraints: const BoxConstraints())),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(26),
                        blurRadius: 4,
                        offset: const Offset(0, 2)),
                    ]),
                  child: IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red, size: 16),
                    onPressed: () => _showDeleteConfirmation(context),
                    padding: const EdgeInsets.all(6),
                    constraints: const BoxConstraints())),
              ])),
      ]);
  }

  /// تبديل حالة المفضلة
  Future<void> _toggleFavorite(BuildContext context) async {
    try {
      final favoritesService = FavoritesService();
      final isFavorite = await favoritesService.isFavorite(estate.id);

      if (isFavorite) {
        await favoritesService.removeFromFavorites(estate.id);
        if (context.mounted) {
          // عرض إشعار فوري
          NotificationService.showInAppNotification(
            context,
            title: 'تم إزالة العقار من المفضلة',
            body: 'تم إزالة "${estate.title}" من قائمة المفضلة',
            type: domain.NotificationType.other,
            duration: const Duration(seconds: 2),
          );

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('تم إزالة العقار من المفضلة'),
              backgroundColor: AppColors.warning));
        }
      } else {
        await favoritesService.addToFavorites(estate.id);
        if (context.mounted) {
          // عرض إشعار فوري
          NotificationService.showInAppNotification(
            context,
            title: 'تم إضافة العقار للمفضلة',
            body: 'تم إضافة "${estate.title}" إلى قائمة المفضلة',
            type: domain.NotificationType.other,
            duration: const Duration(seconds: 2),
          );

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('تم إضافة العقار إلى المفضلة'),
              backgroundColor: AppColors.success));
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: AppColors.error));
      }
    }
  }

  // دالة المقارنة - معلقة مؤقتاً
  /*
  /// تبديل حالة المقارنة
  Future<void> _toggleComparison(BuildContext context) async {
    try {
      final comparisonService = PropertyComparisonService();
      final isInComparison = await comparisonService.isInComparison(estate.id);

      if (isInComparison) {
        await comparisonService.removeFromComparison(estate.id);
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('تم إزالة العقار من المقارنة'),
              backgroundColor: AppColors.warning));
        }
      } else {
        await comparisonService.addToComparison(estate.id);
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('تم إضافة العقار إلى المقارنة'),
              backgroundColor: AppColors.success));
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: AppColors.error));
      }
    }
  }
  */

  /// التحقق من إمكانية عرض زر المفضلة
  Future<bool> _canShowFavoriteButton() async {
    try {
      final uiService = UserInterfaceCustomizationService();
      final userType = await uiService.getCurrentUserType();
      final userTypeString = userType.toString().split('.').last;

      // الشركات العقارية لا يمكنها الوصول للمفضلة
      return UserTypeConstants.canAccessFavorites(userTypeString);
    } catch (e) {
      // في حالة الخطأ، نخفي الزر للأمان
      return false;
    }
  }

  /// التحقق من إمكانية عرض زر النسخ
  Future<bool> _canShowCopyButton() async {
    try {
      final uiService = UserInterfaceCustomizationService();
      final userType = await uiService.getCurrentUserType();

      // فقط المستثمرين (الوكلاء) يمكنهم النسخ
      if (userType != UserType.agent) return false;

      // لا يمكن نسخ العقارات المنسوخة
      if (estate.isCopied) return false;

      // التحقق من نوع العقار - فقط العقارات للإيجار يمكن نسخها
      if (estate.mainCategory != 'عقار للايجار') return false;

      // التحقق من إمكانية النسخ باستخدام الخدمة
      final copyService = EstateCopyService();
      final currentUserId = FirebaseAuth.instance.currentUser?.uid;
      if (currentUserId == null) return false;

      return await copyService.canCopyEstate(estate.id, currentUserId);
    } catch (e) {
      return false;
    }
  }

  /// عرض مربع حوار نسخ العقار
  void _showCopyDialog(BuildContext context) {
    Navigator.pushNamed(
      context,
      '/copy-estate',
      arguments: estate);
  }

  /// عرض مربع حوار تأكيد الحذف
  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text(
          'هل أنت متأكد من رغبتك في حذف هذا العقار؟ لا يمكن التراجع عن هذه العملية.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء')),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white),
            onPressed: () {
              // حذف العقار باستخدام BLoC
              BlocProvider.of<EstateBloc>(context).add(
                DeleteEstateEvent(estate.id));
              Navigator.pop(context);

              // عرض رسالة نجاح
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('تم حذف العقار بنجاح'),
                  backgroundColor: AppColors.success));
            },
            child: const Text('حذف')),
        ]));
  }
}
