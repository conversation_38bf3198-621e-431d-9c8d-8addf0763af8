import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

/// خدمة إدارة الإشعارات الترويجية
class PromotionalNotificationService {
  static final PromotionalNotificationService _instance = PromotionalNotificationService._internal();
  factory PromotionalNotificationService() => _instance;
  PromotionalNotificationService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// جدولة إشعار ترويجي للعقار بعد 24 ساعة
  Future<void> schedulePromotionalNotification({
    required String estateId,
    required String userId,
    required String estateTitle,
  }) async {
    try {
      print('🔔 جدولة إشعار ترويجي للعقار: $estateId');

      // حساب وقت الإشعار الترويجي (24 ساعة من الآن)
      final scheduledTime = DateTime.now().add(const Duration(hours: 24));

      // إنشاء بيانات الإشعار المجدول
      final notificationData = {
        'type': 'promotional_upgrade',
        'estateId': estateId,
        'userId': userId,
        'estateTitle': estateTitle,
        'scheduledTime': Timestamp.fromDate(scheduledTime),
        'createdAt': Timestamp.now(),
        'processed': false,
        'notificationData': {
          'title': 'اجعل إعلانك أكثر تميزاً! ⭐',
          'body': 'هل تريد المزيد من المشترين والمستأجرين لعقارك "$estateTitle"؟',
          'contactInfo': {
            'whatsapp': '+965 9929 8821',
            'email': '<EMAIL>'
          }
        }
      };
      
      // حفظ الإشعار المجدول في Firestore
      final docRef = await _firestore.collection('scheduledNotifications').add(notificationData);

      print('✅ تم جدولة الإشعار الترويجي بنجاح: ${docRef.id}');
      print('⏰ وقت الإشعار الترويجي المجدول: ${scheduledTime.toIso8601String()}');

    } catch (e) {
      print('❌ خطأ في جدولة الإشعار الترويجي: $e');
      // لا نرمي الخطأ لأن فشل جدولة الإشعار لا يجب أن يؤثر على إنشاء الإعلان
    }
  }

  /// إلغاء الإشعار الترويجي المجدول للعقار
  Future<void> cancelScheduledPromotionalNotification(String estateId) async {
    try {
      print('🚫 إلغاء الإشعار الترويجي المجدول للعقار: $estateId');

      // البحث عن الإشعار المجدول
      final query = await _firestore
          .collection('scheduledNotifications')
          .where('estateId', isEqualTo: estateId)
          .where('type', isEqualTo: 'promotional_upgrade')
          .where('processed', isEqualTo: false)
          .get();

      if (query.docs.isNotEmpty) {
        // إلغاء جميع الإشعارات المجدولة للعقار
        final batch = _firestore.batch();
        for (final doc in query.docs) {
          batch.update(doc.reference, {
            'processed': true,
            'cancelled': true,
            'cancelledAt': Timestamp.now(),
            'cancelReason': 'تم إلغاء الإشعار بواسطة المستخدم'
          });
        }
        await batch.commit();

        print('✅ تم إلغاء ${query.docs.length} إشعار ترويجي مجدول للعقار: $estateId');
      } else {
        print('ℹ️ لا توجد إشعارات ترويجية مجدولة للعقار: $estateId');
      }

    } catch (e) {
      print('❌ خطأ في إلغاء الإشعار الترويجي: $e');
    }
  }

  /// الحصول على الإشعارات المجدولة للمستخدم الحالي
  Stream<List<ScheduledNotification>> getUserScheduledNotifications() {
    final userId = _auth.currentUser?.uid;
    if (userId == null) {
      return Stream.value([]);
    }

    return _firestore
        .collection('scheduledNotifications')
        .where('userId', isEqualTo: userId)
        .where('processed', isEqualTo: false)
        .orderBy('scheduledTime', descending: false)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return ScheduledNotification.fromFirestore(doc);
      }).toList();
    });
  }

  /// تحديث وقت الأرشفة المجدول للعقار
  Future<void> updateScheduledArchiveTime({
    required String estateId,
    required DateTime newScheduledTime,
  }) async {
    try {
      debugPrint('🔄 تحديث وقت الأرشفة المجدول للعقار: $estateId');
      
      // البحث عن الإشعار المجدول
      final query = await _firestore
          .collection('scheduledNotifications')
          .where('estateId', isEqualTo: estateId)
          .where('type', isEqualTo: 'estate_archive')
          .where('processed', isEqualTo: false)
          .get();
      
      if (query.docs.isNotEmpty) {
        final batch = _firestore.batch();
        for (final doc in query.docs) {
          batch.update(doc.reference, {
            'scheduledTime': Timestamp.fromDate(newScheduledTime),
            'updatedAt': Timestamp.now(),
          });
        }
        await batch.commit();
        
        debugPrint('✅ تم تحديث وقت الأرشفة المجدول: ${newScheduledTime.toIso8601String()}');
      }
      
    } catch (e) {
      debugPrint('❌ خطأ في تحديث وقت الأرشفة المجدول: $e');
    }
  }

  /// الحصول على إحصائيات الإشعارات المجدولة
  Future<NotificationStats> getNotificationStats() async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        return NotificationStats.empty();
      }

      // عدد الإشعارات المجدولة
      final scheduledQuery = await _firestore
          .collection('scheduledNotifications')
          .where('userId', isEqualTo: userId)
          .where('processed', isEqualTo: false)
          .get();

      // عدد الإشعارات المعالجة
      final processedQuery = await _firestore
          .collection('scheduledNotifications')
          .where('userId', isEqualTo: userId)
          .where('processed', isEqualTo: true)
          .get();

      return NotificationStats(
        scheduledCount: scheduledQuery.docs.length,
        processedCount: processedQuery.docs.length,
        totalCount: scheduledQuery.docs.length + processedQuery.docs.length,
      );
      
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على إحصائيات الإشعارات: $e');
      return NotificationStats.empty();
    }
  }
}

/// نموذج الإشعار المجدول
class ScheduledNotification {
  final String id;
  final String type;
  final String estateId;
  final String userId;
  final String estateTitle;
  final DateTime scheduledTime;
  final DateTime createdAt;
  final bool processed;
  final Map<String, dynamic> notificationData;

  ScheduledNotification({
    required this.id,
    required this.type,
    required this.estateId,
    required this.userId,
    required this.estateTitle,
    required this.scheduledTime,
    required this.createdAt,
    required this.processed,
    required this.notificationData,
  });

  factory ScheduledNotification.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ScheduledNotification(
      id: doc.id,
      type: data['type'] ?? '',
      estateId: data['estateId'] ?? '',
      userId: data['userId'] ?? '',
      estateTitle: data['estateTitle'] ?? '',
      scheduledTime: (data['scheduledTime'] as Timestamp).toDate(),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      processed: data['processed'] ?? false,
      notificationData: data['notificationData'] ?? {},
    );
  }

  /// الوقت المتبقي حتى الأرشفة
  Duration get timeUntilArchive {
    final now = DateTime.now();
    if (scheduledTime.isBefore(now)) {
      return Duration.zero;
    }
    return scheduledTime.difference(now);
  }

  /// هل حان وقت الأرشفة؟
  bool get isReadyForArchive {
    return DateTime.now().isAfter(scheduledTime);
  }
}

/// إحصائيات الإشعارات
class NotificationStats {
  final int scheduledCount;
  final int processedCount;
  final int totalCount;

  NotificationStats({
    required this.scheduledCount,
    required this.processedCount,
    required this.totalCount,
  });

  factory NotificationStats.empty() {
    return NotificationStats(
      scheduledCount: 0,
      processedCount: 0,
      totalCount: 0,
    );
  }
}
