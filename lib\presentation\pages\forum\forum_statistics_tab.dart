import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:percent_indicator/percent_indicator.dart';


import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/user_statistics_model.dart';
import '../../../domain/models/forum/user_level_model.dart';
import '../../../domain/models/forum/badge_model.dart';
import '../../providers/forum_provider.dart';
import '../../providers/auth_provider.dart' as app_auth;
import '../../widgets/common/loading_indicator.dart';


/// علامة تبويب إحصائيات المستخدم الشخصية في المنتدى
/// تعرض إحصائيات نشاط المستخدم الحالي فقط (مواضيعه، مشاركاته، إعجاباته، شاراته، إنجازاته)
/// وليس إحصائيات المنتدى العامة
class ForumStatisticsTab extends StatefulWidget {
  const ForumStatisticsTab({super.key});

  @override
  State<ForumStatisticsTab> createState() => _ForumStatisticsTabState();
}

class _ForumStatisticsTabState extends State<ForumStatisticsTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // جلب إحصائيات المستخدم عند تهيئة التبويب
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadUserStatistics();
    });
  }

  /// تحميل إحصائيات المستخدم
  Future<void> _loadUserStatistics() async {
    final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);

    if (authProvider.isLoggedIn && authProvider.user != null) {
      debugPrint('🔄 [ForumStatisticsTab] بدء جلب إحصائيات المستخدم: ${authProvider.user!.uid}');

      try {
        // محاولة جلب الإحصائيات أولاً
        await forumProvider.fetchUserStatistics(authProvider.user!.uid);

        // التحقق من حالة البيانات بعد التحميل
        debugPrint('🔍 [ForumStatisticsTab] حالة البيانات بعد التحميل: ${forumProvider.userStatisticsState}');
        debugPrint('🔍 [ForumStatisticsTab] البيانات: ${forumProvider.userStatistics?.toString() ?? 'null'}');

        // إذا لم توجد إحصائيات، إجبار إعادة الحساب
        if (forumProvider.userStatistics == null) {
          debugPrint('🔄 [ForumStatisticsTab] لا توجد إحصائيات - إجبار إعادة الحساب للمستخدم: ${authProvider.user!.uid}');
          await forumProvider.recalculateUserStatistics(authProvider.user!.uid);
        }

        debugPrint('✅ [ForumStatisticsTab] تم تحميل الإحصائيات بنجاح');
      } catch (e) {
        debugPrint('❌ [ForumStatisticsTab] خطأ في تحميل الإحصائيات: $e');
      }
    }
  }

  /// إنشاء إحصائيات افتراضية للمستخدم
  UserStatisticsModel _createDefaultUserStats(dynamic user) {
    return UserStatisticsModel(
      userId: user.uid,
      userName: user.displayName ?? 'مستخدم',
      userImage: user.photoURL,
      joinDate: DateTime.now(),
      lastActivityDate: DateTime.now(),
      level: 'مبتدئ',
      topicsCount: 0,
      postsCount: 0,
      totalTopicViews: 0,
      totalTopicLikes: 0,
      totalPostLikes: 0,
      featuredTopicsCount: 0,
      pinnedTopicsCount: 0,
      solvedTopicsCount: 0,
      bestAnswersCount: 0,
      topicSharesCount: 0,
      topicBookmarksCount: 0,
      points: 0,
      badges: [],
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Consumer2<ForumProvider, app_auth.AuthProvider>(
        builder: (context, forumProvider, authProvider, child) {
          debugPrint('🔍 [ForumStatisticsTab] Build - isLoggedIn: ${authProvider.isLoggedIn}');
          debugPrint('🔍 [ForumStatisticsTab] Build - userStatisticsState: ${forumProvider.userStatisticsState}');
          debugPrint('🔍 [ForumStatisticsTab] Build - userStatistics: ${forumProvider.userStatistics?.toString() ?? 'null'}');

          if (!authProvider.isLoggedIn) {
            debugPrint('🔍 [ForumStatisticsTab] عرض شاشة تسجيل الدخول');
            return _buildLoginRequired();
          }

          if (forumProvider.userStatisticsState == LoadingState.loading) {
            debugPrint('🔍 [ForumStatisticsTab] عرض شاشة التحميل');
            return _buildLoadingState();
          }

          if (forumProvider.userStatisticsState == LoadingState.error) {
            return _buildErrorState();
          }

          final userStats = forumProvider.userStatistics;
          if (userStats == null) {
            // إنشاء إحصائيات افتراضية للعرض
            final defaultStats = _createDefaultUserStats(authProvider.user!);
            return DefaultTabController(
              length: 3,
              child: Column(
                children: [
                  _buildHeader(defaultStats),
                  _buildTabBar(),
                  Expanded(
                    child: TabBarView(
                      children: [
                        _buildSummaryTab(defaultStats),
                        _buildActivitiesTab(defaultStats),
                        _buildAchievementsTab(defaultStats, authProvider.user!.uid),
                      ])),
                ]));
          }

          // إذا كانت البيانات موجودة ولكن فارغة، نعرضها مع القيم الافتراضية
          if (forumProvider.userStatisticsState == LoadingState.empty) {
          }
          return DefaultTabController(
            length: 3,
            child: Column(
              children: [
                // Header with user info and create topic button
                _buildHeader(userStats),

                // Tab bar
                _buildTabBar(),

                // Tab content
                Expanded(
                  child: TabBarView(
                    children: [
                      _buildSummaryTab(userStats),
                      _buildActivitiesTab(userStats),
                      _buildAchievementsTab(userStats, authProvider.user!.uid),
                    ])),
              ]));
        }));
  }

  /// بناء حالة تسجيل الدخول المطلوبة
  Widget _buildLoginRequired() {
    return Center(
      child: Container(
        margin: EdgeInsets.all(24),
        padding: EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: Offset(0, 4)),
          ]),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle),
              child: Icon(
                Icons.lock_outline,
                size: 40,
                color: AppColors.primary)),
            SizedBox(height: 24),
            Text(
              'تسجيل الدخول مطلوب',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87)),
            SizedBox(height: 12),
            Text(
              'يجب تسجيل الدخول لعرض إحصائياتك الشخصية\nوتتبع نشاطك في المنتدى',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
                height: 1.5)),
            SizedBox(height: 32),
            Container(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.pushNamed(context, '/login');
                },
                icon: Icon(Icons.login, size: 20),
                label: Text('تسجيل الدخول'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                  elevation: 2))),
          ])));
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          LoadingIndicator(),
          SizedBox(height: 24),
          Text(
            'جاري تحميل إحصائياتك...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600)),
        ]));
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Container(
        margin: EdgeInsets.all(24),
        padding: EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: Offset(0, 4)),
          ]),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400),
            SizedBox(height: 24),
            Text(
              'حدث خطأ',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87)),
            SizedBox(height: 12),
            Text(
              'حدث خطأ في تحميل إحصائياتك الشخصية\nيرجى المحاولة مرة أخرى',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
                height: 1.5)),
            SizedBox(height: 32),
            Container(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _loadUserStatistics,
                icon: Icon(Icons.refresh, size: 20),
                label: Text('إعادة المحاولة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                  elevation: 2))),
          ])));
  }



  /// بناء الهيدر المحسن مع معلومات المستخدم وأزرار الإجراءات
  Widget _buildHeader(UserStatisticsModel userStats) {
    final currentLevel = UserLevelModel.getCurrentLevel(userStats.points);
    final nextLevel = UserLevelModel.getNextLevel(userStats.points);

    double progress = 0.0;
    if (nextLevel != null) {
      final pointsForCurrentLevel = currentLevel.requiredPoints;
      final pointsForNextLevel = nextLevel.requiredPoints;
      final pointsRange = pointsForNextLevel - pointsForCurrentLevel;
      final userPointsInRange = userStats.points - pointsForCurrentLevel;
      progress = userPointsInRange / pointsRange;
    } else {
      progress = 1.0; // أعلى مستوى
    }

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
            AppColors.primary.withValues(alpha: 0.6),
          ],
          stops: [0.0, 0.7, 1.0],
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: Offset(0, 10),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.fromLTRB(20, 20, 20, 30),
          child: Column(
            children: [
              // شريط التنقل العلوي المحسن
              Container(
                padding: EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.08),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.15),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                  // زر الرجوع العصري
                  InkWell(
                    onTap: () => Navigator.pop(context),
                    borderRadius: BorderRadius.circular(16),
                    child: Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.15),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.2),
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 8,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.arrow_back_ios_rounded,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),

                  // العنوان المحسن
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.analytics_rounded,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                        SizedBox(width: 8),
                        Text(
                          'إحصائياتي',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Cairo',
                          ),
                        ),
                      ],
                    ),
                  ),

                  // أزرار الإجراءات العصرية
                  Row(
                    children: [
                      // زر البحث
                      _buildIconButton(
                        icon: Icons.search_rounded,
                        onPressed: () {
                          // فتح البحث
                        },
                      ),
                      SizedBox(width: 8),
                      // زر الفلتر
                      _buildIconButton(
                        icon: Icons.tune_rounded,
                        onPressed: () {
                          // فتح الفلتر
                        },
                      ),
                      SizedBox(width: 8),
                      // زر القائمة
                      _buildIconButton(
                        icon: Icons.more_vert_rounded,
                        onPressed: () {
                          _showMoreOptionsMenu();
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),

              SizedBox(height: 24),

              // معلومات المستخدم
              Row(
                children: [
                  // الصورة الشخصية المحسنة
                  Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 15,
                          offset: Offset(0, 5),
                        ),
                      ],
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 3,
                      ),
                    ),
                    child: userStats.userImage != null
                        ? ClipOval(
                            child: Image.network(
                              userStats.userImage!,
                              width: 70,
                              height: 70,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [AppColors.primary.withValues(alpha: 0.3), AppColors.primary.withValues(alpha: 0.1)],
                                    ),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.person_rounded,
                                    size: 35,
                                    color: AppColors.primary,
                                  ),
                                );
                              },
                            ),
                          )
                        : Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [AppColors.primary.withValues(alpha: 0.3), AppColors.primary.withValues(alpha: 0.1)],
                              ),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.person_rounded,
                              size: 35,
                              color: AppColors.primary,
                            ),
                          ),
                  ),
                  SizedBox(width: 20),

                  // معلومات المستخدم المحسنة
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          userStats.userName,
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 22,
                            fontFamily: 'Cairo',
                          ),
                        ),
                        SizedBox(height: 8),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                currentLevel.icon,
                                color: Colors.white,
                                size: 16,
                              ),
                              SizedBox(width: 6),
                              Text(
                                currentLevel.name,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: 'Cairo',
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.star_rounded,
                              color: Colors.amber,
                              size: 18,
                            ),
                            SizedBox(width: 6),
                            Text(
                              '${userStats.points} نقطة',
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.9),
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                                fontFamily: 'Cairo',
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              SizedBox(height: 24),

              // شريط التقدم المحسن
              if (nextLevel != null) ...[
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'المستوى التالي: ${nextLevel.name}',
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.9),
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Cairo',
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'متبقي ${nextLevel.requiredPoints - userStats.points}',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Cairo',
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 12),
                      Container(
                        height: 8,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: LinearPercentIndicator(
                            lineHeight: 8,
                            percent: progress,
                            backgroundColor: Colors.transparent,
                            progressColor: Colors.white,
                            barRadius: Radius.circular(4),
                            padding: EdgeInsets.zero,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// بناء زر إجراء عصري في الهيدر
  Widget _buildModernActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required Color backgroundColor,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: color,
                size: 18,
              ),
            ),
            SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 10,
                fontWeight: FontWeight.w600,
                fontFamily: 'Cairo',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض قائمة خيارات إضافية
  void _showMoreOptionsMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        margin: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مقبض السحب
            Container(
              margin: EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            SizedBox(height: 20),

            // العنوان
            Text(
              'خيارات إضافية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                fontFamily: 'Cairo',
                color: Colors.black87,
              ),
            ),

            SizedBox(height: 20),

            // الخيارات
            _buildMenuOption(
              icon: Icons.refresh_rounded,
              title: 'تحديث البيانات',
              subtitle: 'إعادة تحميل الإحصائيات',
              onTap: () {
                Navigator.pop(context);
                _loadUserStatistics();
              },
            ),

            _buildMenuOption(
              icon: Icons.share_rounded,
              title: 'مشاركة الإحصائيات',
              subtitle: 'شارك إنجازاتك مع الآخرين',
              onTap: () {
                Navigator.pop(context);
                // مشاركة الإحصائيات
              },
            ),

            _buildMenuOption(
              icon: Icons.download_rounded,
              title: 'تصدير التقرير',
              subtitle: 'احفظ تقريراً مفصلاً',
              onTap: () {
                Navigator.pop(context);
                // تصدير التقرير
              },
            ),

            _buildMenuOption(
              icon: Icons.settings_rounded,
              title: 'إعدادات الإحصائيات',
              subtitle: 'تخصيص عرض البيانات',
              onTap: () {
                Navigator.pop(context);
                // فتح الإعدادات
              },
            ),

            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// بناء خيار في القائمة
  Widget _buildMenuOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary.withValues(alpha: 0.1),
                    AppColors.primary.withValues(alpha: 0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: AppColors.primary,
                size: 24,
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Cairo',
                      color: Colors.black87,
                    ),
                  ),
                  SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 13,
                      fontFamily: 'Cairo',
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios_rounded,
              color: Colors.grey.shade400,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شريط التبويب المحسن
  Widget _buildTabBar() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: TabBar(
          indicator: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppColors.primary,
                AppColors.primary.withValues(alpha: 0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          indicatorPadding: EdgeInsets.all(4),
          labelColor: Colors.white,
          unselectedLabelColor: Colors.grey.shade600,
          labelStyle: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
            fontSize: 13,
          ),
          unselectedLabelStyle: TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.w500,
            fontSize: 13,
          ),
          tabs: [
            Tab(
              height: 60,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.dashboard_rounded, size: 22),
                  SizedBox(height: 4),
                  Text('الملخص'),
                ],
              ),
            ),
            Tab(
              height: 60,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.timeline_rounded, size: 22),
                  SizedBox(height: 4),
                  Text('النشاطات'),
                ],
              ),
            ),
            Tab(
              height: 60,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.emoji_events_rounded, size: 22),
                  SizedBox(height: 4),
                  Text('الإنجازات'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }



  /// بناء علامة تبويب الملخص
  Widget _buildSummaryTab(UserStatisticsModel userStats) {
    debugPrint('🔍 [ForumStatisticsTab] بناء قسم الملخص - البيانات: ${userStats.toString()}');

    return RefreshIndicator(
      onRefresh: _loadUserStatistics,
      child: ListView(
        padding: EdgeInsets.all(16),
        children: [
          // إحصائيات سريعة
          _buildQuickStats(userStats),

          SizedBox(height: 16),

          // الشارات
          _buildBadgesSection(userStats),

          SizedBox(height: 16),

          // إحصائيات المنتدى التفصيلية
          _buildForumStatsSection(userStats),

          SizedBox(height: 16),

          // معلومات العضوية
          _buildMembershipInfo(userStats),

          SizedBox(height: 16),

          // زر عرض إحصائيات المنتدى العامة
          _buildViewFullStatisticsButton(),

          SizedBox(height: 20),
        ]));
  }

  /// بناء الإحصائيات السريعة المحسنة
  Widget _buildQuickStats(UserStatisticsModel userStats) {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.grey.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 15,
            offset: Offset(0, 5),
          ),
        ],
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: Offset(0, 4),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.dashboard_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'نظرة سريعة',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                        fontFamily: 'Cairo',
                      ),
                    ),
                    Text(
                      'إحصائياتك في لمحة',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: _buildQuickStatItem(
                  'المواضيع',
                  userStats.topicsCount.toString(),
                  Icons.topic_rounded,
                  AppColors.primary,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: _buildQuickStatItem(
                  'المشاركات',
                  userStats.postsCount.toString(),
                  Icons.forum_rounded,
                  Colors.green,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildQuickStatItem(
                  'الإعجابات',
                  (userStats.totalTopicLikes + userStats.totalPostLikes).toString(),
                  Icons.favorite_rounded,
                  Colors.red,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: _buildQuickStatItem(
                  'الشارات',
                  userStats.badges.length.toString(),
                  Icons.emoji_events_rounded,
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائي سريع محسن
  Widget _buildQuickStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [color, color.withValues(alpha: 0.8)],
              ),
              borderRadius: BorderRadius.circular(14),
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 0.3),
                  blurRadius: 6,
                  offset: Offset(0, 3),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 28,
            ),
          ),
          SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
              fontFamily: 'Cairo',
            ),
          ),
          SizedBox(height: 6),
          Text(
            title,
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey.shade700,
              fontWeight: FontWeight.w600,
              fontFamily: 'Cairo',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء قسم الشارات
  Widget _buildBadgesSection(UserStatisticsModel userStats) {
    final badges = userStats.badges
        .map((badgeId) => BadgeModel.getBadgeById(badgeId))
        .where((badge) => badge != null)
        .cast<BadgeModel>()
        .toList();

    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.emoji_events,
                color: Colors.orange,
                size: 24),
              SizedBox(width: 12),
              Text(
                'الشارات المحققة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87)),
              Spacer(),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20)),
                child: Text(
                  '${badges.length}',
                  style: TextStyle(
                    color: Colors.orange,
                    fontWeight: FontWeight.bold,
                    fontSize: 14))),
            ]),
          SizedBox(height: 20),
          if (badges.isEmpty)
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.grey.shade200)),
              child: Column(
                children: [
                  Icon(
                    Icons.emoji_events_outlined,
                    size: 48,
                    color: Colors.grey.shade400),
                  SizedBox(height: 12),
                  Text(
                    'لم تحصل على أي شارات بعد',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 16)),
                  SizedBox(height: 8),
                  Text(
                    'استمر في المشاركة لتحصل على شارات جديدة',
                    style: TextStyle(
                      color: Colors.grey.shade500,
                      fontSize: 14)),
                ]))
          else
            GridView.builder(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 0.8),
              itemCount: badges.length,
              itemBuilder: (context, index) {
                final badge = badges[index];
                return Container(
                  decoration: BoxDecoration(
                    color: badge.getColorObject().withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: badge.getColorObject().withValues(alpha: 0.3),
                      width: 2)),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        badge.icon,
                        size: 28,
                        color: badge.getColorObject()),
                      SizedBox(height: 8),
                      Flexible(
                        child: Text(
                          badge.name,
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis)),
                    ]));
              }),
        ]));
  }

  /// بناء قسم إحصائيات المنتدى
  Widget _buildForumStatsSection(UserStatisticsModel userStats) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: Offset(0, 2)),
        ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: AppColors.primary,
                size: 24),
              SizedBox(width: 12),
              Text(
                'إحصائياتي التفصيلية',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87)),
            ]),
          SizedBox(height: 20),
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            mainAxisSpacing: 12,
            crossAxisSpacing: 12,
            childAspectRatio: 1.3,
            children: [
              _buildStatCard(
                'المواضيع',
                userStats.topicsCount.toString(),
                Icons.topic_outlined,
                AppColors.primary,
              ),
              _buildStatCard(
                'المشاركات',
                userStats.postsCount.toString(),
                Icons.forum_outlined,
                Colors.green,
              ),
              _buildStatCard(
                'الإعجابات المستلمة',
                (userStats.totalTopicLikes + userStats.totalPostLikes).toString(),
                Icons.favorite_outline,
                Colors.red),
              _buildStatCard(
                'المشاهدات',
                userStats.totalTopicViews.toString(),
                Icons.visibility_outlined,
                Colors.blue),
            ]),
          SizedBox(height: 16),
          // Additional stats row
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'المواضيع المميزة',
                  userStats.featuredTopicsCount.toString(),
                  Icons.star_outline,
                  Colors.amber)),
              SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'أفضل الإجابات',
                  userStats.bestAnswersCount.toString(),
                  Icons.check_circle_outline,
                  Colors.purple)),
            ]),
        ]));
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: color,
            size: 28),
          SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color)),
          SizedBox(height: 4),
          Flexible(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis)),
        ]));
  }



  /// بناء علامة تبويب النشاطات
  Widget _buildActivitiesTab(UserStatisticsModel userStats) {
    debugPrint('🔍 [ForumStatisticsTab] بناء قسم النشاطات - البيانات: ${userStats.toString()}');

    return RefreshIndicator(
      onRefresh: _loadUserStatistics,
      child: ListView(
        padding: EdgeInsets.all(16),
        children: [
          // معلومات العضوية
          _buildMembershipInfo(userStats),

          SizedBox(height: 24),

          // إحصائيات النشاط التفصيلية
          _buildDetailedActivityStats(userStats),

          SizedBox(height: 24),

          // معدلات النشاط
          _buildActivityRates(userStats),

          SizedBox(height: 20),
        ]));
  }

  /// بناء علامة تبويب الإنجازات
  Widget _buildAchievementsTab(UserStatisticsModel userStats, String userId) {
    debugPrint('🔍 [ForumStatisticsTab] بناء قسم الإنجازات - البيانات: ${userStats.toString()}');

    return RefreshIndicator(
      onRefresh: _loadUserStatistics,
      child: ListView(
        padding: EdgeInsets.all(16),
        children: [
          // الشارات المحققة
          _buildEarnedBadges(userStats),

          SizedBox(height: 24),

          // الإنجازات المحققة
          _buildCompletedAchievements(userStats),

          SizedBox(height: 24),

          // الإنجازات القادمة
          _buildUpcomingAchievements(userStats),

          SizedBox(height: 24),

          // إحصائيات الإنجازات
          _buildAchievementStats(userStats),

          SizedBox(height: 20),
        ]));
  }

  /// بناء قسم الإجراءات السريعة
  Widget _buildViewFullStatisticsButton() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.grey.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 15,
            offset: Offset(0, 5),
          ),
        ],
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // العنوان والأيقونة
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: Offset(0, 4),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.analytics_rounded,
                  size: 28,
                  color: Colors.white,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إحصائيات المنتدى العامة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                        fontFamily: 'Cairo',
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'اطلع على إحصائيات المنتدى الكاملة',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                        fontFamily: 'Cairo',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 20),

          // أزرار الإجراءات
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  icon: Icons.bar_chart_rounded,
                  label: 'الإحصائيات العامة',
                  color: AppColors.primary,
                  onPressed: () {
                    Navigator.pushNamed(context, '/forum/statistics');
                  },
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  icon: Icons.leaderboard_rounded,
                  label: 'لوحة المتصدرين',
                  color: Colors.orange,
                  onPressed: () {
                    Navigator.pushNamed(context, '/forum/leaderboard');
                  },
                ),
              ),
            ],
          ),

          SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  icon: Icons.trending_up_rounded,
                  label: 'التقارير',
                  color: Colors.green,
                  onPressed: () {
                    Navigator.pushNamed(context, '/forum/reports');
                  },
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  icon: Icons.share_rounded,
                  label: 'مشاركة',
                  color: Colors.blue,
                  onPressed: () {
                    // مشاركة الإحصائيات
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color,
                fontFamily: 'Cairo',
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات العضوية
  Widget _buildMembershipInfo(UserStatisticsModel userStats) {
    final membershipDuration = DateTime.now().difference(userStats.joinDate);
    final daysSinceJoining = membershipDuration.inDays;
    final lastActivityDuration = DateTime.now().difference(userStats.lastActivityDate);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person_outline, color: AppColors.primary, size: 24),
                SizedBox(width: 8),
                Text(
                  'معلومات العضوية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
              ]),
            SizedBox(height: 16),

            _buildInfoRow('تاريخ الانضمام', _formatDate(userStats.joinDate)),
            _buildInfoRow('مدة العضوية', '$daysSinceJoining يوم'),
            _buildInfoRow('آخر نشاط', _formatLastActivity(lastActivityDuration)),
            _buildInfoRow('اسم المستخدم', userStats.userName),
            _buildInfoRow('المستوى الحالي', userStats.level),
          ])));
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 14)),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14)),
        ]));
  }

  /// بناء إحصائيات النشاط التفصيلية
  Widget _buildDetailedActivityStats(UserStatisticsModel userStats) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics_outlined, color: AppColors.primary, size: 24),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'إحصائيات النشاط التفصيلية',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis)),
              ]),
            SizedBox(height: 16),

            Column(
              children: [
                Row(
                  children: [
                    Expanded(child: _buildDetailedStatItem('المواضيع المميزة', userStats.featuredTopicsCount, Icons.star)),
                    SizedBox(width: 8),
                    Expanded(child: _buildDetailedStatItem('المواضيع المثبتة', userStats.pinnedTopicsCount, Icons.push_pin)),
                  ]),
                SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(child: _buildDetailedStatItem('المواضيع المحلولة', userStats.solvedTopicsCount, Icons.check_circle)),
                    SizedBox(width: 8),
                    Expanded(child: _buildDetailedStatItem('أفضل الإجابات', userStats.bestAnswersCount, Icons.thumb_up)),
                  ]),
                SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(child: _buildDetailedStatItem('المشاركات', userStats.topicSharesCount, Icons.share)),
                    SizedBox(width: 8),
                    Expanded(child: _buildDetailedStatItem('الإشارات المرجعية', userStats.topicBookmarksCount, Icons.bookmark)),
                  ]),
              ]),
          ])));
  }

  /// بناء عنصر إحصائية تفصيلية
  Widget _buildDetailedStatItem(String title, int value, IconData icon) {
    return Container(
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: AppColors.primary, size: 18),
          SizedBox(height: 4),
          Text(
            value.toString(),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.primary)),
          SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade600),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis),
        ]));
  }

  /// بناء معدلات النشاط
  Widget _buildActivityRates(UserStatisticsModel userStats) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_up, color: AppColors.primary, size: 24),
                SizedBox(width: 8),
                Text(
                  'معدلات النشاط',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
              ]),
            SizedBox(height: 16),

            Column(
              children: [
                _buildActivityRateItem(
                  'النشاط اليومي',
                  userStats.dailyActivityRate,
                  Icons.today,
                  Colors.green),
                SizedBox(height: 12),
                _buildActivityRateItem(
                  'النشاط الأسبوعي',
                  userStats.weeklyActivityRate,
                  Icons.date_range,
                  Colors.blue),
                SizedBox(height: 12),
                _buildActivityRateItem(
                  'النشاط الشهري',
                  userStats.monthlyActivityRate,
                  Icons.calendar_month,
                  Colors.orange),
              ]),
          ])));
  }

  /// بناء عنصر معدل النشاط
  Widget _buildActivityRateItem(String title, double rate, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500)),
                  Text(
                    '${rate.toStringAsFixed(1)}%',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: color)),
                ]),
              SizedBox(height: 4),
              LinearPercentIndicator(
                lineHeight: 6,
                percent: (rate / 100).clamp(0.0, 1.0),
                backgroundColor: Colors.grey.shade200,
                progressColor: color,
                barRadius: Radius.circular(3),
                padding: EdgeInsets.zero),
            ])),
      ]);
  }



  /// بناء الشارات المحققة
  Widget _buildEarnedBadges(UserStatisticsModel userStats) {
    final earnedBadges = userStats.badges
        .map((badgeId) => BadgeModel.getBadgeById(badgeId))
        .where((badge) => badge != null)
        .cast<BadgeModel>()
        .toList();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.emoji_events, color: AppColors.primary, size: 24),
                SizedBox(width: 8),
                Text(
                  'الشارات المحققة (${earnedBadges.length})',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
              ]),
            SizedBox(height: 16),

            if (earnedBadges.isEmpty)
              Center(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Icon(Icons.emoji_events_outlined,
                           size: 48, color: Colors.grey.shade400),
                      SizedBox(height: 8),
                      Text(
                        'لم تحصل على أي شارات بعد',
                        style: TextStyle(color: Colors.grey.shade600)),
                    ])))
            else
              GridView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 0.8),
                itemCount: earnedBadges.length,
                itemBuilder: (context, index) {
                  final badge = earnedBadges[index];
                  return _buildBadgeCard(badge, true);
                }),
          ])));
  }

  /// بناء بطاقة شارة
  Widget _buildBadgeCard(BadgeModel badge, bool isEarned) {
    return Container(
      decoration: BoxDecoration(
        color: isEarned ? badge.getColorObject().withAlpha(25) : Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isEarned ? badge.getColorObject() : Colors.grey.shade300,
          width: isEarned ? 2 : 1)),
      child: Padding(
        padding: EdgeInsets.all(8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              badge.icon,
              size: 32,
              color: isEarned ? badge.getColorObject() : Colors.grey.shade400),
            SizedBox(height: 8),
            Text(
              badge.name,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: isEarned ? Colors.black87 : Colors.grey.shade600),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis),
            if (badge.isRare)
              Container(
                margin: EdgeInsets.only(top: 4),
                padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.purple.shade100,
                  borderRadius: BorderRadius.circular(10)),
                child: Text(
                  'نادرة',
                  style: TextStyle(
                    fontSize: 8,
                    color: Colors.purple.shade700,
                    fontWeight: FontWeight.bold))),
          ])));
  }

  /// بناء الإنجازات المحققة
  Widget _buildCompletedAchievements(UserStatisticsModel userStats) {
    final achievements = userStats.achievements ?? [];
    final completedAchievements = achievements.where((a) => a['completed'] == true).toList();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.check_circle, color: AppColors.success, size: 24),
                SizedBox(width: 8),
                Text(
                  'الإنجازات المحققة (${completedAchievements.length})',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
              ]),
            SizedBox(height: 16),

            if (completedAchievements.isEmpty)
              Center(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Icon(Icons.check_circle_outline,
                           size: 48, color: Colors.grey.shade400),
                      SizedBox(height: 8),
                      Text(
                        'لم تحقق أي إنجازات بعد',
                        style: TextStyle(color: Colors.grey.shade600)),
                    ])))
            else
              Column(
                children: completedAchievements.map((achievement) {
                  return _buildAchievementItem(achievement, true);
                }).toList()),
          ])));
  }

  /// بناء الإنجازات القادمة
  Widget _buildUpcomingAchievements(UserStatisticsModel userStats) {
    final upcomingAchievements = _generateUpcomingAchievements(userStats);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.flag_outlined, color: Colors.orange, size: 24),
                SizedBox(width: 8),
                Text(
                  'الإنجازات القادمة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
              ]),
            SizedBox(height: 16),

            Column(
              children: upcomingAchievements.map((achievement) {
                return _buildAchievementItem(achievement, false);
              }).toList()),
          ])));
  }

  /// بناء عنصر إنجاز
  Widget _buildAchievementItem(Map<String, dynamic> achievement, bool isCompleted) {
    final progress = achievement['progress'] ?? 0.0;
    final target = achievement['target'] ?? 1;
    final current = achievement['current'] ?? 0;

    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isCompleted ? AppColors.success.withAlpha(25) : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isCompleted ? AppColors.success : Colors.grey.shade200)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
                color: isCompleted ? AppColors.success : Colors.grey.shade400,
                size: 20),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  achievement['title'] ?? '',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500))),
              if (achievement['points'] != null)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withAlpha(25),
                    borderRadius: BorderRadius.circular(10)),
                  child: Text(
                    '+${achievement['points']} نقطة',
                    style: TextStyle(
                      fontSize: 10,
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold))),
            ]),
          SizedBox(height: 8),
          Text(
            achievement['description'] ?? '',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600)),
          if (!isCompleted) ...[
            SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: LinearPercentIndicator(
                    lineHeight: 6,
                    percent: (progress / 100).clamp(0.0, 1.0),
                    backgroundColor: Colors.grey.shade200,
                    progressColor: AppColors.primary,
                    barRadius: Radius.circular(3),
                    padding: EdgeInsets.zero)),
                SizedBox(width: 8),
                Text(
                  '$current/$target',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade600)),
              ]),
          ],
        ]));
  }

  /// بناء إحصائيات الإنجازات
  Widget _buildAchievementStats(UserStatisticsModel userStats) {
    final achievements = userStats.achievements ?? [];
    final completedCount = achievements.where((a) => a['completed'] == true).length;
    final totalCount = achievements.length + 5; // إضافة الإنجازات القادمة
    final completionRate = totalCount > 0 ? (completedCount / totalCount) * 100 : 0.0;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: AppColors.primary, size: 24),
                SizedBox(width: 8),
                Text(
                  'إحصائيات الإنجازات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
              ]),
            SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildAchievementStatCard(
                    'المحققة',
                    completedCount.toString(),
                    Icons.check_circle,
                    AppColors.success)),
                SizedBox(width: 12),
                Expanded(
                  child: _buildAchievementStatCard(
                    'المتبقية',
                    (totalCount - completedCount).toString(),
                    Icons.pending,
                    Colors.orange)),
              ]),
            SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildAchievementStatCard(
                    'الشارات',
                    userStats.badges.length.toString(),
                    Icons.emoji_events,
                    AppColors.primary)),
                SizedBox(width: 12),
                Expanded(
                  child: _buildAchievementStatCard(
                    'معدل الإنجاز',
                    '${completionRate.toStringAsFixed(1)}%',
                    Icons.trending_up,
                    Colors.purple)),
              ]),
            SizedBox(height: 16),

            // شريط التقدم الإجمالي
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'التقدم الإجمالي',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500)),
                    Text(
                      '$completedCount/$totalCount',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary)),
                  ]),
                SizedBox(height: 8),
                LinearPercentIndicator(
                  lineHeight: 8,
                  percent: (completionRate / 100).clamp(0.0, 1.0),
                  backgroundColor: Colors.grey.shade200,
                  progressColor: AppColors.primary,
                  barRadius: Radius.circular(4),
                  padding: EdgeInsets.zero),
              ]),
          ])));
  }

  /// بناء بطاقة إحصائية إنجاز
  Widget _buildAchievementStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withAlpha(51))),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color)),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade700)),
        ]));
  }



  /// توليد الإنجازات القادمة
  List<Map<String, dynamic>> _generateUpcomingAchievements(UserStatisticsModel userStats) {
    List<Map<String, dynamic>> achievements = [];

    // إنجاز المواضيع
    if (userStats.topicsCount < 10) {
      achievements.add({
        'title': 'كاتب نشط',
        'description': 'أنشئ 10 مواضيع في المنتدى',
        'current': userStats.topicsCount,
        'target': 10,
        'progress': (userStats.topicsCount / 10 * 100).clamp(0, 100),
        'points': 50,
      });
    }

    // إنجاز المشاركات
    if (userStats.postsCount < 50) {
      achievements.add({
        'title': 'مشارك متفاعل',
        'description': 'أضف 50 رد في المنتدى',
        'current': userStats.postsCount,
        'target': 50,
        'progress': (userStats.postsCount / 50 * 100).clamp(0, 100),
        'points': 100,
      });
    }

    // إنجاز الإعجابات
    final totalLikes = userStats.totalTopicLikes + userStats.totalPostLikes;
    if (totalLikes < 100) {
      achievements.add({
        'title': 'محبوب المجتمع',
        'description': 'احصل على 100 إعجاب',
        'current': totalLikes,
        'target': 100,
        'progress': (totalLikes / 100 * 100).clamp(0, 100),
        'points': 75,
      });
    }

    return achievements;
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    final months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  /// تنسيق آخر نشاط
  String _formatLastActivity(Duration duration) {
    if (duration.inDays > 0) {
      return 'منذ ${duration.inDays} يوم';
    } else if (duration.inHours > 0) {
      return 'منذ ${duration.inHours} ساعة';
    } else if (duration.inMinutes > 0) {
      return 'منذ ${duration.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  /// بناء زر أيقونة بسيط
  Widget _buildIconButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 6,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }
}