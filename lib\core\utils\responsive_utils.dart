import 'package:flutter/material.dart';

/// أدوات التصميم المتجاوب للتطبيق
class ResponsiveUtils {
  /// أحجام الشاشات المختلفة
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  /// الحصول على نوع الجهاز بناءً على عرض الشاشة
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < mobileBreakpoint) {
      return DeviceType.mobile;
    } else if (width < tabletBreakpoint) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }

  /// التحقق من كون الجهاز هاتف ذكي
  static bool isMobile(BuildContext context) {
    return getDeviceType(context) == DeviceType.mobile;
  }

  /// التحقق من كون الجهاز تابلت
  static bool isTablet(BuildContext context) {
    return getDeviceType(context) == DeviceType.tablet;
  }

  /// التحقق من كون الجهاز سطح مكتب
  static bool isDesktop(BuildContext context) {
    return getDeviceType(context) == DeviceType.desktop;
  }

  /// الحصول على عدد الأعمدة المناسب للشبكة
  static int getGridColumns(BuildContext context) {
    final deviceType = getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
        return 2;
      case DeviceType.tablet:
        return 3;
      case DeviceType.desktop:
        return 4;
    }
  }

  /// الحصول على المسافات المناسبة
  static double getPadding(BuildContext context) {
    final deviceType = getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
        return 16.0;
      case DeviceType.tablet:
        return 24.0;
      case DeviceType.desktop:
        return 32.0;
    }
  }

  /// الحصول على حجم الخط المناسب
  static double getFontSize(BuildContext context, FontSizeType type) {
    final deviceType = getDeviceType(context);
    
    switch (type) {
      case FontSizeType.small:
        return deviceType == DeviceType.mobile ? 12 : 14;
      case FontSizeType.medium:
        return deviceType == DeviceType.mobile ? 14 : 16;
      case FontSizeType.large:
        return deviceType == DeviceType.mobile ? 16 : 18;
      case FontSizeType.extraLarge:
        return deviceType == DeviceType.mobile ? 20 : 24;
      case FontSizeType.title:
        return deviceType == DeviceType.mobile ? 24 : 28;
    }
  }

  /// الحصول على ارتفاع البطاقة المناسب
  static double getCardHeight(BuildContext context) {
    final deviceType = getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
        return 280.0;
      case DeviceType.tablet:
        return 320.0;
      case DeviceType.desktop:
        return 360.0;
    }
  }

  /// الحصول على عرض البطاقة المناسب
  static double getCardWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return screenWidth * 0.65;
      case DeviceType.tablet:
        return screenWidth * 0.45;
      case DeviceType.desktop:
        return screenWidth * 0.3;
    }
  }

  /// الحصول على ارتفاع AppBar المناسب
  static double getAppBarHeight(BuildContext context) {
    final deviceType = getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
        return kToolbarHeight;
      case DeviceType.tablet:
        return kToolbarHeight + 8;
      case DeviceType.desktop:
        return kToolbarHeight + 16;
    }
  }

  /// الحصول على حجم الأيقونة المناسب
  static double getIconSize(BuildContext context, IconSizeType type) {
    final deviceType = getDeviceType(context);
    
    switch (type) {
      case IconSizeType.small:
        return deviceType == DeviceType.mobile ? 16 : 18;
      case IconSizeType.medium:
        return deviceType == DeviceType.mobile ? 20 : 24;
      case IconSizeType.large:
        return deviceType == DeviceType.mobile ? 24 : 28;
      case IconSizeType.extraLarge:
        return deviceType == DeviceType.mobile ? 32 : 36;
    }
  }

  /// الحصول على نصف قطر الحواف المناسب
  static double getBorderRadius(BuildContext context) {
    final deviceType = getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
        return 12.0;
      case DeviceType.tablet:
        return 16.0;
      case DeviceType.desktop:
        return 20.0;
    }
  }

  /// الحصول على ارتفاع الزر المناسب
  static double getButtonHeight(BuildContext context) {
    final deviceType = getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
        return 48.0;
      case DeviceType.tablet:
        return 52.0;
      case DeviceType.desktop:
        return 56.0;
    }
  }

  /// الحصول على عرض الحد الأقصى للمحتوى
  static double getMaxContentWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return screenWidth;
      case DeviceType.tablet:
        return 800;
      case DeviceType.desktop:
        return 1200;
    }
  }

  /// الحصول على المسافة بين العناصر
  static double getSpacing(BuildContext context, SpacingType type) {
    final deviceType = getDeviceType(context);
    
    switch (type) {
      case SpacingType.small:
        return deviceType == DeviceType.mobile ? 8 : 12;
      case SpacingType.medium:
        return deviceType == DeviceType.mobile ? 16 : 20;
      case SpacingType.large:
        return deviceType == DeviceType.mobile ? 24 : 32;
      case SpacingType.extraLarge:
        return deviceType == DeviceType.mobile ? 32 : 48;
    }
  }

  /// التحقق من الاتجاه الأفقي
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// التحقق من الاتجاه العمودي
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  /// الحصول على ارتفاع الشاشة الآمن
  static double getSafeAreaHeight(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.height - 
           mediaQuery.padding.top - 
           mediaQuery.padding.bottom;
  }

  /// الحصول على عرض الشاشة الآمن
  static double getSafeAreaWidth(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.width - 
           mediaQuery.padding.left - 
           mediaQuery.padding.right;
  }
}

/// أنواع الأجهزة
enum DeviceType {
  mobile,
  tablet,
  desktop,
}

/// أنواع أحجام الخطوط
enum FontSizeType {
  small,
  medium,
  large,
  extraLarge,
  title,
}

/// أنواع أحجام الأيقونات
enum IconSizeType {
  small,
  medium,
  large,
  extraLarge,
}

/// أنواع المسافات
enum SpacingType {
  small,
  medium,
  large,
  extraLarge,
}
