import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

/**
 * مشغل يتم تنفيذه عند إنشاء عقار جديد
 */
export const onEstateCreated = functions.firestore
  .document('estates/{estateId}')
  .onCreate(async (snap, context) => {
    const estateId = context.params.estateId;
    const estateData = snap.data();
    
    console.log(`🏠 تم إنشاء عقار جديد: ${estateId}`);
    console.log(`📋 عنوان العقار: ${estateData.title}`);
    console.log(`👤 مالك العقار: ${estateData.ownerId}`);
    
    try {
      const db = admin.firestore();
      
      // التحقق من وجود البيانات المطلوبة
      if (!estateData.ownerId || !estateData.title) {
        console.error('❌ بيانات العقار غير مكتملة');
        return;
      }
      
      // حساب وقت الإشعار الترويجي الأول (24 ساعة من الآن)
      const createdAt = estateData.createdAt?.toDate() || new Date();
      const firstPromotionalTime = new Date(createdAt.getTime() + (24 * 60 * 60 * 1000)); // 24 ساعة

      console.log(`⏰ وقت الإنشاء: ${createdAt.toISOString()}`);
      console.log(`📅 وقت الإشعار الترويجي الأول: ${firstPromotionalTime.toISOString()}`);

      // إنشاء إشعار ترويجي مجدول
      const notificationData = {
        type: 'promotional_upgrade',
        estateId: estateId,
        userId: estateData.ownerId,
        estateTitle: estateData.title,
        scheduledTime: admin.firestore.Timestamp.fromDate(firstPromotionalTime),
        createdAt: admin.firestore.Timestamp.fromDate(createdAt),
        processed: false,
        notificationData: {
          title: 'اجعل إعلانك أكثر تميزاً! ⭐',
          contactInfo: {
            whatsapp: '+965 9929 8821',
            email: '<EMAIL>'
          }
        }
      };
      
      // حفظ الإشعار المجدول
      const notificationRef = await db.collection('scheduledNotifications').add(notificationData);

      console.log(`✅ تم جدولة الإشعار الترويجي: ${notificationRef.id}`);

      // إضافة مرجع الإشعار المجدول إلى العقار
      await snap.ref.update({
        scheduledNotificationId: notificationRef.id,
        promotionalScheduledAt: admin.firestore.Timestamp.fromDate(firstPromotionalTime)
      });

      // إضافة سجل في النظام
      await db.collection('systemLogs').add({
        type: 'estate_created_promotional_scheduled',
        estateId: estateId,
        userId: estateData.ownerId,
        estateTitle: estateData.title,
        scheduledNotificationId: notificationRef.id,
        promotionalTime: admin.firestore.Timestamp.fromDate(firstPromotionalTime),
        timestamp: admin.firestore.Timestamp.now(),
        details: 'تم جدولة إشعار ترويجي للعقار بعد 24 ساعة من الإنشاء'
      });

      console.log(`📝 تم إضافة سجل النظام للعقار: ${estateId}`);

      // إرسال إشعار ترحيبي للمستخدم (اختياري)
      await sendWelcomeNotification(estateData.ownerId, estateData.title, estateId);
      
    } catch (error) {
      console.error(`❌ خطأ في معالجة إنشاء العقار ${estateId}:`, error);
      
      // إضافة سجل خطأ
      await admin.firestore().collection('systemLogs').add({
        type: 'estate_creation_error',
        estateId: estateId,
        userId: estateData.ownerId || 'unknown',
        error: String(error),
        timestamp: admin.firestore.Timestamp.now(),
        details: 'خطأ في معالجة إنشاء العقار وجدولة الإشعار'
      });
    }
  });

/**
 * إرسال إشعار ترحيبي عند إنشاء العقار
 */
async function sendWelcomeNotification(userId: string, estateTitle: string, estateId: string): Promise<void> {
  try {
    const db = admin.firestore();
    const messaging = admin.messaging();
    
    // الحصول على بيانات المستخدم
    const userDoc = await db.collection('users').doc(userId).get();
    const userData = userDoc.data();
    
    const welcomeTitle = 'تم نشر إعلانك بنجاح! 🎉';
    const welcomeBody = `تم نشر إعلان "${estateTitle}" بنجاح في تطبيق Krea.

🚀 نصائح لزيادة فرص البيع/الإيجار:
📌 استفد من خدمة التثبيت لظهور أولوي
⭐ اجعل إعلانك مميزاً بشارة خاصة
🔝 احصل على المزيد من المشاهدات

💡 ستصلك إشعارات دورية بنصائح لتحسين إعلانك

للترقية، تواصل معنا:
📱 واتساب: +965 9929 8821
📧 إيميل: <EMAIL>

شكراً لثقتك في تطبيق Krea! 🏠`;

    // إرسال إشعار push
    if (userData && userData.fcmToken) {
      try {
        const message = {
          token: userData.fcmToken,
          notification: {
            title: welcomeTitle,
            body: 'تم نشر إعلانك بنجاح! اضغط للمزيد من التفاصيل.'
          },
          data: {
            type: 'estate_published',
            estateId: estateId,
            estateTitle: estateTitle,
            fullMessage: welcomeBody
          },
          android: {
            notification: {
              icon: 'ic_notification',
              color: '#2E7D32',
              sound: 'default',
              channelId: 'estate_notifications'
            }
          },
          apns: {
            payload: {
              aps: {
                sound: 'default',
                badge: 1
              }
            }
          }
        };
        
        await messaging.send(message);
        console.log(`✅ تم إرسال إشعار الترحيب للمستخدم: ${userId}`);
        
      } catch (pushError) {
        console.error('❌ خطأ في إرسال إشعار الترحيب:', pushError);
      }
    }
    
    // حفظ الإشعار في قاعدة البيانات
    await db.collection('users')
      .doc(userId)
      .collection('notifications')
      .add({
        title: welcomeTitle,
        body: welcomeBody,
        type: 'estate_published',
        data: {
          estateId: estateId,
          estateTitle: estateTitle
        },
        timestamp: admin.firestore.Timestamp.now(),
        isRead: false,
        priority: 'normal'
      });
    
    console.log(`✅ تم حفظ إشعار الترحيب للمستخدم: ${userId}`);
    
  } catch (error) {
    console.error(`❌ خطأ في إرسال إشعار الترحيب للمستخدم ${userId}:`, error);
  }
}
