# Firebase Functions - KREA

هذا المجلد يحتوي على Firebase Functions لتطبيق KREA العقاري.

## الوظائف المتاحة

### 1. إشعارات الأرشفة التلقائية

#### `scheduleNotificationProcessor`
- **النوع**: مجدولة (تعمل كل دقيقة)
- **الوصف**: تتحقق من الإشعارات المجدولة وتعالجها
- **التوقيت**: كل دقيقة
- **المنطقة الزمنية**: Asia/Kuwait

#### `processScheduledNotifications`
- **النوع**: HTTPS Callable
- **الوصف**: معالجة يدوية للإشعارات المجدولة
- **المصادقة**: مطلوبة

#### `estateCreated`
- **النوع**: Firestore Trigger
- **الوصف**: يتم تشغيلها عند إنشاء عقار جديد
- **المسار**: `estates/{estateId}`

## كيفية عمل النظام

### 1. إنشاء العقار
```
المستخدم ينشئ إعلان → يتم حفظه في Firestore → estateCreated trigger يعمل
```

### 2. جدولة الإشعار
```
estateCreated → إنشاء مستند في scheduledNotifications → جدولة الأرشفة بعد 24 ساعة
```

### 3. معالجة الإشعار
```
scheduleNotificationProcessor (كل دقيقة) → فحص الإشعارات المستحقة → أرشفة العقار + إرسال إشعار
```

## بنية البيانات

### مجموعة `scheduledNotifications`
```json
{
  "type": "estate_archive",
  "estateId": "estate_id_here",
  "userId": "user_id_here", 
  "estateTitle": "عنوان العقار",
  "scheduledTime": "2025-01-13T10:00:00Z",
  "createdAt": "2025-01-12T10:00:00Z",
  "processed": false,
  "notificationData": {
    "title": "تم أرشفة إعلانك 📦",
    "body": "تم أرشفة إعلان...",
    "contactInfo": {
      "whatsapp": "+965 9929 8821",
      "email": "<EMAIL>"
    }
  }
}
```

### مجموعة `systemLogs`
```json
{
  "type": "estate_archived",
  "estateId": "estate_id_here",
  "userId": "user_id_here",
  "estateTitle": "عنوان العقار", 
  "timestamp": "2025-01-13T10:00:00Z",
  "details": "تم أرشفة العقار تلقائياً بعد 24 ساعة وإرسال إشعار للمستخدم"
}
```

## الإعداد والنشر

### 1. تثبيت التبعيات
```bash
cd functions
npm install
```

### 2. بناء المشروع
```bash
npm run build
```

### 3. نشر الوظائف
```bash
firebase deploy --only functions
```

### 4. تشغيل المحاكي المحلي
```bash
npm run serve
```

## معلومات الاتصال المستخدمة

- **رقم الواتساب**: +965 9929 8821
- **البريد الإلكتروني**: <EMAIL>

## الأمان

- جميع الوظائف تتحقق من المصادقة عند الحاجة
- البيانات الحساسة محمية
- السجلات مفصلة للمراقبة

## المراقبة

- جميع العمليات مسجلة في `systemLogs`
- إحصائيات مفصلة للإشعارات المعالجة
- تتبع الأخطاء والاستثناءات

## الاختبار

يمكن اختبار الوظائف باستخدام Firebase Emulator:

```bash
firebase emulators:start --only functions,firestore
```

## الدعم

للدعم الفني، تواصل معنا:
- البريد الإلكتروني: <EMAIL>
- الواتساب: +965 9929 8821
