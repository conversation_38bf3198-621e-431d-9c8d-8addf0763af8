/// ثوابت رسائل المصادقة والتحقق
class AuthMessages {
  // رسائل النجاح
  static const String loginSuccess = "تم تسجيل الدخول بنجاح";
  static const String signupSuccess = "تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني لتفعيل الحساب";
  static const String passwordResetSent = "تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني";
  static const String verificationEmailSent = "تم إرسال رسالة التحقق بنجاح";
  static const String logoutSuccess = "تم تسجيل الخروج بنجاح";

  // رسائل الأخطاء العامة
  static const String unexpectedError = "حدث خطأ غير متوقع";
  static const String networkError = "فشل في الاتصال بالإنترنت";
  static const String loginError = "حدث خطأ أثناء تسجيل الدخول";
  static const String signupError = "حدث خطأ أثناء إنشاء الحساب";

  // رسائل أخطاء التحقق من البيانات
  static const String emailRequired = "الرجاء إدخال البريد الإلكتروني";
  static const String emailInvalid = "البريد الإلكتروني غير صالح";
  static const String passwordRequired = "الرجاء إدخال كلمة المرور";
  static const String passwordTooShort = "كلمة المرور يجب ألا تقل عن 6 أحرف";
  static const String passwordsNotMatch = "كلمتا المرور غير متطابقتين";
  static const String confirmPasswordRequired = "الرجاء تأكيد كلمة المرور";

  // رسائل أخطاء Firebase Auth
  static const String userNotFound = "لم يتم العثور على مستخدم بهذا البريد الإلكتروني";
  static const String wrongPassword = "كلمة المرور غير صحيحة";
  static const String emailAlreadyInUse = "البريد الإلكتروني مستخدم بالفعل";
  static const String weakPassword = "كلمة المرور ضعيفة جداً";
  static const String userDisabled = "تم تعطيل هذا الحساب";
  static const String tooManyRequests = "تم تعطيل الوصول مؤقتاً بسبب كثرة المحاولات الفاشلة";
  static const String operationNotAllowed = "تسجيل الدخول بالبريد الإلكتروني غير مفعل";
  static const String accountExistsWithDifferentCredential = "يوجد حساب بهذا البريد الإلكتروني بطريقة دخول مختلفة";
  static const String requiresRecentLogin = "تتطلب هذه العملية إعادة تسجيل الدخول";
  static const String invalidCredential = "بيانات الدخول غير صحيحة";

  // رسائل التحقق من البريد الإلكتروني
  static const String emailNotVerified = "حسابك غير مفعل! يرجى التحقق من بريدك الإلكتروني وتأكيد حسابك للمتابعة. تم إرسال رسالة تأكيد جديدة.";
  static const String verificationEmailFailed = "فشل في إرسال رسالة التحقق";
  static const String noUserNeedsVerification = "لا يوجد مستخدم يحتاج إلى تحقق من البريد الإلكتروني";

  // رسائل إعادة تعيين كلمة المرور
  static const String passwordResetFailed = "فشل في إرسال رسالة إعادة تعيين كلمة المرور";
  static const String passwordResetTooManyRequests = "تم إرسال عدد كبير من الطلبات، يرجى المحاولة لاحقاً";

  // رسائل الترحيب
  static const String welcomeBack = "مرحبًا بعودتك!";
  static const String welcomeBackWithPoints = "مرحبًا بعودتك! تم إضافة 5 نقاط لتسجيل الدخول اليومي.";
  
  // رسائل برنامج الولاء
  static String welcomeBackLegacyUser(int points) => 
      "مرحبًا بعودتك! تم إضافة $points نقطة إلى رصيدك كمستخدم قديم في برنامج الولاء الجديد!";

  // رسائل التوجيه
  static const String goToLogin = "العودة إلى تسجيل الدخول";
  static const String goToSignup = "إنشاء حساب جديد";
  static const String forgotPassword = "نسيت كلمة المرور؟";

  // رسائل الأزرار
  static const String login = "تسجيل الدخول";
  static const String signup = "إنشاء حساب";
  static const String sendResetLink = "إرسال رابط إعادة التعيين";
  static const String resendVerification = "إعادة إرسال رسالة التحقق";
  static String resendCooldown(int seconds) => "إعادة الإرسال خلال $seconds ثانية";

  // رسائل التحقق من البيانات المتقدمة
  static const String nameRequired = "الرجاء إدخال الاسم الكامل";
  static const String phoneRequired = "الرجاء إدخال رقم الهاتف";
  static const String addressRequired = "الرجاء إدخال العنوان";
  static const String postalCodeRequired = "الرجاء إدخال الرمز البريدي";
  static const String documentRequired = "الرجاء رفع المستند المطلوب";

  // رسائل التحقق من البريد الكويتي
  static const String kuwaitEmailRequired = "الرجاء إدخال بريد إلكتروني صحيح بنطاق مدعوم";

  // رسائل الحالات
  static const String loading = "جاري التحميل...";
  static const String processing = "جاري المعالجة...";
  static const String sending = "جاري الإرسال...";

  // رسائل التأكيد
  static const String confirmLogout = "هل أنت متأكد من تسجيل الخروج؟";
  static const String confirmDeleteAccount = "هل أنت متأكد من حذف الحساب؟";

  // رسائل الإرشادات
  static const String emailVerificationInstructions = 
      "يرجى فتح الرسالة والنقر على رابط التحقق لتفعيل حسابك";
  static const String passwordResetInstructions = 
      "أدخل بريدك الإلكتروني وسنرسل لك رابطًا لإعادة تعيين كلمة المرور";
  static const String checkSpamFolder = 
      "تحقق من مجلد الرسائل غير المرغوب فيها (Spam) إذا لم تجد الرسالة";

  // رسائل الأمان
  static const String biometricAuthFailed = "فشل في التحقق البيومتري";
  static const String biometricNotAvailable = "التحقق البيومتري غير متاح";
  static const String biometricNotEnrolled = "لم يتم تسجيل بيانات بيومترية";

  // رسائل الجلسة
  static const String sessionExpired = "انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى";
  static const String rememberMe = "تذكرني";

  // رسائل الإحالة
  static const String referralCodeApplied = "تم تطبيق رمز الإحالة بنجاح";
  static const String referralCodeInvalid = "رمز الإحالة غير صالح";
  static const String referralCodeExpired = "انتهت صلاحية رمز الإحالة";
}
