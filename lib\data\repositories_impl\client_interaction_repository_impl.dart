import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:uuid/uuid.dart';
import '../../domain/entities/client_interaction.dart';
import '../../domain/entities/appointment.dart';
import '../../domain/repositories/client_interaction_repository.dart';

/// تطبيق مستودع التفاعلات مع العملاء باستخدام Firestore
class ClientInteractionRepositoryImpl implements ClientInteractionRepository {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;
  final FirebaseStorage _storage;
  final Uuid _uuid;

  ClientInteractionRepositoryImpl({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
    FirebaseStorage? storage,
    Uuid? uuid,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance,
        _storage = storage ?? FirebaseStorage.instance,
        _uuid = uuid ?? const Uuid();

  @override
  Future<String> createInteraction(ClientInteraction interaction) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول لإنشاء تفاعل');
      }

      final interactionId = _uuid.v4();
      final newInteraction = interaction.copyWith(
        id: interactionId,
        agentId: user.uid,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('client_interactions')
          .doc(interactionId)
          .set(newInteraction.toMap());

      return interactionId;
    } catch (e) {
      throw Exception('فشل في إنشاء التفاعل: $e');
    }
  }

  @override
  Future<void> updateInteraction(ClientInteraction interaction) async {
    try {
      final updatedInteraction = interaction.copyWith(
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('client_interactions')
          .doc(interaction.id)
          .update(updatedInteraction.toMap());
    } catch (e) {
      throw Exception('فشل في تحديث التفاعل: $e');
    }
  }

  @override
  Future<void> deleteInteraction(String interactionId) async {
    try {
      await _firestore
          .collection('client_interactions')
          .doc(interactionId)
          .delete();
    } catch (e) {
      throw Exception('فشل في حذف التفاعل: $e');
    }
  }

  @override
  Future<ClientInteraction?> getInteractionById(String interactionId) async {
    try {
      final doc = await _firestore
          .collection('client_interactions')
          .doc(interactionId)
          .get();

      if (doc.exists) {
        return ClientInteraction.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في الحصول على التفاعل: $e');
    }
  }

  @override
  Future<List<ClientInteraction>> getClientInteractions(String clientId) async {
    try {
      final querySnapshot = await _firestore
          .collection('client_interactions')
          .where('clientId', isEqualTo: clientId)
          .orderBy('interactionDate', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ClientInteraction.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على تفاعلات العميل: $e');
    }
  }

  @override
  Future<List<ClientInteraction>> getAgentInteractions(String agentId) async {
    try {
      final querySnapshot = await _firestore
          .collection('client_interactions')
          .where('agentId', isEqualTo: agentId)
          .orderBy('interactionDate', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ClientInteraction.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على تفاعلات الوكيل: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getAgentInteractionsPaginated({
    required String agentId,
    int limit = 20,
    String? lastInteractionId,
    InteractionType? type,
    InteractionStatus? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Query interactionsQuery = _firestore
          .collection('client_interactions')
          .where('agentId', isEqualTo: agentId);

      // تطبيق الفلاتر
      if (type != null) {
        interactionsQuery = interactionsQuery.where('type', 
            isEqualTo: type.toString().split('.').last);
      }

      if (status != null) {
        interactionsQuery = interactionsQuery.where('status', 
            isEqualTo: status.toString().split('.').last);
      }

      if (startDate != null) {
        interactionsQuery = interactionsQuery.where('interactionDate', 
            isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        interactionsQuery = interactionsQuery.where('interactionDate', 
            isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      interactionsQuery = interactionsQuery.orderBy('interactionDate', descending: true);

      // تطبيق التحميل المتدرج
      if (lastInteractionId != null) {
        final lastDoc = await _firestore
            .collection('client_interactions')
            .doc(lastInteractionId)
            .get();
        if (lastDoc.exists) {
          interactionsQuery = interactionsQuery.startAfterDocument(lastDoc);
        }
      }

      interactionsQuery = interactionsQuery.limit(limit);

      final querySnapshot = await interactionsQuery.get();
      final interactions = querySnapshot.docs
          .map((doc) => ClientInteraction.fromMap(doc.data() as Map<String, dynamic>))
          .toList();

      return {
        'interactions': interactions,
        'lastInteractionId': interactions.isNotEmpty ? interactions.last.id : null,
        'hasMore': querySnapshot.docs.length == limit,
      };
    } catch (e) {
      throw Exception('فشل في الحصول على التفاعلات: $e');
    }
  }

  @override
  Future<List<ClientInteraction>> searchInteractions({
    required String agentId,
    String? query,
    InteractionType? type,
    InteractionStatus? status,
    InteractionOutcome? outcome,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Query interactionsQuery = _firestore
          .collection('client_interactions')
          .where('agentId', isEqualTo: agentId);

      if (type != null) {
        interactionsQuery = interactionsQuery.where('type', 
            isEqualTo: type.toString().split('.').last);
      }

      if (status != null) {
        interactionsQuery = interactionsQuery.where('status', 
            isEqualTo: status.toString().split('.').last);
      }

      if (outcome != null) {
        interactionsQuery = interactionsQuery.where('outcome', 
            isEqualTo: outcome.toString().split('.').last);
      }

      if (startDate != null) {
        interactionsQuery = interactionsQuery.where('interactionDate', 
            isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        interactionsQuery = interactionsQuery.where('interactionDate', 
            isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      if (query != null && query.isNotEmpty) {
        interactionsQuery = interactionsQuery
            .where('title', isGreaterThanOrEqualTo: query)
            .where('title', isLessThan: '$query\uf8ff');
      }

      final querySnapshot = await interactionsQuery
          .orderBy('interactionDate', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ClientInteraction.fromMap(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception('فشل في البحث عن التفاعلات: $e');
    }
  }

  // تطبيق مبسط لباقي الوظائف
  @override
  Future<String> createAppointment(Appointment appointment) async {
    try {
      final appointmentId = _uuid.v4();
      final newAppointment = appointment.copyWith(
        id: appointmentId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('appointments')
          .doc(appointmentId)
          .set(newAppointment.toMap());

      return appointmentId;
    } catch (e) {
      throw Exception('فشل في إنشاء الموعد: $e');
    }
  }

  @override
  Future<void> updateAppointment(Appointment appointment) async {
    try {
      final updatedAppointment = appointment.copyWith(
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('appointments')
          .doc(appointment.id)
          .update(updatedAppointment.toMap());
    } catch (e) {
      throw Exception('فشل في تحديث الموعد: $e');
    }
  }

  @override
  Future<void> deleteAppointment(String appointmentId) async {
    try {
      await _firestore
          .collection('appointments')
          .doc(appointmentId)
          .delete();
    } catch (e) {
      throw Exception('فشل في حذف الموعد: $e');
    }
  }

  @override
  Future<Appointment?> getAppointmentById(String appointmentId) async {
    try {
      final doc = await _firestore
          .collection('appointments')
          .doc(appointmentId)
          .get();

      if (doc.exists) {
        return Appointment.fromMap(doc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في الحصول على الموعد: $e');
    }
  }

  @override
  Future<List<Appointment>> getClientAppointments(String clientId) async {
    try {
      final querySnapshot = await _firestore
          .collection('appointments')
          .where('clientId', isEqualTo: clientId)
          .orderBy('appointmentDate', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => Appointment.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على مواعيد العميل: $e');
    }
  }

  @override
  Future<List<Appointment>> getAgentAppointments(String agentId) async {
    try {
      final querySnapshot = await _firestore
          .collection('appointments')
          .where('agentId', isEqualTo: agentId)
          .orderBy('appointmentDate', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => Appointment.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على مواعيد الوكيل: $e');
    }
  }

  @override
  Future<List<Appointment>> getTodayAppointments(String agentId) async {
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);

      final querySnapshot = await _firestore
          .collection('appointments')
          .where('agentId', isEqualTo: agentId)
          .where('appointmentDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('appointmentDate', isLessThanOrEqualTo: Timestamp.fromDate(endOfDay))
          .orderBy('appointmentDate', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => Appointment.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على مواعيد اليوم: $e');
    }
  }

  @override
  Future<List<Appointment>> getWeekAppointments(String agentId) async {
    try {
      final now = DateTime.now();
      final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
      final endOfWeek = startOfWeek.add(const Duration(days: 6));

      final querySnapshot = await _firestore
          .collection('appointments')
          .where('agentId', isEqualTo: agentId)
          .where('appointmentDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfWeek))
          .where('appointmentDate', isLessThanOrEqualTo: Timestamp.fromDate(endOfWeek))
          .orderBy('appointmentDate', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => Appointment.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على مواعيد الأسبوع: $e');
    }
  }

  @override
  Future<List<Appointment>> getMonthAppointments(String agentId) async {
    try {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0);

      final querySnapshot = await _firestore
          .collection('appointments')
          .where('agentId', isEqualTo: agentId)
          .where('appointmentDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfMonth))
          .where('appointmentDate', isLessThanOrEqualTo: Timestamp.fromDate(endOfMonth))
          .orderBy('appointmentDate', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => Appointment.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على مواعيد الشهر: $e');
    }
  }

  @override
  Future<List<Appointment>> getUpcomingAppointments(String agentId, {int daysAhead = 7}) async {
    try {
      final now = DateTime.now();
      final futureDate = now.add(Duration(days: daysAhead));

      final querySnapshot = await _firestore
          .collection('appointments')
          .where('agentId', isEqualTo: agentId)
          .where('appointmentDate', isGreaterThanOrEqualTo: Timestamp.fromDate(now))
          .where('appointmentDate', isLessThanOrEqualTo: Timestamp.fromDate(futureDate))
          .orderBy('appointmentDate', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => Appointment.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على المواعيد القادمة: $e');
    }
  }

  @override
  Future<List<Appointment>> getOverdueAppointments(String agentId) async {
    try {
      final now = DateTime.now();

      final querySnapshot = await _firestore
          .collection('appointments')
          .where('agentId', isEqualTo: agentId)
          .where('appointmentDate', isLessThan: Timestamp.fromDate(now))
          .where('status', isEqualTo: 'scheduled')
          .orderBy('appointmentDate', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Appointment.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على المواعيد المتأخرة: $e');
    }
  }

  // تطبيق مبسط لباقي الوظائف
  @override
  Future<void> addFollowUpReminder(String clientId, DateTime followUpDate, String notes) async {
    try {
      final interactionId = _uuid.v4();
      final followUpInteraction = ClientInteraction(
        id: interactionId,
        clientId: clientId,
        clientName: '',
        agentId: _auth.currentUser?.uid ?? '',
        agentName: '',
        type: InteractionType.followUp,
        status: InteractionStatus.pending,
        title: 'تذكير متابعة',
        description: notes,
        interactionDate: followUpDate,
        nextFollowUpDate: followUpDate,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection('client_interactions')
          .doc(interactionId)
          .set(followUpInteraction.toMap());
    } catch (e) {
      throw Exception('فشل في إضافة تذكير المتابعة: $e');
    }
  }

  @override
  Future<List<ClientInteraction>> getFollowUpReminders(String agentId) async {
    try {
      final querySnapshot = await _firestore
          .collection('client_interactions')
          .where('agentId', isEqualTo: agentId)
          .where('type', isEqualTo: 'followUp')
          .where('status', isEqualTo: 'pending')
          .orderBy('nextFollowUpDate', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => ClientInteraction.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على تذكيرات المتابعة: $e');
    }
  }

  @override
  Future<List<ClientInteraction>> getTodayFollowUps(String agentId) async {
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);

      final querySnapshot = await _firestore
          .collection('client_interactions')
          .where('agentId', isEqualTo: agentId)
          .where('type', isEqualTo: 'followUp')
          .where('nextFollowUpDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('nextFollowUpDate', isLessThanOrEqualTo: Timestamp.fromDate(endOfDay))
          .orderBy('nextFollowUpDate', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => ClientInteraction.fromMap(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('فشل في الحصول على متابعات اليوم: $e');
    }
  }

  @override
  Future<void> updateFollowUpStatus(String interactionId, InteractionStatus status) async {
    try {
      await _firestore
          .collection('client_interactions')
          .doc(interactionId)
          .update({
        'status': status.toString().split('.').last,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('فشل في تحديث حالة المتابعة: $e');
    }
  }

  @override
  Future<void> addFollowUpNote(String interactionId, String note) async {
    try {
      await _firestore
          .collection('client_interactions')
          .doc(interactionId)
          .update({
        'notes': note,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('فشل في إضافة ملاحظة المتابعة: $e');
    }
  }

  // تطبيق مبسط للتقارير والإحصائيات
  @override
  Future<Map<String, dynamic>> getInteractionStatistics(String agentId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Query query = _firestore
          .collection('client_interactions')
          .where('agentId', isEqualTo: agentId);

      if (startDate != null) {
        query = query.where('interactionDate',
            isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('interactionDate',
            isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      final querySnapshot = await query.get();
      final interactions = querySnapshot.docs
          .map((doc) => ClientInteraction.fromMap(doc.data() as Map<String, dynamic>))
          .toList();

      return {
        'totalInteractions': interactions.length,
        'completedInteractions': interactions.where((i) => i.status == InteractionStatus.completed).length,
        'pendingInteractions': interactions.where((i) => i.status == InteractionStatus.pending).length,
        'positiveOutcomes': interactions.where((i) => i.outcome == InteractionOutcome.positive).length,
        'conversionRate': interactions.isNotEmpty ?
            (interactions.where((i) => i.outcome == InteractionOutcome.converted).length / interactions.length) * 100 : 0,
      };
    } catch (e) {
      throw Exception('فشل في الحصول على إحصائيات التفاعلات: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getAgentPerformanceReport(String agentId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final stats = await getInteractionStatistics(agentId, startDate: startDate, endDate: endDate);
      return {
        'agentId': agentId,
        'reportType': 'performance',
        'generatedAt': DateTime.now().toIso8601String(),
        'statistics': stats,
        'performanceScore': _calculatePerformanceScore(stats),
      };
    } catch (e) {
      throw Exception('فشل في إنشاء تقرير الأداء: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getClientInteractionReport(String clientId) async {
    try {
      final interactions = await getClientInteractions(clientId);
      return {
        'clientId': clientId,
        'totalInteractions': interactions.length,
        'lastInteraction': interactions.isNotEmpty ? interactions.first.interactionDate.toIso8601String() : null,
        'interactionTypes': _groupByType(interactions),
        'outcomes': _groupByOutcome(interactions),
      };
    } catch (e) {
      throw Exception('فشل في إنشاء تقرير تفاعلات العميل: $e');
    }
  }

  // وظائف مساعدة
  double _calculatePerformanceScore(Map<String, dynamic> stats) {
    final total = stats['totalInteractions'] as int;
    if (total == 0) return 0.0;

    final completed = stats['completedInteractions'] as int;
    final positive = stats['positiveOutcomes'] as int;
    final conversionRate = stats['conversionRate'] as double;

    return ((completed / total) * 40) + ((positive / total) * 30) + (conversionRate * 0.3);
  }

  Map<String, int> _groupByType(List<ClientInteraction> interactions) {
    final Map<String, int> result = {};
    for (final interaction in interactions) {
      final type = interaction.type.toString().split('.').last;
      result[type] = (result[type] ?? 0) + 1;
    }
    return result;
  }

  Map<String, int> _groupByOutcome(List<ClientInteraction> interactions) {
    final Map<String, int> result = {};
    for (final interaction in interactions) {
      if (interaction.outcome != null) {
        final outcome = interaction.outcome.toString().split('.').last;
        result[outcome] = (result[outcome] ?? 0) + 1;
      }
    }
    return result;
  }

  // تطبيق مبسط لباقي الوظائف المطلوبة
  @override
  Future<Map<String, dynamic>> getAppointmentStatistics(String agentId, {DateTime? startDate, DateTime? endDate}) async {
    try {
      final appointments = await getAgentAppointments(agentId);
      return {
        'totalAppointments': appointments.length,
        'completedAppointments': appointments.where((a) => a.status == AppointmentStatus.completed).length,
        'cancelledAppointments': appointments.where((a) => a.status == AppointmentStatus.cancelled).length,
      };
    } catch (e) {
      throw Exception('فشل في الحصول على إحصائيات المواعيد: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getConversionReport(String agentId, {DateTime? startDate, DateTime? endDate}) async {
    try {
      final interactions = await getAgentInteractions(agentId);
      final converted = interactions.where((i) => i.outcome == InteractionOutcome.converted).length;
      return {
        'totalInteractions': interactions.length,
        'convertedInteractions': converted,
        'conversionRate': interactions.isNotEmpty ? (converted / interactions.length) * 100 : 0,
      };
    } catch (e) {
      throw Exception('فشل في إنشاء تقرير التحويل: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getRevenueReport(String agentId, {DateTime? startDate, DateTime? endDate}) async {
    try {
      final interactions = await getAgentInteractions(agentId);
      final totalRevenue = interactions.fold(0.0, (sum, i) => sum + (i.actualRevenue ?? 0));
      return {
        'totalRevenue': totalRevenue,
        'averageRevenue': interactions.isNotEmpty ? totalRevenue / interactions.length : 0,
        'revenueByType': _groupRevenueByType(interactions),
      };
    } catch (e) {
      throw Exception('فشل في إنشاء تقرير العائد: $e');
    }
  }

  Map<String, double> _groupRevenueByType(List<ClientInteraction> interactions) {
    final Map<String, double> result = {};
    for (final interaction in interactions) {
      final type = interaction.type.toString().split('.').last;
      result[type] = (result[type] ?? 0) + (interaction.actualRevenue ?? 0);
    }
    return result;
  }

  // تطبيق مبسط للوظائف المتبقية
  @override
  Future<String> uploadInteractionAttachment(String interactionId, File file) async {
    try {
      final ref = _storage.ref().child('interactions/$interactionId/${_uuid.v4()}');
      final uploadTask = await ref.putFile(file);
      return await uploadTask.ref.getDownloadURL();
    } catch (e) {
      throw Exception('فشل في رفع المرفق: $e');
    }
  }

  @override
  Future<void> deleteInteractionAttachment(String interactionId, String attachmentUrl) async {
    try {
      final ref = _storage.refFromURL(attachmentUrl);
      await ref.delete();
    } catch (e) {
      throw Exception('فشل في حذف المرفق: $e');
    }
  }

  @override
  Future<List<String>> getInteractionAttachments(String interactionId) async {
    try {
      final interaction = await getInteractionById(interactionId);
      return interaction?.attachments ?? [];
    } catch (e) {
      throw Exception('فشل في الحصول على المرفقات: $e');
    }
  }

  @override
  Future<void> sendAppointmentReminder(String appointmentId) async {
    // تطبيق مبسط - يمكن تطويره لاحقاً
  }

  @override
  Future<void> sendFollowUpReminder(String interactionId) async {
    // تطبيق مبسط - يمكن تطويره لاحقاً
  }

  @override
  Future<void> sendInteractionNotification(String interactionId) async {
    // تطبيق مبسط - يمكن تطويره لاحقاً
  }

  @override
  Future<void> scheduleAutomaticReminders(String agentId) async {
    // تطبيق مبسط - يمكن تطويره لاحقاً
  }

  @override
  Future<String> exportClientInteractions(String clientId, String format) async {
    return 'exports/client_${clientId}_interactions.$format';
  }

  @override
  Future<String> exportInteractionReport(String agentId, {DateTime? startDate, DateTime? endDate, String format = 'excel'}) async {
    return 'exports/agent_${agentId}_report.$format';
  }

  @override
  Future<int> importInteractionsFromFile(String agentId, File file) async {
    return 0; // تطبيق مبسط
  }

  @override
  Future<String> exportAppointmentSchedule(String agentId, {DateTime? startDate, DateTime? endDate, String format = 'pdf'}) async {
    return 'exports/agent_${agentId}_schedule.$format';
  }

  @override
  Stream<List<ClientInteraction>> listenToClientInteractions(String clientId) {
    return _firestore
        .collection('client_interactions')
        .where('clientId', isEqualTo: clientId)
        .orderBy('interactionDate', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ClientInteraction.fromMap(doc.data()))
            .toList());
  }

  @override
  Stream<List<ClientInteraction>> listenToAgentInteractions(String agentId) {
    return _firestore
        .collection('client_interactions')
        .where('agentId', isEqualTo: agentId)
        .orderBy('interactionDate', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ClientInteraction.fromMap(doc.data()))
            .toList());
  }

  @override
  Stream<List<Appointment>> listenToAgentAppointments(String agentId) {
    return _firestore
        .collection('appointments')
        .where('agentId', isEqualTo: agentId)
        .orderBy('appointmentDate', descending: false)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => Appointment.fromMap(doc.data()))
            .toList());
  }

  @override
  Stream<List<ClientInteraction>> listenToFollowUpReminders(String agentId) {
    return _firestore
        .collection('client_interactions')
        .where('agentId', isEqualTo: agentId)
        .where('type', isEqualTo: 'followUp')
        .where('status', isEqualTo: 'pending')
        .orderBy('nextFollowUpDate', descending: false)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ClientInteraction.fromMap(doc.data()))
            .toList());
  }

  @override
  Future<Map<String, dynamic>> analyzeInteractionPatterns(String agentId, {DateTime? startDate, DateTime? endDate}) async {
    try {
      final interactions = await getAgentInteractions(agentId);
      return {
        'mostCommonType': _getMostCommonType(interactions),
        'averageInteractionsPerDay': _calculateAveragePerDay(interactions),
        'peakHours': _analyzePeakHours(interactions),
      };
    } catch (e) {
      throw Exception('فشل في تحليل أنماط التفاعل: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> analyzeCommunicationChannels(String agentId, {DateTime? startDate, DateTime? endDate}) async {
    try {
      final interactions = await getAgentInteractions(agentId);
      return {
        'channelEffectiveness': _analyzeChannelEffectiveness(interactions),
        'preferredChannels': _getPreferredChannels(interactions),
      };
    } catch (e) {
      throw Exception('فشل في تحليل قنوات التواصل: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> analyzePeakTimes(String agentId, {DateTime? startDate, DateTime? endDate}) async {
    try {
      final interactions = await getAgentInteractions(agentId);
      return {
        'peakHours': _analyzePeakHours(interactions),
        'peakDays': _analyzePeakDays(interactions),
      };
    } catch (e) {
      throw Exception('فشل في تحليل أوقات الذروة: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> predictClientNeeds(String clientId) async {
    try {
      final interactions = await getClientInteractions(clientId);
      return {
        'predictedNeeds': _predictNeeds(interactions),
        'recommendedActions': _recommendActions(interactions),
      };
    } catch (e) {
      throw Exception('فشل في توقع احتياجات العميل: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> suggestOptimalContactTimes(String clientId) async {
    try {
      final interactions = await getClientInteractions(clientId);
      return {
        'optimalHours': _getOptimalContactHours(interactions),
        'optimalDays': _getOptimalContactDays(interactions),
      };
    } catch (e) {
      throw Exception('فشل في اقتراح أوقات التواصل المثلى: $e');
    }
  }

  // وظائف مساعدة للتحليلات
  String _getMostCommonType(List<ClientInteraction> interactions) {
    final typeCount = _groupByType(interactions);
    return typeCount.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  double _calculateAveragePerDay(List<ClientInteraction> interactions) {
    if (interactions.isEmpty) return 0;
    final days = interactions.last.interactionDate.difference(interactions.first.interactionDate).inDays;
    return days > 0 ? interactions.length / days : interactions.length.toDouble();
  }

  Map<String, int> _analyzePeakHours(List<ClientInteraction> interactions) {
    final Map<String, int> hourCount = {};
    for (final interaction in interactions) {
      final hour = interaction.interactionDate.hour.toString();
      hourCount[hour] = (hourCount[hour] ?? 0) + 1;
    }
    return hourCount;
  }

  Map<String, double> _analyzeChannelEffectiveness(List<ClientInteraction> interactions) {
    final Map<String, List<ClientInteraction>> byType = {};
    for (final interaction in interactions) {
      final type = interaction.type.toString().split('.').last;
      byType[type] = (byType[type] ?? [])..add(interaction);
    }

    final Map<String, double> effectiveness = {};
    byType.forEach((type, typeInteractions) {
      final successful = typeInteractions.where((i) => i.outcome == InteractionOutcome.positive).length;
      effectiveness[type] = typeInteractions.isNotEmpty ? (successful / typeInteractions.length) * 100 : 0;
    });

    return effectiveness;
  }

  List<String> _getPreferredChannels(List<ClientInteraction> interactions) {
    final typeCount = _groupByType(interactions);
    final sorted = typeCount.entries.toList()..sort((a, b) => b.value.compareTo(a.value));
    return sorted.take(3).map((e) => e.key).toList();
  }

  Map<String, int> _analyzePeakDays(List<ClientInteraction> interactions) {
    final Map<String, int> dayCount = {};
    for (final interaction in interactions) {
      final day = interaction.interactionDate.weekday.toString();
      dayCount[day] = (dayCount[day] ?? 0) + 1;
    }
    return dayCount;
  }

  List<String> _predictNeeds(List<ClientInteraction> interactions) {
    // تطبيق مبسط للتوقع
    return ['متابعة دورية', 'عرض جديد', 'استشارة'];
  }

  List<String> _recommendActions(List<ClientInteraction> interactions) {
    // تطبيق مبسط للتوصيات
    return ['جدولة مكالمة', 'إرسال عرض', 'ترتيب اجتماع'];
  }

  List<int> _getOptimalContactHours(List<ClientInteraction> interactions) {
    final hourCount = _analyzePeakHours(interactions);
    final sorted = hourCount.entries.toList()..sort((a, b) => b.value.compareTo(a.value));
    return sorted.take(3).map((e) => int.parse(e.key)).toList();
  }

  List<int> _getOptimalContactDays(List<ClientInteraction> interactions) {
    final dayCount = _analyzePeakDays(interactions);
    final sorted = dayCount.entries.toList()..sort((a, b) => b.value.compareTo(a.value));
    return sorted.take(3).map((e) => int.parse(e.key)).toList();
  }
}
