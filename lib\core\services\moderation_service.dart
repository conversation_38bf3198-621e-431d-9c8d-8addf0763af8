import 'dart:developer' as developer;

import 'package:cloud_firestore/cloud_firestore.dart';

import '../../domain/models/forum/report_model.dart';
import '../../domain/models/forum/topic_model.dart';

/// خدمة الإشراف والإدارة المتقدمة
class ModerationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// الحصول على جميع التقارير
  Future<List<ReportModel>> getAllReports({
    ReportStatus? status,
    ReportContentType? contentType,
    int limit = 50,
  }) async {
    try {
      Query query = _firestore.collection('forum_reports');

      if (status != null) {
        query = query.where('status', isEqualTo: status.toString().split('.').last);
      }

      if (contentType != null) {
        query = query.where('contentType', isEqualTo: contentType.toString().split('.').last);
      }

      query = query.orderBy('createdAt', descending: true).limit(limit);

      final snapshot = await query.get();
      return snapshot.docs.map((doc) => ReportModel.fromFirestore(doc)).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على التقارير: $e');
      return [];
    }
  }

  /// معالجة تقرير
  Future<bool> handleReport({
    required String reportId,
    required String moderatorId,
    required ReportStatus newStatus,
    String? adminNotes,
    bool deleteContent = false,
    bool banUser = false,
    int banDurationDays = 0,
  }) async {
    try {
      final reportRef = _firestore.collection('forum_reports').doc(reportId);
      final reportDoc = await reportRef.get();
      
      if (!reportDoc.exists) return false;
      
      final report = ReportModel.fromFirestore(reportDoc);

      // تحديث حالة التقرير
      await reportRef.update({
        'status': newStatus.toString().split('.').last,
        'handledBy': moderatorId,
        'handledAt': Timestamp.now(),
        'adminNotes': adminNotes,
      });

      // حذف المحتوى إذا طُلب ذلك
      if (deleteContent) {
        await _deleteReportedContent(report);
      }

      // حظر المستخدم إذا طُلب ذلك
      if (banUser) {
        await _banUser(report.contentId, moderatorId, banDurationDays, adminNotes ?? '');
      }

      // إرسال إشعار للمبلغ
      await _notifyReporter(report, newStatus);

      return true;
    } catch (e) {
      // خطأ في معالجة التقرير
      return false;
    }
  }

  /// حذف المحتوى المبلغ عنه
  Future<void> _deleteReportedContent(ReportModel report) async {
    try {
      switch (report.contentType) {
        case ReportContentType.topic:
          await _deleteTopic(report.contentId);
          break;
        case ReportContentType.post:
          await _deletePost(report.contentId);
          break;
        case ReportContentType.user:
          // لا نحذف المستخدم، فقط نحظره
          break;
      }
    } catch (e) {
      // خطأ في حذف المحتوى المبلغ عنه
    }
  }

  /// حذف موضوع
  Future<void> _deleteTopic(String topicId) async {
    try {
      // حذف جميع المشاركات المرتبطة بالموضوع
      final postsSnapshot = await _firestore
          .collection('forum_posts')
          .where('topicId', isEqualTo: topicId)
          .get();

      for (final postDoc in postsSnapshot.docs) {
        await postDoc.reference.delete();
      }

      // حذف الموضوع
      await _firestore.collection('forum_topics').doc(topicId).delete();

      developer.log('تم حذف الموضوع $topicId وجميع مشاركاته');
    } catch (e) {
      developer.log('خطأ في حذف الموضوع: $e');
    }
  }

  /// حذف مشاركة
  Future<void> _deletePost(String postId) async {
    try {
      // حذف الردود على هذه المشاركة
      final repliesSnapshot = await _firestore
          .collection('forum_posts')
          .where('parentId', isEqualTo: postId)
          .get();

      for (final replyDoc in repliesSnapshot.docs) {
        await replyDoc.reference.delete();
      }

      // حذف المشاركة
      await _firestore.collection('forum_posts').doc(postId).delete();

      developer.log('تم حذف المشاركة $postId وجميع ردودها');
    } catch (e) {
      developer.log('خطأ في حذف المشاركة: $e');
    }
  }

  /// حظر مستخدم
  Future<void> _banUser(String userId, String moderatorId, int durationDays, String reason) async {
    try {
      final banEndDate = durationDays > 0 
          ? DateTime.now().add(Duration(days: durationDays))
          : null; // حظر دائم إذا كان 0

      await _firestore.collection('user_bans').doc(userId).set({
        'userId': userId,
        'moderatorId': moderatorId,
        'reason': reason,
        'banDate': Timestamp.now(),
        'banEndDate': banEndDate != null ? Timestamp.fromDate(banEndDate) : null,
        'isActive': true,
        'isPermanent': durationDays == 0,
      });

    } catch (e) {
      // خطأ في حظر المستخدم
    }
  }

  /// إرسال إشعار للمبلغ
  Future<void> _notifyReporter(ReportModel report, ReportStatus status) async {
    try {
      String message = '';
      switch (status) {
        case ReportStatus.resolved:
          message = 'تم حل التقرير الذي قدمته وتم اتخاذ الإجراء المناسب';
          break;
        case ReportStatus.rejected:
          message = 'تم رفض التقرير الذي قدمته بعد المراجعة';
          break;
        default:
          return;
      }

      await _firestore.collection('forum_notifications').add({
        'userId': report.reporterId,
        'type': 'report_update',
        'title': 'تحديث التقرير',
        'message': message,
        'isRead': false,
        'createdAt': Timestamp.now(),
        'relatedId': report.id,
      });
    } catch (e) {
      // خطأ في إرسال إشعار للمبلغ
    }
  }

  /// فحص إذا كان المستخدم محظور
  Future<bool> isUserBanned(String userId) async {
    try {
      final banDoc = await _firestore.collection('user_bans').doc(userId).get();
      
      if (!banDoc.exists) return false;
      
      final banData = banDoc.data()!;
      final isActive = banData['isActive'] ?? false;
      
      if (!isActive) return false;
      
      final isPermanent = banData['isPermanent'] ?? false;
      if (isPermanent) return true;
      
      final banEndDate = (banData['banEndDate'] as Timestamp?)?.toDate();
      if (banEndDate != null && DateTime.now().isAfter(banEndDate)) {
        // انتهى الحظر، قم بإلغائه
        await _firestore.collection('user_bans').doc(userId).update({
          'isActive': false,
        });
        return false;
      }
      
      return true;
    } catch (e) {
      developer.log('خطأ في فحص حظر المستخدم: $e');
      return false;
    }
  }

  /// إلغاء حظر مستخدم
  Future<bool> unbanUser(String userId, String moderatorId) async {
    try {
      await _firestore.collection('user_bans').doc(userId).update({
        'isActive': false,
        'unbannedBy': moderatorId,
        'unbannedAt': Timestamp.now(),
      });

      return true;
    } catch (e) {
      // خطأ في إلغاء حظر المستخدم
      return false;
    }
  }

  /// الحصول على قائمة المستخدمين المحظورين
  Future<List<Map<String, dynamic>>> getBannedUsers({int limit = 50}) async {
    try {
      final snapshot = await _firestore
          .collection('user_bans')
          .where('isActive', isEqualTo: true)
          .orderBy('banDate', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) => {
        'id': doc.id,
        ...doc.data(),
      }).toList();
    } catch (e) {
      // خطأ في الحصول على المستخدمين المحظورين
      return [];
    }
  }

  /// تثبيت موضوع
  Future<bool> pinTopic(String topicId, String moderatorId) async {
    try {
      await _firestore.collection('forum_topics').doc(topicId).update({
        'type': TopicType.pinned.index,
        'pinnedBy': moderatorId,
        'pinnedAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });

      developer.log('تم تثبيت الموضوع $topicId بواسطة $moderatorId');
      return true;
    } catch (e) {
      developer.log('خطأ في تثبيت الموضوع: $e');
      return false;
    }
  }

  /// إلغاء تثبيت موضوع
  Future<bool> unpinTopic(String topicId, String moderatorId) async {
    try {
      await _firestore.collection('forum_topics').doc(topicId).update({
        'type': TopicType.normal.index,
        'pinnedBy': null,
        'pinnedAt': null,
        'updatedAt': Timestamp.now(),
      });

      return true;
    } catch (e) {
      // خطأ في إلغاء تثبيت الموضوع
      return false;
    }
  }

  /// تمييز موضوع
  Future<bool> featureTopic(String topicId, String moderatorId) async {
    try {
      await _firestore.collection('forum_topics').doc(topicId).update({
        'type': TopicType.featured.index,
        'featuredBy': moderatorId,
        'featuredAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });

      return true;
    } catch (e) {
      // خطأ في تمييز الموضوع
      return false;
    }
  }

  /// إحصائيات الإشراف
  Future<Map<String, dynamic>> getModerationStats() async {
    try {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final startOfWeek = now.subtract(Duration(days: now.weekday - 1));

      // إحصائيات التقارير
      final totalReports = await _getReportsCount();
      final pendingReports = await _getReportsCount(status: ReportStatus.pending);
      final monthlyReports = await _getReportsCount(since: startOfMonth);
      final weeklyReports = await _getReportsCount(since: startOfWeek);

      // إحصائيات المحتوى المحذوف
      final deletedTopics = await _getDeletedContentCount('forum_topics', startOfMonth);
      final deletedPosts = await _getDeletedContentCount('forum_posts', startOfMonth);

      // إحصائيات المستخدمين المحظورين
      final totalBans = await _getBannedUsersCount();
      final activeBans = await _getBannedUsersCount(activeOnly: true);

      return {
        'reports': {
          'total': totalReports,
          'pending': pendingReports,
          'monthly': monthlyReports,
          'weekly': weeklyReports,
        },
        'deletedContent': {
          'topics': deletedTopics,
          'posts': deletedPosts,
        },
        'bans': {
          'total': totalBans,
          'active': activeBans,
        },
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      // خطأ في الحصول على إحصائيات الإشراف
      return {};
    }
  }

  /// الحصول على عدد التقارير
  Future<int> _getReportsCount({ReportStatus? status, DateTime? since}) async {
    try {
      Query query = _firestore.collection('forum_reports');

      if (status != null) {
        query = query.where('status', isEqualTo: status.toString().split('.').last);
      }

      if (since != null) {
        query = query.where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(since));
      }

      final snapshot = await query.get();
      return snapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }

  /// الحصول على عدد المحتوى المحذوف
  Future<int> _getDeletedContentCount(String collection, DateTime since) async {
    try {
      // هذا يتطلب تتبع المحتوى المحذوف في مجموعة منفصلة
      final snapshot = await _firestore
          .collection('deleted_content')
          .where('collection', isEqualTo: collection)
          .where('deletedAt', isGreaterThanOrEqualTo: Timestamp.fromDate(since))
          .get();

      return snapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }

  /// الحصول على عدد المستخدمين المحظورين
  Future<int> _getBannedUsersCount({bool activeOnly = false}) async {
    try {
      Query query = _firestore.collection('user_bans');

      if (activeOnly) {
        query = query.where('isActive', isEqualTo: true);
      }

      final snapshot = await query.get();
      return snapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }
}
