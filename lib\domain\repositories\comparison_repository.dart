// تم تعليق مستودع المقارنة مؤقتاً - سيتم تفعيله لاحقاً
/*
import '../entities/estate_comparison.dart';
import '../entities/estate_base.dart';

/// واجهة مستودع مقارنة العقارات
abstract class ComparisonRepository {
  /// إنشاء مقارنة جديدة
  Future<String> createComparison(EstateComparison comparison);

  /// تحديث مقارنة موجودة
  Future<void> updateComparison(EstateComparison comparison);

  /// حذف مقارنة
  Future<void> deleteComparison(String comparisonId);

  /// الحصول على مقارنة بواسطة المعرف
  Future<EstateComparison?> getComparisonById(String comparisonId);

  /// الحصول على مقارنات المستخدم
  Future<List<EstateComparison>> getUserComparisons(String userId);

  /// الحصول على العقارات المقارنة
  Future<List<EstateBase>> getComparisonEstates(String comparisonId);

  /// إضافة عقار إلى المقارنة
  Future<void> addEstateToComparison(String comparisonId, String estateId);

  /// إزالة عقار من المقارنة
  Future<void> removeEstateFromComparison(String comparisonId, String estateId);

  /// إنشاء مقارنة سريعة (غير محفوظة)
  Future<String> createQuickComparison(String userId, List<String> estateIds);

  /// حفظ مقارنة
  Future<void> saveComparison(String comparisonId, String title);

  /// إلغاء حفظ مقارنة
  Future<void> unsaveComparison(String comparisonId);

  /// إضافة ملاحظة إلى المقارنة
  Future<void> addComparisonNote(String comparisonId, String note);

  /// إخفاء خاصية في المقارنة
  Future<void> hideComparisonProperty(String comparisonId, String property);

  /// إظهار خاصية في المقارنة
  Future<void> showComparisonProperty(String comparisonId, String property);

  /// الحصول على الخصائص المتاحة للمقارنة
  Future<List<String>> getAvailableComparisonProperties();

  /// مشاركة المقارنة
  Future<String> shareComparison(String comparisonId);

  /// الحصول على مقارنة مشتركة
  Future<EstateComparison?> getSharedComparison(String shareId);
}
*/
