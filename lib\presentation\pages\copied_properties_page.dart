import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/domain/entities/estate.dart';
import 'package:kuwait_corners/domain/entities/estate_factory.dart';
import 'package:kuwait_corners/domain/entities/estate_converter.dart';
import 'package:kuwait_corners/presentation/widgets/estate_card.dart';
import 'package:kuwait_corners/presentation/widgets/loading_widget.dart';
import 'package:kuwait_corners/presentation/pages/copied_estate_details_page.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;

/// صفحة العقارات المنسوخة للوكلاء
class CopiedPropertiesPage extends StatefulWidget {
  const CopiedPropertiesPage({super.key});

  @override
  State<CopiedPropertiesPage> createState() => _CopiedPropertiesPageState();
}

class _CopiedPropertiesPageState extends State<CopiedPropertiesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'العقارات المنسوخة',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white)),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics_outlined),
            onPressed: () {
              Navigator.pushNamed(context, '/copy-analytics');
            }),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {});
            }),
        ]),
      body: Stack(
        children: [
          // الأشكال الهندسية في الخلفية
          Positioned.fill(
            child: CustomPaint(
              painter: CopiedPropertiesShapesPainter(),
            ),
          ),
          // المحتوى الرئيسي
          Column(
            children: [
              _buildSearchBar(),
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildAllCopiedTab(),
                    _buildActiveTab(),
                  ])),
            ]),
        ]));
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: AppColors.border, width: 1),
        )),
      child: TextField(
        controller: _searchController,
        style: GoogleFonts.cairo(),
        decoration: InputDecoration(
          hintText: 'البحث في العقارات المنسوخة...',
          hintStyle: GoogleFonts.cairo(color: AppColors.textLight),
          prefixIcon: Icon(Icons.search, color: AppColors.primary),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  })
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppColors.border)),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppColors.primary, width: 2)),
          filled: true,
          fillColor: AppColors.background),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        }));
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        labelStyle: GoogleFonts.cairo(
          fontWeight: FontWeight.bold,
          fontSize: 14),
        unselectedLabelStyle: GoogleFonts.cairo(
          fontWeight: FontWeight.w500,
          fontSize: 14),
        tabs: const [
          Tab(text: 'جميع المنسوخة'),
          Tab(text: 'النشطة'),
        ]));
  }

  Widget _buildAllCopiedTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getAllCopiedPropertiesStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل العقارات المنسوخة');
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyState('لا توجد عقارات منسوخة');
        }

        final properties = snapshot.data!.docs;
        final filteredProperties = _filterProperties(properties);

        return RefreshIndicator(
          onRefresh: () async {
            setState(() {});
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredProperties.length,
            itemBuilder: (context, index) {
              final propertyDoc = filteredProperties[index];
              final propertyData = propertyDoc.data() as Map<String, dynamic>;

              try {
                final estateBase = EstateFactory.createFromSnapshot(propertyDoc);
                final estate = EstateConverter.toLegacyEstate(estateBase);
                if (estate != null) {
                  return _buildCopiedPropertyCard(estate, propertyData);
                }
                return const SizedBox.shrink();
              } catch (e) {
                return const SizedBox.shrink();
              }
            }));
      });
  }

  Widget _buildActiveTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getActivePropertiesStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل العقارات النشطة');
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyState('لا توجد عقارات نشطة');
        }

        final properties = snapshot.data!.docs;

        return RefreshIndicator(
          onRefresh: () async {
            setState(() {});
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: properties.length,
            itemBuilder: (context, index) {
              final propertyDoc = properties[index];
              final propertyData = propertyDoc.data() as Map<String, dynamic>;

              try {
                final estateBase = EstateFactory.createFromSnapshot(propertyDoc);
                final estate = EstateConverter.toLegacyEstate(estateBase);
                if (estate != null) {
                  return _buildCopiedPropertyCard(estate, propertyData);
                }
                return const SizedBox.shrink();
              } catch (e) {
                return const SizedBox.shrink();
              }
            }));
      });
  }



  Widget _buildCopiedPropertyCard(Estate estate, Map<String, dynamic> data) {
    final isPaid = data['isPaid'] ?? false;
    final isActive = data['isActive'] ?? false;
    final copiedAt = data['copiedAt'] as Timestamp?;
    final views = data['views'] ?? 0;
    final inquiries = data['inquiries'] ?? 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border, width: 1),
      ),
      child: Column(
        children: [
          Stack(
            children: [
              _buildCopiedEstateCard(estate, data),
              Positioned(
                top: 12,
                right: 12,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor(isPaid, isActive),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2))
                    ]),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getStatusIcon(isPaid, isActive),
                        size: 12,
                        color: Colors.white),
                      const SizedBox(width: 4),
                      Text(
                        _getStatusText(isPaid, isActive),
                        style: GoogleFonts.cairo(
                          fontSize: 11,
                          color: Colors.white,
                          fontWeight: FontWeight.w600)),
                    ]))),
            ]),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12))),
            child: Column(
              children: [
                // معلومات إضافية
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoChip(
                        Icons.visibility_outlined,
                        '$views مشاهدة',
                        AppColors.primary)),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildInfoChip(
                        Icons.message_outlined,
                        '$inquiries استفسار',
                        Colors.orange)),
                  ]),
                if (copiedAt != null) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Icon(Icons.access_time, size: 16, color: AppColors.textLight),
                      const SizedBox(width: 8),
                      Text(
                        'تم النسخ: ${_formatDate(copiedAt.toDate())}',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: AppColors.textLight)),
                    ]),
                ],
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _editCopiedProperty(estate),
                        icon: const Icon(Icons.edit_outlined, size: 16),
                        label: Text('تعديل', style: GoogleFonts.cairo(fontWeight: FontWeight.w600)),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.primary,
                          side: BorderSide(color: AppColors.primary),
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8))))),
                    const SizedBox(width: 8),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.border),
                        borderRadius: BorderRadius.circular(8)),
                      child: PopupMenuButton<String>(
                        onSelected: (value) => _handlePropertyAction(value, estate),
                        icon: Icon(Icons.more_vert, color: AppColors.textSecondary),
                        itemBuilder: (context) => [
                          PopupMenuItem(
                            value: 'manage',
                            child: Row(
                              children: [
                                Icon(Icons.settings_outlined, size: 16, color: AppColors.textSecondary),
                                const SizedBox(width: 8),
                                Text('إدارة', style: GoogleFonts.cairo()),
                              ])),
                          if (isPaid && isActive)
                            PopupMenuItem(
                              value: 'renew',
                              child: Row(
                                children: [
                                  Icon(Icons.refresh, size: 16, color: AppColors.textSecondary),
                                  const SizedBox(width: 8),
                                  Text('تجديد', style: GoogleFonts.cairo()),
                                ])),
                          PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete_outline, size: 16, color: Colors.red),
                                const SizedBox(width: 8),
                                Text('حذف', style: GoogleFonts.cairo(color: Colors.red)),
                              ])),
                        ])),
                  ]),
              ])),
        ]));
  }

  /// بناء بطاقة العقار المنسوخ مع الصور الصحيحة
  Widget _buildCopiedEstateCard(Estate estate, Map<String, dynamic> data) {
    final images = data['images'] as List<dynamic>? ?? [];
    final imageUrls = images.map((e) => e.toString()).toList();

    return GestureDetector(
      onTap: () => _viewCopiedPropertyDetails(estate, data),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة العقار
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: imageUrls.isNotEmpty
                    ? Stack(
                        children: [
                          CachedNetworkImage(
                            imageUrl: imageUrls.first,
                            fit: BoxFit.cover,
                            width: double.infinity,
                            placeholder: (context, url) => Container(
                              color: Colors.grey[200],
                              child: const Center(
                                child: CircularProgressIndicator(),
                              ),
                            ),
                            errorWidget: (context, url, error) => Container(
                              color: Colors.grey[200],
                              child: const Icon(Icons.error),
                            ),
                          ),
                          // مؤشر عدد الصور
                          if (imageUrls.length > 1)
                            Positioned(
                              bottom: 8,
                              left: 8,
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0.7),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(Icons.photo_library, color: Colors.white, size: 12),
                                    const SizedBox(width: 4),
                                    Text(
                                      '${imageUrls.length}',
                                      style: GoogleFonts.cairo(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                        ],
                      )
                    : Container(
                        color: Colors.grey[200],
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.home_outlined, size: 40, color: Colors.grey[400]),
                            const SizedBox(height: 8),
                            Text(
                              'لا توجد صورة',
                              style: GoogleFonts.cairo(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
              ),
            ),
            // معلومات العقار
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    estate.title,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.location_on, size: 16, color: AppColors.textSecondary),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          estate.location,
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${estate.price.toStringAsFixed(0)} د.ك',
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          estate.subCategory ?? 'عقار',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.content_copy_outlined,
            size: 80,
            color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600])),
        ]));
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red[600])),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              setState(() {});
            },
            child: const Text('إعادة المحاولة')),
        ]));
  }

  // Firebase Streams
  Stream<QuerySnapshot> _getAllCopiedPropertiesStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    return FirebaseFirestore.instance
        .collection('estates')
        .where('ownerId', isEqualTo: currentUser.uid)
        .where('isCopied', isEqualTo: true)
        .orderBy('copiedAt', descending: true)
        .snapshots();
  }

  Stream<QuerySnapshot> _getActivePropertiesStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    return FirebaseFirestore.instance
        .collection('estates')
        .where('ownerId', isEqualTo: currentUser.uid)
        .where('isCopied', isEqualTo: true)
        .orderBy('copiedAt', descending: true)
        .snapshots();
  }



  List<QueryDocumentSnapshot> _filterProperties(List<QueryDocumentSnapshot> properties) {
    if (_searchQuery.isEmpty) return properties;

    final searchTerm = _searchQuery.toLowerCase();
    return properties.where((property) {
      final data = property.data() as Map<String, dynamic>;
      final title = (data['title'] ?? '').toString().toLowerCase();
      final description = (data['description'] ?? '').toString().toLowerCase();
      final location = (data['location'] ?? '').toString().toLowerCase();

      return title.contains(searchTerm) ||
             description.contains(searchTerm) ||
             location.contains(searchTerm);
    }).toList();
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  // Action Methods
  void _viewPropertyDetails(Estate estate) {
    Navigator.pushNamed(
      context,
      '/estate-details',
      arguments: estate);
  }

  void _viewCopiedPropertyDetails(Estate estate, Map<String, dynamic> data) {
    // إنشاء صفحة معاينة مخصصة للعقارات المنسوخة
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CopiedEstateDetailsPage(
          estate: estate,
          copiedData: data,
        ),
      ),
    );
  }

  void _editCopiedProperty(Estate estate) {
    Navigator.pushNamed(
      context,
      '/edit-estate',
      arguments: estate);
  }

  void _handlePropertyAction(String action, Estate estate) {
    switch (action) {
      case 'manage':
        _manageProperty(estate);
        break;
      case 'renew':
        _renewProperty(estate);
        break;
      case 'delete':
        _deleteProperty(estate.id);
        break;
    }
  }

  void _manageProperty(Estate estate) {
    Navigator.pushNamed(
      context,
      '/manage-property',
      arguments: estate);
  }

  void _renewProperty(Estate estate) {
    Navigator.pushNamed(
      context,
      '/renew-property',
      arguments: estate);
  }



  void _deleteProperty(String propertyId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا العقار المنسوخ؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء')),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف')),
        ]));

    if (confirmed == true) {
      try {
        await FirebaseFirestore.instance
            .collection('estates')
            .doc(propertyId)
            .delete();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف العقار بنجاح')));
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('حدث خطأ في حذف العقار')));
        }
      }
    }
  }

  // دوال مساعدة للحالة والتصميم
  Color _getStatusColor(bool isPaid, bool isActive) {
    if (isPaid && isActive) return Colors.green;
    if (isPaid) return Colors.orange;
    return Colors.red;
  }

  IconData _getStatusIcon(bool isPaid, bool isActive) {
    if (isPaid && isActive) return Icons.check_circle;
    if (isPaid) return Icons.schedule;
    return Icons.pending;
  }

  String _getStatusText(bool isPaid, bool isActive) {
    if (isPaid && isActive) return 'نشط';
    if (isPaid) return 'مدفوع';
    return 'معلق';
  }

  Widget _buildInfoChip(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              text,
              style: GoogleFonts.cairo(
                fontSize: 11,
                color: color,
                fontWeight: FontWeight.w600),
              overflow: TextOverflow.ellipsis)),
        ]));
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }
}

/// رسام الأشكال الهندسية للعقارات المنسوخة
class CopiedPropertiesShapesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.primary.withValues(alpha: 0.06)
      ..style = PaintingStyle.fill;

    final strokePaint = Paint()
      ..color = AppColors.primary.withValues(alpha: 0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // إنشاء أشكال متنوعة معبرة عن العقارات المنسوخة
    _drawCopiedPropertyShapes(canvas, size, paint, strokePaint);
  }

  void _drawCopiedPropertyShapes(Canvas canvas, Size size, Paint fillPaint, Paint strokePaint) {
    final random = math.Random(123); // seed ثابت للحصول على نفس النمط

    // رسم أشكال مختلفة
    for (int i = 0; i < 18; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final shapeType = random.nextInt(7);
      final scale = 0.3 + random.nextDouble() * 0.5; // حجم متغير

      canvas.save();
      canvas.translate(x, y);
      canvas.scale(scale);

      switch (shapeType) {
        case 0:
          _drawCopyIcon(canvas, fillPaint, strokePaint);
          break;
        case 1:
          _drawPropertyIcon(canvas, fillPaint, strokePaint);
          break;
        case 2:
          _drawDocumentStack(canvas, strokePaint);
          break;
        case 3:
          _drawAnalyticsChart(canvas, strokePaint);
          break;
        case 4:
          _drawMoneyIcon(canvas, fillPaint, strokePaint);
          break;
        case 5:
          _drawCheckmark(canvas, strokePaint);
          break;
        case 6:
          _drawClockIcon(canvas, strokePaint);
          break;
      }

      canvas.restore();
    }
  }

  // رسم أيقونة النسخ
  void _drawCopyIcon(Canvas canvas, Paint fillPaint, Paint strokePaint) {
    // المستند الأول
    final rect1 = const Rect.fromLTWH(-10, -12, 16, 20);
    canvas.drawRect(rect1, fillPaint);
    canvas.drawRect(rect1, strokePaint);

    // المستند الثاني (المنسوخ)
    final rect2 = const Rect.fromLTWH(-6, -8, 16, 20);
    canvas.drawRect(rect2, fillPaint);
    canvas.drawRect(rect2, strokePaint);
  }

  // رسم أيقونة العقار
  void _drawPropertyIcon(Canvas canvas, Paint fillPaint, Paint strokePaint) {
    final path = Path();
    // قاعدة المنزل
    path.addRect(const Rect.fromLTWH(-12, -8, 24, 16));
    // سقف المنزل
    path.moveTo(-15, -8);
    path.lineTo(0, -20);
    path.lineTo(15, -8);
    path.close();

    canvas.drawPath(path, fillPaint);
    canvas.drawPath(path, strokePaint);
  }

  // رسم مكدس المستندات
  void _drawDocumentStack(Canvas canvas, Paint strokePaint) {
    for (int i = 0; i < 3; i++) {
      final rect = Rect.fromLTWH(-8 + i * 2, -10 + i * 2, 16, 20);
      canvas.drawRect(rect, strokePaint);
    }
  }

  // رسم مخطط التحليلات
  void _drawAnalyticsChart(Canvas canvas, Paint strokePaint) {
    final path = Path();
    path.moveTo(-12, 8);
    path.lineTo(-6, -2);
    path.lineTo(0, 4);
    path.lineTo(6, -8);
    path.lineTo(12, 0);

    canvas.drawPath(path, strokePaint);

    // نقاط البيانات
    final points = [-12, -6, 0, 6, 12];
    final values = [8, -2, 4, -8, 0];
    for (int i = 0; i < points.length; i++) {
      canvas.drawCircle(Offset(points[i].toDouble(), values[i].toDouble()), 2, strokePaint);
    }
  }

  // رسم أيقونة المال
  void _drawMoneyIcon(Canvas canvas, Paint fillPaint, Paint strokePaint) {
    // دائرة العملة
    canvas.drawCircle(const Offset(0, 0), 12, fillPaint);
    canvas.drawCircle(const Offset(0, 0), 12, strokePaint);

    // رمز العملة
    final path = Path();
    path.moveTo(-4, -6);
    path.lineTo(4, -6);
    path.moveTo(0, -10);
    path.lineTo(0, 10);
    path.moveTo(-4, 6);
    path.lineTo(4, 6);

    canvas.drawPath(path, strokePaint);
  }

  // رسم علامة الصح
  void _drawCheckmark(Canvas canvas, Paint strokePaint) {
    final path = Path();
    path.moveTo(-8, 0);
    path.lineTo(-2, 6);
    path.lineTo(8, -6);

    canvas.drawPath(path, strokePaint);
  }

  // رسم أيقونة الساعة
  void _drawClockIcon(Canvas canvas, Paint strokePaint) {
    // دائرة الساعة
    canvas.drawCircle(const Offset(0, 0), 10, strokePaint);

    // عقارب الساعة
    canvas.drawLine(const Offset(0, 0), const Offset(0, -6), strokePaint);
    canvas.drawLine(const Offset(0, 0), const Offset(4, 0), strokePaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}