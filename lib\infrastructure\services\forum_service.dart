import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../domain/entities/forum_category.dart';
import '../../domain/models/forum/topic_model.dart';
import '../../domain/models/forum/post_model.dart';
import '../../domain/models/forum/achievement_model.dart';

/// خدمة المنتدى
class ForumService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// الحصول على جميع الفئات
  Future<List<ForumCategory>> getCategories() async {
    try {
      final snapshot = await _firestore
          .collection('forum_categories')
          .where('isActive', isEqualTo: true)
          .orderBy('name')
          .get();

      return snapshot.docs
          .map((doc) => ForumCategory.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('خطأ في تحميل الفئات: $e');
    }
  }

  /// إنشاء فئة جديدة
  Future<ForumCategory> createCategory({
    required String name,
    String? description,
    String? iconName,
    String? color,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      final now = DateTime.now();
      final categoryData = {
        'name': name,
        'description': description,
        'iconName': iconName,
        'color': color,
        'topicsCount': 0,
        'postsCount': 0,
        'isActive': true,
        'createdAt': Timestamp.fromDate(now),
        'createdBy': user.uid,
      };

      final docRef = await _firestore
          .collection('forum_categories')
          .add(categoryData);

      return ForumCategory(
        id: docRef.id,
        name: name,
        description: description,
        iconName: iconName,
        color: color,
        createdAt: now,
      );
    } catch (e) {
      throw Exception('خطأ في إنشاء الفئة: $e');
    }
  }

  /// تحديث فئة
  Future<void> updateCategory(
    String categoryId, {
    String? name,
    String? description,
    String? iconName,
    String? color,
    bool? isActive,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      final updateData = <String, dynamic>{
        'updatedAt': Timestamp.fromDate(DateTime.now()),
        'updatedBy': user.uid,
      };

      if (name != null) updateData['name'] = name;
      if (description != null) updateData['description'] = description;
      if (iconName != null) updateData['iconName'] = iconName;
      if (color != null) updateData['color'] = color;
      if (isActive != null) updateData['isActive'] = isActive;

      await _firestore
          .collection('forum_categories')
          .doc(categoryId)
          .update(updateData);
    } catch (e) {
      throw Exception('خطأ في تحديث الفئة: $e');
    }
  }

  /// حذف فئة
  Future<void> deleteCategory(String categoryId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      // التحقق من وجود مواضيع في الفئة
      final topicsSnapshot = await _firestore
          .collection('forum_topics')
          .where('categoryId', isEqualTo: categoryId)
          .limit(1)
          .get();

      if (topicsSnapshot.docs.isNotEmpty) {
        throw Exception('لا يمكن حذف فئة تحتوي على مواضيع');
      }

      await _firestore
          .collection('forum_categories')
          .doc(categoryId)
          .delete();
    } catch (e) {
      throw Exception('خطأ في حذف الفئة: $e');
    }
  }

  /// الحصول على المواضيع حسب الفئة
  Future<List<TopicModel>> getTopicsByCategory(String categoryId) async {
    try {
      final snapshot = await _firestore
          .collection('forum_topics')
          .where('categoryId', isEqualTo: categoryId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => TopicModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('خطأ في تحميل المواضيع: $e');
    }
  }

  /// إنشاء موضوع جديد
  Future<TopicModel> createTopic({
    required String categoryId,
    required String title,
    required String content,
    List<String>? tags,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      final now = DateTime.now();
      final topicData = {
        'categoryId': categoryId,
        'title': title,
        'content': content,
        'tags': tags ?? [],
        'authorId': user.uid,
        'authorName': user.displayName ?? user.email ?? 'مستخدم',
        'createdAt': Timestamp.fromDate(now),
        'updatedAt': Timestamp.fromDate(now),
        'viewsCount': 0,
        'likesCount': 0,
        'repliesCount': 0,
        'isLocked': false,
        'isPinned': false,
      };

      final docRef = await _firestore
          .collection('forum_topics')
          .add(topicData);

      // تحديث عدد المواضيع في الفئة
      await _firestore
          .collection('forum_categories')
          .doc(categoryId)
          .update({
        'topicsCount': FieldValue.increment(1),
      });

      return TopicModel.fromMap({
        'id': docRef.id,
        ...topicData,
      });
    } catch (e) {
      throw Exception('خطأ في إنشاء الموضوع: $e');
    }
  }

  /// إضافة رد على موضوع
  Future<PostModel> addReply({
    required String topicId,
    required String content,
    String? parentPostId,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      final now = DateTime.now();
      final postData = {
        'topicId': topicId,
        'content': content,
        'authorId': user.uid,
        'authorName': user.displayName ?? user.email ?? 'مستخدم',
        'createdAt': Timestamp.fromDate(now),
        'updatedAt': Timestamp.fromDate(now),
        'likesCount': 0,
        'isEdited': false,
        'parentPostId': parentPostId,
      };

      final docRef = await _firestore
          .collection('forum_posts')
          .add(postData);

      // تحديث عدد الردود في الموضوع
      await _firestore
          .collection('forum_topics')
          .doc(topicId)
          .update({
        'repliesCount': FieldValue.increment(1),
        'updatedAt': Timestamp.fromDate(now),
      });

      return PostModel.fromMap({
        'id': docRef.id,
        ...postData,
      });
    } catch (e) {
      throw Exception('خطأ في إضافة الرد: $e');
    }
  }

  /// الحصول على ردود موضوع
  Future<List<PostModel>> getTopicReplies(String topicId) async {
    try {
      final snapshot = await _firestore
          .collection('forum_posts')
          .where('topicId', isEqualTo: topicId)
          .orderBy('createdAt')
          .get();

      return snapshot.docs
          .map((doc) => PostModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('خطأ في تحميل الردود: $e');
    }
  }

  /// الحصول على إنجازات المستخدم
  Future<List<AchievementModel>> getUserAchievements(String userId) async {
    try {
      // هذه دالة مؤقتة - يجب تطويرها لاحقاً
      return AchievementModel.getAvailableAchievements();
    } catch (e) {
      throw Exception('خطأ في تحميل الإنجازات: $e');
    }
  }

  /// البحث في المواضيع
  Future<List<TopicModel>> searchTopics(String query) async {
    try {
      // البحث في العناوين
      final titleSnapshot = await _firestore
          .collection('forum_topics')
          .where('title', isGreaterThanOrEqualTo: query)
          .where('title', isLessThanOrEqualTo: '$query\uf8ff')
          .get();

      return titleSnapshot.docs
          .map((doc) => TopicModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('خطأ في البحث: $e');
    }
  }

  /// الحصول على أحدث المواضيع
  Future<List<TopicModel>> getLatestTopics({int limit = 10}) async {
    try {
      final snapshot = await _firestore
          .collection('forum_topics')
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => TopicModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('خطأ في تحميل المواضيع: $e');
    }
  }
}
