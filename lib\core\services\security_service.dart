import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// خدمة الأمان والتشفير
class SecurityService {
  static final SecurityService _instance = SecurityService._internal();
  factory SecurityService() => _instance;
  SecurityService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// تشفير النص الحساس
  String encryptSensitiveData(String data, String key) {
    try {
      final bytes = utf8.encode(data);
      final keyBytes = utf8.encode(key);
      final hmac = Hmac(sha256, keyBytes);
      final digest = hmac.convert(bytes);
      return base64.encode(digest.bytes);
    } catch (e) {
      throw Exception('فشل في تشفير البيانات: $e');
    }
  }

  /// فك تشفير النص الحساس
  String decryptSensitiveData(String encryptedData, String key) {
    try {
      final bytes = base64.decode(encryptedData);
      // ملاحظة: هذا مثال بسيط، في الواقع نحتاج خوارزمية تشفير أكثر تعقيداً
      return utf8.decode(bytes);
    } catch (e) {
      throw Exception('فشل في فك تشفير البيانات: $e');
    }
  }

  /// توليد مفتاح تشفير عشوائي
  String generateEncryptionKey() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64.encode(bytes);
  }

  /// تحقق من صلاحيات المستخدم
  Future<bool> checkUserPermission(String userId, String permission) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) return false;

      final userData = userDoc.data()!;
      final userPermissions = List<String>.from(userData['permissions'] ?? []);
      final userRole = userData['role'] as String?;

      // تحقق من الصلاحية المباشرة
      if (userPermissions.contains(permission)) return true;

      // تحقق من صلاحيات الدور
      if (userRole != null) {
        final rolePermissions = await _getRolePermissions(userRole);
        return rolePermissions.contains(permission);
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على صلاحيات الدور
  Future<List<String>> _getRolePermissions(String role) async {
    try {
      final roleDoc = await _firestore.collection('roles').doc(role).get();
      if (!roleDoc.exists) return [];

      final roleData = roleDoc.data()!;
      return List<String>.from(roleData['permissions'] ?? []);
    } catch (e) {
      return [];
    }
  }

  /// تحقق من نوع المستخدم
  Future<bool> isUserType(String userId, List<int> allowedTypes) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) return false;

      final userData = userDoc.data()!;
      final userType = userData['type'] as int?;

      return userType != null && allowedTypes.contains(userType);
    } catch (e) {
      return false;
    }
  }

  /// تحقق من ملكية المورد
  Future<bool> isResourceOwner(String userId, String collection, String documentId) async {
    try {
      final doc = await _firestore.collection(collection).doc(documentId).get();
      if (!doc.exists) return false;

      final data = doc.data()!;
      return data['userId'] == userId;
    } catch (e) {
      return false;
    }
  }





  /// تحقق من معدل الطلبات (Rate Limiting)
  Future<bool> checkRateLimit(String userId, String action, {int maxRequests = 10, Duration window = const Duration(minutes: 1)}) async {
    try {
      final now = DateTime.now();
      final windowStart = now.subtract(window);

      final query = await _firestore
          .collection('rate_limits')
          .where('userId', isEqualTo: userId)
          .where('action', isEqualTo: action)
          .where('timestamp', isGreaterThan: Timestamp.fromDate(windowStart))
          .get();

      if (query.docs.length >= maxRequests) {
        return false; // تجاوز الحد المسموح
      }

      // تسجيل الطلب الحالي
      await _firestore.collection('rate_limits').add({
        'userId': userId,
        'action': action,
        'timestamp': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      // في حالة الخطأ، نسمح بالعملية
      return true;
    }
  }

  /// تنظيف سجلات معدل الطلبات القديمة
  Future<void> cleanupOldRateLimitRecords() async {
    try {
      final cutoffTime = DateTime.now().subtract(const Duration(hours: 24));
      final query = await _firestore
          .collection('rate_limits')
          .where('timestamp', isLessThan: Timestamp.fromDate(cutoffTime))
          .get();

      final batch = _firestore.batch();
      for (final doc in query.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
    } catch (e) {
      print('فشل في تنظيف سجلات معدل الطلبات: $e');
    }
  }

  /// تحقق من صحة البيانات المدخلة
  bool validateInput(String input, String type) {
    switch (type) {
      case 'email':
        return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(input);
      case 'phone':
        return RegExp(r'^\+?[1-9]\d{1,14}$').hasMatch(input);
      case 'price':
        final price = double.tryParse(input);
        return price != null && price > 0 && price <= 10000000;
      case 'text':
        return input.trim().isNotEmpty && input.length <= 1000;
      case 'title':
        return input.trim().isNotEmpty && input.length <= 100;
      default:
        return true;
    }
  }

  /// تنظيف النص من المحتوى الضار
  String sanitizeInput(String input) {
    return input
        .replaceAll(RegExp(r'<[^>]*>'), '') // إزالة HTML tags
        .replaceAll(RegExp(r'[<>&"\x27`]'), '') // إزالة الرموز الخطيرة
        .trim();
  }

  /// تحقق من الأذونات المتقدمة
  Future<bool> hasAdvancedPermission(String userId, String resource, String action) async {
    try {
      // تحقق من الصلاحيات الأساسية
      final hasBasicPermission = await checkUserPermission(userId, '$resource:$action');
      if (hasBasicPermission) return true;

      // تحقق من الصلاحيات المشروطة
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) return false;

      final userData = userDoc.data()!;
      final conditionalPermissions = userData['conditionalPermissions'] as Map<String, dynamic>?;

      if (conditionalPermissions != null) {
        final resourcePermissions = conditionalPermissions[resource] as Map<String, dynamic>?;
        if (resourcePermissions != null) {
          final actionConditions = resourcePermissions[action] as Map<String, dynamic>?;
          if (actionConditions != null) {
            return await _evaluateConditions(userId, actionConditions);
          }
        }
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// تقييم الشروط المتقدمة
  Future<bool> _evaluateConditions(String userId, Map<String, dynamic> conditions) async {
    try {
      // مثال على تقييم الشروط
      if (conditions.containsKey('timeWindow')) {
        final timeWindow = conditions['timeWindow'] as Map<String, dynamic>;
        final startHour = timeWindow['start'] as int;
        final endHour = timeWindow['end'] as int;
        final currentHour = DateTime.now().hour;
        
        if (currentHour < startHour || currentHour > endHour) {
          return false;
        }
      }

      if (conditions.containsKey('maxDaily')) {
        final maxDaily = conditions['maxDaily'] as int;
        final today = DateTime.now();
        final startOfDay = DateTime(today.year, today.month, today.day);
        
        final todayActions = await _firestore
            .collection('user_activity_logs')
            .where('userId', isEqualTo: userId)
            .where('timestamp', isGreaterThan: Timestamp.fromDate(startOfDay))
            .get();
            
        if (todayActions.docs.length >= maxDaily) {
          return false;
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }
}
