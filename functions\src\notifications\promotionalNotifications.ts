import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

/**
 * معالجة الإشعارات الترويجية المجدولة
 */
export const processScheduledNotifications = functions.https.onCall(async (data, context) => {
  // التحقق من المصادقة
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'المستخدم غير مصادق');
  }

  try {
    console.log('🔄 بدء معالجة الإشعارات الترويجية المجدولة يدوياً...');
    
    const db = admin.firestore();
    const now = admin.firestore.Timestamp.now();
    
    // البحث عن الإشعارات المجدولة التي حان وقتها
    const query = await db.collection('scheduledNotifications')
      .where('processed', '==', false)
      .where('scheduledTime', '<=', now.toDate())
      .limit(10)
      .get();
    
    if (query.empty) {
      return { success: true, message: 'لا توجد إشعارات مجدولة للمعالجة', processed: 0 };
    }
    
    let processedCount = 0;
    const batch = db.batch();
    
    for (const doc of query.docs) {
      const notificationData = doc.data();
      
      try {
        if (notificationData.type === 'promotional_upgrade') {
          await processPromotionalUpgrade(notificationData);
          processedCount++;
          
          // جدولة الإشعار التالي
          await scheduleNextPromotionalNotification(notificationData);
        }
        
        // تحديد الإشعار كمعالج
        batch.update(doc.ref, {
          processed: true,
          processedAt: now.toDate()
        });
        
      } catch (error) {
        console.error(`❌ خطأ في معالجة الإشعار ${doc.id}:`, error);
        
        // تحديد الإشعار كفاشل
        batch.update(doc.ref, {
          processed: true,
          processedAt: now.toDate(),
          error: String(error)
        });
      }
    }
    
    await batch.commit();
    
    return {
      success: true,
      message: `تم معالجة ${processedCount} إشعار ترويجي بنجاح`,
      processed: processedCount
    };
    
  } catch (error) {
    console.error('❌ خطأ في معالجة الإشعارات الترويجية:', error);
    throw new functions.https.HttpsError('internal', 'خطأ في معالجة الإشعارات الترويجية');
  }
});

/**
 * معالجة إشعار ترويجي لترقية العقار
 */
async function processPromotionalUpgrade(notificationData: any): Promise<void> {
  const db = admin.firestore();
  const messaging = admin.messaging();
  
  console.log(`🚀 إرسال إشعار ترويجي للعقار: ${notificationData.estateId}`);
  
  // التحقق من أن العقار لا يزال موجود وغير مؤرشف
  const estateDoc = await db.collection('estates').doc(notificationData.estateId).get();
  if (!estateDoc.exists || estateDoc.data()?.isArchived) {
    console.log(`⚠️ العقار ${notificationData.estateId} غير موجود أو مؤرشف، تم تخطي الإشعار`);
    return;
  }
  
  // الحصول على بيانات المستخدم
  const userDoc = await db.collection('users').doc(notificationData.userId).get();
  const userData = userDoc.data();
  
  // إنشاء رسالة الإشعار الترويجي
  const notificationTitle = 'اجعل إعلانك أكثر تميزاً! ⭐';
  const notificationBody = `هل تريد المزيد من المشترين والمستأجرين لعقارك "${notificationData.estateTitle}"؟

🚀 استفد من خدماتنا المميزة:
📌 تثبيت الإعلان في المقدمة
⭐ تمييز الإعلان بشارة خاصة
🔝 ظهور أولوي في نتائج البحث

💰 احصل على المزيد من العروض والاستفسارات!

للترقية، تواصل معنا:
📱 واتساب: +965 9929 8821
📧 إيميل: <EMAIL>

تطبيق Krea - شريكك في النجاح العقاري 🏠`;

  // إرسال إشعار push إذا كان المستخدم لديه رمز FCM
  if (userData && userData.fcmToken) {
    try {
      const message = {
        token: userData.fcmToken,
        notification: {
          title: notificationTitle,
          body: 'اجعل إعلانك أكثر تميزاً! اضغط للمزيد من التفاصيل.'
        },
        data: {
          type: 'promotional_upgrade',
          estateId: notificationData.estateId,
          estateTitle: notificationData.estateTitle,
          whatsapp: '+965 9929 8821',
          email: '<EMAIL>',
          fullMessage: notificationBody
        },
        android: {
          notification: {
            icon: 'ic_notification',
            color: '#2E7D32',
            sound: 'default',
            channelId: 'promotional_notifications',
            priority: 'high' as 'high'
          }
        },
        apns: {
          payload: {
            aps: {
              sound: 'default',
              badge: 1,
              alert: {
                title: notificationTitle,
                body: 'اجعل إعلانك أكثر تميزاً! اضغط للمزيد من التفاصيل.'
              }
            }
          }
        }
      };
      
      await messaging.send(message);
      console.log(`✅ تم إرسال إشعار push ترويجي للمستخدم: ${notificationData.userId}`);
      
    } catch (pushError) {
      console.error('❌ خطأ في إرسال إشعار push ترويجي:', pushError);
    }
  }
  
  // حفظ الإشعار في قاعدة البيانات
  await db.collection('users')
    .doc(notificationData.userId)
    .collection('notifications')
    .add({
      title: notificationTitle,
      body: notificationBody,
      type: 'promotional_upgrade',
      data: {
        estateId: notificationData.estateId,
        estateTitle: notificationData.estateTitle,
        contactInfo: {
          whatsapp: '+965 9929 8821',
          email: '<EMAIL>'
        }
      },
      timestamp: admin.firestore.Timestamp.now(),
      isRead: false,
      priority: 'normal'
    });
  
  console.log(`✅ تم حفظ الإشعار الترويجي للمستخدم: ${notificationData.userId}`);
  
  // إضافة سجل في مجموعة logs للمراقبة
  await db.collection('systemLogs').add({
    type: 'promotional_notification_sent',
    estateId: notificationData.estateId,
    userId: notificationData.userId,
    estateTitle: notificationData.estateTitle,
    timestamp: admin.firestore.Timestamp.now(),
    details: 'تم إرسال إشعار ترويجي للمستخدم لترقية إعلانه'
  });
}

/**
 * جدولة الإشعار الترويجي التالي بعد 24 ساعة
 */
async function scheduleNextPromotionalNotification(notificationData: any): Promise<void> {
  try {
    const db = admin.firestore();
    
    // حساب وقت الإشعار التالي (24 ساعة من الآن)
    const nextScheduledTime = new Date(Date.now() + (24 * 60 * 60 * 1000));
    
    // إنشاء إشعار ترويجي جديد مجدول
    await db.collection('scheduledNotifications').add({
      type: 'promotional_upgrade',
      estateId: notificationData.estateId,
      userId: notificationData.userId,
      estateTitle: notificationData.estateTitle,
      scheduledTime: admin.firestore.Timestamp.fromDate(nextScheduledTime),
      createdAt: admin.firestore.Timestamp.now(),
      processed: false,
      notificationData: {
        title: 'اجعل إعلانك أكثر تميزاً! ⭐',
        contactInfo: {
          whatsapp: '+965 9929 8821',
          email: '<EMAIL>'
        }
      }
    });
    
    console.log(`✅ تم جدولة الإشعار الترويجي التالي للعقار: ${notificationData.estateId} في ${nextScheduledTime.toISOString()}`);
    
  } catch (error) {
    console.error(`❌ خطأ في جدولة الإشعار الترويجي التالي: ${error}`);
  }
}
