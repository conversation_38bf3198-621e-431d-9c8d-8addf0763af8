import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:battery_plus/battery_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'cache_manager.dart';

/// خدمة تحسين الأداء
class PerformanceService {
  static final PerformanceService _instance = PerformanceService._internal();

  factory PerformanceService() {
    return _instance;
  }

  PerformanceService._internal();

  final AppCacheManager _cacheManager = AppCacheManager();
  final Battery _battery = Battery();
  final Connectivity _connectivity = Connectivity();
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  final PerformanceMonitor _performanceMonitor = PerformanceMonitor();

  late SharedPreferences _prefs;
  late PackageInfo _packageInfo;

  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  StreamSubscription<BatteryState>? _batterySubscription;

  ConnectivityResult _connectionStatus = ConnectivityResult.none;
  BatteryState _batteryState = BatteryState.unknown;
  int _batteryLevel = 100;
  bool _isLowPowerMode = false;
  bool _isPerformanceMode = false;

  /// تهيئة خدمة تحسين الأداء
  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    _packageInfo = await PackageInfo.fromPlatform();

    // تهيئة مراقبة الاتصال
    _connectionStatus = await _connectivity.checkConnectivity();
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);

    // تهيئة مراقبة البطارية
    _batteryLevel = await _battery.batteryLevel;
    _batteryState = await _battery.batteryState;
    _batterySubscription =
        _battery.onBatteryStateChanged.listen(_updateBatteryState);

    // تحميل إعدادات الأداء
    _loadPerformanceSettings();

    // تهيئة مراقب الأداء
    await _performanceMonitor.init();
  }

  /// تحرير الموارد
  void dispose() {
    _connectivitySubscription?.cancel();
    _batterySubscription?.cancel();
    _performanceMonitor.dispose();
  }

  /// تحديث حالة الاتصال
  void _updateConnectionStatus(ConnectivityResult result) {
    _connectionStatus = result;
    _adjustPerformanceBasedOnConditions();
  }

  /// تحديث حالة البطارية
  void _updateBatteryState(BatteryState state) async {
    _batteryState = state;
    _batteryLevel = await _battery.batteryLevel;

    // التحقق من وضع توفير الطاقة
    if (Platform.isIOS) {
      _isLowPowerMode = await _battery.isInBatterySaveMode;
    } else if (Platform.isAndroid) {
      _isLowPowerMode = await _battery.isInBatterySaveMode;
    }

    _adjustPerformanceBasedOnConditions();
  }

  /// تحميل إعدادات الأداء
  void _loadPerformanceSettings() {
    _isPerformanceMode = _prefs.getBool('performance_mode') ?? false;
  }

  /// حفظ إعدادات الأداء
  Future<void> _savePerformanceSettings() async {
    await _prefs.setBool('performance_mode', _isPerformanceMode);
  }

  /// ضبط الأداء بناءً على الظروف
  void _adjustPerformanceBasedOnConditions() {
    // إذا كان وضع الأداء مفعل، لا نقوم بأي تعديلات تلقائية
    if (_isPerformanceMode) {
      return;
    }

    // ضبط جودة الصور بناءً على نوع الاتصال
    if (_connectionStatus == ConnectivityResult.mobile) {
      setImageQuality('medium');
    } else if (_connectionStatus == ConnectivityResult.wifi) {
      setImageQuality('high');
    } else {
      setImageQuality('low');
    }

    // ضبط التحميل المسبق بناءً على حالة البطارية
    if (_batteryLevel < 20 || _isLowPowerMode) {
      setPreloadingEnabled(false);
    } else {
      setPreloadingEnabled(true);
    }
  }

  /// الحصول على حالة الاتصال
  ConnectivityResult get connectionStatus => _connectionStatus;

  /// الحصول على حالة البطارية
  BatteryState get batteryState => _batteryState;

  /// الحصول على مستوى البطارية
  int get batteryLevel => _batteryLevel;

  /// التحقق مما إذا كان وضع توفير الطاقة مفعل
  bool get isLowPowerMode => _isLowPowerMode;

  /// التحقق مما إذا كان وضع الأداء مفعل
  bool get isPerformanceMode => _isPerformanceMode;

  /// تفعيل/تعطيل وضع الأداء
  Future<void> setPerformanceMode(bool enabled) async {
    _isPerformanceMode = enabled;
    await _savePerformanceSettings();

    if (enabled) {
      // تفعيل إعدادات الأداء العالي
      setImageQuality('high');
      setPreloadingEnabled(true);
      setAnimationsEnabled(true);
    } else {
      // ضبط الأداء بناءً على الظروف الحالية
      _adjustPerformanceBasedOnConditions();
    }
  }

  /// ضبط جودة الصور
  void setImageQuality(String quality) {
    _prefs.setString('image_quality', quality);
  }

  /// الحصول على جودة الصور
  String getImageQuality() {
    return _prefs.getString('image_quality') ?? 'medium';
  }

  /// تفعيل/تعطيل التحميل المسبق
  void setPreloadingEnabled(bool enabled) {
    _prefs.setBool('preloading_enabled', enabled);
  }

  /// التحقق مما إذا كان التحميل المسبق مفعل
  bool isPreloadingEnabled() {
    return _prefs.getBool('preloading_enabled') ?? true;
  }

  /// تفعيل/تعطيل الرسوم المتحركة
  void setAnimationsEnabled(bool enabled) {
    _prefs.setBool('animations_enabled', enabled);
  }

  /// التحقق مما إذا كانت الرسوم المتحركة مفعلة
  bool areAnimationsEnabled() {
    return _prefs.getBool('animations_enabled') ?? true;
  }

  /// الحصول على معلومات الجهاز
  Future<Map<String, dynamic>> getDeviceInfo() async {
    if (Platform.isAndroid) {
      final androidInfo = await _deviceInfo.androidInfo;
      return {
        'platform': 'Android',
        'version': androidInfo.version.release,
        'sdkInt': androidInfo.version.sdkInt,
        'manufacturer': androidInfo.manufacturer,
        'model': androidInfo.model,
        'device': androidInfo.device,
        'isPhysicalDevice': androidInfo.isPhysicalDevice,
      };
    } else if (Platform.isIOS) {
      final iosInfo = await _deviceInfo.iosInfo;
      return {
        'platform': 'iOS',
        'version': iosInfo.systemVersion,
        'name': iosInfo.name,
        'model': iosInfo.model,
        'localizedModel': iosInfo.localizedModel,
        'identifierForVendor': iosInfo.identifierForVendor,
        'isPhysicalDevice': iosInfo.isPhysicalDevice,
      };
    }

    return {
      'platform': Platform.operatingSystem,
      'version': Platform.operatingSystemVersion,
    };
  }

  /// الحصول على معلومات التطبيق
  Map<String, String> getAppInfo() {
    return {
      'appName': _packageInfo.appName,
      'packageName': _packageInfo.packageName,
      'version': _packageInfo.version,
      'buildNumber': _packageInfo.buildNumber,
    };
  }

  /// الحصول على إحصائيات الأداء
  Map<String, dynamic> getPerformanceMetrics() {
    return {
      'fps': _performanceMonitor.currentFps,
      'memoryUsage': _performanceMonitor.currentMemoryUsage,
      'cpuUsage': _performanceMonitor.currentCpuUsage,
      'batteryLevel': _batteryLevel,
      'isLowPowerMode': _isLowPowerMode,
      'connectionType': _connectionStatus.toString(),
    };
  }

  /// تنظيف التخزين المؤقت
  Future<void> clearCache() async {
    await _cacheManager.clearCache();
  }

  /// الحصول على حجم التخزين المؤقت
  Future<String> getCacheSize() async {
    return await _cacheManager.getReadableCacheSize();
  }

  /// تحسين استخدام الذاكرة
  Future<void> optimizeMemoryUsage() async {
    // تنظيف التخزين المؤقت إذا كان حجمه كبيراً
    final cacheSize = await _cacheManager.getCacheSize();
    if (cacheSize > 100 * 1024 * 1024) {
      // أكبر من 100 ميجابايت
      await _cacheManager.clearCache();
    }

    // إجبار جامع القمامة على العمل
    // ملاحظة: هذا ليس مضموناً في دارت/فلاتر، ولكنه قد يساعد
    // ignore: unused_local_variable
    final dummy = <String>[];
  }

  /// تحسين استخدام البطارية
  void optimizeBatteryUsage() {
    if (_batteryLevel < 20 || _isLowPowerMode) {
      // تقليل جودة الصور
      setImageQuality('low');

      // تعطيل التحميل المسبق
      setPreloadingEnabled(false);

      // تقليل الرسوم المتحركة
      setAnimationsEnabled(false);
    }
  }

  /// تحسين استخدام البيانات
  void optimizeDataUsage() {
    if (_connectionStatus == ConnectivityResult.mobile) {
      // تقليل جودة الصور على اتصال البيانات الخلوية
      setImageQuality('low');

      // تعطيل التحميل المسبق
      setPreloadingEnabled(false);
    }
  }

  /// تحسين الأداء العام
  Future<void> optimizePerformance() async {
    // تحسين استخدام الذاكرة
    await optimizeMemoryUsage();

    // تحسين استخدام البطارية
    optimizeBatteryUsage();

    // تحسين استخدام البيانات
    optimizeDataUsage();
  }

  /// بدء مراقبة الأداء
  void startPerformanceMonitoring() {
    _performanceMonitor.start();
  }

  /// إيقاف مراقبة الأداء
  void stopPerformanceMonitoring() {
    _performanceMonitor.stop();
  }

  /// الحصول على تقرير الأداء
  Future<Map<String, dynamic>> getPerformanceReport() async {
    final deviceInfo = await getDeviceInfo();
    final appInfo = getAppInfo();
    final performanceMetrics = getPerformanceMetrics();
    final cacheSize = await getCacheSize();

    return {
      'device': deviceInfo,
      'app': appInfo,
      'performance': performanceMetrics,
      'cacheSize': cacheSize,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

/// مراقب الأداء
class PerformanceMonitor {
  double _currentFps = 0.0;
  double _currentMemoryUsage = 0.0;
  double _currentCpuUsage = 0.0;

  Timer? _monitorTimer;
  final Stopwatch _stopwatch = Stopwatch();
  int _frameCount = 0;

  /// تهيئة مراقب الأداء
  Future<void> init() async {
    // تهيئة مراقبة معدل الإطارات
    _stopwatch.start();

    // إضافة مستمع للإطارات
    WidgetsBinding.instance.addPostFrameCallback((_) => _onFrame());
  }

  /// تحرير الموارد
  void dispose() {
    _monitorTimer?.cancel();
    _stopwatch.stop();
  }

  /// بدء المراقبة
  void start() {
    _monitorTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      _updateMetrics();
    });
  }

  /// إيقاف المراقبة
  void stop() {
    _monitorTimer?.cancel();
    _monitorTimer = null;
  }

  /// معالجة الإطار
  void _onFrame() {
    _frameCount++;
    WidgetsBinding.instance.addPostFrameCallback((_) => _onFrame());
  }

  /// تحديث المقاييس
  void _updateMetrics() {
    // حساب معدل الإطارات
    final elapsedSeconds = _stopwatch.elapsedMilliseconds / 1000;
    if (elapsedSeconds > 0) {
      _currentFps = _frameCount / elapsedSeconds;
    }

    _frameCount = 0;
    _stopwatch.reset();
    _stopwatch.start();

    // محاولة الحصول على استخدام الذاكرة
    // ملاحظة: هذه طريقة تقريبية وليست دقيقة
    _updateMemoryUsage();

    // محاولة الحصول على استخدام المعالج
    // ملاحظة: هذه طريقة تقريبية وليست دقيقة
    _updateCpuUsage();
  }

  /// تحديث استخدام الذاكرة
  void _updateMemoryUsage() {
    // هذه طريقة تقريبية لتقدير استخدام الذاكرة
    // في التطبيقات الحقيقية، يمكن استخدام مكتبات خارجية أو منصات خاصة
    _currentMemoryUsage = 0.0;
  }

  /// تحديث استخدام المعالج
  void _updateCpuUsage() {
    // هذه طريقة تقريبية لتقدير استخدام المعالج
    // في التطبيقات الحقيقية، يمكن استخدام مكتبات خارجية أو منصات خاصة
    _currentCpuUsage = 0.0;
  }

  /// الحصول على معدل الإطارات الحالي
  double get currentFps => _currentFps;

  /// الحصول على استخدام الذاكرة الحالي
  double get currentMemoryUsage => _currentMemoryUsage;

  /// الحصول على استخدام المعالج الحالي
  double get currentCpuUsage => _currentCpuUsage;
}

/// امتدادات لتسهيل استخدام خدمة تحسين الأداء
extension PerformanceServiceExtensions on PerformanceService {
  /// الحصول على حجم الصورة المناسب بناءً على جودة الصور
  String getAppropriateImageSize() {
    final quality = getImageQuality();

    switch (quality) {
      case 'low':
        return 'small';
      case 'medium':
        return 'medium';
      case 'high':
        return 'large';
      default:
        return 'medium';
    }
  }

  /// الحصول على مدة التخزين المؤقت المناسبة بناءً على نوع الاتصال
  Duration getAppropriateCacheDuration() {
    switch (_connectionStatus) {
      case ConnectivityResult.wifi:
        return const Duration(hours: 24);
      case ConnectivityResult.mobile:
        return const Duration(hours: 48);
      default:
        return const Duration(days: 7);
    }
  }

  /// التحقق مما إذا كان يجب تحميل الصور عالية الدقة
  bool shouldLoadHighResolutionImages() {
    if (_isPerformanceMode) {
      return true;
    }

    if (_connectionStatus == ConnectivityResult.wifi && _batteryLevel > 30) {
      return true;
    }

    return false;
  }

  /// التحقق مما إذا كان يجب تحميل الجولات الافتراضية 360 درجة
  bool shouldLoad360Tours() {
    if (_isPerformanceMode) {
      return true;
    }

    if (_connectionStatus == ConnectivityResult.wifi && _batteryLevel > 50) {
      return true;
    }

    return false;
  }

  /// الحصول على عدد العناصر المناسب للتحميل المسبق
  int getAppropriatePreloadCount() {
    if (!isPreloadingEnabled()) {
      return 0;
    }

    if (_isPerformanceMode) {
      return 10;
    }

    if (_connectionStatus == ConnectivityResult.wifi) {
      return 5;
    }

    if (_connectionStatus == ConnectivityResult.mobile) {
      return 2;
    }

    return 0;
  }

  /// الحصول على مدة الرسوم المتحركة المناسبة
  Duration getAppropriateAnimationDuration() {
    if (!areAnimationsEnabled()) {
      return Duration.zero;
    }

    if (_isPerformanceMode) {
      return const Duration(milliseconds: 300);
    }

    if (_batteryLevel < 20 || _isLowPowerMode) {
      return const Duration(milliseconds: 100);
    }

    return const Duration(milliseconds: 200);
  }
}

/// امتدادات لتسهيل استخدام خدمة تحسين الأداء في واجهة المستخدم
extension PerformanceUIExtensions on PerformanceService {
  /// الحصول على مدة الرسوم المتحركة المناسبة لواجهة المستخدم
  Duration getAppropriateUIAnimationDuration() {
    if (!areAnimationsEnabled()) {
      return Duration.zero;
    }

    if (_isLowPowerMode || _batteryLevel < 20) {
      return const Duration(milliseconds: 100);
    }

    return const Duration(milliseconds: 300);
  }

  /// الحصول على منحنى الرسوم المتحركة المناسب لواجهة المستخدم
  Curve getAppropriateUIAnimationCurve() {
    if (!areAnimationsEnabled()) {
      return Curves.linear;
    }

    if (_isLowPowerMode || _batteryLevel < 20) {
      return Curves.easeIn;
    }

    return Curves.easeInOut;
  }

  /// الحصول على عدد الصور المناسب للعرض في القائمة
  int getAppropriateListImageCount() {
    if (_isPerformanceMode) {
      return 20;
    }

    if (_connectionStatus == ConnectivityResult.wifi) {
      return 15;
    }

    if (_connectionStatus == ConnectivityResult.mobile) {
      return 10;
    }

    return 5;
  }

  /// الحصول على جودة الصور المناسبة للعرض في القائمة
  String getAppropriateListImageQuality() {
    if (_isPerformanceMode) {
      return 'medium';
    }

    if (_connectionStatus == ConnectivityResult.wifi) {
      return 'medium';
    }

    return 'small';
  }

  /// الحصول على جودة الصور المناسبة للعرض في صفحة التفاصيل
  String getAppropriateDetailImageQuality() {
    if (_isPerformanceMode) {
      return 'large';
    }

    if (_connectionStatus == ConnectivityResult.wifi) {
      return 'large';
    }

    return 'medium';
  }
}
