{"database": {"rules": "database.rules.json"}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "hosting": {"public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/referral", "destination": "/referral.html"}, {"source": "/referral/**", "destination": "/referral.html"}, {"source": "/privacy-policy", "destination": "/privacy-policy.html"}, {"source": "/terms-of-service", "destination": "/terms-of-service.html"}, {"source": "/delete-account", "destination": "/delete-account.html"}]}, "functions": [{"source": "functions", "codebase": "default", "runtime": "nodejs18", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}], "emulators": {"functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "storage": {"port": 9199}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}}