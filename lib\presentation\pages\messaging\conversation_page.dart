// lib/presentation/pages/messaging/conversation_page.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';

import '../../../core/services/messaging_service.dart';
import '../../../core/services/notification_service.dart';
import '../../../core/theme/app_colors.dart';
import '../../../domain/models/messaging/conversation_model.dart';
import '../../../domain/models/messaging/message_model.dart';
import '../../../domain/models/notification_model.dart' as domain;
import '../../widgets/messaging/message_bubble.dart';
import '../../widgets/messaging/typing_indicator.dart';

/// صفحة المحادثة
class ConversationPage extends StatefulWidget {
  final String conversationId;
  final String title;

  const ConversationPage({
    super.key,
    required this.conversationId,
    required this.title,
  });

  @override
  State<ConversationPage> createState() => _ConversationPageState();
}

class _ConversationPageState extends State<ConversationPage> {
  final MessagingService _messagingService = MessagingService();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  List<Message> _messages = [];
  Conversation? _conversation;
  bool _isLoading = true;
  bool _isSending = false;
  final bool _isTyping = false;
  String? _currentUserId;

  @override
  void initState() {
    super.initState();
    _currentUserId = FirebaseAuth.instance.currentUser?.uid;
    _loadConversation();
    _listenToMessages();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// تحميل بيانات المحادثة
  Future<void> _loadConversation() async {
    try {
      final conversation = await _messagingService.getConversation(widget.conversationId);
      if (mounted) {
        setState(() {
          _conversation = conversation;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المحادثة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// الاستماع للرسائل الجديدة
  void _listenToMessages() {
    _messagingService.listenToMessages(widget.conversationId).listen(
      (messages) {
        if (mounted) {
          setState(() {
            _messages = messages;
          });
          _scrollToBottom();
        }
      },
      onError: (error) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في استقبال الرسائل: $error'),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
    );
  }

  /// إرسال رسالة نصية
  Future<void> _sendMessage() async {
    final content = _messageController.text.trim();
    if (content.isEmpty || _isSending) return;

    setState(() {
      _isSending = true;
      _messageController.clear();
    });

    try {
      await _messagingService.sendMessage(
        conversationId: widget.conversationId,
        content: content,
        type: MessageType.text,
      );

      // عرض إشعار فوري عند إرسال الرسالة بنجاح
      if (mounted) {
        NotificationService.showInAppNotification(
          context,
          title: 'تم إرسال الرسالة',
          body: 'تم إرسال رسالتك بنجاح',
          type: domain.NotificationType.newMessage,
          duration: const Duration(seconds: 2),
        );
      }

      _scrollToBottom();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إرسال الرسالة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isSending = false);
    }
  }

  /// إرسال صورة
  Future<void> _sendImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    
    if (pickedFile == null) return;

    setState(() => _isSending = true);

    try {
      await _messagingService.sendMessage(
        conversationId: widget.conversationId,
        content: 'صورة',
        type: MessageType.image,
        attachments: [File(pickedFile.path)],
      );
      _scrollToBottom();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إرسال الصورة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isSending = false);
    }
  }

  /// التمرير إلى أسفل
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.title,
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showConversationInfo,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // قائمة الرسائل
                Expanded(
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: _messages.length + (_isTyping ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (index == _messages.length && _isTyping) {
                        return const TypingIndicator();
                      }
                      
                      final message = _messages[index];
                      final isMe = message.senderId == _currentUserId;
                      
                      return MessageBubble(
                        message: message,
                        isMe: isMe,
                        showAvatar: !isMe,
                      );
                    },
                  ),
                ),
                
                // شريط إدخال الرسالة
                _buildMessageInput(),
              ],
            ),
    );
  }

  /// بناء شريط إدخال الرسالة
  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // زر الصورة
          IconButton(
            icon: const Icon(Icons.image, color: AppColors.primary),
            onPressed: _isSending ? null : _sendImage,
          ),
          
          // حقل النص
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'اكتب رسالة...',
                hintStyle: GoogleFonts.cairo(),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey.shade100,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 10,
                ),
              ),
              style: GoogleFonts.cairo(),
              maxLines: null,
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
              enabled: !_isSending,
            ),
          ),
          
          const SizedBox(width: 8),
          
          // زر الإرسال
          Container(
            decoration: const BoxDecoration(
              color: AppColors.primary,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: _isSending
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.send, color: Colors.white),
              onPressed: _isSending ? null : _sendMessage,
            ),
          ),
        ],
      ),
    );
  }

  /// عرض معلومات المحادثة
  void _showConversationInfo() {
    if (_conversation == null) return;

    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات المحادثة',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            if (_conversation!.propertyRequestTitle != null) ...[
              Text(
                'طلب العقار:',
                style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
              ),
              Text(
                _conversation!.propertyRequestTitle!,
                style: GoogleFonts.cairo(),
              ),
              const SizedBox(height: 12),
            ],
            
            Text(
              'المشاركون:',
              style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
            ),
            ...(_conversation!.participantNames ?? []).map(
              (name) => Padding(
                padding: const EdgeInsets.only(left: 16, top: 4),
                child: Text('• $name', style: GoogleFonts.cairo()),
              ),
            ),
            
            const SizedBox(height: 16),
            Text(
              'تاريخ الإنشاء: ${DateFormat('dd/MM/yyyy HH:mm').format(_conversation!.createdAt)}',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
