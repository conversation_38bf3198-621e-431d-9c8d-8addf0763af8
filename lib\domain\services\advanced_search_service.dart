import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:math' as math;

import '../entities/estate.dart';

class AdvancedSearchService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// البحث في العقارات بناءً على المعايير المحددة
  Future<List<Estate>> searchProperties(Map<String, dynamic> criteria) async {
    try {
      print('🔍 بدء البحث بالمعايير: $criteria');

      // جلب جميع العقارات المتاحة (بدون فلاتر معقدة على مستوى قاعدة البيانات)
      Query query = _firestore.collection('estates');

      // فلترة العقارات المتاحة فقط (فلتر أساسي)
      if (criteria['availableOnly'] != false) {
        query = query.where('isAvailable', isEqualTo: true);
      }

      // فلترة الإعلانات المدفوعة فقط إذا طُلب ذلك
      if (criteria['paidAdsOnly'] == true) {
        query = query.where('isPaymentVerified', isEqualTo: true);
      }

      // ترتيب حسب تاريخ الإنشاء
      query = query.orderBy('createdAt', descending: true);

      // تحديد عدد النتائج
      query = query.limit(2000); // زيادة الحد للحصول على نتائج أكثر

      final snapshot = await query.get();
      print('📊 تم جلب ${snapshot.docs.length} عقار من قاعدة البيانات');

      List<Estate> results = snapshot.docs
          .map((doc) => _mapToEstate(doc.data() as Map<String, dynamic>, doc.id))
          .toList();

      print('📊 عدد العقارات قبل الفلترة: ${results.length}');

      // طباعة عينة من البيانات الفعلية للتشخيص
      if (results.isNotEmpty) {
        print('📋 عينة من البيانات الفعلية:');
        for (int i = 0; i < results.length && i < 3; i++) {
          final estate = results[i];
          print('  العقار ${i + 1}:');
          print('    title: ${estate.title}');
          print('    mainCategory: "${estate.mainCategory}"');
          print('    subCategory: "${estate.subCategory}"');
          print('    propertyType: "${estate.propertyType}"');
          print('    usageType: "${estate.usageType}"');
          print('    location: "${estate.location}"');
        }
      }

      // تطبيق جميع الفلاتر في الذاكرة
      results = _applyAllFilters(results, criteria);
      print('✅ تم تطبيق الفلاتر، النتائج النهائية: ${results.length}');

      // ترتيب النتائج
      results = _sortResults(results, criteria);

      // حفظ البحث في التاريخ
      await _saveSearchHistory(criteria, results.length);

      return results;
    } catch (e) {
      print('❌ خطأ في البحث: $e');
      throw Exception('فشل في البحث عن العقارات');
    }
  }

  /// تطبيق جميع الفلاتر في الذاكرة - نسخة محسنة وموحدة
  List<Estate> _applyAllFilters(List<Estate> estates, Map<String, dynamic> criteria) {
    List<Estate> filtered = List.from(estates);

    print('🔍 تطبيق فلاتر في الذاكرة على ${filtered.length} عقار');
    print('📋 المعايير الأصلية: $criteria');

    // توحيد المعايير أولاً
    final unifiedCriteria = _unifyCriteria(criteria);
    print('📋 المعايير الموحدة: $unifiedCriteria');

    // فلتر نوع الاستغلال - استخدام المعايير الموحدة
    if (unifiedCriteria['usageType'] != null && unifiedCriteria['usageType'].isNotEmpty) {
      final usageTypeSearch = unifiedCriteria['usageType'].toString();
      filtered = filtered.where((estate) {
        return _matchesUsageType(estate, usageTypeSearch);
      }).toList();
      print('✅ فلتر نوع الاستغلال ($usageTypeSearch): ${filtered.length} عقار');
    }

    // فلتر نوع العقار - استخدام المعايير الموحدة
    if (unifiedCriteria['propertyType'] != null && unifiedCriteria['propertyType'].isNotEmpty) {
      final propertyTypeSearch = unifiedCriteria['propertyType'].toString();
      filtered = filtered.where((estate) {
        return _matchesPropertyType(estate, propertyTypeSearch);
      }).toList();
      print('✅ فلتر نوع العقار ($propertyTypeSearch): ${filtered.length} عقار');
    }

    // فلتر البحث النصي - استخدام المعايير الموحدة
    if (unifiedCriteria['keyword'] != null && unifiedCriteria['keyword'].isNotEmpty) {
      final keyword = unifiedCriteria['keyword'].toString().toLowerCase();
      filtered = filtered.where((estate) {
        return estate.title.toLowerCase().contains(keyword) ||
               estate.description.toLowerCase().contains(keyword) ||
               estate.location.toLowerCase().contains(keyword);
      }).toList();
      print('✅ فلتر الكلمة المفتاحية ($keyword): ${filtered.length} عقار');
    }

    if (unifiedCriteria['query'] != null && unifiedCriteria['query'].isNotEmpty) {
      final query = unifiedCriteria['query'].toString().toLowerCase();
      filtered = filtered.where((estate) {
        return estate.title.toLowerCase().contains(query) ||
               estate.description.toLowerCase().contains(query) ||
               estate.location.toLowerCase().contains(query);
      }).toList();
      print('✅ فلتر البحث النصي ($query): ${filtered.length} عقار');
    }

    // فلتر الموقع - استخدام المعايير الموحدة
    if (unifiedCriteria['location'] != null && unifiedCriteria['location'].isNotEmpty) {
      final location = unifiedCriteria['location'].toString().toLowerCase();
      filtered = filtered.where((estate) {
        return estate.location.toLowerCase().contains(location) ||
               estate.governorate?.toLowerCase().contains(location) == true;
      }).toList();
      print('✅ فلتر الموقع ($location): ${filtered.length} عقار');
    }

    // فلتر نطاق السعر - استخدام المعايير الموحدة
    if (unifiedCriteria['minPrice'] != null && unifiedCriteria['minPrice'] > 0) {
      final minPrice = unifiedCriteria['minPrice'];
      filtered = filtered.where((estate) => estate.price >= minPrice).toList();
      print('✅ فلتر السعر الأدنى ($minPrice): ${filtered.length} عقار');
    }
    if (unifiedCriteria['maxPrice'] != null && unifiedCriteria['maxPrice'] < 1000000) {
      final maxPrice = unifiedCriteria['maxPrice'];
      filtered = filtered.where((estate) => estate.price <= maxPrice).toList();
      print('✅ فلتر السعر الأقصى ($maxPrice): ${filtered.length} عقار');
    }

    // فلتر نطاق المساحة - استخدام المعايير الموحدة
    if (unifiedCriteria['minArea'] != null && unifiedCriteria['minArea'] > 0) {
      final minArea = unifiedCriteria['minArea'];
      filtered = filtered.where((estate) =>
        estate.area != null && estate.area! >= minArea).toList();
      print('✅ فلتر المساحة الدنيا ($minArea): ${filtered.length} عقار');
    }
    if (unifiedCriteria['maxArea'] != null && unifiedCriteria['maxArea'] < 10000) {
      final maxArea = unifiedCriteria['maxArea'];
      filtered = filtered.where((estate) =>
        estate.area != null && estate.area! <= maxArea).toList();
      print('✅ فلتر المساحة القصوى ($maxArea): ${filtered.length} عقار');
    }

    // فلتر عدد الغرف - استخدام المعايير الموحدة
    if (unifiedCriteria['rooms'] != null && unifiedCriteria['rooms'] > 0) {
      final roomsCount = unifiedCriteria['rooms'];
      filtered = filtered.where((estate) =>
        estate.numberOfRooms == roomsCount || estate.rooms == roomsCount).toList();
      print('✅ فلتر عدد الغرف ($roomsCount): ${filtered.length} عقار');
    }

    // فلتر عدد الحمامات - استخدام المعايير الموحدة
    if (unifiedCriteria['bathrooms'] != null && unifiedCriteria['bathrooms'] > 0) {
      final bathroomsCount = unifiedCriteria['bathrooms'];
      filtered = filtered.where((estate) =>
        estate.numberOfBathrooms == bathroomsCount || estate.bathrooms == bathroomsCount).toList();
      print('✅ فلتر عدد الحمامات ($bathroomsCount): ${filtered.length} عقار');
    }

    // فلتر نوع العقار (subCategory) - البحث الذكي
    if (criteria['subCategory'] != null && criteria['subCategory'].isNotEmpty) {
      final subCategorySearch = criteria['subCategory'].toString();
      filtered = filtered.where((estate) {
        if (estate.subCategory == null) return false;

        // البحث المباشر
        if (estate.subCategory == subCategorySearch) return true;

        // البحث بالاحتواء
        if (estate.subCategory!.contains(subCategorySearch)) return true;

        // البحث الذكي للأنماط المختلفة
        final subCategory = estate.subCategory!.toLowerCase();
        final searchTerm = subCategorySearch.toLowerCase();

        return subCategory.contains(searchTerm) ||
               subCategory.contains('$searchTerm للبيع') ||
               subCategory.contains('$searchTerm للإيجار') ||
               subCategory.contains('$searchTerm للايجار') ||
               subCategory.contains('$searchTerm للبدل');
      }).toList();
      print('✅ فلتر نوع العقار (${criteria['subCategory']}): ${filtered.length} عقار');
    }

    // فلتر propertyType (للتوافق)
    if (criteria['propertyType'] != null && criteria['propertyType'].isNotEmpty) {
      final propertyTypeSearch = criteria['propertyType'].toString();
      filtered = filtered.where((estate) {
        return estate.propertyType == propertyTypeSearch ||
               estate.subCategory?.contains(propertyTypeSearch) == true;
      }).toList();
      print('✅ فلتر propertyType (${criteria['propertyType']}): ${filtered.length} عقار');
    }

    // فلتر المميزات والمرافق - استخدام المعايير الموحدة
    final amenityFilters = {
      'hasCentralAC': 'تكييف مركزي',
      'hasElevator': 'مصعد',
      'hasSwimmingPool': 'مسبح',
      'hasGarage': 'مرآب',
      'hasMaidRoom': 'غرفة خادمة',
      'isFullyFurnished': 'مفروش',
      'hasGarden': 'حديقة',
      'hasPool': 'مسبح',
      'hasBalcony': 'شرفة',
    };

    for (final entry in amenityFilters.entries) {
      if (unifiedCriteria[entry.key] == true) {
        filtered = filtered.where((estate) {
          switch (entry.key) {
            case 'hasCentralAC':
              return estate.hasCentralAC == true;
            case 'hasElevator':
              return estate.hasElevator == true;
            case 'hasSwimmingPool':
              return estate.hasSwimmingPool == true;
            case 'hasGarage':
              return estate.hasGarage == true;
            case 'hasMaidRoom':
              return estate.hasMaidRoom == true;
            case 'isFullyFurnished':
              return estate.isFullyFurnished == true;
            case 'hasGarden':
              return estate.hasGarden == true;
            case 'hasPool':
              return estate.hasPool == true;
            case 'hasBalcony':
              return estate.hasBalcony == true;
            default:
              return true;
          }
        }).toList();
        print('✅ فلتر ${entry.value}: ${filtered.length} عقار');
      }
    }

    // فلتر الجولات الافتراضية والمحتوى الإضافي (سيتم تطبيقها لاحقاً)
    // TODO: إضافة فلاتر الجولات الافتراضية والفيديو والمخططات عند إضافة الخصائص للكلاس

    // فلتر البحث الجغرافي
    if (criteria['latitude'] != null && criteria['longitude'] != null && criteria['radius'] != null) {
      filtered = _applyGeographicFilter(filtered, criteria);
    }

    return filtered;
  }

  /// تطبيق فلتر البحث الجغرافي
  List<Estate> _applyGeographicFilter(List<Estate> estates, Map<String, dynamic> criteria) {
    final centerLat = criteria['latitude'] as double;
    final centerLng = criteria['longitude'] as double;
    final radiusKm = criteria['radius'] as double;

    return estates.where((estate) {
      if (estate.latitude == null || estate.longitude == null) return false;

      final distance = _calculateDistance(
        centerLat, centerLng,
        estate.latitude!, estate.longitude!
      );

      return distance <= radiusKm;
    }).toList();
  }

  /// حساب المسافة بين نقطتين جغرافيتين (بالكيلومتر)
  double _calculateDistance(double lat1, double lng1, double lat2, double lng2) {
    const double earthRadius = 6371; // نصف قطر الأرض بالكيلومتر

    final dLat = _degreesToRadians(lat2 - lat1);
    final dLng = _degreesToRadians(lng2 - lng1);

    final a = math.sin(dLat / 2) * math.sin(dLat / 2) +
              math.cos(_degreesToRadians(lat1)) * math.cos(_degreesToRadians(lat2)) *
              math.sin(dLng / 2) * math.sin(dLng / 2);

    final c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));

    return earthRadius * c;
  }

  /// تحويل الدرجات إلى راديان
  double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }

  /// توحيد المعايير - حل احترافي لمشكلة تعدد أسماء الحقول
  Map<String, dynamic> _unifyCriteria(Map<String, dynamic> originalCriteria) {
    final unified = <String, dynamic>{};

    print('🔧 بدء توحيد المعايير...');

    // 1. توحيد نوع الاستغلال (mainCategory/usageType/purpose)
    String? unifiedUsageType;
    if (originalCriteria['mainCategory'] != null) {
      unifiedUsageType = originalCriteria['mainCategory'].toString();
    } else if (originalCriteria['usageType'] != null) {
      unifiedUsageType = originalCriteria['usageType'].toString();
    } else if (originalCriteria['purpose'] != null) {
      unifiedUsageType = originalCriteria['purpose'].toString();
    }

    if (unifiedUsageType != null && unifiedUsageType.isNotEmpty) {
      unified['usageType'] = unifiedUsageType;
      print('✅ توحيد نوع الاستغلال: $unifiedUsageType');
    }

    // 2. توحيد نوع العقار (subCategory/propertyType)
    String? unifiedPropertyType;
    if (originalCriteria['subCategory'] != null) {
      unifiedPropertyType = originalCriteria['subCategory'].toString();
    } else if (originalCriteria['propertyType'] != null) {
      unifiedPropertyType = originalCriteria['propertyType'].toString();
    }

    if (unifiedPropertyType != null && unifiedPropertyType.isNotEmpty) {
      unified['propertyType'] = unifiedPropertyType;
      print('✅ توحيد نوع العقار: $unifiedPropertyType');
    }

    // 3. توحيد السعر (priceMin/minPrice, priceMax/maxPrice)
    double? unifiedMinPrice;
    if (originalCriteria['priceMin'] != null) {
      unifiedMinPrice = _toDouble(originalCriteria['priceMin']);
    } else if (originalCriteria['minPrice'] != null) {
      unifiedMinPrice = _toDouble(originalCriteria['minPrice']);
    }

    double? unifiedMaxPrice;
    if (originalCriteria['priceMax'] != null) {
      unifiedMaxPrice = _toDouble(originalCriteria['priceMax']);
    } else if (originalCriteria['maxPrice'] != null) {
      unifiedMaxPrice = _toDouble(originalCriteria['maxPrice']);
    }

    if (unifiedMinPrice != null && unifiedMinPrice > 0) {
      unified['minPrice'] = unifiedMinPrice;
      print('✅ توحيد السعر الأدنى: $unifiedMinPrice');
    }
    if (unifiedMaxPrice != null && unifiedMaxPrice < 1000000) {
      unified['maxPrice'] = unifiedMaxPrice;
      print('✅ توحيد السعر الأقصى: $unifiedMaxPrice');
    }

    // 4. توحيد المساحة (areaMin/minArea, areaMax/maxArea)
    double? unifiedMinArea;
    if (originalCriteria['areaMin'] != null) {
      unifiedMinArea = _toDouble(originalCriteria['areaMin']);
    } else if (originalCriteria['minArea'] != null) {
      unifiedMinArea = _toDouble(originalCriteria['minArea']);
    }

    double? unifiedMaxArea;
    if (originalCriteria['areaMax'] != null) {
      unifiedMaxArea = _toDouble(originalCriteria['areaMax']);
    } else if (originalCriteria['maxArea'] != null) {
      unifiedMaxArea = _toDouble(originalCriteria['maxArea']);
    }

    if (unifiedMinArea != null && unifiedMinArea > 0) {
      unified['minArea'] = unifiedMinArea;
      print('✅ توحيد المساحة الدنيا: $unifiedMinArea');
    }
    if (unifiedMaxArea != null && unifiedMaxArea < 10000) {
      unified['maxArea'] = unifiedMaxArea;
      print('✅ توحيد المساحة القصوى: $unifiedMaxArea');
    }

    // 5. توحيد عدد الغرف (rooms/numberOfRooms)
    int? unifiedRooms;
    if (originalCriteria['rooms'] != null) {
      unifiedRooms = _toInt(originalCriteria['rooms']);
    } else if (originalCriteria['numberOfRooms'] != null) {
      unifiedRooms = _toInt(originalCriteria['numberOfRooms']);
    }

    if (unifiedRooms != null && unifiedRooms > 0) {
      unified['rooms'] = unifiedRooms;
      print('✅ توحيد عدد الغرف: $unifiedRooms');
    }

    // 6. توحيد عدد الحمامات (bathrooms/numberOfBathrooms)
    int? unifiedBathrooms;
    if (originalCriteria['bathrooms'] != null) {
      unifiedBathrooms = _toInt(originalCriteria['bathrooms']);
    } else if (originalCriteria['numberOfBathrooms'] != null) {
      unifiedBathrooms = _toInt(originalCriteria['numberOfBathrooms']);
    }

    if (unifiedBathrooms != null && unifiedBathrooms > 0) {
      unified['bathrooms'] = unifiedBathrooms;
      print('✅ توحيد عدد الحمامات: $unifiedBathrooms');
    }

    // 7. توحيد الموقع (location/governorate/area)
    String? unifiedLocation;
    if (originalCriteria['location'] != null) {
      unifiedLocation = originalCriteria['location'].toString();
    } else if (originalCriteria['governorate'] != null) {
      unifiedLocation = originalCriteria['governorate'].toString();
    } else if (originalCriteria['area'] != null) {
      unifiedLocation = originalCriteria['area'].toString();
    }

    if (unifiedLocation != null && unifiedLocation.isNotEmpty) {
      unified['location'] = unifiedLocation;
      print('✅ توحيد الموقع: $unifiedLocation');
    }

    // 8. نسخ باقي المعايير كما هي
    final directCopyFields = [
      'keyword', 'query', 'floorNumber', 'buildingAge', 'availableOnly', 'paidAdsOnly',
      'hasCentralAC', 'hasElevator', 'hasSwimmingPool', 'hasGarage', 'hasMaidRoom',
      'isFullyFurnished', 'hasGarden', 'hasPool', 'hasBalcony', 'hasDriverRoom',
      'hasPrivateEntrance', 'hasEquippedKitchen', 'sortBy', 'descending'
    ];

    for (final field in directCopyFields) {
      if (originalCriteria[field] != null) {
        unified[field] = originalCriteria[field];
      }
    }

    print('✅ تم توحيد المعايير بنجاح. عدد المعايير الموحدة: ${unified.length}');
    return unified;
  }

  /// تحويل قيمة إلى double بأمان
  double? _toDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  /// تحويل قيمة إلى int بأمان
  int? _toInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) return int.tryParse(value);
    return null;
  }

  /// ترتيب النتائج
  List<Estate> _sortResults(List<Estate> estates, Map<String, dynamic> criteria) {
    final sortBy = criteria['sortBy'] ?? 'createdAt';
    final descending = criteria['descending'] ?? true;

    estates.sort((a, b) {
      int comparison = 0;

      switch (sortBy) {
        case 'price':
          comparison = a.price.compareTo(b.price);
          break;
        case 'area':
          final aArea = a.area ?? 0;
          final bArea = b.area ?? 0;
          comparison = aArea.compareTo(bArea);
          break;
        case 'createdAt':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case 'viewsCount':
          final aViews = a.viewsCount ?? 0;
          final bViews = b.viewsCount ?? 0;
          comparison = aViews.compareTo(bViews);
          break;
        default:
          comparison = a.createdAt.compareTo(b.createdAt);
      }

      return descending ? -comparison : comparison;
    });

    return estates;
  }

  /// حفظ تاريخ البحث
  Future<void> _saveSearchHistory(Map<String, dynamic> criteria, int resultsCount) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      await _firestore.collection('searchHistory').add({
        'userId': user.uid,
        'criteria': criteria,
        'resultsCount': resultsCount,
        'timestamp': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('خطأ في حفظ تاريخ البحث: $e');
    }
  }

  /// تطبيق فلاتر قاعدة البيانات
  Query _applyFilters(Query query, Map<String, dynamic> criteria) {
    print('🔍 تطبيق فلاتر قاعدة البيانات: $criteria'); // للتتبع

    // فلتر نوع الاستغلال - التعامل مع القيم العربية مباشرة
    if (criteria['mainCategory'] != null && criteria['mainCategory'].isNotEmpty) {
      query = query.where('mainCategory', isEqualTo: criteria['mainCategory']);
      print('✅ تطبيق فلتر نوع الاستغلال (mainCategory): ${criteria['mainCategory']}');
    }

    // فلتر نوع الاستغلال (البحث في حقول متعددة للتوافق مع القيم الإنجليزية)
    if (criteria['usageType'] != null && criteria['usageType'].isNotEmpty) {
      // تحويل القيم الإنجليزية إلى العربية للبحث في mainCategory
      String searchValue = criteria['usageType'];
      switch (criteria['usageType']) {
        case 'sale':
          searchValue = 'عقار للبيع';
          break;
        case 'rent':
          searchValue = 'عقار للايجار';
          break;
        case 'swap':
          searchValue = 'عقار للبدل';
          break;
        case 'international':
          searchValue = 'عقار دولي';
          break;
        case 'commercial':
          searchValue = 'تجاري';
          break;
      }

      // البحث في حقل mainCategory (حيث يتم حفظ أنواع الاستغلال باللغة العربية)
      query = query.where('mainCategory', isEqualTo: searchValue);
      print('✅ تطبيق فلتر نوع الاستغلال (usageType -> mainCategory): $searchValue');
    }

    // تم إزالة فلتر نوع العقار من مستوى قاعدة البيانات
    // سيتم تطبيقه على مستوى التطبيق للحصول على مرونة أكبر في البحث

    // تم دمج فلتر الفئة الرئيسية مع فلتر mainCategory أعلاه لتجنب التكرار

    // تم إزالة فلتر الفئة الفرعية من مستوى قاعدة البيانات
    // سيتم تطبيقه على مستوى التطبيق للحصول على مرونة أكبر في البحث

    // فلتر السعر
    if (criteria['priceMin'] != null && criteria['priceMin'] > 0) {
      query = query.where('price', isGreaterThanOrEqualTo: criteria['priceMin']);
      print('✅ تطبيق فلتر السعر الأدنى: ${criteria['priceMin']}');
    }
    if (criteria['priceMax'] != null && criteria['priceMax'] < 1000000) {
      query = query.where('price', isLessThanOrEqualTo: criteria['priceMax']);
      print('✅ تطبيق فلتر السعر الأقصى: ${criteria['priceMax']}');
    }

    // فلتر المساحة
    if (criteria['areaMin'] != null && criteria['areaMin'] > 0) {
      query = query.where('area', isGreaterThanOrEqualTo: criteria['areaMin']);
      print('✅ تطبيق فلتر المساحة الدنيا: ${criteria['areaMin']}');
    }
    if (criteria['areaMax'] != null && criteria['areaMax'] < 1000) {
      query = query.where('area', isLessThanOrEqualTo: criteria['areaMax']);
      print('✅ تطبيق فلتر المساحة القصوى: ${criteria['areaMax']}');
    }

    // فلتر عدد الغرف (البحث في حقل numberOfRooms أو rooms)
    if (criteria['rooms'] != null) {
      query = query.where('numberOfRooms', isEqualTo: criteria['rooms']);
      print('✅ تطبيق فلتر عدد الغرف (numberOfRooms): ${criteria['rooms']}');
    }

    // فلتر عدد الحمامات (البحث في حقل numberOfBathrooms أو bathrooms)
    if (criteria['bathrooms'] != null) {
      query = query.where('numberOfBathrooms', isEqualTo: criteria['bathrooms']);
      print('✅ تطبيق فلتر عدد الحمامات (numberOfBathrooms): ${criteria['bathrooms']}');
    }

    // فلتر الفئة (للتوافق مع الإصدارات القديمة)
    if (criteria['category'] != null && criteria['category'].isNotEmpty) {
      query = query.where('mainCategory', isEqualTo: criteria['category']);
      print('✅ تطبيق فلتر الفئة: ${criteria['category']}');
    }

    return query;
  }



  /// فحص تطابق نوع الاستغلال مع المعايير المحددة
  bool _matchesUsageType(Estate estate, String searchType) {
    // البحث في mainCategory
    if (estate.mainCategory != null) {
      if (estate.mainCategory == searchType) return true;
      if (estate.mainCategory!.toLowerCase().contains(searchType.toLowerCase())) return true;
    }

    // البحث في usageType
    if (estate.usageType != null) {
      if (estate.usageType == searchType) return true;
      if (estate.usageType!.toLowerCase().contains(searchType.toLowerCase())) return true;
    }

    // البحث في purpose
    if (estate.purpose != null) {
      if (estate.purpose == searchType) return true;
      if (estate.purpose!.toLowerCase().contains(searchType.toLowerCase())) return true;
    }

    // خريطة تحويل أنواع الاستغلال
    final usageTypeMap = {
      'عقار للبيع': ['sale', 'بيع', 'للبيع', 'للبيع'],
      'عقار للايجار': ['rent', 'إيجار', 'للايجار', 'للإيجار', 'ايجار'],
      'عقار للبدل': ['swap', 'بدل', 'للبدل', 'مبادلة'],
      'عقار دولي': ['international', 'دولي'],
      'تجاري': ['commercial', 'تجاري'],
    };

    // البحث باستخدام خريطة التحويل
    final searchLower = searchType.toLowerCase();
    for (final entry in usageTypeMap.entries) {
      if (searchLower.contains(entry.key.toLowerCase()) ||
          entry.key.toLowerCase().contains(searchLower)) {
        for (final variant in entry.value) {
          if (estate.mainCategory?.toLowerCase().contains(variant.toLowerCase()) == true ||
              estate.usageType?.toLowerCase().contains(variant.toLowerCase()) == true ||
              estate.purpose?.toLowerCase().contains(variant.toLowerCase()) == true) {
            return true;
          }
        }
      }
    }

    return false;
  }

  /// فحص تطابق نوع العقار مع المعايير المحددة - حل شامل لمشكلة عدم التطابق
  bool _matchesPropertyType(Estate property, String searchType) {
    print('🔍 فحص تطابق نوع العقار:');
    print('  البحث عن: $searchType');
    print('  subCategory في العقار: ${property.subCategory}');
    print('  propertyType في العقار: ${property.propertyType}');

    // البحث المباشر في propertyType
    if (property.propertyType != null && property.propertyType == searchType) {
      print('  ✅ تطابق مباشر في propertyType');
      return true;
    }

    // البحث المباشر في subCategory
    if (property.subCategory != null && property.subCategory == searchType) {
      print('  ✅ تطابق مباشر في subCategory');
      return true;
    }

    // البحث بالاحتواء في subCategory (للتعامل مع "شقة للبيع" vs "شقة")
    if (property.subCategory != null && property.subCategory!.contains(searchType)) {
      print('  ✅ تطابق بالاحتواء في subCategory');
      return true;
    }

    // خريطة تحويل شاملة للتعامل مع جميع الأشكال المختلفة
    final propertyTypeMap = {
      'شقة': [
        'شقة', 'شقة للبيع', 'شقة للايجار', 'شقة للبدل', 'شقة للإيجار',
        'apartment', 'شقق', 'شقة سكنية'
      ],
      'منزل': [
        'منزل', 'بيت', 'بيت للبيع', 'بيت للايجار', 'بيت للبدل', 'بيت للإيجار',
        'house', 'فيلا', 'منازل', 'بيوت', 'villa'
      ],
      'بيت': [
        'بيت', 'منزل', 'بيت للبيع', 'بيت للايجار', 'بيت للبدل', 'بيت للإيجار',
        'house', 'فيلا', 'منازل', 'بيوت', 'villa'
      ],
      'أرض': [
        'أرض', 'اراضي', 'أراضي', 'اراضي للبيع', 'أراضي للبيع', 'اراضي للايجار',
        'land', 'قطعة أرض', 'قطع أراضي'
      ],
      'مكتب': [
        'مكتب', 'مكتب للبيع', 'مكتب للايجار', 'مكتب للإيجار',
        'office', 'مكاتب', 'مكتب إداري'
      ],
      'محل تجاري': [
        'محل تجاري', 'محل', 'محل للبيع', 'محل للايجار', 'محل للإيجار',
        'shop', 'store', 'محلات', 'محل تجاري'
      ],
      'محل': [
        'محل', 'محل تجاري', 'محل للبيع', 'محل للايجار', 'محل للإيجار',
        'shop', 'store', 'محلات'
      ],
      'مخزن': [
        'مخزن', 'مخزن للبيع', 'مخزن للايجار', 'مخزن للإيجار',
        'warehouse', 'مخازن', 'مستودع'
      ],
      'مزرعة': [
        'مزرعة', 'مزرعة للبيع', 'مزرعة للايجار', 'مزرعة للإيجار',
        'farm', 'مزارع', 'مزرعة زراعية'
      ],
      'عمارة': [
        'عمارة', 'عمارة للبيع', 'عمارة للايجار', 'عمارة للإيجار',
        'building', 'عمارات', 'مبنى', 'ادوار'
      ]
    };

    // البحث باستخدام خريطة التحويل
    final searchLower = searchType.toLowerCase();
    for (final entry in propertyTypeMap.entries) {
      final key = entry.key.toLowerCase();
      final variants = entry.value;

      // إذا كان البحث يطابق المفتاح أو يحتويه
      if (searchLower == key || searchLower.contains(key) || key.contains(searchLower)) {
        for (final variant in variants) {
          final variantLower = variant.toLowerCase();

          // فحص subCategory
          if (property.subCategory != null &&
              (property.subCategory!.toLowerCase() == variantLower ||
               property.subCategory!.toLowerCase().contains(variantLower) ||
               variantLower.contains(property.subCategory!.toLowerCase()))) {
            print('  ✅ تطابق عبر خريطة التحويل في subCategory: ${property.subCategory} ↔ $variant');
            return true;
          }

          // فحص propertyType
          if (property.propertyType != null &&
              (property.propertyType!.toLowerCase() == variantLower ||
               property.propertyType!.toLowerCase().contains(variantLower) ||
               variantLower.contains(property.propertyType!.toLowerCase()))) {
            print('  ✅ تطابق عبر خريطة التحويل في propertyType: ${property.propertyType} ↔ $variant');
            return true;
          }
        }
      }
    }

    // البحث العكسي - إذا كان العقار يحتوي على نوع مركب والبحث عن نوع بسيط
    if (property.subCategory != null) {
      final subCategoryLower = property.subCategory!.toLowerCase();
      for (final entry in propertyTypeMap.entries) {
        final variants = entry.value;
        for (final variant in variants) {
          if (subCategoryLower.contains(variant.toLowerCase()) &&
              entry.key.toLowerCase() == searchLower) {
            print('  ✅ تطابق عكسي: ${property.subCategory} يحتوي على $variant والبحث عن ${entry.key}');
            return true;
          }
        }
      }
    }

    print('  ❌ لا يوجد تطابق');
    return false;
  }

  /// تطبيق فلاتر إضافية
  List<Estate> _applyAdditionalFilters(List<Estate> properties, Map<String, dynamic> criteria) {
    List<Estate> filtered = properties;

    // البحث النصي
    if (criteria['keyword'] != null && criteria['keyword'].isNotEmpty) {
      final keyword = criteria['keyword'].toString().toLowerCase();
      filtered = filtered.where((property) {
        return property.title.toLowerCase().contains(keyword) ||
               property.description.toLowerCase().contains(keyword) ||
               property.location.toLowerCase().contains(keyword);
      }).toList();
    }

    // فلتر الموقع النصي
    if (criteria['location'] != null && criteria['location'].isNotEmpty) {
      final location = criteria['location'].toString().toLowerCase();
      filtered = filtered.where((property) {
        return property.location.toLowerCase().contains(location);
      }).toList();
    }

    // فلتر المرافق
    if (criteria['amenities'] != null && criteria['amenities'].isNotEmpty) {
      final amenities = criteria['amenities'] as List<String>;
      filtered = filtered.where((property) {
        return _hasAmenities(property, amenities);
      }).toList();
    }

    // فلتر البحث الجغرافي
    if (criteria['searchLocation'] != null && criteria['searchRadius'] != null) {
      final searchLocation = criteria['searchLocation'] as LatLng;
      final searchRadius = criteria['searchRadius'] as double;

      filtered = filtered.where((property) {
        if (property.latitude != null && property.longitude != null) {
          final distance = _calculateDistance(
            searchLocation.latitude,
            searchLocation.longitude,
            property.latitude!,
            property.longitude!);
          return distance <= searchRadius;
        }
        return false;
      }).toList();
    }

    return filtered;
  }

  /// التحقق من وجود المرافق في العقار
  bool _hasAmenities(Estate property, List<String> amenities) {
    for (final amenity in amenities) {
      switch (amenity) {
        case 'تكييف مركزي':
          if (!property.hasCentralAC) return false;
          break;
        case 'مصعد':
          if (!(property.hasElevator ?? false)) return false;
          break;
        case 'مرآب':
          if (!property.hasGarage) return false;
          break;
        case 'غرفة خادمة':
          if (!property.hasMaidRoom) return false;
          break;
        case 'مفروش':
          if (!(property.isFullyFurnished ?? false)) return false;
          break;
        // يمكن إضافة المزيد من المرافق هنا
      }
    }
    return true;
  }





  /// الحصول على تاريخ البحث
  Future<List<Map<String, dynamic>>> getSearchHistory() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('searchHistory')
          .orderBy('searchedAt', descending: true)
          .limit(20)
          .get();

      return snapshot.docs.map((doc) => doc.data()).toList();
    } catch (e) {
      print('Error getting search history: $e');
      return [];
    }
  }

  /// حفظ البحث المفضل
  Future<bool> saveFavoriteSearch(String name, Map<String, dynamic> criteria) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final favoriteSearch = {
        'name': name,
        'criteria': criteria,
        'savedAt': FieldValue.serverTimestamp(),
        'userId': user.uid,
      };

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('savedSearches')
          .add(favoriteSearch);

      return true;
    } catch (e) {
      print('Error saving favorite search: $e');
      return false;
    }
  }

  /// الحصول على البحثات المحفوظة
  Future<List<Map<String, dynamic>>> getSavedSearches() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('savedSearches')
          .orderBy('savedAt', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('Error getting saved searches: $e');
      return [];
    }
  }

  /// حذف بحث محفوظ
  Future<bool> deleteSavedSearch(String searchId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('savedSearches')
          .doc(searchId)
          .delete();

      return true;
    } catch (e) {
      print('Error deleting saved search: $e');
      return false;
    }
  }

  /// إنشاء تنبيه بحث
  Future<bool> createSearchAlert(String name, Map<String, dynamic> criteria) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final searchAlert = {
        'name': name,
        'criteria': criteria,
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
        'lastChecked': FieldValue.serverTimestamp(),
        'userId': user.uid,
      };

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('searchAlerts')
          .add(searchAlert);

      return true;
    } catch (e) {
      print('Error creating search alert: $e');
      return false;
    }
  }

  /// الحصول على تنبيهات البحث
  Future<List<Map<String, dynamic>>> getSearchAlerts() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('searchAlerts')
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('Error getting search alerts: $e');
      return [];
    }
  }

  /// تعطيل تنبيه البحث
  Future<bool> disableSearchAlert(String alertId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('searchAlerts')
          .doc(alertId)
          .update({'isActive': false});

      return true;
    } catch (e) {
      print('Error disabling search alert: $e');
      return false;
    }
  }

  /// البحث الصوتي (تحويل الصوت إلى نص)
  Future<String> speechToText() async {
    // TODO: Implement speech to text functionality
    // يمكن استخدام مكتبة speech_to_text
    throw UnimplementedError('Speech to text not implemented yet');
  }

  /// اقتراحات البحث الذكية
  Future<List<String>> getSearchSuggestions(String query) async {
    try {
      if (query.isEmpty) return [];

      // البحث في العقارات الموجودة للحصول على اقتراحات
      final snapshot = await _firestore
          .collection('estates')
          .where('title', isGreaterThanOrEqualTo: query)
          .where('title', isLessThan: '${query}z')
          .limit(10)
          .get();

      final suggestions = <String>{};

      for (final doc in snapshot.docs) {
        final data = doc.data();
        suggestions.add(data['title'] ?? '');
        suggestions.add(data['location'] ?? '');
        suggestions.add(data['propertyType'] ?? '');
      }

      return suggestions.where((s) => s.isNotEmpty).take(5).toList();
    } catch (e) {
      print('Error getting search suggestions: $e');
      return [];
    }
  }

  /// البحث المشابه
  Future<List<Estate>> findSimilarProperties(Estate property) async {
    try {
      final criteria = {
        'propertyType': property.propertyType,
        'priceMin': property.price * 0.8,
        'priceMax': property.price * 1.2,
        'areaMin': (property.area ?? 0) * 0.8,
        'areaMax': (property.area ?? 0) * 1.2,
        'location': property.location,
      };

      final results = await searchProperties(criteria);

      // إزالة العقار الحالي من النتائج
      results.removeWhere((p) => p.id == property.id);

      return results.take(10).toList();
    } catch (e) {
      print('Error finding similar properties: $e');
      return [];
    }
  }

  /// تحليل اتجاهات البحث
  Future<Map<String, dynamic>> getSearchTrends() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return {};

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('searchHistory')
          .orderBy('searchedAt', descending: true)
          .limit(50)
          .get();

      final trends = <String, int>{};

      for (final doc in snapshot.docs) {
        final criteria = doc.data()['criteria'] as Map<String, dynamic>;

        // تحليل الكلمات المفتاحية
        if (criteria['keyword'] != null && criteria['keyword'].isNotEmpty) {
          final keyword = criteria['keyword'].toString().toLowerCase();
          trends[keyword] = (trends[keyword] ?? 0) + 1;
        }

        // تحليل المواقع
        if (criteria['location'] != null && criteria['location'].isNotEmpty) {
          final location = criteria['location'].toString();
          trends[location] = (trends[location] ?? 0) + 1;
        }

        // تحليل أنواع العقارات
        if (criteria['propertyType'] != null && criteria['propertyType'].isNotEmpty) {
          final type = criteria['propertyType'].toString();
          trends[type] = (trends[type] ?? 0) + 1;
        }
      }

      // ترتيب الاتجاهات
      final sortedTrends = trends.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      return {
        'topKeywords': sortedTrends.take(5).map((e) => e.key).toList(),
        'searchCount': snapshot.docs.length,
        'trends': Map.fromEntries(sortedTrends.take(10)),
      };
    } catch (e) {
      print('Error getting search trends: $e');
      return {};
    }
  }

  /// تحويل البيانات من Firestore إلى Estate object
  Estate _mapToEstate(Map<String, dynamic> data, String id) {
    return Estate(
      id: id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      price: (data['price'] ?? 0).toDouble(),
      location: data['location'] ?? '',
      photoUrls: List<String>.from(data['photoUrls'] ?? []),
      isFeatured: data['isFeatured'] ?? false,
      planType: data['planType'] ?? 'free',
      startDate: data['startDate'] != null
          ? (data['startDate'] is String
              ? DateTime.parse(data['startDate'])
              : (data['startDate'] as Timestamp).toDate())
          : null,
      endDate: data['endDate'] != null
          ? (data['endDate'] is String
              ? DateTime.parse(data['endDate'])
              : (data['endDate'] as Timestamp).toDate())
          : null,
      createdAt: data['createdAt'] != null
          ? (data['createdAt'] is String
              ? DateTime.parse(data['createdAt'])
              : (data['createdAt'] as Timestamp).toDate())
          : DateTime.now(),
      // الحقول الجديدة من النظام المحسن
      mainCategory: data['mainCategory'],
      subCategory: data['subCategory'],
      usageType: data['usageType'] ?? data['purpose'], // دعم كلا الحقلين
      postedByUserType: data['postedByUserType'],
      hidePhone: data['hidePhone'] ?? false,
      extraPhones: List<String>.from(data['extraPhones'] ?? []),
      shareLocation: data['shareLocation'] ?? false,
      lat: data['lat']?.toDouble(),
      lng: data['lng']?.toDouble(),
      // المميزات والتجهيزات
      hasCentralAC: data['hasCentralAC'] ?? false,
      hasSecurity: data['hasSecurity'],
      allowPets: data['allowPets'],
      hasElevator: data['hasElevator'],
      hasSwimmingPool: data['hasSwimmingPool'],
      hasMaidRoom: data['hasMaidRoom'] ?? false,
      hasGarage: data['hasGarage'] ?? false,
      hasBalcony: data['hasBalcony'],
      isFullyFurnished: data['isFullyFurnished'],
      hasGarden: data['hasGarden'],
      hasPool: data['hasPool'],
      hasDriverRoom: data['hasDriverRoom'],
      hasPrivateEntrance: data['hasPrivateEntrance'],
      hasEquippedKitchen: data['hasEquippedKitchen'],
      // تفاصيل العقار
      rebound: data['rebound'],
      numberOfRooms: data['numberOfRooms'],
      internalLocation: data['internalLocation'],
      salon: data['salon'],
      area: data['area']?.toDouble(),
      floorNumber: data['floorNumber'],
      numberOfBathrooms: data['numberOfBathrooms'],
      buildingAge: data['buildingAge'],
      numberOfFloors: data['numberOfFloors'],
      propertyType: data['propertyType'],
      // إعدادات الإعلان
      autoRepublish: data['autoRepublish'] ?? false,
      kuwaitCornersPin: data['kuwaitCornersPin'] ?? false,
      movingAd: data['movingAd'] ?? false,
      vipBadge: data['vipBadge'] ?? false,
      pinnedOnHome: data['pinnedOnHome'] ?? false,
      discountCode: data['discountCode'],
      // بيانات المُعلِن
      advertiserImage: data['advertiserImage'],
      advertiserName: data['advertiserName'],
      advertiserEmail: data['advertiserEmail'],
      advertiserRegistrationDate: data['advertiserRegistrationDate'] != null
          ? (data['advertiserRegistrationDate'] is String
              ? DateTime.parse(data['advertiserRegistrationDate'])
              : (data['advertiserRegistrationDate'] as Timestamp).toDate())
          : null,
      advertiserAdsCount: data['advertiserAdsCount'],
      // ملكية العقار
      ownerId: data['ownerId'],
      originalEstateId: data['originalEstateId'],
      isOriginal: data['isOriginal'] ?? true,
      copiedBy: List<String>.from(data['copiedBy'] ?? []),
      isCopied: data['isCopied'] ?? false,
      copiedAt: data['copiedAt'] != null
          ? (data['copiedAt'] is String
              ? DateTime.parse(data['copiedAt'])
              : (data['copiedAt'] as Timestamp).toDate())
          : null,
      copyCount: data['copyCount'] ?? 0,
      // الدفع والتحقق
      isPaymentVerified: data['isPaymentVerified'] ?? false,
      isPaidAd: data['isPaidAd'] ?? false,
      adType: data['adType'],
      adExpiryDate: data['adExpiryDate'] != null
          ? (data['adExpiryDate'] is String
              ? DateTime.parse(data['adExpiryDate'])
              : (data['adExpiryDate'] as Timestamp).toDate())
          : null,
      // الإحصائيات
      viewsCount: data['viewsCount'],
      inquiriesCount: data['inquiriesCount'],
      favoritesCount: data['favoritesCount'],
      // الموقع الجغرافي (دعم كلا النظامين)
      latitude: data['latitude']?.toDouble() ?? data['lat']?.toDouble(),
      longitude: data['longitude']?.toDouble() ?? data['lng']?.toDouble(),
      // الحقول للتوافق مع النظام القديم
      rooms: data['rooms'] ?? data['numberOfRooms'],
      bathrooms: data['bathrooms'] ?? data['numberOfBathrooms'],
      floors: data['floors'] ?? data['numberOfFloors'],
      purpose: data['purpose'] ?? data['usageType'],
      // الحالة
      isAvailable: data['isAvailable'] ?? true);
  }
}
