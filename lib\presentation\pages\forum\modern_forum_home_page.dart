import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../core/theme/app_colors.dart';
import '../../../core/enums/view_modes.dart';
import '../../../domain/models/forum/topic_model.dart';
import '../../../domain/models/forum/category_model.dart';
import '../../../domain/models/forum/filter_options_model.dart';
import '../../providers/forum_provider.dart';
import '../../providers/auth_provider.dart' as app_auth;

import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/error_view.dart';
import '../../widgets/common/empty_view.dart';
import '../../widgets/forum/modern_categories_grid.dart';
import '../../widgets/forum/modern_topics_list.dart';
import '../../widgets/forum/modern_filter_panel.dart';
import '../../widgets/forum/modern_forum_header.dart';
import '../../widgets/forum/modern_forum_section.dart';
import '../../widgets/forum/simple_forum_background.dart';
import '../../widgets/forum/forum_statistics_widget.dart';
import 'forum_statistics_tab.dart';

/// صفحة الرئيسية الحديثة للوبي
class ModernForumHomePage extends StatefulWidget {
  /// مسار الصفحة
  static const String routeName = '/forum';

  const ModernForumHomePage({super.key});

  @override
  State<ModernForumHomePage> createState() => _ModernForumHomePageState();
}

class _ModernForumHomePageState extends State<ModernForumHomePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final PageController _pageController = PageController();

  int _currentIndex = 0;
  bool _isSearching = false;
  bool _showFilterPanel = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // تهيئة timeago بالعربية
    timeago.setLocaleMessages('ar', timeago.ArMessages());

    // إضافة مستمع للتمرير لتحميل المزيد من المواضيع
    _scrollController.addListener(_scrollListener);

    // جلب البيانات عند تحميل الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);
      forumProvider.fetchCategories();
      forumProvider.fetchTopics(refresh: true);
      forumProvider.fetchForumStatistics();

      // جلب بيانات المستخدم إذا كان مسجلاً
      final authProvider =
          Provider.of<app_auth.AuthProvider>(context, listen: false);
      if (authProvider.isLoggedIn && authProvider.user != null) {
        // يمكن إضافة استدعاءات لجلب بيانات المستخدم هنا
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  /// مستمع التمرير لتحميل المزيد من المواضيع
  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final forumProvider = Provider.of<ForumProvider>(context, listen: false);

      // تحميل المزيد من المواضيع فقط إذا:
      // 1. لا يوجد تحميل حالي
      // 2. هناك المزيد من المواضيع للتحميل
      // 3. تم تحديد فئة (لا نقوم بالتمرير في الصفحة الرئيسية)
      if (forumProvider.topicsState != LoadingState.loading &&
          forumProvider.hasMoreTopics &&
          forumProvider.selectedCategoryId != null) {
        forumProvider.fetchTopics();
      }
    }
  }

  /// بدء البحث
  void _startSearch() {
    setState(() {
      _isSearching = true;
    });
  }

  /// إلغاء البحث
  void _cancelSearch() {
    setState(() {
      _isSearching = false;
      _searchController.clear();
    });

    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    forumProvider.setSearchQuery('');
    forumProvider.fetchTopics(refresh: true);
  }

  /// تنفيذ البحث
  void _performSearch(String query) {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    forumProvider.setSearchQuery(query);
    forumProvider.fetchTopics(refresh: true);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // رأس اللوبي مع الخلفية المتحركة - نفس تصميم الصفحة الرئيسية
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary,
                    AppColors.primary.withValues(alpha: 0.8),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30))),
              child: Stack(
                children: [
                  // الأشكال الهندسية المتحركة
                  SimpleForumBackground(
                    primaryColor: Colors.transparent,
                    secondaryColor: Colors.white.withValues(alpha: 0.1)),
                  // رأس اللوبي
                  ModernForumHeader(
                    title: 'Lobby كريا',
                    description: 'مكان للنقاش وتبادل الآراء حول العقارات في Lobby كريا',
                    icon: Icons.forum,
                    backgroundColor: Colors.transparent,
                    isSearchActive: _isSearching,
                    searchController: _searchController,
                    onSearchTap: _startSearch,
                    onSearchCancel: _cancelSearch,
                    onSearchSubmitted: _performSearch,
                    onCategoriesTap: () {
                      Navigator.pushNamed(context, '/modern-forum/categories');
                    },
                    onNotificationsTap: () {
                      Navigator.pushNamed(context, '/forum/notifications');
                    }),

                  // زر إنشاء موضوع جديد
                  Positioned(
                    bottom: 20,
                    left: 20,
                    right: 20,
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pushNamed(context, '/forum/create-topic');
                        },
                        icon: Container(
                          padding: EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(6)),
                          child: Icon(
                            Icons.add,
                            color: Colors.white,
                            size: 20)),
                        label: Text(
                          'إنشاء موضوع جديد',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: AppColors.primary,
                          padding: EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                          elevation: 4,
                          shadowColor: Colors.black.withValues(alpha: 0.3))))),
                ])),

            // شريط التبويب
            _buildTabBar(),

            // لوحة الفلترة
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              height: _showFilterPanel ? null : 0,
              child: Visibility(
                visible: _showFilterPanel,
                child: Consumer<ForumProvider>(
                  builder: (context, forumProvider, _) {
                    return ModernFilterPanel(
                      filterOptions: forumProvider.filterOptions ??
                          ForumFilterOptionsModel.defaultOptions,
                      onFilterChanged: (options) {
                        forumProvider.setFilterOptions(options);
                        forumProvider.fetchTopics(refresh: true);
                        setState(() {
                          _showFilterPanel = false;
                        });
                      },
                      categories: forumProvider.categories,
                      showAdvancedOptions: true);
                  }))),

            // محتوى الصفحة
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentIndex = index;
                    _tabController.animateTo(index);
                  });
                },
                children: [
                  _buildAllTopicsTab(),
                  _buildMyTopicsTab(),
                  _buildSavedTopicsTab(),
                  ForumStatisticsTab(),
                ])),
          ])));
  }

  /// بناء شريط التبويب
  Widget _buildTabBar() {
    return Column(
      children: [
        // شريط التبويب مع خلفية شفافة
        Container(
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(30),
              bottomRight: Radius.circular(30))),
          child: TabBar(
            controller: _tabController,
            onTap: (index) {
              _pageController.animateToPage(
                index,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut);
            },
            indicatorColor: AppColors.primary,
            labelColor: AppColors.primary,
            unselectedLabelColor: Colors.grey,
            indicatorWeight: 3,
            tabs: const [
              Tab(
                icon: Icon(Icons.forum),
                text: 'المواضيع'),
              Tab(
                icon: Icon(Icons.person),
                text: 'مواضيعي'),
              Tab(
                icon: Icon(Icons.bookmark),
                text: 'المحفوظات'),
              Tab(
                icon: Icon(Icons.bar_chart),
                text: 'الإحصائيات'),
            ])),

        // شريط الفلترة والترتيب
        if (_currentIndex == 0) // عرض شريط الفلترة فقط في تبويب المواضيع
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(30),
                bottomRight: Radius.circular(30)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 5,
                  offset: const Offset(0, 2)),
              ]),
            child: Row(
              children: [
                // زر الفلترة
                InkWell(
                  onTap: () {
                    setState(() {
                      _showFilterPanel = !_showFilterPanel;
                    });
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300)),
                    child: Row(
                      children: [
                        Icon(Icons.filter_list,
                            size: 18, color: AppColors.primary),
                        const SizedBox(width: 4),
                        Text(
                          'فلترة',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.primary)),
                      ]))),

                const SizedBox(width: 8),

                // زر إلغاء الفلترة (يظهر فقط عند وجود فئة مختارة)
                Consumer<ForumProvider>(
                  builder: (context, forumProvider, _) {
                    if (forumProvider.selectedCategoryId != null) {
                      final selectedCategory = forumProvider.categories.firstWhere(
                        (c) => c.id == forumProvider.selectedCategoryId,
                        orElse: () => CategoryModel(
                          id: '',
                          name: 'فئة',
                          description: '',
                          icon: 'category',
                          color: '#9E9E9E',
                          order: 0,
                          createdAt: DateTime.now(),
                          updatedAt: DateTime.now()));

                      return InkWell(
                        onTap: () {
                          forumProvider.setSelectedCategory(null);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('تم إلغاء الفلترة'),
                              duration: Duration(seconds: 1),
                              backgroundColor: Colors.orange));
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.orange.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.orange.shade300)),
                          child: Row(
                            children: [
                              Icon(Icons.clear,
                                  size: 18, color: Colors.orange.shade700),
                              const SizedBox(width: 4),
                              Text(
                                selectedCategory.name,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.orange.shade700)),
                            ])));
                    }
                    return const SizedBox.shrink();
                  }),

                const SizedBox(width: 8),

                // زر عرض القائمة/الشبكة
                Consumer<ForumProvider>(
                  builder: (context, forumProvider, _) {
                    final isListView = forumProvider.viewMode == 'list';
                    return InkWell(
                      onTap: () {
                        forumProvider.setViewMode(
                          isListView ? 'grid' : 'list');
                      },
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade300)),
                        child: Icon(
                          isListView ? Icons.grid_view : Icons.view_list,
                          size: 18,
                          color: AppColors.primary)));
                  }),

                const Spacer(),

                // زر الترتيب
                Consumer<ForumProvider>(
                  builder: (context, forumProvider, _) {
                    return PopupMenuButton<String>(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade300)),
                        child: Row(
                          children: [
                            Icon(Icons.sort,
                                size: 18, color: AppColors.primary),
                            const SizedBox(width: 4),
                            Text(
                              'ترتيب',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppColors.primary)),
                          ])),
                      onSelected: (sortBy) {
                        forumProvider.setSortBy(sortBy);
                        forumProvider.fetchTopics(refresh: true);
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'createdAt',
                          child: Text('تاريخ الإنشاء')),
                        const PopupMenuItem(
                          value: 'updatedAt',
                          child: Text('آخر تحديث')),
                        const PopupMenuItem(
                          value: 'viewsCount',
                          child: Text('عدد المشاهدات')),
                        const PopupMenuItem(
                          value: 'repliesCount',
                          child: Text('عدد الردود')),
                        const PopupMenuItem(
                          value: 'likesCount',
                          child: Text('عدد الإعجابات')),
                      ]);
                  }),
              ])),
      ]);
  }

  /// بناء علامة تبويب جميع المواضيع
  Widget _buildAllTopicsTab() {
    return Consumer<ForumProvider>(
      builder: (context, forumProvider, child) {
        // حالة التحميل الأولي
        if (forumProvider.topicsState == LoadingState.loading &&
            forumProvider.topics.isEmpty &&
            forumProvider.categories.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const LoadingIndicator(),
                const SizedBox(height: 16),
                Text(
                  'جاري تحميل المنتدى...',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 16)),
              ]));
        }

        // حالة الخطأ
        if (forumProvider.topicsState == LoadingState.error) {
          return ErrorView(
            message: 'حدث خطأ في تحميل المواضيع',
            onRetry: () {
              forumProvider.fetchCategories();
              forumProvider.fetchTopics(refresh: true);
              forumProvider.fetchForumStatistics();
            });
        }

        // حالة عدم وجود مواضيع ولكن توجد فئات
        if (forumProvider.topicsState == LoadingState.empty ||
            (forumProvider.topics.isEmpty && forumProvider.categories.isNotEmpty)) {
          return SingleChildScrollView(
            child: Column(
              children: [
                // قسم إحصائيات المنتدى
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: ForumStatisticsWidget(
                    isCompact: true,
                    onTap: () {
                      Navigator.pushNamed(context, '/forum/statistics');
                    })),

                // عرض الفئات حتى لو لم توجد مواضيع
                _buildCategoriesSection(forumProvider),

                // رسالة عدم وجود مواضيع
                const SizedBox(height: 40),
                const EmptyView(
                  message: 'لا توجد مواضيع حالياً\nكن أول من يبدأ النقاش!',
                  icon: Icons.forum_outlined),
              ]));
        }

        // تقسيم المواضيع حسب الفئات
        final Map<String, List<TopicModel>> topicsByCategory = {};
        final List<TopicModel> featuredTopics = [];
        final List<TopicModel> popularTopics = [];
        final List<TopicModel> recentTopics = [];

        // تصنيف المواضيع
        for (final topic in forumProvider.topics) {
          // المواضيع المميزة (المثبتة أو المختارة من قبل المشرفين)
          if (topic.status == TopicStatus.pinned || topic.isStaffPicked) {
            featuredTopics.add(topic);
          }

          // المواضيع الشائعة (الأكثر مشاهدة أو إعجاباً)
          if (topic.viewsCount > 50 || topic.likesCount > 10) {
            popularTopics.add(topic);
          }

          // أحدث المواضيع (آخر 10 مواضيع)
          if (recentTopics.length < 10) {
            recentTopics.add(topic);
          }

          // تجميع المواضيع حسب الفئة
          if (!topicsByCategory.containsKey(topic.categoryId)) {
            topicsByCategory[topic.categoryId] = [];
          }
          topicsByCategory[topic.categoryId]!.add(topic);
        }

        // ترتيب المواضيع الشائعة حسب عدد المشاهدات
        popularTopics.sort((a, b) => b.viewsCount.compareTo(a.viewsCount));

        // ترتيب أحدث المواضيع حسب تاريخ الإنشاء
        recentTopics.sort((a, b) => b.createdAt.compareTo(a.createdAt));

        return SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            children: [
              // قسم إحصائيات المنتدى
              if (forumProvider.selectedCategoryId == null)
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: ForumStatisticsWidget(
                    isCompact: true,
                    onTap: () {
                      Navigator.pushNamed(context, '/forum/statistics');
                    })),

              // قسم الفئات
              _buildCategoriesSection(forumProvider),

              // عرض المواضيع المفلترة حسب الفئة المختارة
              if (forumProvider.selectedCategoryId != null)
                _buildFilteredTopicsSection(forumProvider),

              // قسم المواضيع المميزة
              if (featuredTopics.isNotEmpty && forumProvider.selectedCategoryId == null)
                ModernForumSection(
                  title: 'مواضيع مميزة',
                  description: 'مواضيع مثبتة ومختارة من قبل المشرفين',
                  icon: Icons.star,
                  color: Colors.amber,
                  content: SizedBox(
                    height: 220,
                    child: ModernTopicsList(
                      topics: featuredTopics,
                      onTopicTap: (topic) {
                        Navigator.pushNamed(
                          context,
                          '/modern-forum/topic',
                          arguments: topic.id);
                      },
                      onLikeTap: (topic) => _handleLikeTap(topic, forumProvider),
                      onBookmarkTap: (topic) => _handleBookmarkTap(topic, forumProvider),
                      onShareTap: (topic) => _handleShareTap(topic, forumProvider),
                      currentUserId: Provider.of<app_auth.AuthProvider>(context, listen: false).user?.uid,
                      scrollController: null,
                      isLoadingMore: false,
                      viewMode: TopicViewMode.compact))),

              // قسم المواضيع الشائعة
              if (popularTopics.isNotEmpty && forumProvider.selectedCategoryId == null)
                ModernForumSection(
                  title: 'مواضيع شائعة',
                  description: 'المواضيع الأكثر مشاهدة وتفاعلاً',
                  icon: Icons.trending_up,
                  color: Colors.deepPurple,
                  content: SizedBox(
                    height: 220,
                    child: ModernTopicsList(
                      topics: popularTopics.take(5).toList(),
                      onTopicTap: (topic) {
                        Navigator.pushNamed(
                          context,
                          '/modern-forum/topic',
                          arguments: topic.id);
                      },
                      onLikeTap: (topic) => _handleLikeTap(topic, forumProvider),
                      onBookmarkTap: (topic) => _handleBookmarkTap(topic, forumProvider),
                      onShareTap: (topic) => _handleShareTap(topic, forumProvider),
                      currentUserId: Provider.of<app_auth.AuthProvider>(context, listen: false).user?.uid,
                      scrollController: null,
                      isLoadingMore: false,
                      viewMode: TopicViewMode.list))),

              // قسم أحدث المواضيع
              if (recentTopics.isNotEmpty && forumProvider.selectedCategoryId == null)
                ModernForumSection(
                  title: 'أحدث المواضيع',
                  description: 'آخر المواضيع المضافة',
                  icon: Icons.new_releases,
                  color: Colors.green,
                  content: SizedBox(
                    height: 220,
                    child: ModernTopicsList(
                      topics: recentTopics.take(5).toList(),
                      onTopicTap: (topic) {
                        Navigator.pushNamed(
                          context,
                          '/modern-forum/topic',
                          arguments: topic.id);
                      },
                      onLikeTap: (topic) => _handleLikeTap(topic, forumProvider),
                      onBookmarkTap: (topic) => _handleBookmarkTap(topic, forumProvider),
                      onShareTap: (topic) => _handleShareTap(topic, forumProvider),
                      currentUserId: Provider.of<app_auth.AuthProvider>(context, listen: false).user?.uid,
                      scrollController: null,
                      isLoadingMore: false,
                      viewMode: forumProvider.viewMode == 'list' ? TopicViewMode.list : TopicViewMode.grid)),
                  onViewAllTap: () {
                    // عرض جميع المواضيع مرتبة حسب تاريخ الإنشاء
                    forumProvider.setSortBy('createdAt');
                    forumProvider.fetchTopics(refresh: true);
                  }),

              // عرض المواضيع حسب الفئات (فقط عندما لا توجد فئة مختارة)
              if (forumProvider.selectedCategoryId == null)
                ...topicsByCategory.entries.map((entry) {
                  final categoryId = entry.key;
                  final categoryTopics = entry.value;
                  // البحث عن الفئة في قائمة الفئات
                  final category = forumProvider.categories.firstWhere(
                    (c) => c.id == categoryId,
                    orElse: () => CategoryModel(
                      id: categoryId,
                      name: 'فئة غير معروفة',
                      description: '',
                      icon: 'category',
                      color: '#9E9E9E',
                      order: 0,
                      createdAt: DateTime.now(),
                      updatedAt: DateTime.now()));

                  // تحويل قيم الفئة إلى الأنواع المناسبة
                  final IconData categoryIcon = _getCategoryIcon(category.icon);
                  final Color categoryColor = _getCategoryColor(category.color);

                  return ModernForumSection(
                    title: category.name,
                    description: '${categoryTopics.length} موضوع - ${category.description}',
                    icon: categoryIcon,
                    color: categoryColor,
                    content: SizedBox(
                      height: 220,
                      child: ModernTopicsList(
                        topics: categoryTopics.take(5).toList(),
                        onTopicTap: (topic) {
                          Navigator.pushNamed(
                            context,
                            '/modern-forum/topic',
                            arguments: topic.id);
                        },
                        onLikeTap: (topic) => _handleLikeTap(topic, forumProvider),
                        onBookmarkTap: (topic) => _handleBookmarkTap(topic, forumProvider),
                        onShareTap: (topic) => _handleShareTap(topic, forumProvider),
                        currentUserId: Provider.of<app_auth.AuthProvider>(context, listen: false).user?.uid,
                        scrollController: null,
                        isLoadingMore: false,
                        viewMode: forumProvider.viewMode == 'list' ? TopicViewMode.list : TopicViewMode.grid)),
                    onViewAllTap: () {
                      // عرض مواضيع الفئة المحددة
                      forumProvider.setSelectedCategory(categoryId);
                    });
                }),

              // مساحة إضافية في الأسفل
              const SizedBox(height: 80),
            ]));
      });
  }

  /// معالجة النقر على زر الإعجاب
  void _handleLikeTap(TopicModel topic, ForumProvider forumProvider) async {
    final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
    if (authProvider.isLoggedIn && authProvider.user != null) {
      final userId = authProvider.user!.uid;
      final isCurrentlyLiked = topic.likedBy?.contains(userId) ?? false;

      try {
        // تنفيذ العملية مع تحديث محلي فوري
        if (isCurrentlyLiked) {
          await forumProvider.unlikeTopic(topic.id, userId);
        } else {
          await forumProvider.likeTopic(topic.id, userId);
        }

        // تحديث الموضوع محلياً بدلاً من إعادة تحميل جميع المواضيع
        forumProvider.updateTopicLocally(topic.id, (updatedTopic) {
          final newLikedBy = List<String>.from(updatedTopic.likedBy ?? []);
          if (isCurrentlyLiked) {
            newLikedBy.remove(userId);
          } else {
            if (!newLikedBy.contains(userId)) {
              newLikedBy.add(userId);
            }
          }

          return updatedTopic.copyWith(
            likedBy: newLikedBy,
            likesCount: isCurrentlyLiked
              ? (updatedTopic.likesCount - 1).clamp(0, double.infinity).toInt()
              : updatedTopic.likesCount + 1);
        });

        // إظهار رسالة تأكيد
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isCurrentlyLiked ? 'تم إلغاء الإعجاب' : 'تم الإعجاب بالموضوع'),
              duration: const Duration(seconds: 1),
              backgroundColor: isCurrentlyLiked ? Colors.orange : Colors.green));
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('حدث خطأ أثناء تحديث الإعجاب'),
              backgroundColor: Colors.red));
        }
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب تسجيل الدخول أولاً')));
    }
  }

  /// معالجة النقر على زر الحفظ
  void _handleBookmarkTap(TopicModel topic, ForumProvider forumProvider) async {
    final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);
    if (authProvider.isLoggedIn && authProvider.user != null) {
      final userId = authProvider.user!.uid;
      final isCurrentlyBookmarked = topic.bookmarkedBy?.contains(userId) ?? false;

      try {
        // تنفيذ العملية مع تحديث محلي فوري
        if (isCurrentlyBookmarked) {
          await forumProvider.unbookmarkTopic(topic.id, userId);
        } else {
          await forumProvider.bookmarkTopic(topic.id, userId);
        }

        // تحديث الموضوع محلياً بدلاً من إعادة تحميل جميع المواضيع
        forumProvider.updateTopicLocally(topic.id, (updatedTopic) {
          final newBookmarkedBy = List<String>.from(updatedTopic.bookmarkedBy ?? []);
          if (isCurrentlyBookmarked) {
            newBookmarkedBy.remove(userId);
          } else {
            if (!newBookmarkedBy.contains(userId)) {
              newBookmarkedBy.add(userId);
            }
          }

          return updatedTopic.copyWith(
            bookmarkedBy: newBookmarkedBy,
            bookmarksCount: isCurrentlyBookmarked
              ? (updatedTopic.bookmarksCount - 1).clamp(0, double.infinity).toInt()
              : updatedTopic.bookmarksCount + 1);
        });

        // إظهار رسالة تأكيد
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isCurrentlyBookmarked ? 'تم إلغاء حفظ الموضوع' : 'تم حفظ الموضوع'),
              duration: const Duration(seconds: 1),
              backgroundColor: isCurrentlyBookmarked ? Colors.orange : Colors.blue));
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('حدث خطأ أثناء تحديث الحفظ'),
              backgroundColor: Colors.red));
        }
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب تسجيل الدخول أولاً')));
    }
  }

  /// معالجة النقر على زر المشاركة
  void _handleShareTap(TopicModel topic, ForumProvider forumProvider) async {
    final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);

    try {
      // إنشاء نص المشاركة المحسن
      final shareText = '''
🔗 موضوع من Lobby كريا

📝 ${topic.title}

${topic.content.isNotEmpty ? (topic.content.length > 100 ? '${topic.content.substring(0, 100)}...' : topic.content) : ''}

👤 بواسطة: ${topic.userName}
📊 ${topic.repliesCount} رد • ${topic.viewsCount} مشاهدة • ${topic.likesCount} إعجاب

📱 حمل تطبيق Krea للعقارات
      ''';

      // مشاركة الموضوع باستخدام Share
      await Share.share(
        shareText.trim(),
        subject: 'موضوع من Lobby كريا: ${topic.title}');

      // تسجيل المشاركة في Firebase إذا كان المستخدم مسجل الدخول
      if (authProvider.isLoggedIn && authProvider.user != null) {
        await forumProvider.shareTopic(topic.id, authProvider.user!.uid);
      }

      // إظهار رسالة تأكيد
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم مشاركة الموضوع بنجاح'),
            duration: Duration(seconds: 1),
            backgroundColor: Colors.green));
      }
    } catch (e) {
      // إظهار رسالة خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء المشاركة'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.red));
      }
    }
  }

  /// تحويل اسم الأيقونة إلى كائن IconData
  IconData _getCategoryIcon(String iconName) {
    switch (iconName) {
      case 'home':
        return Icons.home;
      case 'apartment':
        return Icons.apartment;
      case 'business':
        return Icons.business;
      case 'store':
        return Icons.store;
      case 'landscape':
        return Icons.landscape;
      case 'help':
        return Icons.help;
      case 'info':
        return Icons.info;
      case 'question_answer':
        return Icons.question_answer;
      case 'forum':
        return Icons.forum;
      case 'chat':
        return Icons.chat;
      case 'category':
        return Icons.category;
      default:
        return Icons.category;
    }
  }

  /// تحويل كود اللون إلى كائن Color
  Color _getCategoryColor(String colorCode) {
    if (colorCode.startsWith('#')) {
      try {
        return Color(int.parse('0xFF${colorCode.substring(1)}'));
      } catch (e) {
        return Colors.grey;
      }
    } else if (colorCode == 'primary') {
      return AppColors.primary;
    } else if (colorCode == 'secondary') {
      return AppColors.secondary;
    } else if (colorCode == 'accent') {
      return Colors.deepPurple;
    } else if (colorCode == 'info') {
      return AppColors.info;
    } else if (colorCode == 'success') {
      return AppColors.success;
    } else if (colorCode == 'warning') {
      return AppColors.warning;
    } else if (colorCode == 'error') {
      return AppColors.error;
    } else {
      return Colors.grey;
    }
  }

  /// بناء قسم المواضيع المفلترة حسب الفئة
  Widget _buildFilteredTopicsSection(ForumProvider forumProvider) {
    final selectedCategory = forumProvider.categories.firstWhere(
      (c) => c.id == forumProvider.selectedCategoryId,
      orElse: () => CategoryModel(
        id: '',
        name: 'فئة غير معروفة',
        description: '',
        icon: 'category',
        color: '#9E9E9E',
        order: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now()));

    // فلترة المواضيع حسب الفئة المختارة
    final filteredTopics = forumProvider.topics
        .where((topic) => topic.categoryId == forumProvider.selectedCategoryId)
        .toList();

    if (filteredTopics.isEmpty) {
      return ModernForumSection(
        title: 'مواضيع ${selectedCategory.name}',
        description: 'لا توجد مواضيع في هذه الفئة حالياً',
        icon: _getCategoryIcon(selectedCategory.icon),
        color: _getCategoryColor(selectedCategory.color),
        content: SizedBox(
          height: 120,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.topic_outlined,
                  size: 48,
                  color: Colors.grey.shade400),
                const SizedBox(height: 8),
                Text(
                  'لا توجد مواضيع في هذه الفئة',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 14)),
                const SizedBox(height: 8),
                TextButton(
                  onPressed: () {
                    forumProvider.setSelectedCategory(null);
                  },
                  child: const Text('عرض جميع المواضيع')),
              ]))));
    }

    return ModernForumSection(
      title: 'مواضيع ${selectedCategory.name}',
      description: '${filteredTopics.length} موضوع في هذه الفئة',
      icon: _getCategoryIcon(selectedCategory.icon),
      color: _getCategoryColor(selectedCategory.color),
      content: SizedBox(
        height: 300,
        child: ModernTopicsList(
          topics: filteredTopics,
          onTopicTap: (topic) {
            Navigator.pushNamed(
              context,
              '/modern-forum/topic',
              arguments: topic.id);
          },
          onLikeTap: (topic) => _handleLikeTap(topic, forumProvider),
          onBookmarkTap: (topic) => _handleBookmarkTap(topic, forumProvider),
          onShareTap: (topic) => _handleShareTap(topic, forumProvider),
          currentUserId: Provider.of<app_auth.AuthProvider>(context, listen: false).user?.uid,
          scrollController: null,
          isLoadingMore: false,
          viewMode: TopicViewMode.list)),
      onViewAllTap: () {
        // إلغاء الفلترة لعرض جميع المواضيع
        forumProvider.setSelectedCategory(null);
      });
  }

  /// بناء قسم الفئات
  Widget _buildCategoriesSection(ForumProvider forumProvider) {
    if (forumProvider.categoriesState == LoadingState.loading) {
      return const SizedBox(
        height: 120,
        child: Center(child: LoadingIndicator()));
    } else if (forumProvider.categoriesState == LoadingState.error) {
      return const SizedBox(
        height: 120,
        child: Center(child: Text('حدث خطأ في تحميل الفئات')));
    } else if (forumProvider.categoriesState == LoadingState.empty) {
      return const SizedBox(
        height: 120,
        child: Center(child: Text('لا توجد فئات')));
    }

    return ModernForumSection(
      title: 'الفئات',
      description: 'تصفح المواضيع حسب الفئة',
      icon: Icons.category,
      color: AppColors.primary,
      content: SizedBox(
        height: 120,
        child: ModernCategoriesGrid(
          categories: forumProvider.categories,
          onCategoryTap: (category) {
            // تحديد الفئة المختارة وإعادة تحميل المواضيع
            forumProvider.setSelectedCategory(category.id);

            // إظهار رسالة تأكيد
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم تحديد فئة: ${category.name}'),
                duration: const Duration(seconds: 1),
                backgroundColor: AppColors.primary));
          },
          viewMode: CategoryViewMode.carousel)),
      onViewAllTap: () {
        // الانتقال إلى صفحة الأقسام
        Navigator.pushNamed(context, '/modern-forum/categories');
      });
  }

  /// بناء علامة تبويب مواضيعي
  Widget _buildMyTopicsTab() {
    final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn || authProvider.user == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.login, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              'يجب تسجيل الدخول لعرض مواضيعك',
              style: TextStyle(fontSize: 16, color: Colors.grey.shade700)),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                // التنقل إلى صفحة تسجيل الدخول
                Navigator.pushNamed(context, '/login');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12)),
              child: const Text('تسجيل الدخول')),
          ]));
    }

    return FutureBuilder<List<TopicModel>>(
      future: Provider.of<ForumProvider>(context, listen: false)
          .getUserTopics(authProvider.user!.uid),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: LoadingIndicator());
        } else if (snapshot.hasError) {
          return ErrorView(
            message: 'حدث خطأ في تحميل المواضيع',
            onRetry: () => setState(() {}));
        } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const EmptyView(
            message: 'لم تقم بإنشاء أي مواضيع بعد',
            icon: Icons.topic_outlined);
        }

        final topics = snapshot.data!;
        return ModernTopicsList(
          topics: topics,
          onTopicTap: (topic) {
            Navigator.pushNamed(
              context,
              '/modern-forum/topic',
              arguments: topic.id);
          },
          onLikeTap: (topic) => _handleLikeTap(topic, Provider.of<ForumProvider>(context, listen: false)),
          onBookmarkTap: (topic) => _handleBookmarkTap(topic, Provider.of<ForumProvider>(context, listen: false)),
          onShareTap: (topic) => _handleShareTap(topic, Provider.of<ForumProvider>(context, listen: false)),
          currentUserId: authProvider.user!.uid,
          scrollController: null,
          isLoadingMore: false,
          viewMode: Provider.of<ForumProvider>(context, listen: false).viewMode == 'list' ? TopicViewMode.list : (Provider.of<ForumProvider>(context, listen: false).viewMode == 'grid' ? TopicViewMode.grid : TopicViewMode.compact));
      });
  }

  /// بناء علامة تبويب المواضيع المحفوظة
  Widget _buildSavedTopicsTab() {
    final authProvider = Provider.of<app_auth.AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn || authProvider.user == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bookmark_border, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              'يجب تسجيل الدخول لعرض المواضيع المحفوظة',
              style: TextStyle(fontSize: 16, color: Colors.grey.shade700)),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                // التنقل إلى صفحة تسجيل الدخول
                Navigator.pushNamed(context, '/login');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12)),
              child: const Text('تسجيل الدخول')),
          ]));
    }

    return FutureBuilder<List<TopicModel>>(
      future: Provider.of<ForumProvider>(context, listen: false)
          .getBookmarkedTopics(authProvider.user!.uid),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: LoadingIndicator());
        } else if (snapshot.hasError) {
          return ErrorView(
            message: 'حدث خطأ في تحميل المواضيع المحفوظة',
            onRetry: () => setState(() {}));
        } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const EmptyView(
            message: 'لم تقم بحفظ أي مواضيع بعد',
            icon: Icons.bookmark_border);
        }

        final topics = snapshot.data!;
        return ModernTopicsList(
          topics: topics,
          onTopicTap: (topic) {
            Navigator.pushNamed(
              context,
              '/modern-forum/topic',
              arguments: topic.id);
          },
          onLikeTap: (topic) => _handleLikeTap(topic, Provider.of<ForumProvider>(context, listen: false)),
          onBookmarkTap: (topic) {
            Provider.of<ForumProvider>(context, listen: false)
                .unbookmarkTopic(topic.id, authProvider.user!.uid);
            setState(() {});
          },
          onShareTap: (topic) => _handleShareTap(topic, Provider.of<ForumProvider>(context, listen: false)),
          currentUserId: authProvider.user!.uid,
          scrollController: ScrollController(),
          isLoadingMore: false,
          viewMode: Provider.of<ForumProvider>(context, listen: false).viewMode == 'list' ? TopicViewMode.list : (Provider.of<ForumProvider>(context, listen: false).viewMode == 'grid' ? TopicViewMode.grid : TopicViewMode.compact));
      });
  }
}