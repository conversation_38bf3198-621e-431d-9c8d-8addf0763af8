import 'dart:io';
import '../entities/virtual_tour.dart';

/// واجهة مستودع الجولات الافتراضية
abstract class VirtualTourRepository {
  /// الحصول على جولات العقار الافتراضية
  Future<List<VirtualTour>> getEstateVirtualTours(String estateId);
  
  /// الحصول على جولة افتراضية بواسطة المعرف
  Future<VirtualTour?> getVirtualTourById(String tourId);
  
  /// إضافة جولة افتراضية جديدة
  Future<String> addVirtualTour(VirtualTour tour, {File? thumbnailFile});
  
  /// تحديث جولة افتراضية موجودة
  Future<void> updateVirtualTour(VirtualTour tour, {File? newThumbnailFile});
  
  /// حذف جولة افتراضية
  Future<void> deleteVirtualTour(String tourId);
  
  /// تحميل صورة مصغرة للجولة
  Future<String> uploadThumbnailImage(File file, String estateId, String tourId);
  
  /// زيادة عدد مشاهدات الجولة الافتراضية
  Future<void> incrementVirtualTourViews(String tourId);
  
  /// الحصول على الجولات الافتراضية الأكثر مشاهدة
  Future<List<VirtualTour>> getMostViewedVirtualTours({int limit = 10});
  
  /// الحصول على الجولات الافتراضية للمستخدم
  Future<List<VirtualTour>> getUserVirtualTours(String userId);
  
  /// تغيير حالة الجولة الافتراضية (نشطة/غير نشطة)
  Future<void> toggleVirtualTourStatus(String tourId, bool isActive);
}
