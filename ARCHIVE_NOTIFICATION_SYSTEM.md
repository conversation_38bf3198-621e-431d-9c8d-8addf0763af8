# 📦 نظام إشعارات الأرشفة التلقائية - Kuwait Corners

## 🎯 نظرة عامة

تم تطوير نظام متكامل لإرسال إشعارات تلقائية للمستخدمين بعد 24 ساعة من إنشاء الإعلان لإعلامهم بأن إعلانهم قد تم أرشفته تلقائياً، مع توفير معلومات الاتصال لتفعيل الإعلان مرة أخرى.

## 🏗️ مكونات النظام

### 1. Firebase Functions
- **scheduleNotificationProcessor**: وظيفة مجدولة تعمل كل دقيقة
- **estateCreated**: مشغل يعمل عند إنشاء عقار جديد
- **processScheduledNotifications**: وظيفة قابلة للاستدعاء للمعالجة اليدوية

### 2. خدمات Flutter
- **ArchiveNotificationService**: إدارة الإشعارات المجدولة
- **NotificationService**: إرسال الإشعارات (محدث)
- **CreateEstateNew**: جدولة الإشعارات عند الإنشاء (محدث)

### 3. قواعد البيانات
- **scheduledNotifications**: تخزين الإشعارات المجدولة
- **systemLogs**: تسجيل عمليات النظام
- **users/{userId}/notifications**: إشعارات المستخدمين

## 🔄 تدفق العمل

```mermaid
graph TD
    A[المستخدم ينشئ إعلان] --> B[حفظ في Firestore]
    B --> C[تشغيل estateCreated trigger]
    C --> D[إنشاء مستند في scheduledNotifications]
    D --> E[إرسال إشعار ترحيبي]
    E --> F[انتظار 24 ساعة]
    F --> G[scheduleNotificationProcessor يفحص]
    G --> H[أرشفة العقار]
    H --> I[إرسال إشعار الأرشفة]
    I --> J[حفظ في notifications]
```

## 📱 رسائل الإشعارات

### إشعار الترحيب (فوري)
```
العنوان: "تم نشر إعلانك بنجاح! 🎉"

المحتوى: 
"تم نشر إعلان '[عنوان العقار]' بنجاح في تطبيق Kuwait Corners.

⏰ سيتم أرشفة الإعلان تلقائياً بعد 24 ساعة من النشر.

لتفعيله مرة أخرى بعد الأرشفة، تواصل معنا:
📱 واتساب: +965 9929 8821
📧 إيميل: <EMAIL>

شكراً لثقتك في تطبيق Kuwait Corners! 🏠"
```

### إشعار الأرشفة (بعد 24 ساعة)
```
العنوان: "تم أرشفة إعلانك 📦"

المحتوى:
"تم أرشفة إعلان '[عنوان العقار]' تلقائياً بعد 24 ساعة من النشر.

لتفعيل الإعلان مرة أخرى، تواصل مع إدارة التطبيق:

📱 واتساب: +965 9929 8821
📧 إيميل: <EMAIL>

شكراً لاستخدامك تطبيق Kuwait Corners 🏠"
```

## 💻 كيفية الاستخدام

### 1. في التطبيق (تلقائي)
```dart
// عند إنشاء إعلان جديد، النظام يعمل تلقائياً
// لا حاجة لأي كود إضافي
```

### 2. إدارة الإشعارات المجدولة
```dart
final archiveService = ArchiveNotificationService();

// الحصول على الإشعارات المجدولة للمستخدم
Stream<List<ScheduledNotification>> notifications = 
    archiveService.getUserScheduledNotifications();

// إلغاء إشعار مجدول
await archiveService.cancelScheduledArchiveNotification(estateId);

// تحديث وقت الأرشفة
await archiveService.updateScheduledArchiveTime(
  estateId: estateId,
  newScheduledTime: DateTime.now().add(Duration(hours: 48)),
);

// الحصول على إحصائيات
NotificationStats stats = await archiveService.getNotificationStats();
```

### 3. إرسال إشعارات مخصصة
```dart
final notificationService = NotificationService();

// إشعار أرشفة مخصص
await notificationService.sendEstateArchivedNotification(
  userId,
  estateId,
  estateTitle,
);

// إشعار نشر مخصص
await notificationService.sendEstatePublishedNotification(
  userId,
  estateId,
  estateTitle,
);
```

## 🔧 إعدادات Firebase

### 1. Firestore Rules
```javascript
// إضافة قواعد للمجموعات الجديدة
match /scheduledNotifications/{notificationId} {
  allow read, write: if request.auth != null;
}

match /systemLogs/{logId} {
  allow read: if request.auth != null && 
    request.auth.token.admin == true;
  allow write: if request.auth != null;
}
```

### 2. Firestore Indexes
```json
{
  "indexes": [
    {
      "collectionGroup": "scheduledNotifications",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "processed", "order": "ASCENDING"},
        {"fieldPath": "scheduledTime", "order": "ASCENDING"}
      ]
    },
    {
      "collectionGroup": "scheduledNotifications", 
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "userId", "order": "ASCENDING"},
        {"fieldPath": "processed", "order": "ASCENDING"}
      ]
    }
  ]
}
```

## 📊 مراقبة النظام

### 1. السجلات
```bash
# مراقبة وظائف Firebase
firebase functions:log --only scheduleNotificationProcessor
firebase functions:log --only estateCreated

# فلترة الأخطاء
firebase functions:log --filter "ERROR"
```

### 2. الإحصائيات
```dart
// في التطبيق
final stats = await ArchiveNotificationService().getNotificationStats();
print('إشعارات مجدولة: ${stats.scheduledCount}');
print('إشعارات معالجة: ${stats.processedCount}');
```

### 3. قاعدة البيانات
```javascript
// فحص الإشعارات المجدولة
db.collection('scheduledNotifications')
  .where('processed', '==', false)
  .get()

// فحص السجلات
db.collection('systemLogs')
  .where('type', '==', 'estate_archived')
  .orderBy('timestamp', 'desc')
  .limit(10)
  .get()
```

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة:

#### 1. الإشعارات لا تصل
```bash
# فحص FCM tokens
firebase functions:log --filter "FCM"

# فحص أذونات الإشعارات في التطبيق
```

#### 2. الوظائف لا تعمل
```bash
# فحص نشر الوظائف
firebase functions:list

# إعادة نشر
firebase deploy --only functions
```

#### 3. أخطاء قاعدة البيانات
```bash
# فحص قواعد Firestore
firebase firestore:rules:get

# فحص الفهارس
firebase firestore:indexes
```

## 🔄 التحديثات المستقبلية

### ميزات مقترحة:
1. **إشعارات تذكير**: قبل الأرشفة بساعتين
2. **تمديد الفترة**: إمكانية تمديد فترة النشر
3. **إشعارات مخصصة**: حسب نوع العقار أو المنطقة
4. **تحليلات متقدمة**: معدلات الفتح والتفاعل

### تحسينات تقنية:
1. **Batch Processing**: معالجة مجمعة للإشعارات
2. **Retry Logic**: إعادة المحاولة عند الفشل
3. **Rate Limiting**: تحديد معدل الإرسال
4. **A/B Testing**: اختبار رسائل مختلفة

## 📞 معلومات الاتصال

### للمستخدمين (في الإشعارات):
- **واتساب**: +965 9929 8821
- **إيميل**: <EMAIL>

### للدعم الفني:
- **إيميل**: <EMAIL>
- **واتساب**: +965 9929 8821

## ✅ قائمة التحقق

- [x] إنشاء Firebase Functions
- [x] تحديث خدمة الإشعارات
- [x] إضافة جدولة الإشعارات
- [x] اختبار النظام محلياً
- [x] نشر Functions
- [x] اختبار النظام في الإنتاج
- [x] مراقبة السجلات
- [x] توثيق النظام

---

**ملاحظة**: تأكد من اختبار النظام بشكل شامل قبل الاعتماد عليه في الإنتاج.
