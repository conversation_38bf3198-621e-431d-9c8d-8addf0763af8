import 'dart:developer' as developer;
import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

import '../../domain/models/forum/badge_model.dart';
import '../../domain/models/forum/achievement_model.dart';
import '../../domain/models/forum/user_statistics_model.dart';
import '../../domain/models/loyalty/reward_model.dart';
import '../../domain/models/loyalty/user_reward_redemption_model.dart';
import 'loyalty_program_service.dart';

/// خدمة إدارة المكافآت والنقاط المحسنة
class RewardsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final LoyaltyProgramService _loyaltyService = LoyaltyProgramService();

  /// منح نقاط للمستخدم
  Future<void> awardPoints(String userId, int points, String reason) async {
    try {
      final userStatsRef = _firestore.collection('user_statistics').doc(userId);
      
      await _firestore.runTransaction((transaction) async {
        final userStatsDoc = await transaction.get(userStatsRef);
        
        if (userStatsDoc.exists) {
          final currentPoints = userStatsDoc.data()?['points'] ?? 0;
          final newPoints = currentPoints + points;
          
          transaction.update(userStatsRef, {
            'points': newPoints,
            'updatedAt': Timestamp.now(),
          });
          

          
          // فحص الإنجازات والشارات الجديدة
          await _checkAchievements(userId);
          await _checkLevelUp(userId, newPoints);
        }
      });
      
      developer.log('تم منح $points نقطة للمستخدم $userId - السبب: $reason');
    } catch (e) {
      developer.log('خطأ في منح النقاط: $e');
    }
  }

  /// فحص الإنجازات الجديدة
  Future<void> _checkAchievements(String userId) async {
    try {
      final userStatsDoc = await _firestore.collection('user_statistics').doc(userId).get();
      if (!userStatsDoc.exists) return;

      final userStats = UserStatisticsModel.fromFirestore(userStatsDoc);
      final availableAchievements = AchievementModel.getAvailableAchievements();
      
      for (final achievement in availableAchievements) {
        final isEarned = _checkAchievementCondition(userStats, achievement);
        if (isEarned) {
          await _awardAchievement(userId, achievement);
        }
      }
    } catch (e) {
      developer.log('خطأ في فحص الإنجازات: $e');
    }
  }

  /// فحص شروط الإنجاز
  bool _checkAchievementCondition(UserStatisticsModel userStats, AchievementModel achievement) {
    switch (achievement.category) {
      case AchievementCategory.topics:
        return userStats.topicsCount >= achievement.targetValue;
      case AchievementCategory.posts:
        return userStats.postsCount >= achievement.targetValue;
      case AchievementCategory.interaction:
        if (achievement.id.contains('liker')) {
          return userStats.totalLikesGiven >= achievement.targetValue;
        } else if (achievement.id.contains('popular')) {
          return (userStats.totalTopicLikes + userStats.totalPostLikes) >= achievement.targetValue;
        }
        return false;
      case AchievementCategory.activity:
        // يحتاج تطبيق منطق الأيام المتتالية
        return false;
      default:
        return false;
    }
  }

  /// منح إنجاز للمستخدم
  Future<void> _awardAchievement(String userId, AchievementModel achievement) async {
    try {
      final achievementRef = _firestore
          .collection('user_achievements')
          .doc(userId)
          .collection('achievements')
          .doc(achievement.id);

      final existingDoc = await achievementRef.get();
      if (existingDoc.exists) return; // الإنجاز موجود بالفعل

      await achievementRef.set({
        'id': achievement.id,
        'name': achievement.name,
        'description': achievement.description,
        'category': achievement.category.index,
        'rewardPoints': achievement.rewardPoints,
        'completedAt': Timestamp.now(),
        'currentValue': achievement.targetValue,
        'targetValue': achievement.targetValue,
      });

      // منح النقاط المكافأة
      await awardPoints(userId, achievement.rewardPoints.toInt(), 'إنجاز: ${achievement.name}');
      
      developer.log('تم منح الإنجاز ${achievement.name} للمستخدم $userId');
    } catch (e) {
      developer.log('خطأ في منح الإنجاز: $e');
    }
  }

  /// فحص ترقية المستوى
  Future<void> _checkLevelUp(String userId, int currentPoints) async {
    try {
      final userStatsRef = _firestore.collection('user_statistics').doc(userId);
      final userStatsDoc = await userStatsRef.get();
      
      if (!userStatsDoc.exists) return;
      
      final currentLevel = userStatsDoc.data()?['level'] ?? 'مبتدئ';
      final newLevel = _calculateLevel(currentPoints);
      
      if (newLevel != currentLevel) {
        await userStatsRef.update({
          'level': newLevel,
          'updatedAt': Timestamp.now(),
        });
        
        // منح شارة المستوى الجديد
        await _awardLevelBadge(userId, newLevel);
        
        developer.log('تم ترقية المستخدم $userId إلى مستوى $newLevel');
      }
    } catch (e) {
      developer.log('خطأ في فحص ترقية المستوى: $e');
    }
  }

  /// حساب المستوى بناءً على النقاط
  String _calculateLevel(int points) {
    if (points >= 1000) return 'خبير';
    if (points >= 500) return 'متقدم';
    if (points >= 200) return 'متوسط';
    if (points >= 100) return 'نشط';
    return 'مبتدئ';
  }

  /// منح شارة المستوى
  Future<void> _awardLevelBadge(String userId, String level) async {
    try {
      String badgeId;
      switch (level) {
        case 'نشط':
          badgeId = 'level_active';
          break;
        case 'متوسط':
          badgeId = 'level_intermediate';
          break;
        case 'متقدم':
          badgeId = 'level_advanced';
          break;
        case 'خبير':
          badgeId = 'level_expert';
          break;
        default:
          badgeId = 'level_beginner';
      }

      final badgeRef = _firestore
          .collection('user_badges')
          .doc(userId)
          .collection('badges')
          .doc(badgeId);

      await badgeRef.set({
        'id': badgeId,
        'earnedAt': Timestamp.now(),
        'level': level,
      });
      
      developer.log('تم منح شارة المستوى $level للمستخدم $userId');
    } catch (e) {
      developer.log('خطأ في منح شارة المستوى: $e');
    }
  }



  /// الحصول على إنجازات المستخدم
  Future<List<AchievementModel>> getUserAchievements(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('user_achievements')
          .doc(userId)
          .collection('achievements')
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        return AchievementModel.fromMap(data);
      }).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على إنجازات المستخدم: $e');
      return [];
    }
  }

  /// الحصول على شارات المستخدم
  Future<List<BadgeModel>> getUserBadges(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('user_badges')
          .doc(userId)
          .collection('badges')
          .get();

      final badges = <BadgeModel>[];
      for (final doc in snapshot.docs) {
        final badgeId = doc.data()['id'];
        final badge = BadgeModel.getBadgeById(badgeId);
        if (badge != null) {
          badges.add(badge.copyWith(
            earnedAt: (doc.data()['earnedAt'] as Timestamp?)?.toDate(),
          ));
        }
      }
      
      return badges;
    } catch (e) {
      developer.log('خطأ في الحصول على شارات المستخدم: $e');
      return [];
    }
  }

  /// الحصول على سجل النقاط
  Future<List<Map<String, dynamic>>> getPointsHistory(String userId, {int limit = 20}) async {
    try {
      final snapshot = await _firestore
          .collection('points_transactions')
          .where('userId', isEqualTo: userId)
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) => doc.data()).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على سجل النقاط: $e');
      return [];
    }
  }

  /// منح نقاط للأنشطة المختلفة
  Future<void> awardPointsForActivity(String userId, String activity) async {
    int points = 0;
    String reason = '';

    switch (activity) {
      case 'create_topic':
        points = 10;
        reason = 'إنشاء موضوع جديد';
        break;
      case 'create_post':
        points = 5;
        reason = 'إضافة رد';
        break;
      case 'receive_like':
        points = 2;
        reason = 'الحصول على إعجاب';
        break;
      case 'give_like':
        points = 1;
        reason = 'إعطاء إعجاب';
        break;
      case 'daily_login':
        points = 3;
        reason = 'تسجيل الدخول اليومي';
        break;
      case 'create_poll':
        points = 8;
        reason = 'إنشاء استطلاع رأي';
        break;
      case 'vote_poll':
        points = 2;
        reason = 'التصويت في استطلاع';
        break;
      default:
        return;
    }

    if (points > 0) {
      await awardPoints(userId, points, reason);
    }
  }

  // ===== وظائف المكافآت الحقيقية =====

  /// الحصول على جميع المكافآت المتاحة
  Future<List<RewardModel>> getAvailableRewards() async {
    try {
      final querySnapshot = await _firestore
          .collection('rewards')
          .where('status', isEqualTo: RewardStatus.available.index)
          .orderBy('pointsRequired')
          .get();

      return querySnapshot.docs
          .map((doc) => RewardModel.fromFirestore(doc))
          .where((reward) => reward.isAvailable)
          .toList();
    } catch (e) {
      developer.log('خطأ في الحصول على المكافآت المتاحة: $e');
      return [];
    }
  }

  /// الحصول على المكافآت حسب النوع
  Future<List<RewardModel>> getRewardsByType(RewardType type) async {
    try {
      final querySnapshot = await _firestore
          .collection('rewards')
          .where('type', isEqualTo: type.index)
          .where('status', isEqualTo: RewardStatus.available.index)
          .orderBy('pointsRequired')
          .get();

      return querySnapshot.docs
          .map((doc) => RewardModel.fromFirestore(doc))
          .where((reward) => reward.isAvailable)
          .toList();
    } catch (e) {
      developer.log('خطأ في الحصول على المكافآت حسب النوع: $e');
      return [];
    }
  }

  /// الحصول على المكافآت التي يمكن للمستخدم استبدالها
  Future<List<RewardModel>> getAffordableRewards(int userPoints) async {
    try {
      final allRewards = await getAvailableRewards();
      return allRewards
          .where((reward) => reward.pointsRequired <= userPoints)
          .toList();
    } catch (e) {
      developer.log('خطأ في الحصول على المكافآت القابلة للاستبدال: $e');
      return [];
    }
  }

  /// استبدال مكافأة
  Future<Map<String, dynamic>> redeemReward(String rewardId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return {'success': false, 'message': 'المستخدم غير مسجل الدخول'};
      }

      // الحصول على بيانات المكافأة
      final rewardDoc = await _firestore.collection('rewards').doc(rewardId).get();
      if (!rewardDoc.exists) {
        return {'success': false, 'message': 'المكافأة غير موجودة'};
      }

      final reward = RewardModel.fromFirestore(rewardDoc);
      if (!reward.isAvailable) {
        return {'success': false, 'message': 'المكافأة غير متاحة حالياً'};
      }

      // التحقق من نقاط المستخدم
      final loyaltyData = await _loyaltyService.getCurrentUserLoyaltyData();
      if (loyaltyData == null || loyaltyData.points < reward.pointsRequired) {
        return {'success': false, 'message': 'نقاطك غير كافية لاستبدال هذه المكافأة'};
      }

      // التحقق من عدد مرات الاستبدال المسموحة
      if (reward.maxRedemptionsPerUser > 0) {
        final userRedemptions = await getUserRedemptionCount(user.uid, rewardId);
        if (userRedemptions >= reward.maxRedemptionsPerUser) {
          return {'success': false, 'message': 'لقد تجاوزت الحد المسموح لاستبدال هذه المكافأة'};
        }
      }

      // إنشاء رمز كوبون إذا كان مطلوباً
      String? couponCode;
      DateTime? couponExpiryDate;
      if (reward.type == RewardType.adDiscount ||
          reward.type == RewardType.packageDiscount) {
        couponCode = _generateCouponCode();
        couponExpiryDate = DateTime.now().add(
          Duration(days: reward.couponValidityDays ?? 30)
        );
      }

      // إنشاء سجل الاستبدال
      final redemptionId = _firestore.collection('user_redemptions').doc().id;
      final redemption = UserRewardRedemptionModel(
        id: redemptionId,
        userId: user.uid,
        rewardId: rewardId,
        rewardName: reward.name,
        rewardDescription: reward.description,
        rewardType: reward.type.index,
        pointsUsed: reward.pointsRequired,
        rewardValue: reward.value,
        redemptionDate: DateTime.now(),
        status: RedemptionStatus.confirmed,
        couponCode: couponCode,
        couponExpiryDate: couponExpiryDate,
        updatedAt: DateTime.now(),
      );

      // حفظ سجل الاستبدال
      await _firestore
          .collection('user_redemptions')
          .doc(redemptionId)
          .set(redemption.toFirestore());

      // خصم النقاط من المستخدم
      final success = await _loyaltyService.redeemPoints(
        reward.pointsRequired,
        'استبدال مكافأة: ${reward.name}',
      );

      if (!success) {
        // حذف سجل الاستبدال في حالة فشل خصم النقاط
        await _firestore.collection('user_redemptions').doc(redemptionId).delete();
        return {'success': false, 'message': 'فشل في خصم النقاط'};
      }

      // تحديث كمية المكافأة المتاحة
      if (reward.availableQuantity > 0) {
        await _firestore.collection('rewards').doc(rewardId).update({
          'availableQuantity': FieldValue.increment(-1),
          'updatedAt': Timestamp.now(),
        });
      }

      return {
        'success': true,
        'message': 'تم استبدال المكافأة بنجاح',
        'redemption': redemption,
        'couponCode': couponCode,
      };
    } catch (e) {
      developer.log('خطأ في استبدال المكافأة: $e');
      return {'success': false, 'message': 'حدث خطأ أثناء استبدال المكافأة'};
    }
  }

  /// الحصول على عدد مرات استبدال المستخدم لمكافأة معينة
  Future<int> getUserRedemptionCount(String userId, String rewardId) async {
    try {
      final querySnapshot = await _firestore
          .collection('user_redemptions')
          .where('userId', isEqualTo: userId)
          .where('rewardId', isEqualTo: rewardId)
          .where('status', whereIn: [
            RedemptionStatus.confirmed.index,
            RedemptionStatus.delivered.index,
          ])
          .get();

      return querySnapshot.docs.length;
    } catch (e) {
      developer.log('خطأ في الحصول على عدد استبدالات المستخدم: $e');
      return 0;
    }
  }

  /// الحصول على استبدالات المستخدم
  Future<List<UserRewardRedemptionModel>> getUserRedemptions() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final querySnapshot = await _firestore
          .collection('user_redemptions')
          .where('userId', isEqualTo: user.uid)
          .orderBy('redemptionDate', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => UserRewardRedemptionModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      developer.log('خطأ في الحصول على استبدالات المستخدم: $e');
      return [];
    }
  }

  /// الحصول على الكوبونات النشطة للمستخدم
  Future<List<UserRewardRedemptionModel>> getActiveCoupons() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final querySnapshot = await _firestore
          .collection('user_redemptions')
          .where('userId', isEqualTo: user.uid)
          .where('status', whereIn: [
            RedemptionStatus.confirmed.index,
            RedemptionStatus.delivered.index,
          ])
          .get();

      final redemptions = querySnapshot.docs
          .map((doc) => UserRewardRedemptionModel.fromFirestore(doc))
          .where((redemption) => redemption.isCouponValid)
          .toList();

      return redemptions;
    } catch (e) {
      developer.log('خطأ في الحصول على الكوبونات النشطة: $e');
      return [];
    }
  }

  /// توليد رمز كوبون عشوائي
  String _generateCouponCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return 'KREA${List.generate(6, (index) => chars[random.nextInt(chars.length)]).join()}';
  }

  /// التحقق من صحة كوبون
  Future<Map<String, dynamic>> validateCoupon(String couponCode) async {
    try {
      final querySnapshot = await _firestore
          .collection('user_redemptions')
          .where('couponCode', isEqualTo: couponCode)
          .where('status', whereIn: [
            RedemptionStatus.confirmed.index,
            RedemptionStatus.delivered.index,
          ])
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        return {'valid': false, 'message': 'كود الكوبون غير صحيح'};
      }

      final redemption = UserRewardRedemptionModel.fromFirestore(
        querySnapshot.docs.first
      );

      if (!redemption.isCouponValid) {
        return {'valid': false, 'message': 'كود الكوبون منتهي الصلاحية'};
      }

      return {
        'valid': true,
        'redemption': redemption,
        'discount': redemption.rewardValue,
        'minimumAmount': await _getMinimumAmountForCoupon(redemption.rewardId),
      };
    } catch (e) {
      developer.log('خطأ في التحقق من الكوبون: $e');
      return {'valid': false, 'message': 'حدث خطأ أثناء التحقق من الكوبون'};
    }
  }

  /// الحصول على الحد الأدنى للمبلغ لتطبيق الكوبون
  Future<double?> _getMinimumAmountForCoupon(String rewardId) async {
    try {
      final rewardDoc = await _firestore.collection('rewards').doc(rewardId).get();
      if (rewardDoc.exists) {
        final reward = RewardModel.fromFirestore(rewardDoc);
        return reward.minimumAmount;
      }
      return null;
    } catch (e) {
      developer.log('خطأ في الحصول على الحد الأدنى للكوبون: $e');
      return null;
    }
  }

  /// استخدام كوبون
  Future<bool> useCoupon(String couponCode) async {
    try {
      final querySnapshot = await _firestore
          .collection('user_redemptions')
          .where('couponCode', isEqualTo: couponCode)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        await querySnapshot.docs.first.reference.update({
          'status': RedemptionStatus.delivered.index,
          'deliveryDate': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        });
        return true;
      }
      return false;
    } catch (e) {
      developer.log('خطأ في استخدام الكوبون: $e');
      return false;
    }
  }

  /// إنشاء المكافآت الافتراضية
  Future<void> createDefaultRewards() async {
    try {
      final defaultRewards = _getDefaultRewards();

      for (final reward in defaultRewards) {
        final existingDoc = await _firestore
            .collection('rewards')
            .doc(reward.id)
            .get();

        if (!existingDoc.exists) {
          await _firestore
              .collection('rewards')
              .doc(reward.id)
              .set(reward.toFirestore());
        }
      }
    } catch (e) {
      developer.log('خطأ في إنشاء المكافآت الافتراضية: $e');
    }
  }

  /// الحصول على المكافآت الافتراضية
  List<RewardModel> _getDefaultRewards() {
    return [
      // خصومات على الإعلانات
      RewardModel(
        id: 'ad_discount_10',
        name: 'خصم 10% على الإعلانات',
        description: 'احصل على خصم 10% على نشر إعلان جديد',
        type: RewardType.adDiscount,
        pointsRequired: 50,
        value: 10.0,
        imageUrl: 'assets/images/rewards/ad_discount.png',
        color: Colors.green.shade400,
        icon: Icons.local_offer,
        availableQuantity: -1,
        maxRedemptionsPerUser: 5,
        status: RewardStatus.available,
        couponValidityDays: 30,
        minimumAmount: 5.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      RewardModel(
        id: 'ad_discount_20',
        name: 'خصم 20% على الإعلانات',
        description: 'احصل على خصم 20% على نشر إعلان جديد',
        type: RewardType.adDiscount,
        pointsRequired: 100,
        value: 20.0,
        imageUrl: 'assets/images/rewards/ad_discount.png',
        color: Colors.green.shade500,
        icon: Icons.local_offer,
        availableQuantity: -1,
        maxRedemptionsPerUser: 3,
        status: RewardStatus.available,
        couponValidityDays: 30,
        minimumAmount: 10.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // خصومات على الباقات
      RewardModel(
        id: 'package_discount_15',
        name: 'خصم 15% على الباقات',
        description: 'احصل على خصم 15% على شراء أي باقة إعلانات',
        type: RewardType.packageDiscount,
        pointsRequired: 150,
        value: 15.0,
        imageUrl: 'assets/images/rewards/package_discount.png',
        color: Colors.blue.shade400,
        icon: Icons.card_giftcard,
        availableQuantity: -1,
        maxRedemptionsPerUser: 2,
        status: RewardStatus.available,
        couponValidityDays: 45,
        minimumAmount: 20.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // إعلان مجاني
      RewardModel(
        id: 'free_ad_basic',
        name: 'إعلان مجاني',
        description: 'احصل على إعلان مجاني لمدة 7 أيام',
        type: RewardType.freeAd,
        pointsRequired: 200,
        value: 5.0,
        imageUrl: 'assets/images/rewards/free_ad.png',
        color: Colors.orange.shade400,
        icon: Icons.add_circle,
        availableQuantity: 100,
        maxRedemptionsPerUser: 1,
        status: RewardStatus.available,
        couponValidityDays: 60,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // ترقية مجانية
      RewardModel(
        id: 'free_upgrade_featured',
        name: 'ترقية مجانية للإعلان المميز',
        description: 'احصل على ترقية مجانية لجعل إعلانك مميزاً لمدة 3 أيام',
        type: RewardType.freeUpgrade,
        pointsRequired: 300,
        value: 10.0,
        imageUrl: 'assets/images/rewards/free_upgrade.png',
        color: Colors.purple.shade400,
        icon: Icons.star,
        availableQuantity: 50,
        maxRedemptionsPerUser: 2,
        status: RewardStatus.available,
        couponValidityDays: 30,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // نقاط إضافية
      RewardModel(
        id: 'bonus_points_50',
        name: '50 نقطة إضافية',
        description: 'احصل على 50 نقطة إضافية فوراً',
        type: RewardType.bonusPoints,
        pointsRequired: 100,
        value: 50.0,
        imageUrl: 'assets/images/rewards/bonus_points.png',
        color: Colors.amber.shade400,
        icon: Icons.stars,
        availableQuantity: -1,
        maxRedemptionsPerUser: 3,
        status: RewardStatus.available,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // خدمة مميزة
      RewardModel(
        id: 'premium_support',
        name: 'دعم مميز لمدة شهر',
        description: 'احصل على دعم مميز وأولوية في الرد لمدة شهر كامل',
        type: RewardType.premiumService,
        pointsRequired: 500,
        value: 25.0,
        imageUrl: 'assets/images/rewards/premium_support.png',
        color: Colors.indigo.shade400,
        icon: Icons.support_agent,
        availableQuantity: 20,
        maxRedemptionsPerUser: 1,
        status: RewardStatus.available,
        couponValidityDays: 90,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  /// تهيئة النظام وإنشاء المكافآت الافتراضية
  Future<void> initializeRewardsSystem() async {
    try {
      await createDefaultRewards();
      developer.log('تم تهيئة نظام المكافآت بنجاح');
    } catch (e) {
      developer.log('خطأ في تهيئة نظام المكافآت: $e');
    }
  }
}
