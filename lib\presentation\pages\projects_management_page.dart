import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/core/services/notification_service.dart';
import 'package:kuwait_corners/domain/models/notification_model.dart' as domain;
import 'package:kuwait_corners/presentation/widgets/loading_widget.dart';
import 'package:intl/intl.dart';
// import 'package:kuwait_corners/core/services/security_monitoring_service.dart';
import '../widgets/responsive_layout.dart';

/// صفحة إدارة المشاريع العقارية للشركات
class ProjectsManagementPage extends StatefulWidget {
  const ProjectsManagementPage({super.key});

  @override
  State<ProjectsManagementPage> createState() => _ProjectsManagementPageState();
}

class _ProjectsManagementPageState extends State<ProjectsManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedStatus = 'الكل';

  final List<String> _statusOptions = [
    'الكل',
    'قيد التخطيط',
    'قيد التنفيذ',
    'مكتمل',
    'متوقف',
    'ملغي',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllProjectsTab(),
                _buildActiveProjectsTab(),
                _buildCompletedProjectsTab(),
                _buildArchivedProjectsTab(),
              ])),
        ]),
      floatingActionButton: _buildFloatingActionButton());
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'إدارة المشاريع',
        style: CairoTextStyles.appBarTitle),
      backgroundColor: AppColors.primary,
      elevation: 0,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          icon: const Icon(Icons.analytics),
          onPressed: _showProjectAnalytics),
        IconButton(
          icon: const Icon(Icons.calendar_today),
          onPressed: _showProjectTimeline),
      ]);
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1)),
        ]),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث عن مشروع...',
              hintStyle: const TextStyle(),
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                      })
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!)),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary))),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            }),
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: _statusOptions.map((status) {
                final isSelected = _selectedStatus == status;
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(
                      status,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.grey[700],
                        fontSize: 12)),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedStatus = status;
                      });
                    },
                    backgroundColor: Colors.grey[200],
                    selectedColor: AppColors.primary));
              }).toList())),
        ]));
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: AppColors.primary,
        isScrollable: true,
        labelStyle: CairoTextStyles.bodyMedium.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 14),
        unselectedLabelStyle: CairoTextStyles.bodyMedium.copyWith(
          fontWeight: FontWeight.normal,
          fontSize: 13),
        tabs: const [
          Tab(text: 'الكل'),
          Tab(text: 'نشطة'),
          Tab(text: 'مكتملة'),
          Tab(text: 'أرشيف'),
        ]));
  }

  Widget _buildAllProjectsTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getAllProjectsStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل المشاريع');
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyState('لا توجد مشاريع حالياً');
        }

        final projects = snapshot.data!.docs;
        final filteredProjects = _filterProjects(projects);

        return RefreshIndicator(
          onRefresh: () async {
            setState(() {});
          },
          child: ResponsiveLayout(
            mobile: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: filteredProjects.length,
              itemBuilder: (context, index) {
                final projectDoc = filteredProjects[index];
                final projectData = projectDoc.data() as Map<String, dynamic>;
                return _buildProjectCard(projectData, projectDoc.id);
              }),
            tablet: GridView.builder(
              padding: const EdgeInsets.all(16),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 1.2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: filteredProjects.length,
              itemBuilder: (context, index) {
                final projectDoc = filteredProjects[index];
                final projectData = projectDoc.data() as Map<String, dynamic>;
                return _buildProjectCard(projectData, projectDoc.id);
              }),
            desktop: GridView.builder(
              padding: const EdgeInsets.all(24),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 1.3,
                crossAxisSpacing: 24,
                mainAxisSpacing: 24,
              ),
              itemCount: filteredProjects.length,
              itemBuilder: (context, index) {
                final projectDoc = filteredProjects[index];
                final projectData = projectDoc.data() as Map<String, dynamic>;
                return _buildProjectCard(projectData, projectDoc.id);
              }),
          ));
      });
  }

  Widget _buildActiveProjectsTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getActiveProjectsStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل المشاريع النشطة');
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyState('لا توجد مشاريع نشطة');
        }

        final activeProjects = snapshot.data!.docs;

        return RefreshIndicator(
          onRefresh: () async {
            setState(() {});
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: activeProjects.length,
            itemBuilder: (context, index) {
              final projectDoc = activeProjects[index];
              final projectData = projectDoc.data() as Map<String, dynamic>;
              return _buildActiveProjectCard(projectData, projectDoc.id);
            }));
      });
  }

  Widget _buildCompletedProjectsTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getCompletedProjectsStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل المشاريع المكتملة');
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyState('لا توجد مشاريع مكتملة');
        }

        final completedProjects = snapshot.data!.docs;

        return RefreshIndicator(
          onRefresh: () async {
            setState(() {});
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: completedProjects.length,
            itemBuilder: (context, index) {
              final projectDoc = completedProjects[index];
              final projectData = projectDoc.data() as Map<String, dynamic>;
              return _buildCompletedProjectCard(projectData, projectDoc.id);
            }));
      });
  }

  Widget _buildArchivedProjectsTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getArchivedProjectsStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل الأرشيف');
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyState('لا توجد مشاريع في الأرشيف');
        }

        final archivedProjects = snapshot.data!.docs;

        return RefreshIndicator(
          onRefresh: () async {
            setState(() {});
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: archivedProjects.length,
            itemBuilder: (context, index) {
              final projectDoc = archivedProjects[index];
              final projectData = projectDoc.data() as Map<String, dynamic>;
              return _buildArchivedProjectCard(projectData, projectDoc.id);
            }));
      });
  }

  Widget _buildProjectCard(Map<String, dynamic> projectData, String projectId) {
    final name = projectData['name'] ?? 'مشروع غير محدد';
    final status = projectData['status'] ?? 'قيد التخطيط';
    final progress = projectData['progress'] ?? 0.0;
    final priority = projectData['priority'] ?? 'متوسط';
    final startDate = projectData['startDate'] as Timestamp?;
    final type = projectData['type'] ?? 'سكني';

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: InkWell(
        onTap: () => _viewProjectDetails(projectId),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف الأول: العنوان والحالة
              Row(
                children: [
                  Expanded(
                    child: Text(
                      name,
                      style: CairoTextStyles.cardTitle,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 12),
                  _buildStatusChip(status),
                ],
              ),
              const SizedBox(height: 8),

              // الصف الثاني: النوع والأولوية
              Row(
                children: [
                  Icon(Icons.category, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 6),
                  Text(
                    type,
                    style: CairoTextStyles.cardSubtitle,
                  ),
                  const Spacer(),
                  _buildPriorityChip(priority),
                ],
              ),
              const SizedBox(height: 12),

              // شريط التقدم
              Row(
                children: [
                  Icon(Icons.timeline, size: 16, color: AppColors.primary),
                  const SizedBox(width: 6),
                  Text(
                    'التقدم',
                    style: CairoTextStyles.bodySmall.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${progress.toStringAsFixed(0)}%',
                    style: CairoTextStyles.bodySmall.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 6),
              LinearProgressIndicator(
                value: progress / 100,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  progress >= 75 ? Colors.green :
                  progress >= 50 ? AppColors.primary : Colors.orange,
                ),
              ),
              const SizedBox(height: 12),

              // تاريخ البداية
              if (startDate != null)
                Row(
                  children: [
                    Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 6),
                    Text(
                      'بدء: ${_formatDate(startDate.toDate())}',
                      style: CairoTextStyles.bodySmall,
                    ),
                  ],
                ),

              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _viewProjectDetails(projectId),
                      icon: const Icon(Icons.visibility, size: 16),
                      label: Text(
                        'التفاصيل',
                        style: CairoTextStyles.button.copyWith(fontSize: 14),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _editProject(projectId),
                      icon: const Icon(Icons.edit, size: 16),
                      label: Text(
                        'تعديل',
                        style: CairoTextStyles.button.copyWith(
                          fontSize: 14,
                          color: AppColors.primary,
                        ),
                      ),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.primary,
                        side: BorderSide(color: AppColors.primary),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleProjectAction(value, projectId),
                    icon: Icon(Icons.more_vert, color: Colors.grey[600]),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'team',
                        child: Row(
                          children: [
                            const Icon(Icons.group, size: 16),
                            const SizedBox(width: 8),
                            Text('الفريق', style: CairoTextStyles.bodyMedium),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'tasks',
                        child: Row(
                          children: [
                            const Icon(Icons.task_alt, size: 16),
                            const SizedBox(width: 8),
                            Text('المهام', style: CairoTextStyles.bodyMedium),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'timeline',
                        child: Row(
                          children: [
                            Icon(Icons.timeline, size: 16, color: AppColors.primary),
                            const SizedBox(width: 8),
                            Text('الجدول الزمني', style: CairoTextStyles.bodyMedium),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'analytics',
                        child: Row(
                          children: [
                            Icon(Icons.analytics, size: 16, color: Colors.blue),
                            const SizedBox(width: 8),
                            Text('التحليلات', style: CairoTextStyles.bodyMedium),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'archive',
                        child: Row(
                          children: [
                            const Icon(Icons.archive, size: 16),
                            const SizedBox(width: 8),
                            Text('أرشفة', style: CairoTextStyles.bodyMedium),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            const Icon(Icons.delete, size: 16, color: Colors.red),
                            const SizedBox(width: 8),
                            Text(
                              'حذف',
                              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.red),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActiveProjectCard(Map<String, dynamic> projectData, String projectId) {
    return _buildProjectCard(projectData, projectId);
  }

  Widget _buildCompletedProjectCard(Map<String, dynamic> projectData, String projectId) {
    return _buildProjectCard(projectData, projectId);
  }

  Widget _buildArchivedProjectCard(Map<String, dynamic> projectData, String projectId) {
    return _buildProjectCard(projectData, projectId);
  }

  Widget _buildStatusChip(String status) {
    Color color;
    switch (status) {
      case 'قيد التنفيذ':
        color = Colors.blue;
        break;
      case 'مكتمل':
        color = Colors.green;
        break;
      case 'متوقف':
        color = Colors.orange;
        break;
      case 'ملغي':
        color = Colors.red;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color)),
      child: Text(
        status,
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.w500)));
  }

  Widget _buildPriorityChip(String priority) {
    Color color;
    IconData icon;
    switch (priority) {
      case 'عالي':
        color = Colors.red;
        icon = Icons.keyboard_arrow_up;
        break;
      case 'متوسط':
        color = Colors.orange;
        icon = Icons.remove;
        break;
      case 'منخفض':
        color = Colors.green;
        icon = Icons.keyboard_arrow_down;
        break;
      default:
        color = Colors.grey;
        icon = Icons.remove;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3))),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 2),
          Text(
            priority,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w600)),
        ]));
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.business_center_outlined,
            size: 80,
            color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600])),
        ]));
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red[600])),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              setState(() {});
            },
            child: const Text('إعادة المحاولة')),
        ]));
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _addNewProject,
      backgroundColor: AppColors.primary,
      child: const Icon(Icons.add, color: Colors.white));
  }

  // Firebase Streams
  Stream<QuerySnapshot> _getAllProjectsStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    // محاولة البحث بـ userId أولاً، ثم companyId إذا لم توجد نتائج
    return FirebaseFirestore.instance
        .collection('projects')
        .where('userId', isEqualTo: currentUser.uid)
        .orderBy('createdAt', descending: true)
        .snapshots();
  }

  Stream<QuerySnapshot> _getActiveProjectsStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    return FirebaseFirestore.instance
        .collection('projects')
        .where('userId', isEqualTo: currentUser.uid)
        .where('status', isEqualTo: 'قيد التنفيذ')
        .orderBy('startDate', descending: false)
        .snapshots();
  }

  Stream<QuerySnapshot> _getCompletedProjectsStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    return FirebaseFirestore.instance
        .collection('projects')
        .where('userId', isEqualTo: currentUser.uid)
        .where('status', isEqualTo: 'مكتمل')
        .orderBy('endDate', descending: true)
        .snapshots();
  }

  Stream<QuerySnapshot> _getArchivedProjectsStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    return FirebaseFirestore.instance
        .collection('projects')
        .where('userId', isEqualTo: currentUser.uid)
        .where('status', isEqualTo: 'مؤرشف')
        .orderBy('archivedAt', descending: true)
        .snapshots();
  }

  List<QueryDocumentSnapshot> _filterProjects(List<QueryDocumentSnapshot> projects) {
    List<QueryDocumentSnapshot> filtered = projects;

    // تطبيق فلتر الحالة
    if (_selectedStatus != 'الكل') {
      filtered = filtered.where((project) {
        final data = project.data() as Map<String, dynamic>;
        final status = data['status'] ?? '';
        return status == _selectedStatus;
      }).toList();
    }

    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      final searchTerm = _searchQuery.toLowerCase();
      filtered = filtered.where((project) {
        final data = project.data() as Map<String, dynamic>;
        final name = (data['name'] ?? '').toString().toLowerCase();
        final description = (data['description'] ?? '').toString().toLowerCase();

        return name.contains(searchTerm) || description.contains(searchTerm);
      }).toList();
    }

    return filtered;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Action Methods
  void _addNewProject() {
    Navigator.pushNamed(context, '/add-project');
  }

  void _viewProjectDetails(String projectId) {
    Navigator.pushNamed(context, '/project-details', arguments: projectId);
  }

  void _editProject(String projectId) {
    Navigator.pushNamed(context, '/edit-project', arguments: projectId);
  }

  void _handleProjectAction(String action, String projectId) {
    switch (action) {
      case 'edit':
        _editProject(projectId);
        break;
      case 'analytics':
        _showProjectAnalytics();
        break;
      case 'timeline':
        _showProjectTimeline(projectId);
        break;
      case 'team':
        Navigator.pushNamed(context, '/project-team', arguments: projectId);
        break;
      case 'tasks':
        Navigator.pushNamed(context, '/project-tasks', arguments: projectId);
        break;
      case 'archive':
        _archiveProject(projectId);
        break;
      case 'delete':
        _deleteProject(projectId);
        break;
    }
  }

  void _archiveProject(String projectId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد الأرشفة',
          style: CairoTextStyles.titleMedium,
        ),
        content: Text(
          'هل أنت متأكد من أرشفة هذا المشروع؟',
          style: CairoTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'إلغاء',
              style: CairoTextStyles.bodyMedium,
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'أرشفة',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.orange),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await FirebaseFirestore.instance
            .collection('projects')
            .doc(projectId)
            .update({
          'status': 'مؤرشف',
          'archivedAt': FieldValue.serverTimestamp(),
        });

        if (mounted) {
          // عرض إشعار فوري
          NotificationService.showInAppNotification(
            context,
            title: 'تم أرشفة المشروع',
            body: 'تم نقل المشروع إلى الأرشيف بنجاح',
            type: domain.NotificationType.system,
            duration: const Duration(seconds: 3),
          );

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم أرشفة المشروع بنجاح',
                style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'حدث خطأ في أرشفة المشروع',
                style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _deleteProject(String projectId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد الحذف',
          style: CairoTextStyles.titleMedium,
        ),
        content: Text(
          'هل أنت متأكد من حذف هذا المشروع؟ لا يمكن التراجع عن هذا الإجراء.',
          style: CairoTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'إلغاء',
              style: CairoTextStyles.bodyMedium,
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'حذف',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await FirebaseFirestore.instance
            .collection('projects')
            .doc(projectId)
            .delete();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم حذف المشروع بنجاح',
                style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'حدث خطأ في حذف المشروع',
                style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _showProjectAnalytics() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20))),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2))),
            // Header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'تحليلات المشاريع المحسنة',
                      style: CairoTextStyles.headlineMedium)),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context)),
                ])),
            const Divider(),
            // Content
            Expanded(
              child: FutureBuilder<Map<String, dynamic>>(
                future: _getProjectAnalyticsFromProvider(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const LoadingWidget();
                  }

                  if (snapshot.hasError) {
                    return Center(
                      child: Text('حدث خطأ في تحميل التحليلات',
                        style: CairoTextStyles.bodyMedium));
                  }

                  final analytics = snapshot.data ?? {};

                  return SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildEnhancedAnalyticsOverview(analytics),
                        const SizedBox(height: 24),
                        _buildProjectStatusChart(analytics),
                        const SizedBox(height: 24),
                        _buildProgressAnalysis(analytics),
                      ]));
                })),
          ])),
    );
  }

  void _showProjectTimeline([String? projectId]) {
    if (projectId != null) {
      // عرض الجدول الزمني لمشروع محدد
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => _buildProjectTimelineSheet(projectId),
      );
    } else {
      // عرض الجدول الزمني العام لجميع المشاريع
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => _buildGeneralTimelineSheet(),
      );
    }
  }





  Map<String, dynamic> _calculateProjectAnalytics(List<QueryDocumentSnapshot> projects) {
    Map<String, int> statusDistribution = {};
    Map<String, int> priorityDistribution = {};
    Map<String, int> typeDistribution = {};

    double totalBudget = 0;
    double totalSpent = 0;
    double totalProgress = 0;
    int urgentProjects = 0;
    int completedProjects = 0;
    int activeProjects = 0;

    for (var project in projects) {
      final data = project.data() as Map<String, dynamic>;

      // توزيع الحالات
      final status = data['status'] ?? 'قيد التخطيط';
      statusDistribution[status] = (statusDistribution[status] ?? 0) + 1;

      // توزيع الأولويات
      final priority = data['priority'] ?? 'متوسط';
      priorityDistribution[priority] = (priorityDistribution[priority] ?? 0) + 1;

      // توزيع الأنواع
      final type = data['type'] ?? 'سكني';
      typeDistribution[type] = (typeDistribution[type] ?? 0) + 1;

      // الميزانيات
      totalBudget += (data['budget'] ?? 0).toDouble();
      totalSpent += (data['spentBudget'] ?? 0).toDouble();
      totalProgress += (data['progress'] ?? 0).toDouble();

      // العدادات
      if (data['isUrgent'] == true) urgentProjects++;
      if (status == 'مكتمل') completedProjects++;
      if (status == 'قيد التنفيذ') activeProjects++;
    }

    // حساب المشاريع المتأخرة (المشاريع التي تجاوزت تاريخ الانتهاء المتوقع)
    int delayedProjects = 0;
    final now = DateTime.now();
    for (var project in projects) {
      final data = project.data() as Map<String, dynamic>;
      final endDateStr = data['endDate'];
      if (endDateStr != null) {
        try {
          final endDate = DateTime.parse(endDateStr);
          if (endDate.isBefore(now) && data['status'] != 'مكتمل') {
            delayedProjects++;
          }
        } catch (e) {
          // تجاهل الأخطاء في تحليل التاريخ
        }
      }
    }

    return {
      'totalProjects': projects.length,
      'statusDistribution': statusDistribution,
      'priorityDistribution': priorityDistribution,
      'typeDistribution': typeDistribution,
      'totalBudget': totalBudget,
      'totalSpent': totalSpent,
      'averageProgress': projects.isNotEmpty ? totalProgress / projects.length : 0,
      'urgentProjects': urgentProjects,
      'completedProjects': completedProjects,
      'activeProjects': activeProjects,
      'delayedProjects': delayedProjects,
      'completionRate': projects.isNotEmpty ? (completedProjects / projects.length * 100) : 0,
    };
  }

  Widget _buildQuickStats(Map<String, dynamic> analytics) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي المشاريع',
                  '${analytics['totalProjects']}',
                  Icons.business_center,
                  Colors.white,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'المشاريع النشطة',
                  '${analytics['activeProjects']}',
                  Icons.play_arrow,
                  Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'معدل الإنجاز',
                  '${analytics['completionRate'].toStringAsFixed(1)}%',
                  Icons.check_circle,
                  Colors.white,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'المشاريع العاجلة',
                  '${analytics['urgentProjects']}',
                  Icons.priority_high,
                  Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color textColor) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: textColor, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: CairoTextStyles.headlineSmall.copyWith(
              color: textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: CairoTextStyles.bodySmall.copyWith(
              color: textColor.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStatusDistribution(Map<String, int> statusDistribution) {
    if (statusDistribution.isEmpty) {
      return const Text('لا توجد بيانات');
    }

    return Column(
      children: statusDistribution.entries.map((entry) {
        final total = statusDistribution.values.reduce((a, b) => a + b);
        final percentage = (entry.value / total * 100).round();

        Color color;
        switch (entry.key) {
          case 'قيد التنفيذ':
            color = Colors.blue;
            break;
          case 'مكتمل':
            color = Colors.green;
            break;
          case 'متوقف':
            color = Colors.orange;
            break;
          case 'ملغي':
            color = Colors.red;
            break;
          default:
            color = Colors.grey;
        }

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: Row(
            children: [
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          entry.key,
                          style: CairoTextStyles.bodyMedium,
                        ),
                        Text(
                          '${entry.value} ($percentage%)',
                          style: CairoTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: entry.value / total,
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation(color),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildBudgetAnalysis(Map<String, dynamic> analytics) {
    final totalBudget = analytics['totalBudget'] as double;
    final totalSpent = analytics['totalSpent'] as double;
    final remaining = totalBudget - totalSpent;
    final spentPercentage = totalBudget > 0 ? (totalSpent / totalBudget * 100) : 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildBudgetItem(
                  'إجمالي الميزانيات',
                  '${totalBudget.toStringAsFixed(0)} د.ك',
                  AppColors.primary,
                  Icons.account_balance_wallet,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildBudgetItem(
                  'المبلغ المُنفق',
                  '${totalSpent.toStringAsFixed(0)} د.ك',
                  Colors.orange,
                  Icons.money_off,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildBudgetItem(
                  'المبلغ المتبقي',
                  '${remaining.toStringAsFixed(0)} د.ك',
                  Colors.green,
                  Icons.savings,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildBudgetItem(
                  'نسبة الإنفاق',
                  '${spentPercentage.toStringAsFixed(1)}%',
                  spentPercentage > 80 ? Colors.red : Colors.blue,
                  Icons.pie_chart,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: spentPercentage / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation(
              spentPercentage > 80 ? Colors.red : Colors.blue,
            ),
            minHeight: 8,
          ),
        ],
      ),
    );
  }

  Widget _buildBudgetItem(String title, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: CairoTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: CairoTextStyles.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityAnalysis(Map<String, int> priorityDistribution) {
    if (priorityDistribution.isEmpty) {
      return const Text('لا توجد بيانات');
    }

    return Row(
      children: priorityDistribution.entries.map((entry) {
        Color color;
        IconData icon;
        switch (entry.key) {
          case 'عالي':
            color = Colors.red;
            icon = Icons.keyboard_arrow_up;
            break;
          case 'متوسط':
            color = Colors.orange;
            icon = Icons.remove;
            break;
          case 'منخفض':
            color = Colors.green;
            icon = Icons.keyboard_arrow_down;
            break;
          default:
            color = Colors.grey;
            icon = Icons.remove;
        }

        return Expanded(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Column(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(height: 8),
                Text(
                  '${entry.value}',
                  style: CairoTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  entry.key,
                  style: CairoTextStyles.bodySmall.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildTypeAnalysis(Map<String, int> typeDistribution) {
    if (typeDistribution.isEmpty) {
      return const Text('لا توجد بيانات');
    }

    return Column(
      children: typeDistribution.entries.map((entry) {
        final total = typeDistribution.values.reduce((a, b) => a + b);
        final percentage = (entry.value / total * 100).round();

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: AppColors.primary.withValues(alpha: 0.1),
              child: Icon(
                Icons.business,
                color: AppColors.primary,
                size: 20,
              ),
            ),
            title: Text(entry.key),
            subtitle: Text('${entry.value} مشروع'),
            trailing: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '$percentage%',
                style: TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildProjectTimelineSheet(String projectId) {
    return DraggableScrollableSheet(
      initialChildSize: 0.8,
      maxChildSize: 0.95,
      minChildSize: 0.5,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.timeline, color: AppColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      'الجدول الزمني للمشروع',
                      style: CairoTextStyles.headlineSmall.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              const Divider(),
              // Content
              Expanded(
                child: FutureBuilder<DocumentSnapshot>(
                  future: FirebaseFirestore.instance
                      .collection('projects')
                      .doc(projectId)
                      .get(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const LoadingWidget();
                    }

                    if (!snapshot.hasData || !snapshot.data!.exists) {
                      return const Center(
                        child: Text('لم يتم العثور على المشروع'),
                      );
                    }

                    final projectData = snapshot.data!.data() as Map<String, dynamic>;
                    return _buildProjectTimelineContent(projectData, scrollController);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildGeneralTimelineSheet() {
    return DraggableScrollableSheet(
      initialChildSize: 0.8,
      maxChildSize: 0.95,
      minChildSize: 0.5,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Header
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.calendar_view_month, color: AppColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      'الجدول الزمني العام',
                      style: CairoTextStyles.headlineSmall.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              const Divider(),
              // Content
              Expanded(
                child: StreamBuilder<QuerySnapshot>(
                  stream: _getAllProjectsStream(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const LoadingWidget();
                    }

                    if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                      return const Center(
                        child: Text('لا توجد مشاريع'),
                      );
                    }

                    final projects = snapshot.data!.docs;
                    return _buildGeneralTimelineContent(projects, scrollController);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProjectTimelineContent(Map<String, dynamic> projectData, ScrollController scrollController) {
    final startDate = projectData['startDate'] as Timestamp?;
    final endDate = projectData['endDate'] as Timestamp?;
    final progress = projectData['progress'] ?? 0.0;
    final name = projectData['name'] ?? 'مشروع غير محدد';

    return SingleChildScrollView(
      controller: scrollController,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات المشروع
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: CairoTextStyles.headlineSmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.timeline, color: Colors.white, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      'التقدم: ${progress.toStringAsFixed(1)}%',
                      style: CairoTextStyles.bodyMedium.copyWith(
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // الجدول الزمني
          if (startDate != null && endDate != null) ...[
            Text(
              'الجدول الزمني',
              style: CairoTextStyles.headlineSmall,
            ),
            const SizedBox(height: 16),
            _buildTimelineItem(
              'بداية المشروع',
              startDate.toDate(),
              Icons.play_arrow,
              Colors.green,
              true,
            ),
            _buildTimelineItem(
              'نهاية المشروع',
              endDate.toDate(),
              Icons.flag,
              Colors.red,
              false,
            ),
            const SizedBox(height: 24),
          ],

          // مراحل المشروع (محاكاة)
          Text(
            'مراحل المشروع',
            style: CairoTextStyles.headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildProjectPhases(progress),
        ],
      ),
    );
  }

  Widget _buildGeneralTimelineContent(List<QueryDocumentSnapshot> projects, ScrollController scrollController) {
    // ترتيب المشاريع حسب تاريخ البداية
    final sortedProjects = projects.where((project) {
      final data = project.data() as Map<String, dynamic>;
      return data['startDate'] != null;
    }).toList();

    sortedProjects.sort((a, b) {
      final aData = a.data() as Map<String, dynamic>;
      final bData = b.data() as Map<String, dynamic>;
      final aDate = (aData['startDate'] as Timestamp).toDate();
      final bDate = (bData['startDate'] as Timestamp).toDate();
      return aDate.compareTo(bDate);
    });

    return SingleChildScrollView(
      controller: scrollController,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'جدولة المشاريع',
            style: CairoTextStyles.headlineSmall,
          ),
          const SizedBox(height: 16),
          ...sortedProjects.map((project) {
            final data = project.data() as Map<String, dynamic>;
            return _buildTimelineProjectCard(data, project.id);
          }),
        ],
      ),
    );
  }

  Widget _buildTimelineItem(String title, DateTime date, IconData icon, Color color, bool isCompleted) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isCompleted ? color : color.withValues(alpha: 0.3),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: CairoTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  DateFormat('dd/MM/yyyy').format(date),
                  style: CairoTextStyles.bodyMedium.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProjectPhases(double progress) {
    final phases = [
      {'name': 'التخطيط', 'percentage': 20.0},
      {'name': 'التصميم', 'percentage': 40.0},
      {'name': 'التنفيذ', 'percentage': 80.0},
      {'name': 'الإنجاز', 'percentage': 100.0},
    ];

    return Column(
      children: phases.map((phase) {
        final percentage = phase['percentage'] as double;
        final isCompleted = progress >= percentage;
        final isActive = progress >= (percentage - 20) && progress < percentage;

        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isCompleted
                ? Colors.green.withValues(alpha: 0.1)
                : isActive
                    ? AppColors.primary.withValues(alpha: 0.1)
                    : Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isCompleted
                  ? Colors.green
                  : isActive
                      ? AppColors.primary
                      : Colors.grey,
            ),
          ),
          child: Row(
            children: [
              Icon(
                isCompleted ? Icons.check_circle : isActive ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                color: isCompleted
                    ? Colors.green
                    : isActive
                        ? AppColors.primary
                        : Colors.grey,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  phase['name'] as String,
                  style: CairoTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isCompleted
                        ? Colors.green
                        : isActive
                            ? AppColors.primary
                            : Colors.grey,
                  ),
                ),
              ),
              Text(
                '${percentage.toInt()}%',
                style: CairoTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isCompleted
                      ? Colors.green
                      : isActive
                          ? AppColors.primary
                          : Colors.grey,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildTimelineProjectCard(Map<String, dynamic> data, String projectId) {
    final name = data['name'] ?? 'مشروع غير محدد';
    final startDate = data['startDate'] as Timestamp?;
    final endDate = data['endDate'] as Timestamp?;
    final progress = data['progress'] ?? 0.0;
    final status = data['status'] ?? 'قيد التخطيط';

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          Navigator.pop(context);
          _showProjectTimeline(projectId);
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      name,
                      style: CairoTextStyles.bodyLarge.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildStatusChip(status),
                ],
              ),
              const SizedBox(height: 8),
              if (startDate != null && endDate != null) ...[
                Row(
                  children: [
                    Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      '${DateFormat('dd/MM').format(startDate.toDate())} - ${DateFormat('dd/MM').format(endDate.toDate())}',
                      style: CairoTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],
              LinearProgressIndicator(
                value: progress / 100,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation(AppColors.primary),
                minHeight: 4,
              ),
              const SizedBox(height: 4),
              Text(
                '${progress.toStringAsFixed(1)}% مكتمل',
                style: CairoTextStyles.bodySmall.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// الحصول على تحليلات المشاريع من المزود
  Future<Map<String, dynamic>> _getProjectAnalyticsFromProvider() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        return {
          'totalProjects': 0,
          'activeProjects': 0,
          'completedProjects': 0,
          'delayedProjects': 0,
          'averageProgress': 0.0,
          'statusDistribution': <String, int>{},
        };
      }

      final snapshot = await FirebaseFirestore.instance
          .collection('projects')
          .where('userId', isEqualTo: currentUser.uid)
          .get();

      if (snapshot.docs.isEmpty) {
        return {
          'totalProjects': 0,
          'activeProjects': 0,
          'completedProjects': 0,
          'delayedProjects': 0,
          'averageProgress': 0.0,
          'statusDistribution': <String, int>{},
        };
      }

      return _calculateProjectAnalytics(snapshot.docs);
    } catch (e) {
      // إرجاع بيانات افتراضية في حالة الخطأ
      return {
        'totalProjects': 0,
        'activeProjects': 0,
        'completedProjects': 0,
        'delayedProjects': 0,
        'averageProgress': 0.0,
        'statusDistribution': <String, int>{},
      };
    }
  }

  /// بناء نظرة عامة محسنة للتحليلات
  Widget _buildEnhancedAnalyticsOverview(Map<String, dynamic> analytics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('نظرة عامة', style: CairoTextStyles.headlineSmall),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildAnalyticsCard(
                'إجمالي المشاريع',
                '${analytics['totalProjects'] ?? 0}',
                Icons.folder,
                AppColors.primary)),
            const SizedBox(width: 12),
            Expanded(
              child: _buildAnalyticsCard(
                'المشاريع النشطة',
                '${analytics['activeProjects'] ?? 0}',
                Icons.play_circle,
                Colors.blue)),
          ]),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildAnalyticsCard(
                'المشاريع المكتملة',
                '${analytics['completedProjects'] ?? 0}',
                Icons.check_circle,
                Colors.green)),
            const SizedBox(width: 12),
            Expanded(
              child: _buildAnalyticsCard(
                'المشاريع المتأخرة',
                '${analytics['delayedProjects'] ?? 0}',
                Icons.warning,
                Colors.orange)),
          ]),
      ]);
  }

  /// بناء كارت التحليلات
  Widget _buildAnalyticsCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const Spacer(),
              Text(value,
                style: CairoTextStyles.headlineSmall.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold)),
            ]),
          const SizedBox(height: 8),
          Text(title,
            style: CairoTextStyles.bodySmall.copyWith(color: Colors.grey[600]),
            maxLines: 2,
            overflow: TextOverflow.ellipsis),
        ]));
  }

  /// بناء مخطط حالة المشاريع
  Widget _buildProjectStatusChart(Map<String, dynamic> analytics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('توزيع حالة المشاريع', style: CairoTextStyles.headlineSmall),
        const SizedBox(height: 16),
        Container(
          height: 200,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12)),
          child: Center(
            child: Text('سيتم إضافة المخطط البياني قريباً',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600])))),
      ]);
  }

  /// بناء تحليل التقدم
  Widget _buildProgressAnalysis(Map<String, dynamic> analytics) {
    final averageProgress = analytics['averageProgress'] ?? 0.0;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('تحليل التقدم', style: CairoTextStyles.headlineSmall),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12)),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text('متوسط التقدم العام',
                      style: CairoTextStyles.bodyMedium)),
                  Text('${averageProgress.toStringAsFixed(1)}%',
                    style: CairoTextStyles.headlineSmall.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold)),
                ]),
              const SizedBox(height: 12),
              LinearProgressIndicator(
                value: averageProgress / 100,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation(AppColors.primary),
                minHeight: 8),
            ])),
      ]);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }
}
