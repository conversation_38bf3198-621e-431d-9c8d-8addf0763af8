import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// أنواع التفاعل مع العميل
enum InteractionType {
  call,           // مكالمة هاتفية
  meeting,        // اجتماع
  email,          // بريد إلكتروني
  whatsapp,       // واتساب
  sms,            // رسالة نصية
  visit,          // زيارة
  propertyViewing, // معاينة عقار
  followUp,       // متابعة
  complaint,      // شكوى
  inquiry,        // استفسار
  offer,          // عرض
  contract,       // عقد
  payment,        // دفعة
  other,          // أخرى
}

/// حالة التفاعل
enum InteractionStatus {
  pending,        // معلق
  completed,      // مكتمل
  cancelled,      // ملغي
  rescheduled,    // مؤجل
  inProgress,     // قيد التنفيذ
}

/// نتيجة التفاعل
enum InteractionOutcome {
  positive,       // إيجابية
  negative,       // سلبية
  neutral,        // محايدة
  interested,     // مهتم
  notInterested,  // غير مهتم
  needsFollowUp,  // يحتاج متابعة
  converted,      // تم التحويل
  lost,           // فقدان العميل
}

/// نموذج التفاعل مع العميل
class ClientInteraction extends Equatable {
  /// معرف التفاعل
  final String id;
  
  /// معرف العميل
  final String clientId;
  
  /// اسم العميل
  final String clientName;
  
  /// معرف الوكيل/الموظف
  final String agentId;
  
  /// اسم الوكيل/الموظف
  final String agentName;
  
  /// نوع التفاعل
  final InteractionType type;
  
  /// حالة التفاعل
  final InteractionStatus status;
  
  /// نتيجة التفاعل
  final InteractionOutcome? outcome;
  
  /// عنوان التفاعل
  final String title;
  
  /// وصف التفاعل
  final String description;
  
  /// ملاحظات
  final String? notes;
  
  /// تاريخ ووقت التفاعل
  final DateTime interactionDate;
  
  /// مدة التفاعل (بالدقائق)
  final int? duration;
  
  /// معرف العقار المرتبط (إن وجد)
  final String? relatedEstateId;
  
  /// اسم العقار المرتبط
  final String? relatedEstateName;
  
  /// معرف المشروع المرتبط (إن وجد)
  final String? relatedProjectId;
  
  /// اسم المشروع المرتبط
  final String? relatedProjectName;
  
  /// قائمة المرفقات
  final List<String> attachments;
  
  /// تكلفة التفاعل (إن وجدت)
  final double? cost;
  
  /// العائد المتوقع من التفاعل
  final double? expectedRevenue;
  
  /// العائد الفعلي من التفاعل
  final double? actualRevenue;
  
  /// تاريخ المتابعة التالية
  final DateTime? nextFollowUpDate;
  
  /// أولوية المتابعة
  final String? followUpPriority;
  
  /// العلامات
  final List<String> tags;
  
  /// تقييم العميل للتفاعل (1-5)
  final int? clientRating;
  
  /// تعليق العميل
  final String? clientFeedback;
  
  /// هل تم إرسال تذكير
  final bool reminderSent;
  
  /// تاريخ الإنشاء
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث
  final DateTime updatedAt;
  
  /// معلومات إضافية
  final Map<String, dynamic>? additionalInfo;

  const ClientInteraction({
    required this.id,
    required this.clientId,
    required this.clientName,
    required this.agentId,
    required this.agentName,
    required this.type,
    required this.status,
    this.outcome,
    required this.title,
    required this.description,
    this.notes,
    required this.interactionDate,
    this.duration,
    this.relatedEstateId,
    this.relatedEstateName,
    this.relatedProjectId,
    this.relatedProjectName,
    this.attachments = const [],
    this.cost,
    this.expectedRevenue,
    this.actualRevenue,
    this.nextFollowUpDate,
    this.followUpPriority,
    this.tags = const [],
    this.clientRating,
    this.clientFeedback,
    this.reminderSent = false,
    required this.createdAt,
    required this.updatedAt,
    this.additionalInfo,
  });

  /// إنشاء نسخة معدلة من التفاعل
  ClientInteraction copyWith({
    String? id,
    String? clientId,
    String? clientName,
    String? agentId,
    String? agentName,
    InteractionType? type,
    InteractionStatus? status,
    InteractionOutcome? outcome,
    String? title,
    String? description,
    String? notes,
    DateTime? interactionDate,
    int? duration,
    String? relatedEstateId,
    String? relatedEstateName,
    String? relatedProjectId,
    String? relatedProjectName,
    List<String>? attachments,
    double? cost,
    double? expectedRevenue,
    double? actualRevenue,
    DateTime? nextFollowUpDate,
    String? followUpPriority,
    List<String>? tags,
    int? clientRating,
    String? clientFeedback,
    bool? reminderSent,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalInfo,
  }) {
    return ClientInteraction(
      id: id ?? this.id,
      clientId: clientId ?? this.clientId,
      clientName: clientName ?? this.clientName,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      type: type ?? this.type,
      status: status ?? this.status,
      outcome: outcome ?? this.outcome,
      title: title ?? this.title,
      description: description ?? this.description,
      notes: notes ?? this.notes,
      interactionDate: interactionDate ?? this.interactionDate,
      duration: duration ?? this.duration,
      relatedEstateId: relatedEstateId ?? this.relatedEstateId,
      relatedEstateName: relatedEstateName ?? this.relatedEstateName,
      relatedProjectId: relatedProjectId ?? this.relatedProjectId,
      relatedProjectName: relatedProjectName ?? this.relatedProjectName,
      attachments: attachments ?? this.attachments,
      cost: cost ?? this.cost,
      expectedRevenue: expectedRevenue ?? this.expectedRevenue,
      actualRevenue: actualRevenue ?? this.actualRevenue,
      nextFollowUpDate: nextFollowUpDate ?? this.nextFollowUpDate,
      followUpPriority: followUpPriority ?? this.followUpPriority,
      tags: tags ?? this.tags,
      clientRating: clientRating ?? this.clientRating,
      clientFeedback: clientFeedback ?? this.clientFeedback,
      reminderSent: reminderSent ?? this.reminderSent,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalInfo: additionalInfo ?? this.additionalInfo,
    );
  }

  /// تحويل التفاعل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'clientId': clientId,
      'clientName': clientName,
      'agentId': agentId,
      'agentName': agentName,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'outcome': outcome?.toString().split('.').last,
      'title': title,
      'description': description,
      'notes': notes,
      'interactionDate': Timestamp.fromDate(interactionDate),
      'duration': duration,
      'relatedEstateId': relatedEstateId,
      'relatedEstateName': relatedEstateName,
      'relatedProjectId': relatedProjectId,
      'relatedProjectName': relatedProjectName,
      'attachments': attachments,
      'cost': cost,
      'expectedRevenue': expectedRevenue,
      'actualRevenue': actualRevenue,
      'nextFollowUpDate': nextFollowUpDate != null ? Timestamp.fromDate(nextFollowUpDate!) : null,
      'followUpPriority': followUpPriority,
      'tags': tags,
      'clientRating': clientRating,
      'clientFeedback': clientFeedback,
      'reminderSent': reminderSent,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'additionalInfo': additionalInfo,
    };
  }

  /// إنشاء تفاعل من Map
  factory ClientInteraction.fromMap(Map<String, dynamic> map) {
    return ClientInteraction(
      id: map['id'] ?? '',
      clientId: map['clientId'] ?? '',
      clientName: map['clientName'] ?? '',
      agentId: map['agentId'] ?? '',
      agentName: map['agentName'] ?? '',
      type: InteractionType.values.firstWhere(
        (e) => e.toString().split('.').last == map['type'],
        orElse: () => InteractionType.other,
      ),
      status: InteractionStatus.values.firstWhere(
        (e) => e.toString().split('.').last == map['status'],
        orElse: () => InteractionStatus.pending,
      ),
      outcome: map['outcome'] != null 
          ? InteractionOutcome.values.firstWhere(
              (e) => e.toString().split('.').last == map['outcome'],
              orElse: () => InteractionOutcome.neutral,
            )
          : null,
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      notes: map['notes'],
      interactionDate: map['interactionDate'] is Timestamp 
          ? (map['interactionDate'] as Timestamp).toDate() 
          : DateTime.now(),
      duration: map['duration'],
      relatedEstateId: map['relatedEstateId'],
      relatedEstateName: map['relatedEstateName'],
      relatedProjectId: map['relatedProjectId'],
      relatedProjectName: map['relatedProjectName'],
      attachments: List<String>.from(map['attachments'] ?? []),
      cost: map['cost']?.toDouble(),
      expectedRevenue: map['expectedRevenue']?.toDouble(),
      actualRevenue: map['actualRevenue']?.toDouble(),
      nextFollowUpDate: map['nextFollowUpDate'] is Timestamp 
          ? (map['nextFollowUpDate'] as Timestamp).toDate() 
          : null,
      followUpPriority: map['followUpPriority'],
      tags: List<String>.from(map['tags'] ?? []),
      clientRating: map['clientRating'],
      clientFeedback: map['clientFeedback'],
      reminderSent: map['reminderSent'] ?? false,
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt'] as Timestamp).toDate() 
          : DateTime.now(),
      additionalInfo: map['additionalInfo'],
    );
  }

  /// إنشاء تفاعل من DocumentSnapshot
  factory ClientInteraction.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    data['id'] = snapshot.id;
    return ClientInteraction.fromMap(data);
  }

  @override
  List<Object?> get props => [
    id, clientId, clientName, agentId, agentName, type, status, outcome,
    title, description, notes, interactionDate, duration, relatedEstateId,
    relatedEstateName, relatedProjectId, relatedProjectName, attachments,
    cost, expectedRevenue, actualRevenue, nextFollowUpDate, followUpPriority,
    tags, clientRating, clientFeedback, reminderSent, createdAt, updatedAt,
    additionalInfo,
  ];
}
