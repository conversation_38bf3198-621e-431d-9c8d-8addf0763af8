import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../entities/estate.dart';

class FavoritesService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// إضافة عقار للمفضلة
  Future<bool> addToFavorites(String propertyId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // التحقق من وجود العقار مسبقاً
      final existingDoc = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('favorites')
          .doc(propertyId)
          .get();

      if (existingDoc.exists) {
        throw Exception('العقار موجود بالفعل في المفضلة');
      }

      // إضافة العقار للمفضلة
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('favorites')
          .doc(propertyId)
          .set({
        'propertyId': propertyId,
        'addedAt': FieldValue.serverTimestamp(),
        'userId': user.uid,
      });

      // تحديث عداد المفضلة في العقار
      await _firestore
          .collection('estates')
          .doc(propertyId)
          .update({
        'favoritesCount': FieldValue.increment(1),
      });



      return true;
    } catch (e) {
      print('Error adding to favorites: $e');
      rethrow;
    }
  }

  /// إزالة عقار من المفضلة
  Future<bool> removeFromFavorites(String propertyId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('favorites')
          .doc(propertyId)
          .delete();

      // تحديث عداد المفضلة في العقار
      await _firestore
          .collection('estates')
          .doc(propertyId)
          .update({
        'favoritesCount': FieldValue.increment(-1),
      });



      return true;
    } catch (e) {
      print('Error removing from favorites: $e');
      return false;
    }
  }

  /// التحقق من وجود عقار في المفضلة
  Future<bool> isFavorite(String propertyId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final doc = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('favorites')
          .doc(propertyId)
          .get();

      return doc.exists;
    } catch (e) {
      print('Error checking favorite status: $e');
      return false;
    }
  }

  /// الحصول على قائمة العقارات المفضلة
  Future<List<Estate>> getFavoriteProperties() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final favoritesSnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('favorites')
          .orderBy('addedAt', descending: true)
          .get();

      final propertyIds = favoritesSnapshot.docs
          .map((doc) => doc.data()['propertyId'] as String)
          .toList();

      if (propertyIds.isEmpty) return [];

      // جلب تفاصيل العقارات
      final List<Estate> properties = [];
      for (final propertyId in propertyIds) {
        final propertyDoc = await _firestore
            .collection('estates')
            .doc(propertyId)
            .get();

        if (propertyDoc.exists) {
          final data = propertyDoc.data()!;
          // تحويل البيانات إلى Estate object
          properties.add(_mapToEstate(data, propertyDoc.id));
        }
      }

      return properties;
    } catch (e) {
      print('Error getting favorite properties: $e');
      return [];
    }
  }

  /// إضافة عقار للمشاهدة الأخيرة
  Future<void> addToRecentlyViewed(String propertyId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      // إضافة أو تحديث العقار في المشاهدة الأخيرة
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('recentlyViewed')
          .doc(propertyId)
          .set({
        'propertyId': propertyId,
        'viewedAt': FieldValue.serverTimestamp(),
      });

      // الاحتفاظ بآخر 20 عقار فقط
      await _cleanupRecentlyViewed();
    } catch (e) {
      print('Error adding to recently viewed: $e');
    }
  }

  /// تنظيف المشاهدة الأخيرة (الاحتفاظ بآخر 20 عقار)
  Future<void> _cleanupRecentlyViewed() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('recentlyViewed')
          .orderBy('viewedAt', descending: true)
          .get();

      if (snapshot.docs.length > 20) {
        final batch = _firestore.batch();
        for (int i = 20; i < snapshot.docs.length; i++) {
          batch.delete(snapshot.docs[i].reference);
        }
        await batch.commit();
      }
    } catch (e) {
      print('Error cleaning up recently viewed: $e');
    }
  }

  /// الحصول على العقارات المشاهدة مؤخراً
  Future<List<Estate>> getRecentlyViewedProperties() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final recentSnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('recentlyViewed')
          .orderBy('viewedAt', descending: true)
          .limit(20)
          .get();

      final propertyIds = recentSnapshot.docs
          .map((doc) => doc.data()['propertyId'] as String)
          .toList();

      if (propertyIds.isEmpty) return [];

      // جلب تفاصيل العقارات
      final List<Estate> properties = [];
      for (final propertyId in propertyIds) {
        final propertyDoc = await _firestore
            .collection('estates')
            .doc(propertyId)
            .get();

        if (propertyDoc.exists) {
          final data = propertyDoc.data()!;
          properties.add(_mapToEstate(data, propertyDoc.id));
        }
      }

      return properties;
    } catch (e) {
      print('Error getting recently viewed properties: $e');
      return [];
    }
  }

  /// إنشاء تنبيه سعر
  Future<bool> createPriceAlert(String propertyId, double targetPrice) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('priceAlerts')
          .doc(propertyId)
          .set({
        'propertyId': propertyId,
        'targetPrice': targetPrice,
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error creating price alert: $e');
      return false;
    }
  }

  /// إزالة تنبيه السعر
  Future<bool> removePriceAlert(String propertyId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('priceAlerts')
          .doc(propertyId)
          .delete();

      return true;
    } catch (e) {
      print('Error removing price alert: $e');
      return false;
    }
  }

  /// الحصول على العقارات التي لها تنبيهات أسعار
  Future<List<Estate>> getPriceAlertProperties() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final alertsSnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('priceAlerts')
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      final propertyIds = alertsSnapshot.docs
          .map((doc) => doc.data()['propertyId'] as String)
          .toList();

      if (propertyIds.isEmpty) return [];

      // جلب تفاصيل العقارات
      final List<Estate> properties = [];
      for (final propertyId in propertyIds) {
        final propertyDoc = await _firestore
            .collection('estates')
            .doc(propertyId)
            .get();

        if (propertyDoc.exists) {
          final data = propertyDoc.data()!;
          properties.add(_mapToEstate(data, propertyDoc.id));
        }
      }

      return properties;
    } catch (e) {
      print('Error getting price alert properties: $e');
      return [];
    }
  }

  /// التحقق من تنبيهات الأسعار
  Future<List<Map<String, dynamic>>> checkPriceAlerts() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final alertsSnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('priceAlerts')
          .where('isActive', isEqualTo: true)
          .get();

      final List<Map<String, dynamic>> triggeredAlerts = [];

      for (final alertDoc in alertsSnapshot.docs) {
        final alertData = alertDoc.data();
        final propertyId = alertData['propertyId'] as String;
        final targetPrice = alertData['targetPrice'] as double;

        // جلب السعر الحالي للعقار
        final propertyDoc = await _firestore
            .collection('estates')
            .doc(propertyId)
            .get();

        if (propertyDoc.exists) {
          final propertyData = propertyDoc.data()!;
          final currentPrice = propertyData['price'] as double;

          // التحقق من تحقق شرط التنبيه
          if (currentPrice <= targetPrice) {
            triggeredAlerts.add({
              'propertyId': propertyId,
              'propertyTitle': propertyData['title'],
              'targetPrice': targetPrice,
              'currentPrice': currentPrice,
              'alertId': alertDoc.id,
            });
          }
        }
      }

      return triggeredAlerts;
    } catch (e) {
      print('Error checking price alerts: $e');
      return [];
    }
  }

  /// تعطيل تنبيه السعر
  Future<bool> disablePriceAlert(String propertyId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('priceAlerts')
          .doc(propertyId)
          .update({'isActive': false});

      return true;
    } catch (e) {
      print('Error disabling price alert: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات المفضلة
  Future<Map<String, dynamic>> getFavoritesStatistics() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return {};

      final futures = await Future.wait([
        _firestore
            .collection('users')
            .doc(user.uid)
            .collection('favorites')
            .get(),
        _firestore
            .collection('users')
            .doc(user.uid)
            .collection('recentlyViewed')
            .get(),
        _firestore
            .collection('users')
            .doc(user.uid)
            .collection('priceAlerts')
            .where('isActive', isEqualTo: true)
            .get(),
      ]);

      final favoritesCount = futures[0].docs.length;
      final recentlyViewedCount = futures[1].docs.length;
      final priceAlertsCount = futures[2].docs.length;

      // حساب إحصائيات إضافية
      final favoriteProperties = await getFavoriteProperties();
      double totalValue = 0;
      double averagePrice = 0;
      double averageArea = 0;

      if (favoriteProperties.isNotEmpty) {
        totalValue = favoriteProperties.fold(0, (sum, property) => sum + property.price);
        averagePrice = totalValue / favoriteProperties.length;
        averageArea = favoriteProperties.fold(0.0, (sum, property) => sum + (property.area ?? 0)) / favoriteProperties.length;
      }

      return {
        'favoritesCount': favoritesCount,
        'recentlyViewedCount': recentlyViewedCount,
        'priceAlertsCount': priceAlertsCount,
        'totalValue': totalValue,
        'averagePrice': averagePrice,
        'averageArea': averageArea,
      };
    } catch (e) {
      print('Error getting favorites statistics: $e');
      return {};
    }
  }

  /// مسح جميع المفضلة
  Future<bool> clearAllFavorites() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final favoritesRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('favorites');

      final snapshot = await favoritesRef.get();
      final batch = _firestore.batch();

      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      return true;
    } catch (e) {
      print('Error clearing all favorites: $e');
      return false;
    }
  }

  /// مسح المشاهدة الأخيرة
  Future<bool> clearRecentlyViewed() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final recentRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('recentlyViewed');

      final snapshot = await recentRef.get();
      final batch = _firestore.batch();

      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      return true;
    } catch (e) {
      print('Error clearing recently viewed: $e');
      return false;
    }
  }

  /// تصدير المفضلة
  Future<List<Map<String, dynamic>>> exportFavorites() async {
    try {
      final favoriteProperties = await getFavoriteProperties();

      return favoriteProperties.map((property) => {
        'id': property.id,
        'title': property.title,
        'price': property.price,
        'area': property.area,
        'location': property.location,
        'numberOfRooms': property.numberOfRooms,
        'numberOfBathrooms': property.numberOfBathrooms,
        'propertyType': property.propertyType,
        'mainCategory': property.mainCategory,
        'description': property.description,
        'photoUrls': property.photoUrls,
        'createdAt': property.createdAt.toIso8601String(),
      }).toList();
    } catch (e) {
      print('Error exporting favorites: $e');
      return [];
    }
  }

  /// الحصول على توصيات بناءً على المفضلة
  Future<List<Estate>> getRecommendations() async {
    try {
      final favoriteProperties = await getFavoriteProperties();

      if (favoriteProperties.isEmpty) return [];

      // تحليل تفضيلات المستخدم
      final Map<String, int> locationPreferences = {};
      final Map<String, int> typePreferences = {};
      double averagePrice = 0;
      double averageArea = 0;

      for (final property in favoriteProperties) {
        locationPreferences[property.location] =
            (locationPreferences[property.location] ?? 0) + 1;

        if (property.propertyType != null) {
          typePreferences[property.propertyType!] =
              (typePreferences[property.propertyType!] ?? 0) + 1;
        }

        averagePrice += property.price;
        averageArea += (property.area ?? 0);
      }

      averagePrice /= favoriteProperties.length;
      averageArea /= favoriteProperties.length;

      // البحث عن عقارات مشابهة
      Query query = _firestore.collection('estates');

      // فلتر السعر (±30% من المتوسط)
      query = query
          .where('price', isGreaterThanOrEqualTo: averagePrice * 0.7)
          .where('price', isLessThanOrEqualTo: averagePrice * 1.3);

      final snapshot = await query.limit(20).get();

      final recommendations = snapshot.docs
          .map((doc) => _mapToEstate(doc.data() as Map<String, dynamic>, doc.id))
          .where((property) {
            // استبعاد العقارات الموجودة في المفضلة
            return !favoriteProperties.any((fav) => fav.id == property.id);
          })
          .toList();

      // ترتيب التوصيات حسب التشابه
      recommendations.sort((a, b) {
        int scoreA = _calculateSimilarityScore(a, locationPreferences, typePreferences, averageArea);
        int scoreB = _calculateSimilarityScore(b, locationPreferences, typePreferences, averageArea);
        return scoreB.compareTo(scoreA);
      });

      return recommendations.take(10).toList();
    } catch (e) {
      print('Error getting recommendations: $e');
      return [];
    }
  }

  /// حساب درجة التشابه
  int _calculateSimilarityScore(
    Estate property,
    Map<String, int> locationPreferences,
    Map<String, int> typePreferences,
    double averageArea) {
    int score = 0;

    // نقاط الموقع
    score += (locationPreferences[property.location] ?? 0) * 3;

    // نقاط نوع العقار
    if (property.propertyType != null) {
      score += (typePreferences[property.propertyType!] ?? 0) * 2;
    }

    // نقاط المساحة (كلما كانت أقرب للمتوسط كانت أفضل)
    final areaDifference = ((property.area ?? 0) - averageArea).abs();
    if (areaDifference < averageArea * 0.2) {
      score += 2;
    } else if (areaDifference < averageArea * 0.5) {
      score += 1;
    }

    return score;
  }

  /// تحويل البيانات من Firestore إلى Estate object
  Estate _mapToEstate(Map<String, dynamic> data, String id) {
    return Estate(
      id: id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      price: (data['price'] ?? 0).toDouble(),
      location: data['location'] ?? '',
      photoUrls: List<String>.from(data['photoUrls'] ?? []),
      isFeatured: data['isFeatured'] ?? false,
      planType: data['planType'] ?? 'free',
      startDate: data['startDate'] != null
          ? DateTime.parse(data['startDate'])
          : null,
      endDate: data['endDate'] != null
          ? DateTime.parse(data['endDate'])
          : null,
      createdAt: data['createdAt'] != null
          ? DateTime.parse(data['createdAt'])
          : DateTime.now(),
      mainCategory: data['mainCategory'],
      subCategory: data['subCategory'],
      postedByUserType: data['postedByUserType'],
      hidePhone: data['hidePhone'] ?? false,
      extraPhones: List<String>.from(data['extraPhones'] ?? []),
      shareLocation: data['shareLocation'] ?? false,
      lat: data['lat']?.toDouble(),
      lng: data['lng']?.toDouble(),
      hasCentralAC: data['hasCentralAC'] ?? false,
      hasSecurity: data['hasSecurity'],
      allowPets: data['allowPets'],
      hasElevator: data['hasElevator'],
      hasSwimmingPool: data['hasSwimmingPool'],
      hasMaidRoom: data['hasMaidRoom'] ?? false,
      hasGarage: data['hasGarage'] ?? false,
      hasBalcony: data['hasBalcony'],
      isFullyFurnished: data['isFullyFurnished'],
      rebound: data['rebound'],
      numberOfRooms: data['numberOfRooms'],
      internalLocation: data['internalLocation'],
      salon: data['salon'],
      area: data['area']?.toDouble(),
      floorNumber: data['floorNumber'],
      numberOfBathrooms: data['numberOfBathrooms'],
      buildingAge: data['buildingAge'],
      numberOfFloors: data['numberOfFloors'],
      propertyType: data['propertyType'],
      autoRepublish: data['autoRepublish'] ?? false,
      kuwaitCornersPin: data['kuwaitCornersPin'] ?? false,
      movingAd: data['movingAd'] ?? false,
      vipBadge: data['vipBadge'] ?? false,
      pinnedOnHome: data['pinnedOnHome'] ?? false,
      discountCode: data['discountCode'],
      advertiserImage: data['advertiserImage'],
      advertiserName: data['advertiserName'],
      advertiserEmail: data['advertiserEmail'],
      advertiserRegistrationDate: data['advertiserRegistrationDate'] != null
          ? DateTime.parse(data['advertiserRegistrationDate'])
          : null,
      advertiserAdsCount: data['advertiserAdsCount'],
      ownerId: data['ownerId'],
      originalEstateId: data['originalEstateId'],
      isOriginal: data['isOriginal'] ?? true,
      copiedBy: List<String>.from(data['copiedBy'] ?? []),
      isPaymentVerified: data['isPaymentVerified'] ?? false,
      viewsCount: data['viewsCount'],
      inquiriesCount: data['inquiriesCount'],
      favoritesCount: data['favoritesCount'],
      latitude: data['latitude']?.toDouble(),
      longitude: data['longitude']?.toDouble(),
      rooms: data['rooms'],
      bathrooms: data['bathrooms'],
      floors: data['floors'],
      purpose: data['purpose'],
      hasGarden: data['hasGarden'],
      hasPool: data['hasPool'],
      hasDriverRoom: data['hasDriverRoom'],
      hasPrivateEntrance: data['hasPrivateEntrance'],
      hasEquippedKitchen: data['hasEquippedKitchen'],
      isAvailable: data['isAvailable'] ?? true);
  }




}
