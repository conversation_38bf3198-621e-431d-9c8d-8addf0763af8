# دليل نشر Firebase Functions - نظام إشعارات الأرشفة التلقائية

## 📋 نظرة عامة

تم تطوير نظام Firebase Functions متقدم لإرسال إشعارات تلقائية للمستخدمين بعد 24 ساعة من إنشاء الإعلان لإعلامهم بأن إعلانهم قد تم أرشفته.

## 🔧 المتطلبات الأساسية

### 1. تثبيت Firebase CLI
```bash
npm install -g firebase-tools
```

### 2. تسجيل الدخول إلى Firebase
```bash
firebase login
```

### 3. التأكد من المشروع
```bash
firebase projects:list
firebase use real-estate-998a9
```

## 📁 هيكل الملفات المضافة

```
krea/
├── functions/
│   ├── src/
│   │   ├── index.ts                           # الملف الرئيسي
│   │   ├── notifications/
│   │   │   └── archiveNotifications.ts        # معالجة إشعارات الأرشفة
│   │   └── triggers/
│   │       └── estateTriggers.ts              # مشغلات العقارات
│   ├── package.json                           # تبعيات Node.js
│   ├── tsconfig.json                          # إعدادات TypeScript
│   ├── .eslintrc.js                          # إعدادات ESLint
│   └── README.md                              # توثيق Functions
├── firebase.json                              # إعدادات Firebase محدثة
├── lib/core/services/
│   └── archive_notification_service.dart     # خدمة إدارة الإشعارات
└── lib/domain/usecases/
    └── create_estate_new.dart                 # محدث لجدولة الإشعارات
```

## 🚀 خطوات النشر

### 1. الانتقال إلى مجلد Functions
```bash
cd functions
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. بناء المشروع
```bash
npm run build
```

### 4. نشر Functions
```bash
firebase deploy --only functions
```

### 5. التحقق من النشر
```bash
firebase functions:log
```

## 🔄 كيفية عمل النظام

### 1. إنشاء الإعلان
```
المستخدم ينشئ إعلان → حفظ في Firestore → تشغيل estateCreated trigger
```

### 2. جدولة الإشعار
```
estateCreated trigger → إنشاء مستند في scheduledNotifications → جدولة بعد 24 ساعة
```

### 3. معالجة الإشعار
```
scheduleNotificationProcessor (كل دقيقة) → فحص الإشعارات المستحقة → أرشفة + إشعار
```

## 📱 رسالة الإشعار

### إشعار الترحيب (عند النشر)
```
العنوان: "تم نشر إعلانك بنجاح! 🎉"
المحتوى: "تم نشر إعلان '[عنوان العقار]' بنجاح في تطبيق Kuwait Corners.

⏰ سيتم أرشفة الإعلان تلقائياً بعد 24 ساعة من النشر.

لتفعيله مرة أخرى بعد الأرشفة، تواصل معنا:
📱 واتساب: +965 9929 8821
📧 إيميل: <EMAIL>

شكراً لثقتك في تطبيق Kuwait Corners! 🏠"
```

### إشعار الأرشفة (بعد 24 ساعة)
```
العنوان: "تم أرشفة إعلانك 📦"
المحتوى: "تم أرشفة إعلان '[عنوان العقار]' تلقائياً بعد 24 ساعة من النشر.

لتفعيل الإعلان مرة أخرى، تواصل مع إدارة التطبيق:

📱 واتساب: +965 9929 8821
📧 إيميل: <EMAIL>

شكراً لاستخدامك تطبيق Kuwait Corners 🏠"
```

## 🔍 مراقبة النظام

### 1. عرض السجلات
```bash
firebase functions:log --only scheduleNotificationProcessor
firebase functions:log --only estateCreated
```

### 2. مراقبة الأداء
```bash
firebase functions:log --limit 50
```

### 3. فحص الأخطاء
```bash
firebase functions:log --filter "ERROR"
```

## 🗄️ قواعد البيانات المطلوبة

### مجموعة `scheduledNotifications`
- **الغرض**: تخزين الإشعارات المجدولة
- **الفهارس المطلوبة**:
  - `processed` (ascending)
  - `scheduledTime` (ascending)
  - `userId` (ascending)

### مجموعة `systemLogs`
- **الغرض**: تسجيل عمليات النظام
- **الفهارس المطلوبة**:
  - `timestamp` (descending)
  - `type` (ascending)

## ⚙️ إعدادات Firebase Console

### 1. تفعيل Cloud Functions
- انتقل إلى Firebase Console
- اختر المشروع `real-estate-998a9`
- فعل Cloud Functions

### 2. إعداد Cloud Messaging
- تأكد من تفعيل Firebase Cloud Messaging
- إعداد مفاتيح الخادم

### 3. إعداد Firestore Rules
تأكد من أن قواعد Firestore تسمح بالكتابة في:
- `scheduledNotifications`
- `systemLogs`
- `users/{userId}/notifications`

## 🧪 الاختبار

### 1. اختبار محلي
```bash
firebase emulators:start --only functions,firestore
```

### 2. اختبار إنشاء عقار
- أنشئ عقار جديد في التطبيق
- تحقق من إنشاء مستند في `scheduledNotifications`
- تحقق من السجلات

### 3. اختبار معالجة الإشعارات
```bash
# استدعاء الوظيفة يدوياً
firebase functions:shell
> processScheduledNotifications()
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ في الأذونات
```bash
firebase functions:config:set someservice.key="THE API KEY"
```

#### 2. خطأ في التبعيات
```bash
cd functions
rm -rf node_modules
npm install
```

#### 3. خطأ في البناء
```bash
npm run lint
npm run build
```

## 📊 المراقبة والتحليل

### 1. إحصائيات الأداء
- عدد الإشعارات المعالجة يومياً
- معدل نجاح الإرسال
- أوقات الاستجابة

### 2. تتبع الأخطاء
- أخطاء الإرسال
- أخطاء قاعدة البيانات
- أخطاء المصادقة

## 🔄 التحديثات المستقبلية

### ميزات مقترحة:
1. إشعارات تذكير قبل الأرشفة (مثل 2 ساعة قبل)
2. إمكانية تمديد فترة النشر
3. إشعارات مخصصة حسب نوع العقار
4. تحليلات متقدمة للإشعارات

## 📞 الدعم الفني

للدعم الفني أو الاستفسارات:
- **البريد الإلكتروني**: <EMAIL>
- **الواتساب**: +965 9929 8821

## ✅ قائمة التحقق النهائية

- [ ] تثبيت Firebase CLI
- [ ] تسجيل الدخول إلى Firebase
- [ ] تثبيت تبعيات Functions
- [ ] بناء المشروع بنجاح
- [ ] نشر Functions
- [ ] اختبار إنشاء عقار
- [ ] التحقق من جدولة الإشعار
- [ ] اختبار معالجة الإشعارات
- [ ] مراقبة السجلات
- [ ] التأكد من وصول الإشعارات للمستخدمين

---

**ملاحظة**: تأكد من اختبار النظام في بيئة التطوير قبل النشر في الإنتاج.
