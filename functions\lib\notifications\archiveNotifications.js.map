{"version": 3, "file": "archiveNotifications.js", "sourceRoot": "", "sources": ["../../src/notifications/archiveNotifications.ts"], "names": [], "mappings": ";;;AAAA,gDAAgD;AAChD,wCAAwC;AAExC;;GAEG;AACU,QAAA,6BAA6B,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;IAC3F,qBAAqB;IACrB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;QACjB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,CAAC;KAC/E;IAED,IAAI;QACF,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAE1D,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC7B,MAAM,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;QAE5C,6CAA6C;QAC7C,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,wBAAwB,CAAC;aACxD,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC;aAC/B,KAAK,CAAC,eAAe,EAAE,IAAI,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC;aAC1C,KAAK,CAAC,EAAE,CAAC;aACT,GAAG,EAAE,CAAC;QAET,IAAI,KAAK,CAAC,KAAK,EAAE;YACf,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,iCAAiC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;SACpF;QAED,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;QAEzB,KAAK,MAAM,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE;YAC5B,MAAM,gBAAgB,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;YAEpC,IAAI;gBACF,IAAI,gBAAgB,CAAC,IAAI,KAAK,gBAAgB,EAAE;oBAC9C,MAAM,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;oBAC7C,cAAc,EAAE,CAAC;iBAClB;gBAED,uBAAuB;gBACvB,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE;oBACpB,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE;iBAC1B,CAAC,CAAC;aAEJ;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,CAAC,KAAK,CAAC,2BAA2B,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;gBAE3D,sBAAsB;gBACtB,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE;oBACpB,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE;oBACzB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;iBACrB,CAAC,CAAC;aACJ;SACF;QAED,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;QAErB,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,aAAa,cAAc,cAAc;YAClD,SAAS,EAAE,cAAc;SAC1B,CAAC;KAEH;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACnD,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,yBAAyB,CAAC,CAAC;KAC7E;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACH,KAAK,UAAU,oBAAoB,CAAC,gBAAqB;IACvD,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;IAC7B,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;IAEpC,OAAO,CAAC,GAAG,CAAC,oBAAoB,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;IAE7D,eAAe;IACf,MAAM,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;QACnE,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE;QAC3C,cAAc,EAAE,0CAA0C;QAC1D,MAAM,EAAE,UAAU;KACnB,CAAC,CAAC;IAEH,6BAA6B;IAC7B,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;IAChF,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IAEhC,sBAAsB;IACtB,MAAM,iBAAiB,GAAG,oBAAoB,CAAC;IAC/C,MAAM,gBAAgB,GAAG,mBAAmB,gBAAgB,CAAC,WAAW;;;;;;;wCAOlC,CAAC;IAEvC,iDAAiD;IACjD,IAAI,QAAQ,IAAI,QAAQ,CAAC,QAAQ,EAAE;QACjC,IAAI;YACF,MAAM,OAAO,GAAG;gBACd,KAAK,EAAE,QAAQ,CAAC,QAAQ;gBACxB,YAAY,EAAE;oBACZ,KAAK,EAAE,iBAAiB;oBACxB,IAAI,EAAE,2CAA2C;iBAClD;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;oBACvB,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;oBACnC,WAAW,EAAE,gBAAgB,CAAC,WAAW;oBACzC,QAAQ,EAAE,gBAAgB;oBAC1B,KAAK,EAAE,kBAAkB;oBACzB,WAAW,EAAE,gBAAgB;iBAC9B;gBACD,OAAO,EAAE;oBACP,YAAY,EAAE;wBACZ,IAAI,EAAE,iBAAiB;wBACvB,KAAK,EAAE,SAAS;wBAChB,KAAK,EAAE,SAAS;wBAChB,SAAS,EAAE,sBAAsB;wBACjC,QAAQ,EAAE,MAAgB;qBAC3B;iBACF;gBACD,IAAI,EAAE;oBACJ,OAAO,EAAE;wBACP,GAAG,EAAE;4BACH,KAAK,EAAE,SAAS;4BAChB,KAAK,EAAE,CAAC;4BACR,KAAK,EAAE;gCACL,KAAK,EAAE,iBAAiB;gCACxB,IAAI,EAAE,2CAA2C;6BAClD;yBACF;qBACF;iBACF;aACF,CAAC;YAEF,MAAM,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,mCAAmC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;SAE3E;QAAC,OAAO,SAAS,EAAE;YAClB,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,SAAS,CAAC,CAAC;SACxD;KACF;IAED,gCAAgC;IAChC,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;SACzB,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC;SAC5B,UAAU,CAAC,eAAe,CAAC;SAC3B,GAAG,CAAC;QACH,KAAK,EAAE,iBAAiB;QACxB,IAAI,EAAE,gBAAgB;QACtB,IAAI,EAAE,iBAAiB;QACvB,IAAI,EAAE;YACJ,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;YACnC,WAAW,EAAE,gBAAgB,CAAC,WAAW;YACzC,WAAW,EAAE;gBACX,QAAQ,EAAE,gBAAgB;gBAC1B,KAAK,EAAE,kBAAkB;aAC1B;SACF;QACD,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE;QAC1C,MAAM,EAAE,KAAK;QACb,QAAQ,EAAE,MAAM;KACjB,CAAC,CAAC;IAEL,OAAO,CAAC,GAAG,CAAC,8BAA8B,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;IAErE,oCAAoC;IACpC,MAAM,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC;QACpC,IAAI,EAAE,iBAAiB;QACvB,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;QACnC,MAAM,EAAE,gBAAgB,CAAC,MAAM;QAC/B,WAAW,EAAE,gBAAgB,CAAC,WAAW;QACzC,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE;QAC1C,OAAO,EAAE,4DAA4D;KACtE,CAAC,CAAC;AACL,CAAC"}