import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Servicio para monitorear el estado de la conectividad a Internet.
class ConnectivityService {
  final Connectivity _connectivity = Connectivity();
  final StreamController<bool> _connectionStatusController = StreamController<bool>.broadcast();

  /// Stream que emite el estado de la conectividad.
  /// `true` si hay conexión a Internet, `false` si no hay conexión.
  Stream<bool> get connectionStatus => _connectionStatusController.stream;

  ConnectivityService() {
    // Inicializar el servicio
    _init();
  }

  void _init() async {
    // Verificar el estado inicial de la conectividad
    ConnectivityResult result = await _connectivity.checkConnectivity();
    _handleConnectivityChange(result);

    // Escuchar cambios en la conectividad
    _connectivity.onConnectivityChanged.listen(_handleConnectivityChange);
  }

  void _handleConnectivityChange(ConnectivityResult result) {
    bool isConnected = result != ConnectivityResult.none;
    _connectionStatusController.add(isConnected);
  }

  /// Verificar si hay conexión a Internet.
  Future<bool> isConnected() async {
    ConnectivityResult result = await _connectivity.checkConnectivity();
    return result != ConnectivityResult.none;
  }

  /// Liberar recursos cuando el servicio ya no se necesita.
  void dispose() {
    _connectionStatusController.close();
  }
}
