import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:image_picker/image_picker.dart';
import 'package:kuwait_corners/infrastructure/services/image_service.dart';
import 'dart:io';

/// صفحة تعديل العميل
class EditClientPage extends StatefulWidget {
  final String clientId;

  const EditClientPage({super.key, required this.clientId});

  @override
  State<EditClientPage> createState() => _EditClientPageState();
}

class _EditClientPageState extends State<EditClientPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _notesController = TextEditingController();
  final _budgetController = TextEditingController();

  String _selectedType = 'مشتري';
  String _selectedStatus = 'جديد';
  String _selectedInterest = 'شراء';
  bool _isVip = false;
  bool _isLoading = false;
  bool _isLoadingData = true;

  // متغيرات رفع الصورة
  File? _selectedImage;
  String? _currentImageUrl;
  final ImagePicker _imagePicker = ImagePicker();
  final ImageService _imageService = ImageService();

  @override
  void initState() {
    super.initState();
    _loadClientData();
  }

  Future<void> _loadClientData() async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('clients')
          .doc(widget.clientId)
          .get();

      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        setState(() {
          _nameController.text = data['name'] ?? '';
          _emailController.text = data['email'] ?? '';
          _phoneController.text = data['phone'] ?? '';
          _notesController.text = data['notes'] ?? '';
          _budgetController.text = data['budget']?.toString() ?? '';
          _selectedType = data['type'] ?? 'مشتري';
          _selectedStatus = data['status'] ?? 'جديد';
          _selectedInterest = data['interest'] ?? 'شراء';
          _isVip = data['isVip'] ?? false;
          _currentImageUrl = data['imageUrl'];
          _isLoadingData = false;
        });
      } else {
        setState(() {
          _isLoadingData = false;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('لم يتم العثور على بيانات العميل',
                style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white)),
              backgroundColor: Colors.red));
          Navigator.pop(context);
        }
      }
    } catch (e) {
      setState(() {
        _isLoadingData = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في تحميل البيانات: ${e.toString()}',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white)),
            backgroundColor: Colors.red));
        Navigator.pop(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text('تعديل العميل', style: CairoTextStyles.appBarTitle),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white)),
      body: Stack(
        children: [
          // خلفية بأشكال هندسية
          _buildBackgroundShapes(),

          // المحتوى الرئيسي
          _isLoadingData
              ? const Center(child: CircularProgressIndicator())
              : Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildImageSection(),
                        const SizedBox(height: 24),
                        _buildBasicInfoSection(),
                        const SizedBox(height: 24),
                        _buildContactInfoSection(),
                        const SizedBox(height: 24),
                        _buildPreferencesSection(),
                        const SizedBox(height: 24),
                        _buildAdditionalInfoSection(),
                        const SizedBox(height: 32),
                        _buildActionButtons(),
                      ]))),
        ]));
  }

  Widget _buildBackgroundShapes() {
    return Positioned.fill(
      child: Stack(
        children: [
          // أيقونة تعديل - محسنة
          Positioned(
            top: 80,
            right: -35,
            child: Opacity(
              opacity: 0.025,
              child: Icon(
                Icons.edit_outlined,
                size: 110,
                color: AppColors.primary))),

          // أيقونة عميل - أصغر
          Positioned(
            top: 260,
            left: -25,
            child: Opacity(
              opacity: 0.03,
              child: Icon(
                Icons.person_outline,
                size: 85,
                color: AppColors.primary))),

          // أيقونة حفظ - موضعة أفضل
          Positioned(
            bottom: 200,
            right: -20,
            child: Opacity(
              opacity: 0.025,
              child: Icon(
                Icons.save_outlined,
                size: 70,
                color: AppColors.primary))),

          // أيقونة معلومات - أصغر
          Positioned(
            bottom: 350,
            left: -15,
            child: Opacity(
              opacity: 0.03,
              child: Icon(
                Icons.info_outline,
                size: 60,
                color: AppColors.primary))),

          // أيقونة كاميرا - جديدة
          Positioned(
            top: 450,
            right: -12,
            child: Opacity(
              opacity: 0.02,
              child: Icon(
                Icons.camera_alt_outlined,
                size: 55,
                color: AppColors.primary))),

          // دوائر هندسية - أصغر وأكثر توزيعاً
          Positioned(
            top: 160,
            left: 30,
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.withValues(alpha: 0.015)))),

          Positioned(
            top: 320,
            right: 55,
            child: Container(
              width: 22,
              height: 22,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.withValues(alpha: 0.02)))),

          Positioned(
            bottom: 160,
            right: 75,
            child: Container(
              width: 28,
              height: 28,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.withValues(alpha: 0.018)))),

          Positioned(
            bottom: 280,
            left: 65,
            child: Container(
              width: 19,
              height: 19,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.withValues(alpha: 0.022)))),

          // مربعات مدورة - أصغر
          Positioned(
            top: 380,
            right: 28,
            child: Container(
              width: 17,
              height: 17,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: AppColors.primary.withValues(alpha: 0.015)))),

          Positioned(
            top: 220,
            left: 22,
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                color: AppColors.primary.withValues(alpha: 0.018)))),

          Positioned(
            bottom: 190,
            left: 32,
            child: Container(
              width: 26,
              height: 26,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: AppColors.primary.withValues(alpha: 0.02)))),
        ]));
  }

  Widget _buildImageSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(Icons.photo_camera, color: AppColors.primary, size: 20)),
              const SizedBox(width: 12),
              Text('صورة العميل', style: CairoTextStyles.headlineSmall),
            ]),
          const SizedBox(height: 24),
          Center(
            child: Stack(
              children: [
                // الصورة الرئيسية
                Container(
                  width: 140,
                  height: 140,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      width: 3),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 8))
                    ]),
                  child: ClipOval(
                    child: _selectedImage != null
                        ? Image.file(
                            _selectedImage!,
                            width: 140,
                            height: 140,
                            fit: BoxFit.cover)
                        : _currentImageUrl != null
                            ? Image.network(
                                _currentImageUrl!,
                                width: 140,
                                height: 140,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          AppColors.primary.withValues(alpha: 0.1),
                                          AppColors.primary.withValues(alpha: 0.05)
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight)),
                                    child: Icon(
                                      Icons.person,
                                      size: 70,
                                      color: AppColors.primary.withValues(alpha: 0.6)));
                                })
                            : Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      AppColors.primary.withValues(alpha: 0.1),
                                      AppColors.primary.withValues(alpha: 0.05)
                                    ],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight)),
                                child: Icon(
                                  Icons.person,
                                  size: 70,
                                  color: AppColors.primary.withValues(alpha: 0.6))))),

                // زر اختيار الصورة
                Positioned(
                  bottom: 8,
                  right: 8,
                  child: GestureDetector(
                    onTap: _pickImage,
                    child: Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 3),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withValues(alpha: 0.3),
                            blurRadius: 12,
                            offset: const Offset(0, 4))
                        ]),
                      child: const Icon(
                        Icons.camera_alt,
                        color: Colors.white,
                        size: 20)))),
              ])),
        ]));
  }

  Widget _buildBasicInfoSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(Icons.person, color: AppColors.primary, size: 20)),
              const SizedBox(width: 12),
              Text('المعلومات الأساسية', style: CairoTextStyles.headlineSmall),
            ]),
          const SizedBox(height: 24),

          // اسم العميل
          TextFormField(
            controller: _nameController,
            style: CairoTextStyles.bodyMedium,
            decoration: InputDecoration(
              labelText: 'اسم العميل',
              labelStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary, width: 2)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              prefixIcon: Icon(Icons.person_outline, color: AppColors.primary),
              filled: true,
              fillColor: Colors.grey.withValues(alpha: 0.02)),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال اسم العميل';
              }
              return null;
            }),
          const SizedBox(height: 20),

          // نوع العميل وحالته
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedType,
                  style: CairoTextStyles.bodyMedium,
                  decoration: InputDecoration(
                    labelText: 'نوع العميل',
                    labelStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColors.primary, width: 2)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
                    prefixIcon: Icon(Icons.category, color: AppColors.primary),
                    filled: true,
                    fillColor: Colors.grey.withValues(alpha: 0.02)),
                  items: ['مشتري', 'بائع', 'مستأجر', 'مؤجر', 'مستثمر']
                      .map((type) => DropdownMenuItem(
                            value: type,
                            child: Text(type, style: CairoTextStyles.bodyMedium)))
                      .toList(),
                  onChanged: (value) => setState(() => _selectedType = value!))),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedStatus,
                  style: CairoTextStyles.bodyMedium,
                  decoration: InputDecoration(
                    labelText: 'حالة العميل',
                    labelStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColors.primary, width: 2)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
                    prefixIcon: Icon(Icons.flag, color: AppColors.primary),
                    filled: true,
                    fillColor: Colors.grey.withValues(alpha: 0.02)),
                  items: ['جديد', 'نشط', 'محتمل', 'مؤرشف']
                      .map((status) => DropdownMenuItem(
                            value: status,
                            child: Text(status, style: CairoTextStyles.bodyMedium)))
                      .toList(),
                  onChanged: (value) => setState(() => _selectedStatus = value!))),
            ]),
          const SizedBox(height: 20),

          // خيار VIP
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.amber.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.amber.withValues(alpha: 0.2))),
            child: Row(
              children: [
                Checkbox(
                  value: _isVip,
                  onChanged: (value) => setState(() => _isVip = value!),
                  activeColor: Colors.amber,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4))),
                const SizedBox(width: 12),
                Icon(Icons.star, color: Colors.amber, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'عميل VIP',
                    style: CairoTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.amber[800]))),
                if (_isVip)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      borderRadius: BorderRadius.circular(20)),
                    child: Text(
                      'مفعل',
                      style: CairoTextStyles.labelSmall.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold))),
              ])),
        ]));
  }

  Widget _buildContactInfoSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(Icons.contact_phone, color: AppColors.primary, size: 20)),
              const SizedBox(width: 12),
              Text('معلومات التواصل', style: CairoTextStyles.headlineSmall),
            ]),
          const SizedBox(height: 24),

          // رقم الهاتف
          TextFormField(
            controller: _phoneController,
            style: CairoTextStyles.bodyMedium,
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              labelText: 'رقم الهاتف',
              labelStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary, width: 2)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              prefixIcon: Icon(Icons.phone, color: AppColors.primary),
              filled: true,
              fillColor: Colors.grey.withValues(alpha: 0.02))),
          const SizedBox(height: 20),

          // البريد الإلكتروني
          TextFormField(
            controller: _emailController,
            style: CairoTextStyles.bodyMedium,
            keyboardType: TextInputType.emailAddress,
            decoration: InputDecoration(
              labelText: 'البريد الإلكتروني',
              labelStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary, width: 2)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              prefixIcon: Icon(Icons.email, color: AppColors.primary),
              filled: true,
              fillColor: Colors.grey.withValues(alpha: 0.02))),
        ]));
  }

  Widget _buildPreferencesSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(Icons.interests, color: AppColors.primary, size: 20)),
              const SizedBox(width: 12),
              Text('التفضيلات والاهتمامات', style: CairoTextStyles.headlineSmall),
            ]),
          const SizedBox(height: 24),

          // الاهتمام الرئيسي
          DropdownButtonFormField<String>(
            value: _selectedInterest,
            style: CairoTextStyles.bodyMedium,
            decoration: InputDecoration(
              labelText: 'الاهتمام الرئيسي',
              labelStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary, width: 2)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              prefixIcon: Icon(Icons.favorite, color: AppColors.primary),
              filled: true,
              fillColor: Colors.grey.withValues(alpha: 0.02)),
            items: ['شراء', 'إيجار', 'بيع', 'تأجير', 'استثمار', 'تبادل']
                .map((interest) => DropdownMenuItem(
                      value: interest,
                      child: Text(interest, style: CairoTextStyles.bodyMedium)))
                .toList(),
            onChanged: (value) => setState(() => _selectedInterest = value!)),
          const SizedBox(height: 20),

          // الميزانية المتوقعة
          TextFormField(
            controller: _budgetController,
            style: CairoTextStyles.bodyMedium,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'الميزانية المتوقعة (د.ك)',
              labelStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary, width: 2)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              prefixIcon: Icon(Icons.attach_money, color: AppColors.primary),
              filled: true,
              fillColor: Colors.grey.withValues(alpha: 0.02))),
        ]));
  }

  Widget _buildAdditionalInfoSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(Icons.note, color: AppColors.primary, size: 20)),
              const SizedBox(width: 12),
              Text('معلومات إضافية', style: CairoTextStyles.headlineSmall),
            ]),
          const SizedBox(height: 24),

          // ملاحظات
          TextFormField(
            controller: _notesController,
            style: CairoTextStyles.bodyMedium,
            maxLines: 4,
            decoration: InputDecoration(
              labelText: 'ملاحظات',
              labelStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primary, width: 2)),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3))),
              prefixIcon: Icon(Icons.note_alt, color: AppColors.primary),
              alignLabelWithHint: true,
              filled: true,
              fillColor: Colors.grey.withValues(alpha: 0.02))),
        ]));
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1)),
      child: Row(
        children: [
          // زر الإلغاء
          Expanded(
            child: Container(
              height: 56,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.withValues(alpha: 0.3))),
              child: OutlinedButton(
                onPressed: () => Navigator.pop(context),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.grey[700],
                  side: BorderSide.none,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12))),
                child: Text(
                  'إلغاء',
                  style: CairoTextStyles.button.copyWith(
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w600))))),
          const SizedBox(width: 16),

          // زر الحفظ
          Expanded(
            flex: 2,
            child: Container(
              height: 56,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary,
                    AppColors.primary.withValues(alpha: 0.8)
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight)),
              child: ElevatedButton(
                onPressed: _isLoading ? null : _updateClient,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  foregroundColor: Colors.white,
                  shadowColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12))),
                child: _isLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2.5))
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.save, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            'حفظ التعديلات',
                            style: CairoTextStyles.button.copyWith(
                              fontWeight: FontWeight.bold)),
                        ])))),
        ]));
  }

  // دالة اختيار الصورة
  Future<void> _pickImage() async {
    try {
      final XFile? pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        setState(() {
          _selectedImage = File(pickedFile.path);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في اختيار الصورة: ${e.toString()}',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white)),
            backgroundColor: Colors.red));
      }
    }
  }

  Future<void> _updateClient() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      String? imageUrl = _currentImageUrl;

      // رفع الصورة الجديدة إذا تم اختيارها
      if (_selectedImage != null) {
        imageUrl = await _imageService.uploadImage(_selectedImage!, 'clients');
      }

      final budget = double.tryParse(_budgetController.text) ?? 0.0;

      await FirebaseFirestore.instance
          .collection('clients')
          .doc(widget.clientId)
          .update({
        'name': _nameController.text.trim(),
        'email': _emailController.text.trim(),
        'phone': _phoneController.text.trim(),
        'type': _selectedType,
        'status': _selectedStatus,
        'interest': _selectedInterest,
        'isVip': _isVip,
        'budget': budget,
        'notes': _notesController.text.trim(),
        'updatedAt': FieldValue.serverTimestamp(),
        if (imageUrl != null) 'imageUrl': imageUrl,
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تحديث بيانات العميل بنجاح',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white)),
            backgroundColor: Colors.green));
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في تحديث البيانات: ${e.toString()}',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white)),
            backgroundColor: Colors.red));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _notesController.dispose();
    _budgetController.dispose();
    super.dispose();
  }
}
