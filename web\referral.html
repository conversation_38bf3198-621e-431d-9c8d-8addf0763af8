<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>انضم إلى Krea - تطبيق العقارات الأول في الكويت</title>
    
    <!-- Meta Tags for SEO -->
    <meta name="description" content="انضم إلى Krea، تطبيق العقارات الأول في الكويت. اكتشف أفضل العقارات للبيع والإيجار مع مزايا حصرية.">
    <meta name="keywords" content="عقارات الكويت، شقق للبيع، فلل للإيجار، عقارات، كريا، Krea">
    <meta name="author" content="Codnet Moroccan">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="انضم إلى KREA - تطبيق العقارات الأول في الكويت">
    <meta property="og:description" content="اكتشف أفضل العقارات في الكويت مع تطبيق KREA. احصل على مزايا حصرية عند الانضمام!">
    <meta property="og:image" content="https://krea-app.web.app/icons/Icon-512.png">
    <meta property="og:url" content="https://krea-app.web.app/referral">
    <meta property="og:type" content="website">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="انضم إلى KREA - تطبيق العقارات الأول في الكويت">
    <meta name="twitter:description" content="اكتشف أفضل العقارات في الكويت مع تطبيق KREA">
    <meta name="twitter:image" content="https://krea-app.web.app/icons/Icon-512.png">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="favicon.png">
    <link rel="apple-touch-icon" href="icons/Icon-192.png">
    
    <!-- Google Fonts - Cairo -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
        }
        
        h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 28px;
            font-weight: 700;
        }
        
        .subtitle {
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 16px;
            line-height: 1.5;
        }
        
        .features {
            margin: 30px 0;
            text-align: right;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-left: 15px;
            font-size: 18px;
        }
        
        .feature-text {
            flex: 1;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .download-buttons {
            margin: 30px 0;
        }
        
        .download-btn {
            display: inline-block;
            margin: 10px;
            padding: 12px 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            min-width: 200px;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .referral-info {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .referral-code {
            font-size: 24px;
            font-weight: bold;
            color: #2e7d32;
            margin: 10px 0;
            letter-spacing: 2px;
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            color: #95a5a6;
            font-size: 14px;
        }
        
        .loading {
            display: none;
            margin: 20px 0;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            .download-btn {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">K</div>
        
        <h1>مرحباً بك في Krea</h1>
        <p class="subtitle">تطبيق العقارات الأول في الكويت<br>اكتشف أفضل العقارات للبيع والإيجار</p>
        
        <div class="referral-info" id="referralInfo" style="display: none;">
            <h3>🎉 لديك دعوة خاصة!</h3>
            <p>تم دعوتك للانضمام إلى Krea</p>
            <div class="referral-code" id="referralCode"></div>
            <p>استخدم هذا الرمز عند التسجيل واحصل على <strong>20 نقطة مجاناً</strong>!</p>
        </div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🏠</div>
                <div class="feature-text">آلاف العقارات المتنوعة</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🔍</div>
                <div class="feature-text">بحث ذكي وفلاتر متقدمة</div>
            </div>
            <div class="feature">
                <div class="feature-icon">📱</div>
                <div class="feature-text">واجهة سهلة وحديثة</div>
            </div>
            <div class="feature">
                <div class="feature-icon">⭐</div>
                <div class="feature-text">نظام نقاط ومكافآت</div>
            </div>
        </div>
        
        <div class="download-buttons">
            <a href="#" class="download-btn" id="downloadBtn" onclick="downloadApp()">
                📱 تحميل التطبيق
            </a>
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>جاري التحضير...</p>
        </div>
        
        <div class="footer">
            <p>تطبيق Krea - صنع بـ ❤️ في الكويت</p>
            <p>© 2024 Codnet Moroccan. جميع الحقوق محفوظة.</p>
        </div>
    </div>

    <script>
        // استخراج معاملات URL
        function getUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            return {
                code: urlParams.get('code'),
                id: urlParams.get('id')
            };
        }
        
        // عرض معلومات الإحالة
        function displayReferralInfo() {
            const params = getUrlParams();
            if (params.code) {
                document.getElementById('referralInfo').style.display = 'block';
                document.getElementById('referralCode').textContent = params.code;
                
                // حفظ رمز الإحالة في التخزين المحلي
                localStorage.setItem('referralCode', params.code);
                if (params.id) {
                    localStorage.setItem('referralId', params.id);
                }
            }
        }
        
        // تحميل التطبيق
        function downloadApp() {
            const loading = document.getElementById('loading');
            const downloadBtn = document.getElementById('downloadBtn');
            
            loading.style.display = 'block';
            downloadBtn.style.display = 'none';
            
            // محاولة فتح التطبيق أولاً
            const params = getUrlParams();
            const appUrl = params.code ? 
                `krea://referral?code=${params.code}&id=${params.id || ''}` : 
                'krea://';
            
            // محاولة فتح التطبيق
            window.location.href = appUrl;
            
            // إذا لم يتم فتح التطبيق، توجيه إلى متجر التطبيقات
            setTimeout(() => {
                // التحقق من نوع الجهاز
                const userAgent = navigator.userAgent || navigator.vendor || window.opera;
                
                if (/android/i.test(userAgent)) {
                    // Android - توجيه إلى Google Play Store
                    window.location.href = 'https://play.google.com/store/apps/details?id=com.codnet.krea';
                } else if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
                    // iOS - توجيه إلى App Store
                    window.location.href = 'https://apps.apple.com/app/krea/id123456789';
                } else {
                    // أجهزة أخرى - عرض رسالة
                    alert('يرجى تحميل تطبيق KREA من متجر التطبيقات على هاتفك المحمول');
                }
                
                loading.style.display = 'none';
                downloadBtn.style.display = 'inline-block';
            }, 2000);
        }
        
        // تشغيل الكود عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            displayReferralInfo();
        });
    </script>
</body>
</html>
