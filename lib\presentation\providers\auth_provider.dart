import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../core/services/loyalty_program_service.dart';

/// مزود المصادقة
class AuthProvider extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  User? _user;
  bool _isLoading = false;
  String? _error;

  /// المستخدم الحالي
  User? get user => _user;

  /// حالة التحميل
  bool get isLoading => _isLoading;

  /// رسالة الخطأ
  String? get error => _error;

  /// حالة تسجيل الدخول
  bool get isLoggedIn => _user != null;

  /// إنشاء مزود المصادقة
  AuthProvider() {
    _init();
  }

  /// تهيئة المزود
  Future<void> _init() async {
    _auth.authStateChanges().listen((User? user) {
      _user = user;
      notifyListeners();
    });
  }

  /// تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password);

      _user = userCredential.user;
      _isLoading = false;
      notifyListeners();

      // تحديث آخر تسجيل دخول وFCM Token
      if (_user != null) {
        // الحصول على FCM Token
        String? fcmToken;
        try {
          fcmToken = await FirebaseMessaging.instance.getToken();
        } catch (e) {
          // تجاهل خطأ الحصول على FCM Token
        }

        await _firestore.collection('users').doc(_user!.uid).update({
          'lastLoginAt': FieldValue.serverTimestamp(),
          'lastLoginDate': Timestamp.now(),
          if (fcmToken != null) 'fcmToken': fcmToken,
          if (fcmToken != null) 'deviceTokens': FieldValue.arrayUnion([fcmToken]),
          if (fcmToken != null) 'lastTokenUpdate': FieldValue.serverTimestamp(),
        });

        // إضافة نقاط برنامج الولاء للتسجيل اليومي
        try {
          // استدعاء خدمة برنامج الولاء لإضافة نقاط تسجيل الدخول اليومي
          final loyaltyService = LoyaltyProgramService();
          await loyaltyService.addPointsForDailyLogin();
        } catch (loyaltyError) {
          // تجاهل الأخطاء في إضافة نقاط الولاء لتجنب فشل تسجيل الدخول
          debugPrint('خطأ في إضافة نقاط الولاء: $loyaltyError');
        }
      }

      return true;
    } catch (e) {
      _isLoading = false;
      _error = _getAuthErrorMessage(e);
      notifyListeners();
      return false;
    }
  }

  /// إنشاء حساب جديد بالبريد الإلكتروني وكلمة المرور
  Future<bool> signUpWithEmailAndPassword(
    String email,
    String password,
    String displayName,
    String phoneNumber,
    String userType,
    {File? profileImage}
  ) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password);

      _user = userCredential.user;

      // تحديث اسم المستخدم
      await _user?.updateDisplayName(displayName);

      // تحديث بيانات المستخدم في Firestore
      if (_user != null) {
        // الحصول على FCM Token
        String? fcmToken;
        try {
          fcmToken = await FirebaseMessaging.instance.getToken();
        } catch (e) {
          // تجاهل خطأ الحصول على FCM Token
        }

        await _firestore.collection('users').doc(_user!.uid).set({
          'email': email,
          'displayName': displayName,
          'phoneNumber': phoneNumber,
          'userType': userType,
          'createdAt': FieldValue.serverTimestamp(),
          'lastLoginAt': FieldValue.serverTimestamp(),
          'lastLoginDate': Timestamp.now(),
          'isActive': true,
          if (fcmToken != null) 'fcmToken': fcmToken,
          if (fcmToken != null) 'deviceTokens': FieldValue.arrayUnion([fcmToken]),
          if (fcmToken != null) 'lastTokenUpdate': FieldValue.serverTimestamp(),
        });

        // إضافة نقاط برنامج الولاء لإنشاء حساب جديد
        try {
          final loyaltyService = LoyaltyProgramService();

          // إضافة نقاط إنشاء الحساب
          await loyaltyService.addPoints(50, 'إنشاء حساب جديد');

          // إضافة نقاط تسجيل الدخول اليومي
          await loyaltyService.addPointsForDailyLogin();
        } catch (loyaltyError) {
          // تجاهل الأخطاء في إضافة نقاط الولاء لتجنب فشل إنشاء الحساب
          debugPrint('خطأ في إضافة نقاط الولاء: $loyaltyError');
        }
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = _getAuthErrorMessage(e);
      notifyListeners();
      return false;
    }
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _auth.signOut();
      _user = null;
    } catch (e) {
      _error = _getAuthErrorMessage(e);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// إعادة تعيين كلمة المرور
  Future<bool> resetPassword(String email) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _auth.sendPasswordResetEmail(email: email);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = _getAuthErrorMessage(e);
      notifyListeners();
      return false;
    }
  }

  /// تحديث بيانات المستخدم
  Future<bool> updateUserProfile({
    String? displayName,
    String? phoneNumber,
    File? profileImage,
  }) async {
    if (_user == null) return false;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // تحديث اسم المستخدم
      if (displayName != null && displayName.isNotEmpty) {
        await _user?.updateDisplayName(displayName);
      }

      // تحديث بيانات المستخدم في Firestore
      final updateData = <String, dynamic>{
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (displayName != null && displayName.isNotEmpty) {
        updateData['displayName'] = displayName;
      }

      if (phoneNumber != null && phoneNumber.isNotEmpty) {
        updateData['phoneNumber'] = phoneNumber;
      }

      await _firestore.collection('users').doc(_user!.uid).update(updateData);

      // تحديث المستخدم الحالي
      _user = _auth.currentUser;

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = _getAuthErrorMessage(e);
      notifyListeners();
      return false;
    }
  }

  /// الحصول على رسالة خطأ المصادقة
  String _getAuthErrorMessage(dynamic error) {
    if (error is FirebaseAuthException) {
      switch (error.code) {
        case 'user-not-found':
          return 'لم يتم العثور على مستخدم بهذا البريد الإلكتروني';
        case 'wrong-password':
          return 'كلمة المرور غير صحيحة';
        case 'email-already-in-use':
          return 'البريد الإلكتروني مستخدم بالفعل';
        case 'weak-password':
          return 'كلمة المرور ضعيفة';
        case 'invalid-email':
          return 'البريد الإلكتروني غير صالح';
        case 'user-disabled':
          return 'تم تعطيل هذا الحساب';
        case 'too-many-requests':
          return 'تم تعطيل الوصول إلى هذا الحساب مؤقتًا بسبب العديد من محاولات تسجيل الدخول الفاشلة';
        case 'operation-not-allowed':
          return 'تسجيل الدخول بالبريد الإلكتروني وكلمة المرور غير مفعل';
        case 'account-exists-with-different-credential':
          return 'هناك حساب بالفعل بهذا البريد الإلكتروني';
        case 'requires-recent-login':
          return 'تتطلب هذه العملية إعادة مصادقة المستخدم';
        default:
          return 'حدث خطأ: ${error.message}';
      }
    }
    return 'حدث خطأ غير متوقع';
  }
}
