import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'loyalty_program_service.dart';
import 'referral_tracking_service.dart';

/// مستويات الإحالة
enum ReferralTier {
  /// مبتدئ (0-4 إحالات)
  beginner,

  /// متوسط (5-14 إحالات)
  intermediate,

  /// متقدم (15-29 إحالات)
  advanced,

  /// خبير (30-49 إحالات)
  expert,

  /// أسطوري (50+ إحالات)
  legendary,
}

/// نموذج بيانات الإحالة
class ReferralData {
  /// معرف الإحالة
  final String id;

  /// معرف المستخدم المرسل
  final String referrerId;

  /// رمز الإحالة
  final String referralCode;

  /// عدد الإحالات الناجحة
  final int successfulReferrals;

  /// عدد النقاط المكتسبة
  final int pointsEarned;

  /// قائمة المستخدمين المحالين
  final List<Map<String, dynamic>> referredUsers;

  /// تاريخ الإنشاء
  final DateTime createdAt;

  ReferralData({
    required this.id,
    required this.referrerId,
    required this.referralCode,
    required this.successfulReferrals,
    required this.pointsEarned,
    required this.referredUsers,
    required this.createdAt,
  });

  /// إنشاء نموذج من وثيقة Firestore
  factory ReferralData.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return ReferralData(
      id: doc.id,
      referrerId: data['referrerId'] ?? '',
      referralCode: data['referralCode'] ?? '',
      successfulReferrals: data['successfulReferrals'] ?? 0,
      pointsEarned: data['pointsEarned'] ?? 0,
      referredUsers:
          List<Map<String, dynamic>>.from(data['referredUsers'] ?? []),
      createdAt: (data['createdAt'] as Timestamp).toDate());
  }

  /// تحويل النموذج إلى Map لتخزينه في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'referrerId': referrerId,
      'referralCode': referralCode,
      'successfulReferrals': successfulReferrals,
      'pointsEarned': pointsEarned,
      'referredUsers': referredUsers,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }
}

/// خدمة برنامج الإحالة
class ReferralProgramService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final LoyaltyProgramService _loyaltyService = LoyaltyProgramService();
  final ReferralTrackingService _trackingService = ReferralTrackingService();

  /// الحصول على بيانات الإحالة للمستخدم الحالي
  Future<ReferralData?> getCurrentUserReferralData() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return null;
      }

      final doc = await _firestore.collection('referrals').doc(user.uid).get();

      if (!doc.exists) {
        // إنشاء بيانات إحالة جديدة للمستخدم
        final referralCode = _generateReferralCode();

        final newData = ReferralData(
          id: user.uid,
          referrerId: user.uid,
          referralCode: referralCode,
          successfulReferrals: 0,
          pointsEarned: 0,
          referredUsers: [],
          createdAt: DateTime.now());

        await _firestore
            .collection('referrals')
            .doc(user.uid)
            .set(newData.toFirestore());

        return newData;
      }

      return ReferralData.fromFirestore(doc);
    } catch (e) {
      return null;
    }
  }

  /// إنشاء رابط إحالة
  Future<Map<String, String?>> createReferralLinks() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return {'directLink': null, 'webLink': null, 'universalLink': null};
      }

      final referralData = await getCurrentUserReferralData();
      if (referralData == null) {
        return {'directLink': null, 'webLink': null, 'universalLink': null};
      }

      final referralCode = referralData.referralCode;

      // إنشاء رابط مباشر للتطبيق (Custom URL Scheme)
      final directAppLink = 'krea://referral?code=$referralCode';

      // إنشاء معرف فريد للإحالة
      final referralId =
          'ref_${DateTime.now().millisecondsSinceEpoch}_${user.uid.substring(0, 5)}';

      // إنشاء رابط ويب للمستخدمين الذين ليس لديهم التطبيق
      final webLink =
          'https://real-estate-998a9.web.app/referral?code=$referralCode&id=$referralId';

      // إنشاء رابط عالمي يعمل على جميع الأجهزة (صفحة إعادة التوجيه)
      final universalLink =
          'https://real-estate-998a9.web.app/referral?code=$referralCode&id=$referralId';

      return {
        'directLink': directAppLink,
        'webLink': webLink,
        'universalLink': universalLink,
      };
    } catch (e) {
      return {'directLink': null, 'webLink': null, 'universalLink': null};
    }
  }

  /// إنشاء رابط إحالة (للتوافق مع الكود القديم)
  Future<String?> createReferralLink() async {
    final links = await createReferralLinks();
    return links['directLink'];
  }

  /// التحقق من رمز الإحالة
  Future<bool> validateReferralCode(String referralCode) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      // التحقق من أن المستخدم لم يستخدم رمز إحالة من قبل
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (userDoc.exists) {
        final userData = userDoc.data();
        if (userData != null && userData['referredBy'] != null) {
          return false;
        }
      }

      // البحث عن رمز الإحالة
      final query = await _firestore
          .collection('referrals')
          .where('referralCode', isEqualTo: referralCode)
          .limit(1)
          .get();

      if (query.docs.isEmpty) {
        return false;
      }

      final referrerDoc = query.docs.first;
      final referrerId = referrerDoc.id;

      // التحقق من أن المستخدم لا يحاول استخدام رمز الإحالة الخاص به
      if (referrerId == user.uid) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// استخدام رمز الإحالة
  Future<bool> useReferralCode(String referralCode,
      {String? referralId}) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      // التحقق من صلاحية رمز الإحالة باستخدام خدمة التتبع
      final isValid =
          await _trackingService.verifyReferral(referralCode, referralId);
      if (!isValid) {
        return false;
      }

      // البحث عن رمز الإحالة
      final query = await _firestore
          .collection('referrals')
          .where('referralCode', isEqualTo: referralCode)
          .limit(1)
          .get();

      if (query.docs.isEmpty) {
        return false;
      }

      final referrerDoc = query.docs.first;
      final referrerId = referrerDoc.id;

      // تحديث بيانات المستخدم المحال
      await _firestore.collection('users').doc(user.uid).update({
        'referredBy': referrerId,
        'referralCode': referralCode,
        'referralId': referralId,
        'referralTimestamp': FieldValue.serverTimestamp(),
      });

      // تحديث بيانات المستخدم المرسل
      final referredUser = {
        'userId': user.uid,
        'date': Timestamp.now(),
        'referralId': referralId,
        'rewarded': false, // سيتم تحديثه إلى true بعد التحقق
        'status': 'pending', // pending, completed, rejected
      };

      await _firestore.collection('referrals').doc(referrerId).update({
        'pendingReferrals': FieldValue.increment(1),
        'referredUsers': FieldValue.arrayUnion([referredUser]),
        'lastReferralDate': Timestamp.now(),
      });

      // إضافة نقاط للمستخدم المحال (فورًا)
      await _loyaltyService.addPoints(20, 'استخدام رمز إحالة');

      // إكمال عملية الإحالة في نظام التتبع
      await _trackingService.completeReferral();

      // حفظ رمز الإحالة في التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('referralCode', referralCode);
      if (referralId != null) {
        await prefs.setString('referralId', referralId);
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// تحديث حالة الإحالة وإضافة النقاط للمستخدم المرسل
  Future<bool> completeReferralReward(
      String referralCode, String userId) async {
    try {
      // البحث عن رمز الإحالة
      final query = await _firestore
          .collection('referrals')
          .where('referralCode', isEqualTo: referralCode)
          .limit(1)
          .get();

      if (query.docs.isEmpty) {
        return false;
      }

      final referrerDoc = query.docs.first;
      final referrerId = referrerDoc.id;

      // تحديث حالة الإحالة
      final referralData = referrerDoc.data();
      final referredUsers =
          List<Map<String, dynamic>>.from(referralData['referredUsers'] ?? []);

      bool updated = false;
      for (int i = 0; i < referredUsers.length; i++) {
        if (referredUsers[i]['userId'] == userId &&
            referredUsers[i]['status'] == 'pending') {
          referredUsers[i]['rewarded'] = true;
          referredUsers[i]['status'] = 'completed';
          referredUsers[i]['completionDate'] = Timestamp.now();
          updated = true;
          break;
        }
      }

      if (!updated) {
        return false;
      }

      // تحديث بيانات الإحالة
      await _firestore.collection('referrals').doc(referrerId).update({
        'successfulReferrals': FieldValue.increment(1),
        'pendingReferrals': FieldValue.increment(-1),
        'pointsEarned': FieldValue.increment(30),
        'referredUsers': referredUsers,
      });

      // إضافة نقاط للمستخدم المرسل
      await _loyaltyService.addPoints(30, 'دعوة مستخدم جديد',
          userId: referrerId);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على رمز الإحالة المستخدم من التخزين المحلي
  Future<String?> getUsedReferralCode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('referralCode');
    } catch (e) {
      return null;
    }
  }

  /// الحصول على قائمة المستخدمين المحالين
  Future<List<Map<String, dynamic>>> getReferredUsers() async {
    try {
      final referralData = await getCurrentUserReferralData();
      if (referralData == null) {
        return [];
      }

      final referredUsers =
          List<Map<String, dynamic>>.from(referralData.referredUsers);

      // إضافة معلومات المستخدمين
      for (int i = 0; i < referredUsers.length; i++) {
        final userId = referredUsers[i]['userId'] as String;
        final userDoc = await _firestore.collection('users').doc(userId).get();

        if (userDoc.exists) {
          final userData = userDoc.data();
          if (userData != null) {
            referredUsers[i]['name'] = userData['name'] ?? 'مستخدم';
            referredUsers[i]['photoURL'] = userData['photoURL'];
          }
        }
      }

      return referredUsers;
    } catch (e) {
      return [];
    }
  }

  /// إنشاء رمز إحالة فريد
  String _generateReferralCode() {
    // إنشاء رمز إحالة من 8 أحرف
    final uuid = const Uuid().v4();
    return uuid.substring(0, 8).toUpperCase();
  }

  // ===== وظائف الإحالة المحسنة =====

  /// الحصول على مستوى الإحالة الحالي للمستخدم
  Future<ReferralTier> getCurrentReferralTier() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return ReferralTier.beginner;

      final referralData = await getCurrentUserReferralData();
      if (referralData == null) return ReferralTier.beginner;

      final successfulReferrals = referralData.successfulReferrals;
      return _getTierFromReferrals(successfulReferrals);
    } catch (e) {
      return ReferralTier.beginner;
    }
  }

  /// تحديد المستوى من عدد الإحالات
  ReferralTier _getTierFromReferrals(int referrals) {
    if (referrals >= 50) return ReferralTier.legendary;
    if (referrals >= 30) return ReferralTier.expert;
    if (referrals >= 15) return ReferralTier.advanced;
    if (referrals >= 5) return ReferralTier.intermediate;
    return ReferralTier.beginner;
  }

  /// الحصول على اسم المستوى
  String getTierName(ReferralTier tier) {
    switch (tier) {
      case ReferralTier.beginner:
        return 'مبتدئ';
      case ReferralTier.intermediate:
        return 'متوسط';
      case ReferralTier.advanced:
        return 'متقدم';
      case ReferralTier.expert:
        return 'خبير';
      case ReferralTier.legendary:
        return 'أسطوري';
    }
  }

  /// تطبيق مكافآت الإحالة المتدرجة
  Future<Map<String, dynamic>> applyEnhancedReferralRewards(String referrerId, String referredUserId) async {
    try {
      // الحصول على بيانات المُحيل
      final referralData = await _firestore
          .collection('referrals')
          .doc(referrerId)
          .get();

      if (!referralData.exists) {
        return {'success': false, 'message': 'بيانات الإحالة غير موجودة'};
      }

      final data = referralData.data()!;
      final successfulReferrals = data['successfulReferrals'] ?? 0;
      final newReferralCount = successfulReferrals + 1;

      // حساب المكافآت بناءً على المستوى
      final tier = _getTierFromReferrals(newReferralCount);
      final basePoints = _getBasePointsForTier(tier);
      final bonusPoints = _getBonusPointsForMilestone(newReferralCount);

      // إضافة النقاط للمُحيل باستخدام النظام المحسن
      await applyEnhancedReferralRewards(referrerId, referredUserId);

      // إضافة نقاط للمستخدم المُحال
      await _loyaltyService.addPoints(
        30,
        'الانضمام عبر إحالة',
        userId: referredUserId,
      );

      // تحديث إحصائيات الإحالة
      await _firestore.collection('referrals').doc(referrerId).update({
        'successfulReferrals': newReferralCount,
        'currentTier': tier.index,
        'totalPointsEarned': FieldValue.increment(basePoints + bonusPoints),
        'lastRewardDate': Timestamp.now(),
      });

      // التحقق من تحقيق إنجازات جديدة
      final achievements = await _checkForNewAchievements(referrerId, newReferralCount);

      return {
        'success': true,
        'pointsEarned': basePoints + bonusPoints,
        'newTier': getTierName(tier),
        'achievements': achievements,
      };
    } catch (e) {
      return {'success': false, 'message': 'حدث خطأ أثناء تطبيق المكافآت'};
    }
  }

  /// الحصول على النقاط الأساسية للمستوى
  int _getBasePointsForTier(ReferralTier tier) {
    switch (tier) {
      case ReferralTier.beginner:
        return 30;
      case ReferralTier.intermediate:
        return 40;
      case ReferralTier.advanced:
        return 50;
      case ReferralTier.expert:
        return 75;
      case ReferralTier.legendary:
        return 100;
    }
  }

  /// الحصول على نقاط المكافأة للإنجازات
  int _getBonusPointsForMilestone(int referralCount) {
    final milestones = [1, 5, 10, 15, 25, 30, 50, 75, 100];
    if (milestones.contains(referralCount)) {
      return referralCount * 10; // مكافأة إضافية للإنجازات
    }
    return 0;
  }

  /// التحقق من الإنجازات الجديدة
  Future<List<String>> _checkForNewAchievements(String userId, int referralCount) async {
    final achievements = <String>[];

    // قائمة الإنجازات
    final milestones = {
      1: 'الإحالة الأولى',
      5: 'مبتدئ الإحالة',
      10: 'محترف الإحالة',
      15: 'خبير الإحالة',
      25: 'نجم الإحالة',
      30: 'أسطورة الإحالة',
      50: 'ملك الإحالة',
      75: 'إمبراطور الإحالة',
      100: 'أسطورة الإحالات',
    };

    if (milestones.containsKey(referralCount)) {
      final achievementName = milestones[referralCount]!;
      achievements.add(achievementName);

      // حفظ الإنجاز في قاعدة البيانات
      await _firestore
          .collection('user_achievements')
          .doc(userId)
          .collection('referral_achievements')
          .doc('referral_$referralCount')
          .set({
        'name': achievementName,
        'description': 'تحقيق $referralCount إحالة ناجحة',
        'achievedAt': Timestamp.now(),
        'referralCount': referralCount,
        'type': 'referral_milestone',
      });
    }

    return achievements;
  }

  /// الحصول على إحصائيات الإحالة المفصلة
  Future<Map<String, dynamic>> getDetailedReferralStats() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return {};

      final referralData = await getCurrentUserReferralData();
      if (referralData == null) return {};

      final currentTier = await getCurrentReferralTier();
      final nextTierRequirement = _getNextTierRequirement(currentTier);

      return {
        'currentTier': getTierName(currentTier),
        'successfulReferrals': referralData.successfulReferrals,
        'totalPointsEarned': referralData.pointsEarned,
        'nextTierRequirement': nextTierRequirement,
        'referralsToNextTier': nextTierRequirement != null
            ? nextTierRequirement - referralData.successfulReferrals
            : 0,
        'referralCode': referralData.referralCode,
      };
    } catch (e) {
      return {};
    }
  }

  /// الحصول على متطلبات المستوى التالي
  int? _getNextTierRequirement(ReferralTier currentTier) {
    switch (currentTier) {
      case ReferralTier.beginner:
        return 5;
      case ReferralTier.intermediate:
        return 15;
      case ReferralTier.advanced:
        return 30;
      case ReferralTier.expert:
        return 50;
      case ReferralTier.legendary:
        return null; // أعلى مستوى
    }
  }
}
