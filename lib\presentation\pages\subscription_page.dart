import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:kuwait_corners/core/config/app_mode_config.dart';
import 'package:kuwait_corners/core/models/subscription_model.dart';
import 'package:kuwait_corners/core/services/subscription_service.dart';
import 'package:kuwait_corners/presentation/widgets/custom_app_bar.dart';
import 'package:kuwait_corners/presentation/widgets/custom_button.dart';
import 'package:kuwait_corners/presentation/widgets/loading_indicator.dart';

/// صفحة الاشتراكات
class SubscriptionPage extends StatefulWidget {
  const SubscriptionPage({super.key});

  @override
  State<SubscriptionPage> createState() => _SubscriptionPageState();
}

class _SubscriptionPageState extends State<SubscriptionPage> {
  final SubscriptionService _subscriptionService = SubscriptionService();
  bool _isLoading = true;
  SubscriptionModel? _currentSubscription;
  List<Map<String, dynamic>> _availableSubscriptions = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentSubscription =
          await _subscriptionService.getCurrentSubscription();
      final availableSubscriptions =
          await _subscriptionService.getAvailableSubscriptions();

      setState(() {
        _currentSubscription = currentSubscription;
        _availableSubscriptions = availableSubscriptions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء تحميل البيانات: $e')));
      }
    }
  }

  /// شراء اشتراك
  Future<void> _purchaseSubscription(SubscriptionType type) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // هنا يتم تنفيذ عملية الدفع
      // في هذا المثال، نفترض أن عملية الدفع تمت بنجاح
      final paymentId = 'payment_${DateTime.now().millisecondsSinceEpoch}';

      final subscription =
          await _subscriptionService.purchaseSubscription(type, paymentId);

      setState(() {
        _currentSubscription = subscription;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم شراء الاشتراك بنجاح')));
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء شراء الاشتراك: $e')));
      }
    }
  }

  /// تجديد الاشتراك
  Future<void> _renewSubscription() async {
    if (_currentSubscription == null) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // هنا يتم تنفيذ عملية الدفع
      // في هذا المثال، نفترض أن عملية الدفع تمت بنجاح
      final paymentId = 'payment_${DateTime.now().millisecondsSinceEpoch}';

      final success = await _subscriptionService.renewSubscription(
          _currentSubscription!.id, paymentId);

      if (success) {
        await _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم تجديد الاشتراك بنجاح')));
        }
      } else {
        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('فشل تجديد الاشتراك')));
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء تجديد الاشتراك: $e')));
      }
    }
  }

  /// إلغاء الاشتراك
  Future<void> _cancelSubscription() async {
    if (_currentSubscription == null) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _subscriptionService
          .cancelSubscription(_currentSubscription!.id);

      if (success) {
        await _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إلغاء التجديد التلقائي للاشتراك')));
        }
      } else {
        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('فشل إلغاء التجديد التلقائي للاشتراك')));
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content:
                  Text('حدث خطأ أثناء إلغاء التجديد التلقائي للاشتراك: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // في الوضع المعلوماتي، إخفاء الصفحة تماماً
    if (AppModeConfig.isInformationalOnly) {
      // العودة للصفحة السابقة فوراً
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.pop(context);
      });
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'الاشتراكات',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildCurrentSubscription(),
                  const SizedBox(height: 24),
                  const Text(
                    'الاشتراكات المتاحة',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold)),
                  const SizedBox(height: 16),
                  _buildAvailableSubscriptions(),
                ])));
  }

  /// بناء واجهة الاشتراك الحالي
  Widget _buildCurrentSubscription() {
    if (_currentSubscription == null) {
      return const SizedBox();
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الاشتراك الحالي: ${_currentSubscription!.getSubscriptionTypeName()}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
                _buildSubscriptionBadge(_currentSubscription!.type),
              ]),
            const SizedBox(height: 16),
            _buildInfoRow('الإعلانات المتبقية:',
                '${_currentSubscription!.remainingAds} / ${_currentSubscription!.allowedAds}'),
            _buildInfoRow('عدد الصور لكل إعلان:',
                '${_currentSubscription!.allowedImagesPerAd}'),
            _buildInfoRow('مدة عرض الإعلان:',
                '${_currentSubscription!.adDurationDays} يوم'),
            _buildInfoRow(
                'تاريخ الانتهاء:', _formatDate(_currentSubscription!.endDate)),
            _buildInfoRow('التجديد التلقائي:',
                _currentSubscription!.autoRenew ? 'مفعل' : 'غير مفعل'),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                if (_currentSubscription!.type != SubscriptionType.free) ...[
                  CustomButton(
                    text: 'تجديد الاشتراك',
                    onPressed: _renewSubscription,
                    width: 150),
                  CustomButton(
                    text: _currentSubscription!.autoRenew
                        ? 'إلغاء التجديد التلقائي'
                        : 'تفعيل التجديد التلقائي',
                    onPressed: _cancelSubscription,
                    width: 150,
                    color: _currentSubscription!.autoRenew
                        ? Colors.red
                        : Colors.green),
                ],
              ]),
          ])));
  }

  /// بناء واجهة الاشتراكات المتاحة
  Widget _buildAvailableSubscriptions() {
    return Column(
      children: _availableSubscriptions.map((subscription) {
        final type = subscription['type'] as SubscriptionType;
        final isCurrentSubscription =
            _currentSubscription != null && _currentSubscription!.type == type;

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: isCurrentSubscription
                  ? Theme.of(context).primaryColor
                  : Colors.transparent,
              width: 2)),
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      subscription['name'] as String,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold)),
                    _buildSubscriptionBadge(type),
                  ]),
                const SizedBox(height: 8),
                Text(
                  subscription['description'] as String,
                  style: TextStyle(
                    color: Colors.grey[600])),
                const SizedBox(height: 16),
                const Text(
                  'المميزات:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                ...List<Widget>.from(
                  (subscription['features'] as List<String>).map(
                    (feature) => Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.check_circle,
                            color: Colors.green,
                            size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(feature)),
                        ])))),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      type == SubscriptionType.free
                          ? 'مجاني'
                          : '${subscription['price']} ${subscription['currency']}${type == SubscriptionType.monthly ? ' / شهر' : ' / سنة'}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold)),
                    if (!isCurrentSubscription)
                      CustomButton(
                        text: 'اشترك الآن',
                        onPressed: () => _purchaseSubscription(type),
                        width: 120),
                    if (isCurrentSubscription)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(20)),
                        child: const Text(
                          'الاشتراك الحالي',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold))),
                  ]),
              ])));
      }).toList());
  }

  /// بناء شارة نوع الاشتراك
  Widget _buildSubscriptionBadge(SubscriptionType type) {
    Color color;
    String text;

    switch (type) {
      case SubscriptionType.free:
        color = Colors.grey;
        text = 'مجاني';
        break;
      case SubscriptionType.monthly:
        color = Colors.blue;
        text = 'شهري';
        break;
      case SubscriptionType.yearly:
        color = Colors.purple;
        text = 'سنوي';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 12,
        vertical: 6),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(20)),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold)));
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.grey)),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold)),
        ]));
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }
}
