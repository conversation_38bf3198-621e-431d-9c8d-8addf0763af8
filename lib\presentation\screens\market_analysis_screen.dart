import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';

/// شاشة تحليلات السوق
class MarketAnalysisScreen extends StatefulWidget {
  const MarketAnalysisScreen({super.key});

  @override
  State<MarketAnalysisScreen> createState() => _MarketAnalysisScreenState();
}

class _MarketAnalysisScreenState extends State<MarketAnalysisScreen> {
  bool _isLoading = false;
  String? _errorMessage;
  Map<String, dynamic> _marketData = {};

  @override
  void initState() {
    super.initState();
    _loadAnalyses();
  }

  /// تحميل تحليلات السوق من Firebase
  Future<void> _loadAnalyses() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل بيانات العقارات
      final propertiesSnapshot = await FirebaseFirestore.instance
          .collection('properties')
          .get();

      final properties = propertiesSnapshot.docs.map((doc) => {
        'id': doc.id,
        ...doc.data(),
      }).toList();

      // تحميل بيانات طلبات العقارات
      final requestsSnapshot = await FirebaseFirestore.instance
          .collection('propertyRequests')
          .get();

      final requests = requestsSnapshot.docs.map((doc) => {
        'id': doc.id,
        ...doc.data(),
      }).toList();

      // حساب التحليلات
      _calculateMarketAnalytics(properties, requests);

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل تحليلات السوق: $e';
        _isLoading = false;
      });
    }
  }

  /// حساب تحليلات السوق المتقدمة
  void _calculateMarketAnalytics(List<Map<String, dynamic>> properties, List<Map<String, dynamic>> requests) {
    // تحليل الأسعار
    final prices = properties
        .where((p) => p['price'] != null)
        .map((p) => (p['price'] as num).toDouble())
        .toList();

    final avgPrice = prices.isNotEmpty ? prices.reduce((a, b) => a + b) / prices.length : 0.0;
    final maxPrice = prices.isNotEmpty ? prices.reduce((a, b) => a > b ? a : b) : 0.0;
    final minPrice = prices.isNotEmpty ? prices.reduce((a, b) => a < b ? a : b) : 0.0;

    // تحليل أنواع العقارات
    final propertyTypes = <String, int>{};
    final propertyTypePrices = <String, List<double>>{};
    for (final property in properties) {
      final type = property['subCategory'] ?? 'غير محدد';
      final price = (property['price'] as num?)?.toDouble();

      propertyTypes[type] = (propertyTypes[type] ?? 0) + 1;
      if (price != null) {
        propertyTypePrices[type] = (propertyTypePrices[type] ?? [])..add(price);
      }
    }

    // تحليل المناطق
    final areas = <String, int>{};
    final areaPrices = <String, List<double>>{};
    for (final property in properties) {
      final area = property['governorate'] ?? 'غير محدد';
      final price = (property['price'] as num?)?.toDouble();

      areas[area] = (areas[area] ?? 0) + 1;
      if (price != null) {
        areaPrices[area] = (areaPrices[area] ?? [])..add(price);
      }
    }

    // تحليل العرض والطلب
    final totalSupply = properties.length;
    final totalDemand = requests.length;
    final supplyDemandRatio = totalDemand > 0 ? totalSupply / totalDemand : 0.0;

    // تحليل اتجاهات السوق
    final marketTrend = _calculateMarketTrend(properties);
    final priceDistribution = _calculatePriceDistribution(prices);
    final topAreas = _getTopAreas(areas, areaPrices);
    final topPropertyTypes = _getTopPropertyTypes(propertyTypes, propertyTypePrices);

    // حساب مؤشرات الاستثمار
    final investmentScore = _calculateInvestmentScore(avgPrice, supplyDemandRatio, marketTrend);
    final marketHealth = _calculateMarketHealth(totalSupply, totalDemand, avgPrice);

    // حفظ البيانات في المتغير
    _marketData = {
      'avgPrice': avgPrice,
      'maxPrice': maxPrice,
      'minPrice': minPrice,
      'totalSupply': totalSupply,
      'totalDemand': totalDemand,
      'supplyDemandRatio': supplyDemandRatio,
      'propertyTypes': propertyTypes,
      'areas': areas,
      'properties': properties,
      'requests': requests,
      'marketTrend': marketTrend,
      'priceDistribution': priceDistribution,
      'topAreas': topAreas,
      'topPropertyTypes': topPropertyTypes,
      'investmentScore': investmentScore,
      'marketHealth': marketHealth,
      'propertyTypePrices': propertyTypePrices,
      'areaPrices': areaPrices,
    };
  }

  /// حساب اتجاه السوق
  Map<String, dynamic> _calculateMarketTrend(List<Map<String, dynamic>> properties) {
    // تحليل العقارات حسب تاريخ الإضافة
    final now = DateTime.now();
    final lastMonth = now.subtract(const Duration(days: 30));
    final lastWeek = now.subtract(const Duration(days: 7));

    final recentProperties = properties.where((p) {
      final createdAt = p['createdAt'] as Timestamp?;
      return createdAt != null && createdAt.toDate().isAfter(lastMonth);
    }).toList();

    final weeklyProperties = properties.where((p) {
      final createdAt = p['createdAt'] as Timestamp?;
      return createdAt != null && createdAt.toDate().isAfter(lastWeek);
    }).toList();

    final monthlyGrowth = properties.isNotEmpty ? (recentProperties.length / properties.length) * 100 : 0.0;
    final weeklyGrowth = properties.isNotEmpty ? (weeklyProperties.length / properties.length) * 100 : 0.0;

    String trend = 'مستقر';
    if (monthlyGrowth > 10) {
      trend = 'صاعد';
    } else if (monthlyGrowth < -5) {
      trend = 'هابط';
    }

    return {
      'trend': trend,
      'monthlyGrowth': monthlyGrowth,
      'weeklyGrowth': weeklyGrowth,
      'recentCount': recentProperties.length,
    };
  }

  /// حساب توزيع الأسعار
  Map<String, int> _calculatePriceDistribution(List<double> prices) {
    final distribution = <String, int>{
      'أقل من 50,000': 0,
      '50,000 - 100,000': 0,
      '100,000 - 200,000': 0,
      '200,000 - 500,000': 0,
      'أكثر من 500,000': 0,
    };

    for (final price in prices) {
      if (price < 50000) {
        distribution['أقل من 50,000'] = distribution['أقل من 50,000']! + 1;
      } else if (price < 100000) {
        distribution['50,000 - 100,000'] = distribution['50,000 - 100,000']! + 1;
      } else if (price < 200000) {
        distribution['100,000 - 200,000'] = distribution['100,000 - 200,000']! + 1;
      } else if (price < 500000) {
        distribution['200,000 - 500,000'] = distribution['200,000 - 500,000']! + 1;
      } else {
        distribution['أكثر من 500,000'] = distribution['أكثر من 500,000']! + 1;
      }
    }

    return distribution;
  }

  /// الحصول على أفضل المناطق
  List<Map<String, dynamic>> _getTopAreas(Map<String, int> areas, Map<String, List<double>> areaPrices) {
    final topAreas = areas.entries.map((entry) {
      final prices = areaPrices[entry.key] ?? [];
      final avgPrice = prices.isNotEmpty ? prices.reduce((a, b) => a + b) / prices.length : 0.0;

      return {
        'name': entry.key,
        'count': entry.value,
        'avgPrice': avgPrice,
      };
    }).toList();

    topAreas.sort((a, b) => (b['count'] as int).compareTo(a['count'] as int));
    return topAreas.take(5).toList();
  }

  /// الحصول على أفضل أنواع العقارات
  List<Map<String, dynamic>> _getTopPropertyTypes(Map<String, int> types, Map<String, List<double>> typePrices) {
    final topTypes = types.entries.map((entry) {
      final prices = typePrices[entry.key] ?? [];
      final avgPrice = prices.isNotEmpty ? prices.reduce((a, b) => a + b) / prices.length : 0.0;

      return {
        'name': entry.key,
        'count': entry.value,
        'avgPrice': avgPrice,
      };
    }).toList();

    topTypes.sort((a, b) => (b['count'] as int).compareTo(a['count'] as int));
    return topTypes.take(5).toList();
  }

  /// حساب مؤشر الاستثمار
  double _calculateInvestmentScore(double avgPrice, double supplyDemandRatio, Map<String, dynamic> marketTrend) {
    double score = 50.0; // نقطة البداية

    // تأثير نسبة العرض والطلب
    if (supplyDemandRatio < 1.0) {
      score += 20; // طلب أكثر من العرض
    } else if (supplyDemandRatio > 2.0) {
      score -= 15; // عرض أكثر من الطلب
    }

    // تأثير اتجاه السوق
    final trend = marketTrend['trend'] as String;
    switch (trend) {
      case 'صاعد':
        score += 15;
        break;
      case 'هابط':
        score -= 20;
        break;
    }

    // تأثير متوسط الأسعار
    if (avgPrice > 0 && avgPrice < 150000) {
      score += 10; // أسعار معقولة
    } else if (avgPrice > 300000) {
      score -= 5; // أسعار مرتفعة
    }

    return score.clamp(0, 100);
  }

  /// حساب صحة السوق
  String _calculateMarketHealth(int supply, int demand, double avgPrice) {
    final ratio = demand > 0 ? supply / demand : 0.0;

    if (ratio < 0.8) {
      return 'ممتاز'; // طلب عالي
    } else if (ratio < 1.2) {
      return 'جيد'; // متوازن
    } else if (ratio < 2.0) {
      return 'متوسط'; // عرض أكثر قليلاً
    } else {
      return 'ضعيف'; // عرض مفرط
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تحليلات سوق العقارات', style: CairoTextStyles.appBarTitle),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      backgroundColor: Colors.grey[50],
      body: Stack(
        children: [
          // خلفية الأيقونات الاقتصادية
          _buildEconomicIconsBackground(),
          // المحتوى الرئيسي
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _errorMessage != null
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.error, size: 64, color: Colors.red),
                          const SizedBox(height: 16),
                          Text(_errorMessage!, style: CairoTextStyles.bodyMedium),
                        ],
                      ),
                    )
                  : _buildAnalyticsContent(),
        ],
      ),
    );
  }

  Widget _buildAnalyticsContent() {
    if (_marketData.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات للتحليل'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSummaryCards(),
          const SizedBox(height: 24),
          _buildMarketInsightsSection(),
          const SizedBox(height: 24),
          _buildPriceDistributionChart(),
          const SizedBox(height: 24),
          _buildTopAreasSection(),
          const SizedBox(height: 24),
          _buildPropertyTypesChart(),
          const SizedBox(height: 24),
          _buildAreasChart(),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    final avgPrice = _marketData['avgPrice'] ?? 0.0;
    final totalSupply = _marketData['totalSupply'] ?? 0;
    final totalDemand = _marketData['totalDemand'] ?? 0;

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildSummaryCard(
          'متوسط الأسعار',
          '${avgPrice.toStringAsFixed(0)} د.ك',
          Icons.attach_money,
          AppColors.primary,
        ),
        _buildSummaryCard(
          'العرض',
          '$totalSupply عقار',
          Icons.home,
          AppColors.primaryLight,
        ),
        _buildSummaryCard(
          'الطلب',
          '$totalDemand طلب',
          Icons.trending_up,
          AppColors.success,
        ),
        _buildSummaryCard(
          'نسبة العرض/الطلب',
          totalDemand > 0 ? '${(totalSupply / totalDemand).toStringAsFixed(1)}' : '0',
          Icons.balance,
          AppColors.warning,
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: CairoTextStyles.headlineMedium.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: CairoTextStyles.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء قسم رؤى السوق
  Widget _buildMarketInsightsSection() {
    final marketTrend = _marketData['marketTrend'] as Map<String, dynamic>? ?? {};
    final investmentScore = (_marketData['investmentScore'] ?? 0.0) as double;
    final marketHealth = _marketData['marketHealth'] ?? 'غير محدد';

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary.withValues(alpha: 0.1), AppColors.primaryLight.withValues(alpha: 0.05)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.insights, color: Colors.white, size: 20),
              ),
              const SizedBox(width: 12),
              Text(
                'رؤى السوق المتقدمة',
                style: CairoTextStyles.headlineSmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildInsightCard(
                  'اتجاه السوق',
                  marketTrend['trend'] ?? 'مستقر',
                  _getTrendIcon(marketTrend['trend'] ?? 'مستقر'),
                  _getTrendColor(marketTrend['trend'] ?? 'مستقر'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInsightCard(
                  'مؤشر الاستثمار',
                  '${investmentScore.toStringAsFixed(0)}%',
                  Icons.trending_up,
                  _getScoreColor(investmentScore),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildInsightCard(
            'صحة السوق',
            marketHealth,
            Icons.health_and_safety,
            _getHealthColor(marketHealth),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.info.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.info, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'التحليلات مبنية على ${_marketData['properties']?.length ?? 0} عقار و ${_marketData['requests']?.length ?? 0} طلب من البيانات الفعلية',
                    style: CairoTextStyles.bodySmall.copyWith(color: AppColors.info),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة رؤية
  Widget _buildInsightCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: CairoTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: CairoTextStyles.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء مخطط توزيع الأسعار
  Widget _buildPriceDistributionChart() {
    final priceDistribution = _marketData['priceDistribution'] as Map<String, int>? ?? {};

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'توزيع الأسعار',
            style: CairoTextStyles.headlineSmall,
          ),
          const SizedBox(height: 20),
          if (priceDistribution.isEmpty)
            Center(
              child: Text(
                'لا توجد بيانات',
                style: CairoTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            )
          else
            ...priceDistribution.entries.map((entry) {
              final total = priceDistribution.values.reduce((a, b) => a + b);
              final percentage = total > 0 ? (entry.value / total * 100).round() : 0;

              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          entry.key,
                          style: CairoTextStyles.bodyMedium,
                        ),
                        Text(
                          '${entry.value} ($percentage%)',
                          style: CairoTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: total > 0 ? entry.value / total : 0,
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation(AppColors.primary),
                    ),
                  ],
                ),
              );
            }),
        ],
      ),
    );
  }

  /// بناء قسم أفضل المناطق
  Widget _buildTopAreasSection() {
    final topAreas = _marketData['topAreas'] as List<Map<String, dynamic>>? ?? [];

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أفضل المناطق',
            style: CairoTextStyles.headlineSmall,
          ),
          const SizedBox(height: 20),
          if (topAreas.isEmpty)
            Center(
              child: Text(
                'لا توجد بيانات',
                style: CairoTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            )
          else
            ...topAreas.asMap().entries.map((entry) {
              final index = entry.key;
              final area = entry.value;
              final name = area['name'] ?? 'غير محدد';
              final count = area['count'] ?? 0;
              final avgPrice = (area['avgPrice'] ?? 0.0) as double;

              Color rankColor;
              IconData rankIcon;
              switch (index) {
                case 0:
                  rankColor = Colors.amber;
                  rankIcon = Icons.emoji_events;
                  break;
                case 1:
                  rankColor = Colors.grey;
                  rankIcon = Icons.military_tech;
                  break;
                case 2:
                  rankColor = AppColors.secondary;
                  rankIcon = Icons.workspace_premium;
                  break;
                default:
                  rankColor = AppColors.primary;
                  rankIcon = Icons.location_on;
              }

              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: rankColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: rankColor.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(rankIcon, color: rankColor, size: 20),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            name,
                            style: CairoTextStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '$count عقار - متوسط السعر: ${avgPrice.toStringAsFixed(0)} د.ك',
                            style: CairoTextStyles.bodySmall.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }),
        ],
      ),
    );
  }

  /// الحصول على أيقونة الاتجاه
  IconData _getTrendIcon(String trend) {
    switch (trend) {
      case 'صاعد':
        return Icons.trending_up;
      case 'هابط':
        return Icons.trending_down;
      default:
        return Icons.trending_flat;
    }
  }

  /// الحصول على لون الاتجاه
  Color _getTrendColor(String trend) {
    switch (trend) {
      case 'صاعد':
        return AppColors.success;
      case 'هابط':
        return AppColors.error;
      default:
        return AppColors.warning;
    }
  }

  /// الحصول على لون النتيجة
  Color _getScoreColor(double score) {
    if (score >= 70) {
      return AppColors.success;
    } else if (score >= 50) {
      return AppColors.warning;
    } else {
      return AppColors.error;
    }
  }

  /// الحصول على لون الصحة
  Color _getHealthColor(String health) {
    switch (health) {
      case 'ممتاز':
        return AppColors.success;
      case 'جيد':
        return AppColors.primary;
      case 'متوسط':
        return AppColors.warning;
      default:
        return AppColors.error;
    }
  }

  Widget _buildPropertyTypesChart() {
    final propertyTypes = _marketData['propertyTypes'] as Map<String, int>? ?? {};

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أنواع العقارات',
            style: CairoTextStyles.headlineSmall,
          ),
          const SizedBox(height: 20),
          if (propertyTypes.isEmpty)
            Center(
              child: Text(
                'لا توجد بيانات',
                style: CairoTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            )
          else
            ...propertyTypes.entries.map((entry) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      entry.key,
                      style: CairoTextStyles.bodyMedium,
                    ),
                  ),
                  Text(
                    '${entry.value}',
                    style: CairoTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
            )),
        ],
      ),
    );
  }

  Widget _buildAreasChart() {
    final areas = _marketData['areas'] as Map<String, int>? ?? {};

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'توزيع المناطق',
            style: CairoTextStyles.headlineSmall,
          ),
          const SizedBox(height: 20),
          if (areas.isEmpty)
            Center(
              child: Text(
                'لا توجد بيانات',
                style: CairoTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            )
          else
            ...areas.entries.map((entry) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      entry.key,
                      style: CairoTextStyles.bodyMedium,
                    ),
                  ),
                  Text(
                    '${entry.value}',
                    style: CairoTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
            )),
        ],
      ),
    );
  }

  /// بناء خلفية الأيقونات الاقتصادية
  Widget _buildEconomicIconsBackground() {
    final economicIcons = [
      Icons.trending_up,
      Icons.trending_down,
      Icons.bar_chart,
      Icons.pie_chart,
      Icons.attach_money,
      Icons.account_balance,
      Icons.analytics,
      Icons.assessment,
      Icons.business_center,
      Icons.monetization_on,
      Icons.show_chart,
      Icons.timeline,
      Icons.insights,
      Icons.calculate,
      Icons.balance,
      Icons.savings,
      Icons.currency_exchange,
      Icons.price_change,
      Icons.inventory,
      Icons.real_estate_agent,
    ];

    return Positioned.fill(
      child: CustomPaint(
        painter: EconomicIconsPainter(
          icons: economicIcons,
          color: AppColors.primary.withValues(alpha: 0.05),
        ),
      ),
    );
  }
}

/// رسام الأيقونات الاقتصادية في الخلفية
class EconomicIconsPainter extends CustomPainter {
  final List<IconData> icons;
  final Color color;

  EconomicIconsPainter({
    required this.icons,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // إنشاء نمط منتظم من الأيقونات
    const iconSize = 24.0;
    const spacing = 80.0;

    final rows = (size.height / spacing).ceil();
    final cols = (size.width / spacing).ceil();

    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        // إضافة تنويع في المواضع لجعلها تبدو أكثر طبيعية
        final offsetX = (col * spacing) + (row.isEven ? 0 : spacing / 2);
        final offsetY = (row * spacing) + 40;

        // اختيار أيقونة عشوائية
        final iconIndex = (row * cols + col) % icons.length;
        final icon = icons[iconIndex];

        // رسم الأيقونة
        _drawIcon(
          canvas,
          icon,
          Offset(offsetX, offsetY),
          iconSize,
          paint,
        );
      }
    }
  }

  void _drawIcon(Canvas canvas, IconData icon, Offset position, double size, Paint paint) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: String.fromCharCode(icon.codePoint),
        style: TextStyle(
          fontSize: size,
          fontFamily: icon.fontFamily,
          color: paint.color,
          fontWeight: FontWeight.w300,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        position.dx - textPainter.width / 2,
        position.dy - textPainter.height / 2,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
