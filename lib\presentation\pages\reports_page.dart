import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/presentation/widgets/loading_widget.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:kuwait_corners/core/services/excel_export_service.dart';
import 'package:kuwait_corners/domain/services/user_interface_customization_service.dart';
import 'package:kuwait_corners/domain/entities/user.dart' as domain_user;
import 'package:kuwait_corners/core/constants/user_types.dart';

/// صفحة التقارير للشركات العقارية
class ReportsPage extends StatefulWidget {
  const ReportsPage({super.key});

  @override
  State<ReportsPage> createState() => _ReportsPageState();
}

class _ReportsPageState extends State<ReportsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedPeriod = 'all'; // all, day, week, month, year
  final DateTime _selectedDate = DateTime.now();

  // متغيرات نوع المستخدم
  domain_user.UserType? _currentUserType;
  String? _currentUserId;
  bool _isLoadingUserType = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeUserType();
  }

  /// تحديد نوع المستخدم الحالي
  Future<void> _initializeUserType() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        setState(() {
          _isLoadingUserType = false;
        });
        return;
      }

      _currentUserId = user.uid;

      final uiService = UserInterfaceCustomizationService();
      final userType = await uiService.getCurrentUserType();

      setState(() {
        _currentUserType = userType;
        _isLoadingUserType = false;
      });

      print('🔍 Reports Page - User Type: $userType');
      print('🔍 Reports Page - User ID: ${user.uid}');
    } catch (e) {
      print('❌ Error getting user type: $e');
      setState(() {
        _currentUserType = domain_user.UserType.seeker;
        _isLoadingUserType = false;
      });
    }
  }

  /// تحديد الحقل المناسب للفلترة حسب نوع المستخدم
  String _getOwnershipField() {
    if (_currentUserType == null) return 'ownerId';

    switch (_currentUserType!) {
      case domain_user.UserType.company:
        // الشركات: يمكن أن تستخدم companyId أو ownerId حسب كيفية حفظ البيانات
        return 'ownerId'; // نستخدم ownerId لأن هذا ما يتم حفظه فعلياً
      case domain_user.UserType.agent:
      case domain_user.UserType.owner:
      case domain_user.UserType.seeker:
      default:
        return 'ownerId';
    }
  }

  /// التحقق من إمكانية عرض التقارير لنوع المستخدم الحالي
  bool _canShowReports() {
    if (_currentUserType == null) return false;

    // التقارير متاحة فقط للشركات والمستثمرين
    return _currentUserType == domain_user.UserType.company ||
           _currentUserType == domain_user.UserType.agent;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'التقارير',
          style: CairoTextStyles.appBarTitle),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          if (!_isLoadingUserType && _canShowReports())
            IconButton(
              icon: const Icon(Icons.download),
              onPressed: _exportReports),
        ]),
      body: _isLoadingUserType
          ? const LoadingWidget()
          : !_canShowReports()
              ? _buildAccessDeniedWidget()
              : Column(
                  children: [
                    _buildPeriodFilter(),
                    _buildTabBar(),
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          _buildSalesReportTab(),
                          _buildPropertiesReportTab(),
                          _buildClientsReportTab(),
                          _buildFinancialReportTab(),
                        ],
                      ),
                    ),
                  ],
                ),
    );
  }

  /// بناء واجهة رفض الوصول
  Widget _buildAccessDeniedWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.block,
            size: 80,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'غير مخول للوصول',
            style: CairoTextStyles.headlineSmall.copyWith(
              color: Colors.red[600],
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'صفحة التقارير متاحة فقط للشركات والمستثمرين',
            style: CairoTextStyles.bodyMedium.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back),
            label: const Text('العودة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodFilter() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white,
            AppColors.primary.withValues(alpha: 0.02),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.8),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان المحسن
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.date_range_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الفترة الزمنية للتقرير',
                      style: CairoTextStyles.headlineSmall.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'اختر الفترة المطلوبة لعرض البيانات',
                      style: CairoTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // الفلاتر المحسنة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.primary.withValues(alpha: 0.1),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'اختر الفترة:',
                  style: CairoTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  children: [
                    _buildPeriodChip('all', 'جميع الفترات', Icons.all_inclusive),
                    _buildPeriodChip('day', 'اليوم', Icons.today),
                    _buildPeriodChip('week', 'هذا الأسبوع', Icons.view_week),
                    _buildPeriodChip('month', 'هذا الشهر', Icons.calendar_month),
                    _buildPeriodChip('year', 'هذه السنة', Icons.calendar_today),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodChip(String period, String label, IconData icon) {
    final isSelected = _selectedPeriod == period;
    return InkWell(
      onTap: () {
        setState(() {
          _selectedPeriod = period;
          // إعادة تحميل البيانات عند تغيير الفترة
        });
      },
      borderRadius: BorderRadius.circular(16),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isSelected ? null : Colors.grey.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? AppColors.primary
                : AppColors.primary.withValues(alpha: 0.2),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppColors.primary.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ]
              : [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : AppColors.primary,
              size: 18,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: CairoTextStyles.bodyMedium.copyWith(
                color: isSelected ? Colors.white : AppColors.primary,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w600,
                fontSize: 13,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // دالة للحصول على نطاق التاريخ حسب الفترة المحددة
  Map<String, DateTime> _getDateRange() {
    final now = DateTime.now();
    DateTime start, end;

    switch (_selectedPeriod) {
      case 'day':
        start = DateTime(now.year, now.month, now.day);
        end = DateTime(now.year, now.month, now.day, 23, 59, 59);
        break;
      case 'week':
        final weekday = now.weekday;
        start = now.subtract(Duration(days: weekday - 1));
        start = DateTime(start.year, start.month, start.day);
        end = start.add(const Duration(days: 6, hours: 23, minutes: 59, seconds: 59));
        break;
      case 'month':
        start = DateTime(now.year, now.month, 1);
        end = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
        break;
      case 'year':
        start = DateTime(now.year, 1, 1);
        end = DateTime(now.year, 12, 31, 23, 59, 59);
        break;
      default:
        start = DateTime(2020, 1, 1); // تاريخ بداية افتراضي
        end = now;
    }

    return {'start': start, 'end': end};
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: AppColors.primary,
        isScrollable: true,
        labelStyle: CairoTextStyles.labelLarge,
        tabs: const [
          Tab(text: 'تقرير المبيعات'),
          Tab(text: 'تقرير العقارات'),
          Tab(text: 'تقرير العملاء'),
          Tab(text: 'التقرير المالي'),
        ]));
  }

  Widget _buildSalesReportTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getSalesDataStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل بيانات المبيعات');
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyDataWidget(
            'لا توجد بيانات مبيعات',
            'لم يتم تسجيل أي مبيعات حتى الآن',
            Icons.trending_up);
        }

        final salesData = _calculateSalesData(snapshot.data!.docs);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildReportCard(
                'إجمالي المبيعات',
                '${salesData['totalSales'].toStringAsFixed(0)} د.ك',
                Icons.trending_up,
                Colors.green,
                '${salesData['dealsCount']} صفقة مكتملة'),
              const SizedBox(height: 16),
              _buildReportCard(
                'عدد الصفقات',
                '${salesData['dealsCount']}',
                Icons.handshake,
                Colors.blue,
                'هذا الشهر'),
              const SizedBox(height: 16),
              _buildReportCard(
                'متوسط قيمة الصفقة',
                '${salesData['averageDeal'].toStringAsFixed(0)} د.ك',
                Icons.calculate,
                Colors.orange,
                'متوسط السعر'),
              const SizedBox(height: 24),
              Text(
                'أفضل الوكلاء أداءً',
                style: CairoTextStyles.headlineSmall),
              const SizedBox(height: 16),
              _buildTopAgentsFromData(),
            ]));
      });
  }

  Widget _buildPropertiesReportTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getPropertiesDataStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل بيانات العقارات');
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyDataWidget(
            'لا توجد عقارات',
            'لم يتم إضافة أي عقارات حتى الآن',
            Icons.home,
            actionText: 'إضافة عقار جديد',
            onAction: () => Navigator.pushNamed(context, '/add-property'));
        }

        final propertiesData = _calculatePropertiesData(snapshot.data!.docs);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildReportCard(
                'إجمالي العقارات',
                '${propertiesData['totalProperties']}',
                Icons.home,
                Colors.purple,
                'عقار مسجل'),
              const SizedBox(height: 16),
              _buildReportCard(
                'العقارات المباعة',
                '${propertiesData['soldProperties']}',
                Icons.check_circle,
                Colors.green,
                '${((propertiesData['soldProperties'] / propertiesData['totalProperties']) * 100).toStringAsFixed(1)}% من الإجمالي'),
              const SizedBox(height: 16),
              _buildReportCard(
                'العقارات المؤجرة',
                '${propertiesData['rentedProperties']}',
                Icons.key,
                Colors.blue,
                '${((propertiesData['rentedProperties'] / propertiesData['totalProperties']) * 100).toStringAsFixed(1)}% من الإجمالي'),
              const SizedBox(height: 24),
              Text(
                'أنواع العقارات الأكثر طلباً',
                style: CairoTextStyles.headlineSmall),
              const SizedBox(height: 16),
              _buildPropertyTypesFromData(propertiesData['propertyTypes']),
            ]));
      });
  }

  Widget _buildClientsReportTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getClientsDataStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل بيانات العملاء');
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyDataWidget(
            'لا توجد بيانات عملاء',
            'لم يتم تسجيل أي عملاء حتى الآن',
            Icons.people);
        }

        final clientsData = _calculateClientsData(snapshot.data!.docs);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildReportCard(
                'إجمالي العملاء',
                '${clientsData['totalClients']}',
                Icons.people,
                AppColors.primary,
                'عميل مسجل'),
              const SizedBox(height: 16),
              _buildReportCard(
                'العملاء النشطين',
                '${clientsData['activeClients']}',
                Icons.person_pin,
                Colors.green,
                '${((clientsData['activeClients'] / clientsData['totalClients']) * 100).toStringAsFixed(1)}% من الإجمالي'),
              const SizedBox(height: 16),
              _buildReportCard(
                'عملاء جدد هذا الشهر',
                '${clientsData['newClientsThisMonth']}',
                Icons.person_add,
                Colors.blue,
                'عميل جديد'),
              const SizedBox(height: 24),
              Text(
                'توزيع العملاء حسب النوع',
                style: CairoTextStyles.headlineSmall),
              const SizedBox(height: 16),
              _buildClientTypesChart(clientsData['clientTypes']),
              const SizedBox(height: 24),
              Text(
                'أحدث العملاء',
                style: CairoTextStyles.headlineSmall),
              const SizedBox(height: 16),
              _buildRecentClients(snapshot.data!.docs),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFinancialReportTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getSalesDataStream(),
      builder: (context, salesSnapshot) {
        if (salesSnapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (salesSnapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل البيانات المالية');
        }

        final salesData = salesSnapshot.hasData
            ? _calculateSalesData(salesSnapshot.data!.docs)
            : {'totalSales': 0.0, 'dealsCount': 0, 'averageDeal': 0.0};

        return StreamBuilder<QuerySnapshot>(
          stream: _getRentalsDataStream(),
          builder: (context, rentalsSnapshot) {
            final rentalsData = rentalsSnapshot.hasData
                ? _calculateRentalsData(rentalsSnapshot.data!.docs)
                : {'totalRentals': 0.0, 'rentalsCount': 0};

            final financialData = _calculateFinancialData(salesData, rentalsData);

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildReportCard(
                    'إجمالي الإيرادات',
                    '${financialData['totalRevenue'].toStringAsFixed(0)} د.ك',
                    Icons.account_balance_wallet,
                    Colors.green,
                    'من المبيعات والإيجارات'),
                  const SizedBox(height: 16),
                  _buildReportCard(
                    'إيرادات المبيعات',
                    '${financialData['salesRevenue'].toStringAsFixed(0)} د.ك',
                    Icons.trending_up,
                    AppColors.primary,
                    '${salesData['dealsCount']} صفقة بيع'),
                  const SizedBox(height: 16),
                  _buildReportCard(
                    'إيرادات الإيجارات',
                    '${financialData['rentalsRevenue'].toStringAsFixed(0)} د.ك',
                    Icons.key,
                    Colors.blue,
                    '${rentalsData['rentalsCount']} عقد إيجار'),
                  const SizedBox(height: 24),
                  Text(
                    'توزيع الإيرادات',
                    style: CairoTextStyles.headlineSmall),
                  const SizedBox(height: 16),
                  _buildRevenueChart(financialData),
                  const SizedBox(height: 24),
                  Text(
                    'الأداء المالي الشهري',
                    style: CairoTextStyles.headlineSmall),
                  const SizedBox(height: 16),
                  _buildMonthlyPerformanceChart(),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildReportCard(String title, String value, IconData icon, Color color, String subtitle) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(icon, color: Colors.white, size: 28),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: CairoTextStyles.bodyMedium.copyWith(
                      color: Colors.grey[700],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    value,
                    style: CairoTextStyles.headlineMedium.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: CairoTextStyles.bodySmall.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }



  // Firebase Data Streams for Clients
  Stream<QuerySnapshot> _getClientsDataStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    // إذا كان نوع المستخدم لم يتم تحديده بعد، انتظر
    if (_isLoadingUserType) return const Stream.empty();

    // التحقق من إمكانية عرض التقارير
    if (!_canShowReports()) return const Stream.empty();

    // للعملاء نستخدم agentId للمستثمرين و companyId للشركات
    String clientField = 'agentId';
    if (_currentUserType == domain_user.UserType.company) {
      clientField = 'companyId';
    }

    Query query = FirebaseFirestore.instance
        .collection('clients')
        .where(clientField, isEqualTo: currentUser.uid);

    // تطبيق الفلتر الزمني
    if (_selectedPeriod != 'all') {
      final dateRange = _getDateRange();
      query = query
          .where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(dateRange['start']!))
          .where('createdAt', isLessThanOrEqualTo: Timestamp.fromDate(dateRange['end']!));
    }

    return query.snapshots();
  }

  Stream<QuerySnapshot> _getRentalsDataStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    // إذا كان نوع المستخدم لم يتم تحديده بعد، انتظر
    if (_isLoadingUserType) return const Stream.empty();

    // التحقق من إمكانية عرض التقارير
    if (!_canShowReports()) return const Stream.empty();

    final ownershipField = _getOwnershipField();

    Query query = FirebaseFirestore.instance
        .collection('estates')
        .where(ownershipField, isEqualTo: currentUser.uid)
        .where('status', isEqualTo: 'rented');

    // تطبيق الفلتر الزمني
    if (_selectedPeriod != 'all') {
      final dateRange = _getDateRange();
      query = query
          .where('rentedAt', isGreaterThanOrEqualTo: Timestamp.fromDate(dateRange['start']!))
          .where('rentedAt', isLessThanOrEqualTo: Timestamp.fromDate(dateRange['end']!));
    }

    return query.snapshots();
  }

  // Data Calculation Methods for Clients
  Map<String, dynamic> _calculateClientsData(List<QueryDocumentSnapshot> docs) {
    int totalClients = docs.length;
    int activeClients = 0;
    int newClientsThisMonth = 0;
    Map<String, int> clientTypes = {};

    final now = DateTime.now();
    final thisMonth = DateTime(now.year, now.month, 1);

    for (var doc in docs) {
      final data = doc.data() as Map<String, dynamic>;
      final clientType = data['type'] ?? 'غير محدد';
      final createdAt = (data['createdAt'] as Timestamp?)?.toDate();
      final lastContact = (data['lastContact'] as Timestamp?)?.toDate();

      // Count client types
      clientTypes[clientType] = (clientTypes[clientType] ?? 0) + 1;

      // Count new clients this month
      if (createdAt != null && createdAt.isAfter(thisMonth)) {
        newClientsThisMonth++;
      }

      // Count active clients (active in last 30 days)
      if (lastContact != null &&
          lastContact.isAfter(now.subtract(const Duration(days: 30)))) {
        activeClients++;
      }
    }

    return {
      'totalClients': totalClients,
      'activeClients': activeClients,
      'newClientsThisMonth': newClientsThisMonth,
      'clientTypes': clientTypes,
    };
  }

  Map<String, dynamic> _calculateRentalsData(List<QueryDocumentSnapshot> docs) {
    double totalRentals = 0;
    int rentalsCount = docs.length;

    for (var doc in docs) {
      final data = doc.data() as Map<String, dynamic>;
      final price = (data['price'] ?? 0).toDouble();
      totalRentals += price;
    }

    return {
      'totalRentals': totalRentals,
      'rentalsCount': rentalsCount,
    };
  }

  Map<String, dynamic> _calculateFinancialData(
      Map<String, dynamic> salesData, Map<String, dynamic> rentalsData) {
    final salesRevenue = (salesData['totalSales'] ?? 0.0) * 0.03; // 3% commission
    final rentalsRevenue = (rentalsData['totalRentals'] ?? 0.0) * 0.05; // 5% commission
    final totalRevenue = salesRevenue + rentalsRevenue;

    return {
      'totalRevenue': totalRevenue,
      'salesRevenue': salesRevenue,
      'rentalsRevenue': rentalsRevenue,
    };
  }

  Map<String, double> _calculateMonthlyPerformance(
      List<QueryDocumentSnapshot> salesDocs, List<QueryDocumentSnapshot> rentalsDocs) {
    final Map<String, double> monthlyData = {};
    final now = DateTime.now();

    // إنشاء قائمة بآخر 6 أشهر
    for (int i = 5; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthKey = _getMonthName(month.month);
      monthlyData[monthKey] = 0.0;
    }

    // حساب إيرادات المبيعات
    for (var doc in salesDocs) {
      final data = doc.data() as Map<String, dynamic>;
      final soldAt = (data['soldAt'] as Timestamp?)?.toDate();
      final price = (data['price'] ?? 0).toDouble();

      if (soldAt != null) {
        final monthKey = _getMonthName(soldAt.month);
        if (monthlyData.containsKey(monthKey)) {
          monthlyData[monthKey] = (monthlyData[monthKey] ?? 0) + (price * 0.03); // 3% عمولة
        }
      }
    }

    // حساب إيرادات الإيجارات
    for (var doc in rentalsDocs) {
      final data = doc.data() as Map<String, dynamic>;
      final rentedAt = (data['rentedAt'] as Timestamp?)?.toDate();
      final price = (data['price'] ?? 0).toDouble();

      if (rentedAt != null) {
        final monthKey = _getMonthName(rentedAt.month);
        if (monthlyData.containsKey(monthKey)) {
          monthlyData[monthKey] = (monthlyData[monthKey] ?? 0) + (price * 0.05); // 5% عمولة
        }
      }
    }

    return monthlyData;
  }

  String _getMonthName(int month) {
    const months = [
      '', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[month];
  }

  // Widget Builders for Charts and Lists
  Widget _buildClientTypesChart(Map<String, int> clientTypes) {
    if (clientTypes.isEmpty) {
      return Container(
        height: 200,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.pie_chart, size: 48, color: Colors.grey),
              SizedBox(height: 8),
              Text(
                'لا توجد بيانات أنواع العملاء',
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    final totalClients = clientTypes.values.fold(0, (total, clientCount) => total + clientCount);
    final colors = [
      AppColors.primary,
      Colors.blue,
      Colors.orange,
      Colors.green,
      Colors.purple,
      Colors.red,
    ];

    return Container(
      height: 250,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          Expanded(
            child: PieChart(
              PieChartData(
                sections: clientTypes.entries.map((entry) {
                  final index = clientTypes.keys.toList().indexOf(entry.key);
                  final percentage = ((entry.value / totalClients) * 100);
                  final color = colors[index % colors.length];

                  return PieChartSectionData(
                    value: entry.value.toDouble(),
                    title: '${percentage.toStringAsFixed(1)}%',
                    color: color,
                    radius: 60,
                    titleStyle: CairoTextStyles.bodySmall.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                }).toList(),
                centerSpaceRadius: 40,
                sectionsSpace: 2,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 12,
            runSpacing: 8,
            children: clientTypes.entries.map((entry) {
              final index = clientTypes.keys.toList().indexOf(entry.key);
              final color = colors[index % colors.length];

              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${_getClientTypeLabel(entry.key)}: ${entry.value}',
                    style: CairoTextStyles.bodySmall,
                  ),
                ],
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentClients(List<QueryDocumentSnapshot> docs) {
    final recentClients = docs.take(5).toList();

    if (recentClients.isEmpty) {
      return Container(
        height: 120,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.people_outline, size: 32, color: Colors.grey),
              SizedBox(height: 8),
              Text(
                'لا توجد عملاء حديثين',
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: recentClients.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        final name = data['name'] ?? 'غير محدد';
        final interest = data['interest'] ?? 'غير محدد';
        final type = data['type'] ?? 'غير محدد';
        final isVip = data['isVip'] ?? false;
        final createdAt = (data['createdAt'] as Timestamp?)?.toDate();

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: isVip
                ? Border.all(color: Colors.amber.withValues(alpha: 0.4), width: 1.5)
                : Border.all(color: Colors.grey.withValues(alpha: 0.15), width: 1),
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            leading: Stack(
              children: [
                CircleAvatar(
                  backgroundColor: AppColors.primary,
                  radius: 24,
                  child: Text(
                    name.isNotEmpty ? name[0].toUpperCase() : '؟',
                    style: CairoTextStyles.bodyLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (isVip)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                        color: Colors.amber,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.star,
                        size: 12,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
            title: Text(
              name,
              style: CairoTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.interests,
                      size: 14,
                      color: AppColors.primary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'الاهتمام: $interest',
                      style: CairoTextStyles.bodySmall.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Icon(
                      Icons.category,
                      size: 14,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'النوع: $type',
                      style: CairoTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  createdAt != null ? _formatDate(createdAt) : 'غير محدد',
                  style: CairoTextStyles.bodySmall.copyWith(
                    color: Colors.grey[500],
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'جديد',
                    style: CairoTextStyles.bodySmall.copyWith(
                      color: AppColors.primary,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildRevenueChart(Map<String, dynamic> financialData) {
    final salesRevenue = financialData['salesRevenue'] ?? 0.0;
    final rentalsRevenue = financialData['rentalsRevenue'] ?? 0.0;
    final totalRevenue = financialData['totalRevenue'] ?? 0.0;

    if (totalRevenue == 0) {
      return const Center(
        child: Text(
          'لا توجد بيانات إيرادات',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: PieChart(
        PieChartData(
          sections: [
            PieChartSectionData(
              value: salesRevenue,
              title: 'مبيعات\n${((salesRevenue / totalRevenue) * 100).toStringAsFixed(1)}%',
              color: AppColors.primary,
              radius: 60,
              titleStyle: CairoTextStyles.bodySmall.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            PieChartSectionData(
              value: rentalsRevenue,
              title: 'إيجارات\n${((rentalsRevenue / totalRevenue) * 100).toStringAsFixed(1)}%',
              color: Colors.blue,
              radius: 60,
              titleStyle: CairoTextStyles.bodySmall.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
          centerSpaceRadius: 40,
          sectionsSpace: 2,
        ),
      ),
    );
  }

  Widget _buildMonthlyPerformanceChart() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getSalesDataStream(),
      builder: (context, salesSnapshot) {
        return StreamBuilder<QuerySnapshot>(
          stream: _getRentalsDataStream(),
          builder: (context, rentalsSnapshot) {
            if (salesSnapshot.connectionState == ConnectionState.waiting ||
                rentalsSnapshot.connectionState == ConnectionState.waiting) {
              return Container(
                height: 200,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: const Center(child: CircularProgressIndicator()),
              );
            }

            final monthlyData = _calculateMonthlyPerformance(
              salesSnapshot.data?.docs ?? [],
              rentalsSnapshot.data?.docs ?? [],
            );

            if (monthlyData.isEmpty) {
              return Container(
                height: 200,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.bar_chart, size: 48, color: Colors.grey),
                      SizedBox(height: 8),
                      Text(
                        'لا توجد بيانات أداء شهري',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              );
            }

            return Container(
              height: 250,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: monthlyData.values.isNotEmpty
                      ? monthlyData.values.reduce((a, b) => a > b ? a : b) * 1.2
                      : 100,
                  barTouchData: BarTouchData(
                    touchTooltipData: BarTouchTooltipData(
                      getTooltipItem: (group, groupIndex, rod, rodIndex) {
                        final month = monthlyData.keys.elementAt(groupIndex);
                        final value = rod.toY;
                        return BarTooltipItem(
                          '$month\n${value.toStringAsFixed(0)} د.ك',
                          CairoTextStyles.bodySmall.copyWith(color: Colors.white),
                        );
                      },
                    ),
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final months = monthlyData.keys.toList();
                          if (value.toInt() >= 0 && value.toInt() < months.length) {
                            return Text(
                              months[value.toInt()],
                              style: CairoTextStyles.bodySmall,
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            '${(value / 1000).toStringAsFixed(0)}K',
                            style: CairoTextStyles.bodySmall,
                          );
                        },
                      ),
                    ),
                    topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: false),
                  barGroups: monthlyData.entries.map((entry) {
                    final index = monthlyData.keys.toList().indexOf(entry.key);
                    return BarChartGroupData(
                      x: index,
                      barRods: [
                        BarChartRodData(
                          toY: entry.value,
                          color: AppColors.primary,
                          width: 20,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(4),
                            topRight: Radius.circular(4),
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                ),
              ),
            );
          },
        );
      },
    );
  }

  // Helper Methods
  String _getClientTypeLabel(String clientType) {
    switch (clientType) {
      case 'مشتري':
        return 'مشتري';
      case 'مستأجر':
        return 'مستأجر';
      case 'بائع':
        return 'بائع';
      case 'مؤجر':
        return 'مؤجر';
      case 'seeker':
        return 'باحث عن عقار';
      case 'investor':
        return 'مستثمر';
      case 'owner':
        return 'مالك عقار';
      case 'company':
        return 'شركة عقارية';
      default:
        return 'غير محدد';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _exportReports() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تصدير التقارير',
          style: CairoTextStyles.headlineSmall,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'اختر نوع التصدير:',
              style: CairoTextStyles.bodyMedium,
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.pop(context);
                  _exportCurrentTabReport();
                },
                icon: const Icon(Icons.tab),
                label: const Text('تصدير القسم الحالي'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.pop(context);
                  _exportAllReports();
                },
                icon: const Icon(Icons.all_inclusive),
                label: const Text('تصدير جميع الأقسام'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  // دوال تصدير Excel
  Future<void> _exportCurrentTabReport() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('جاري تصدير التقرير...')));

      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      final tabIndex = _tabController.index;
      String reportName = '';
      List<Map<String, dynamic>> data = [];

      // الحصول على نطاق التاريخ للفلتر
      DateTime? startDate, endDate;
      if (_selectedPeriod != 'all') {
        final dateRange = _getDateRange();
        startDate = dateRange['start'];
        endDate = dateRange['end'];
      }

      switch (tabIndex) {
        case 0: // تقرير المبيعات
          reportName = 'تقرير المبيعات';
          data = await ExcelExportService.getSalesReportData();
          break;
        case 1: // تقرير العقارات
          reportName = 'تقرير العقارات';
          data = await ExcelExportService.getPropertiesReportData();
          break;
        case 2: // تقرير العملاء
          reportName = 'تقرير العملاء';
          data = await ExcelExportService.getClientsReportData(
            startDate: startDate,
            endDate: endDate,
          );
          break;
        case 3: // التقرير المالي
          reportName = 'التقرير المالي';
          data = await ExcelExportService.getFinancialReportData();
          break;
      }

      await ExcelExportService.createAndShareExcel(reportName, data);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تصدير التقرير: $e')));
      }
    }
  }

  Future<void> _exportAllReports() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('جاري تصدير جميع التقارير...')));

      // الحصول على نطاق التاريخ للفلتر
      DateTime? startDate, endDate;
      if (_selectedPeriod != 'all') {
        final dateRange = _getDateRange();
        startDate = dateRange['start'];
        endDate = dateRange['end'];
      }

      final allReportsData = {
        'تقرير المبيعات': await ExcelExportService.getSalesReportData(),
        'تقرير العقارات': await ExcelExportService.getPropertiesReportData(),
        'تقرير العملاء': await ExcelExportService.getClientsReportData(
          startDate: startDate,
          endDate: endDate,
        ),
        'التقرير المالي': await ExcelExportService.getFinancialReportData(),
      };

      await ExcelExportService.createAndShareMultiSheetExcel(allReportsData);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تصدير التقارير: $e')));
      }
    }
  }

  // Firebase Data Streams
  Stream<QuerySnapshot> _getSalesDataStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    // إذا كان نوع المستخدم لم يتم تحديده بعد، انتظر
    if (_isLoadingUserType) return const Stream.empty();

    // التحقق من إمكانية عرض التقارير
    if (!_canShowReports()) return const Stream.empty();

    final ownershipField = _getOwnershipField();

    Query query = FirebaseFirestore.instance
        .collection('estates')
        .where(ownershipField, isEqualTo: currentUser.uid)
        .where('status', isEqualTo: 'sold');

    // تطبيق الفلتر الزمني
    if (_selectedPeriod != 'all') {
      final dateRange = _getDateRange();
      query = query
          .where('soldAt', isGreaterThanOrEqualTo: Timestamp.fromDate(dateRange['start']!))
          .where('soldAt', isLessThanOrEqualTo: Timestamp.fromDate(dateRange['end']!));
    }

    return query.snapshots();
  }

  Stream<QuerySnapshot> _getPropertiesDataStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    // إذا كان نوع المستخدم لم يتم تحديده بعد، انتظر
    if (_isLoadingUserType) return const Stream.empty();

    // التحقق من إمكانية عرض التقارير
    if (!_canShowReports()) return const Stream.empty();

    final ownershipField = _getOwnershipField();
    print('🔍 Using ownership field: $ownershipField for user type: $_currentUserType');

    Query query = FirebaseFirestore.instance
        .collection('estates')
        .where(ownershipField, isEqualTo: currentUser.uid);

    // تطبيق الفلتر الزمني
    if (_selectedPeriod != 'all') {
      final dateRange = _getDateRange();
      query = query
          .where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(dateRange['start']!))
          .where('createdAt', isLessThanOrEqualTo: Timestamp.fromDate(dateRange['end']!));
    }

    return query.snapshots();
  }

  Stream<QuerySnapshot> _getTeamDataStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    return FirebaseFirestore.instance
        .collection('team_members')
        .where('companyId', isEqualTo: currentUser.uid)
        .snapshots();
  }

  // Data Calculation Methods
  Map<String, dynamic> _calculateSalesData(List<QueryDocumentSnapshot> docs) {
    double totalSales = 0;
    int dealsCount = docs.length;

    for (var doc in docs) {
      final data = doc.data() as Map<String, dynamic>;
      final price = (data['price'] ?? 0).toDouble();
      totalSales += price;
    }

    double averageDeal = dealsCount > 0 ? totalSales / dealsCount : 0;

    return {
      'totalSales': totalSales,
      'dealsCount': dealsCount,
      'averageDeal': averageDeal,
    };
  }

  Map<String, dynamic> _calculatePropertiesData(List<QueryDocumentSnapshot> docs) {
    int totalProperties = docs.length;
    int soldProperties = 0;
    int rentedProperties = 0;
    Map<String, int> propertyTypes = {};

    for (var doc in docs) {
      final data = doc.data() as Map<String, dynamic>;
      final status = data['status'] ?? '';
      final subCategory = data['subCategory'] ?? '';

      if (status == 'sold') soldProperties++;
      if (status == 'rented') rentedProperties++;

      // Count property types
      if (subCategory.isNotEmpty) {
        propertyTypes[subCategory] = (propertyTypes[subCategory] ?? 0) + 1;
      }
    }

    return {
      'totalProperties': totalProperties,
      'soldProperties': soldProperties,
      'rentedProperties': rentedProperties,
      'propertyTypes': propertyTypes,
    };
  }

  // Widget Builders
  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red[600]),
            textAlign: TextAlign.center),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              setState(() {});
            },
            child: const Text(
              'إعادة المحاولة')),
        ]));
  }

  Widget _buildEmptyDataWidget(String title, String subtitle, IconData icon, {String? actionText, VoidCallback? onAction}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 80,
            color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            title,
            style: CairoTextStyles.headlineSmall.copyWith(
              color: Colors.grey[700],
            ),
            textAlign: TextAlign.center),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: CairoTextStyles.bodyMedium.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center),
          if (actionText != null && onAction != null) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: onAction,
              icon: const Icon(Icons.add),
              label: Text(actionText),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ]));
  }

  Widget _buildTopAgentsFromData() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getTeamDataStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyDataWidget(
            'لا يوجد أعضاء فريق',
            'قم بإضافة أعضاء فريق لعرض إحصائياتهم',
            Icons.group);
        }

        final teamMembers = snapshot.data!.docs;
        // Sort by total sales
        teamMembers.sort((a, b) {
          final aSales = (a.data() as Map<String, dynamic>)['totalSales'] ?? 0;
          final bSales = (b.data() as Map<String, dynamic>)['totalSales'] ?? 0;
          return bSales.compareTo(aSales);
        });

        return Column(
          children: teamMembers.take(5).map((member) {
            final data = member.data() as Map<String, dynamic>;
            final name = data['fullName'] ?? 'غير محدد';
            final sales = (data['totalSales'] ?? 0).toDouble();
            final deals = data['soldRentedCount'] ?? 0;

            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppColors.primary,
                  child: Text(
                    name.isNotEmpty ? name[0] : '؟',
                    style: const TextStyle(color: Colors.white))),
                title: Text(
                  name),
                subtitle: Text(
                  '$deals صفقة'),
                trailing: Text(
                  '${sales.toStringAsFixed(0)} د.ك',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold))));
          }).toList());
      });
  }

  Widget _buildPropertyTypesFromData(Map<String, int> propertyTypes) {
    if (propertyTypes.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات أنواع العقارات',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey)));
    }

    final sortedTypes = propertyTypes.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final totalProperties = propertyTypes.values.fold(0, (total, propertyCount) => total + propertyCount);

    return Column(
      children: sortedTypes.map((entry) {
        final percentage = ((entry.value / totalProperties) * 100).toStringAsFixed(1);

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            title: Text(
              entry.key),
            subtitle: Text(
              '${entry.value} عقار'),
            trailing: Text(
              '$percentage%',
              style: const TextStyle(
                fontWeight: FontWeight.bold))));
      }).toList());
  }



  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
