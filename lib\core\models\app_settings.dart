import 'package:flutter/material.dart';

/// نموذج إعدادات التطبيق الشاملة
class AppSettings {
  final ThemeSettings themeSettings;
  final NotificationSettings notificationSettings;
  final PrivacySettings privacySettings;
  final PerformanceSettings performanceSettings;
  final GeneralSettings generalSettings;
  final DateTime lastUpdated;

  const AppSettings({
    required this.themeSettings,
    required this.notificationSettings,
    required this.privacySettings,
    required this.performanceSettings,
    required this.generalSettings,
    required this.lastUpdated,
  });

  /// إنشاء إعدادات افتراضية
  factory AppSettings.defaultSettings() {
    return AppSettings(
      themeSettings: ThemeSettings.defaultSettings(),
      notificationSettings: NotificationSettings.defaultSettings(),
      privacySettings: PrivacySettings.defaultSettings(),
      performanceSettings: PerformanceSettings.defaultSettings(),
      generalSettings: GeneralSettings.defaultSettings(),
      lastUpdated: DateTime.now(),
    );
  }

  /// إنشاء من JSON
  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      themeSettings: ThemeSettings.fromJson(json['themeSettings'] ?? {}),
      notificationSettings: NotificationSettings.fromJson(json['notificationSettings'] ?? {}),
      privacySettings: PrivacySettings.fromJson(json['privacySettings'] ?? {}),
      performanceSettings: PerformanceSettings.fromJson(json['performanceSettings'] ?? {}),
      generalSettings: GeneralSettings.fromJson(json['generalSettings'] ?? {}),
      lastUpdated: DateTime.tryParse(json['lastUpdated'] ?? '') ?? DateTime.now(),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'themeSettings': themeSettings.toJson(),
      'notificationSettings': notificationSettings.toJson(),
      'privacySettings': privacySettings.toJson(),
      'performanceSettings': performanceSettings.toJson(),
      'generalSettings': generalSettings.toJson(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// إنشاء نسخة معدلة
  AppSettings copyWith({
    ThemeSettings? themeSettings,
    NotificationSettings? notificationSettings,
    PrivacySettings? privacySettings,
    PerformanceSettings? performanceSettings,
    GeneralSettings? generalSettings,
    DateTime? lastUpdated,
  }) {
    return AppSettings(
      themeSettings: themeSettings ?? this.themeSettings,
      notificationSettings: notificationSettings ?? this.notificationSettings,
      privacySettings: privacySettings ?? this.privacySettings,
      performanceSettings: performanceSettings ?? this.performanceSettings,
      generalSettings: generalSettings ?? this.generalSettings,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }
}

/// إعدادات المظهر والثيم
class ThemeSettings {
  final ThemeMode themeMode;
  final bool useSystemTheme;
  final String primaryColor;
  final String accentColor;
  final bool useCustomColors;
  final double fontSize;
  final String fontFamily;

  const ThemeSettings({
    required this.themeMode,
    required this.useSystemTheme,
    required this.primaryColor,
    required this.accentColor,
    required this.useCustomColors,
    required this.fontSize,
    required this.fontFamily,
  });

  factory ThemeSettings.defaultSettings() {
    return const ThemeSettings(
      themeMode: ThemeMode.light,
      useSystemTheme: false,
      primaryColor: '#4CAF50',
      accentColor: '#81C784',
      useCustomColors: false,
      fontSize: 14.0,
      fontFamily: 'Cairo',
    );
  }

  factory ThemeSettings.fromJson(Map<String, dynamic> json) {
    return ThemeSettings(
      themeMode: ThemeMode.values.firstWhere(
        (mode) => mode.toString() == json['themeMode'],
        orElse: () => ThemeMode.light,
      ),
      useSystemTheme: json['useSystemTheme'] ?? false,
      primaryColor: json['primaryColor'] ?? '#4CAF50',
      accentColor: json['accentColor'] ?? '#81C784',
      useCustomColors: json['useCustomColors'] ?? false,
      fontSize: (json['fontSize'] ?? 14.0).toDouble(),
      fontFamily: json['fontFamily'] ?? 'Cairo',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'themeMode': themeMode.toString(),
      'useSystemTheme': useSystemTheme,
      'primaryColor': primaryColor,
      'accentColor': accentColor,
      'useCustomColors': useCustomColors,
      'fontSize': fontSize,
      'fontFamily': fontFamily,
    };
  }

  ThemeSettings copyWith({
    ThemeMode? themeMode,
    bool? useSystemTheme,
    String? primaryColor,
    String? accentColor,
    bool? useCustomColors,
    double? fontSize,
    String? fontFamily,
  }) {
    return ThemeSettings(
      themeMode: themeMode ?? this.themeMode,
      useSystemTheme: useSystemTheme ?? this.useSystemTheme,
      primaryColor: primaryColor ?? this.primaryColor,
      accentColor: accentColor ?? this.accentColor,
      useCustomColors: useCustomColors ?? this.useCustomColors,
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
    );
  }
}

/// إعدادات الإشعارات
class NotificationSettings {
  final bool pushNotifications;
  final bool emailNotifications;
  final bool newEstateNotifications;
  final bool estateUpdateNotifications;
  final bool messageNotifications;
  final bool accountNotifications;
  final bool specialOfferNotifications;
  final bool reminderNotifications;
  final bool systemNotifications;
  final String notificationSound;
  final bool vibration;
  final String quietHoursStart;
  final String quietHoursEnd;

  const NotificationSettings({
    required this.pushNotifications,
    required this.emailNotifications,
    required this.newEstateNotifications,
    required this.estateUpdateNotifications,
    required this.messageNotifications,
    required this.accountNotifications,
    required this.specialOfferNotifications,
    required this.reminderNotifications,
    required this.systemNotifications,
    required this.notificationSound,
    required this.vibration,
    required this.quietHoursStart,
    required this.quietHoursEnd,
  });

  factory NotificationSettings.defaultSettings() {
    return const NotificationSettings(
      pushNotifications: true,
      emailNotifications: true,
      newEstateNotifications: true,
      estateUpdateNotifications: true,
      messageNotifications: true,
      accountNotifications: true,
      specialOfferNotifications: true,
      reminderNotifications: true,
      systemNotifications: true,
      notificationSound: 'default',
      vibration: true,
      quietHoursStart: '22:00',
      quietHoursEnd: '08:00',
    );
  }

  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      pushNotifications: json['pushNotifications'] ?? true,
      emailNotifications: json['emailNotifications'] ?? true,
      newEstateNotifications: json['newEstateNotifications'] ?? true,
      estateUpdateNotifications: json['estateUpdateNotifications'] ?? true,
      messageNotifications: json['messageNotifications'] ?? true,
      accountNotifications: json['accountNotifications'] ?? true,
      specialOfferNotifications: json['specialOfferNotifications'] ?? true,
      reminderNotifications: json['reminderNotifications'] ?? true,
      systemNotifications: json['systemNotifications'] ?? true,
      notificationSound: json['notificationSound'] ?? 'default',
      vibration: json['vibration'] ?? true,
      quietHoursStart: json['quietHoursStart'] ?? '22:00',
      quietHoursEnd: json['quietHoursEnd'] ?? '08:00',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pushNotifications': pushNotifications,
      'emailNotifications': emailNotifications,
      'newEstateNotifications': newEstateNotifications,
      'estateUpdateNotifications': estateUpdateNotifications,
      'messageNotifications': messageNotifications,
      'accountNotifications': accountNotifications,
      'specialOfferNotifications': specialOfferNotifications,
      'reminderNotifications': reminderNotifications,
      'systemNotifications': systemNotifications,
      'notificationSound': notificationSound,
      'vibration': vibration,
      'quietHoursStart': quietHoursStart,
      'quietHoursEnd': quietHoursEnd,
    };
  }

  NotificationSettings copyWith({
    bool? pushNotifications,
    bool? emailNotifications,
    bool? newEstateNotifications,
    bool? estateUpdateNotifications,
    bool? messageNotifications,
    bool? accountNotifications,
    bool? specialOfferNotifications,
    bool? reminderNotifications,
    bool? systemNotifications,
    String? notificationSound,
    bool? vibration,
    String? quietHoursStart,
    String? quietHoursEnd,
  }) {
    return NotificationSettings(
      pushNotifications: pushNotifications ?? this.pushNotifications,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      newEstateNotifications: newEstateNotifications ?? this.newEstateNotifications,
      estateUpdateNotifications: estateUpdateNotifications ?? this.estateUpdateNotifications,
      messageNotifications: messageNotifications ?? this.messageNotifications,
      accountNotifications: accountNotifications ?? this.accountNotifications,
      specialOfferNotifications: specialOfferNotifications ?? this.specialOfferNotifications,
      reminderNotifications: reminderNotifications ?? this.reminderNotifications,
      systemNotifications: systemNotifications ?? this.systemNotifications,
      notificationSound: notificationSound ?? this.notificationSound,
      vibration: vibration ?? this.vibration,
      quietHoursStart: quietHoursStart ?? this.quietHoursStart,
      quietHoursEnd: quietHoursEnd ?? this.quietHoursEnd,
    );
  }
}

/// إعدادات الخصوصية والأمان
class PrivacySettings {
  final bool locationServices;
  final bool saveSearchHistory;
  final bool shareUsageData;
  final bool personalizedAds;
  final bool biometricAuth;
  final bool autoLock;
  final int lockTimeout; // بالدقائق
  final bool twoFactorAuth;
  final bool allowDataCollection;

  const PrivacySettings({
    required this.locationServices,
    required this.saveSearchHistory,
    required this.shareUsageData,
    required this.personalizedAds,
    required this.biometricAuth,
    required this.autoLock,
    required this.lockTimeout,
    required this.twoFactorAuth,
    required this.allowDataCollection,
  });

  factory PrivacySettings.defaultSettings() {
    return const PrivacySettings(
      locationServices: true,
      saveSearchHistory: true,
      shareUsageData: false,
      personalizedAds: true,
      biometricAuth: false,
      autoLock: false,
      lockTimeout: 5,
      twoFactorAuth: false,
      allowDataCollection: true,
    );
  }

  factory PrivacySettings.fromJson(Map<String, dynamic> json) {
    return PrivacySettings(
      locationServices: json['locationServices'] ?? true,
      saveSearchHistory: json['saveSearchHistory'] ?? true,
      shareUsageData: json['shareUsageData'] ?? false,
      personalizedAds: json['personalizedAds'] ?? true,
      biometricAuth: json['biometricAuth'] ?? false,
      autoLock: json['autoLock'] ?? false,
      lockTimeout: json['lockTimeout'] ?? 5,
      twoFactorAuth: json['twoFactorAuth'] ?? false,
      allowDataCollection: json['allowDataCollection'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'locationServices': locationServices,
      'saveSearchHistory': saveSearchHistory,
      'shareUsageData': shareUsageData,
      'personalizedAds': personalizedAds,
      'biometricAuth': biometricAuth,
      'autoLock': autoLock,
      'lockTimeout': lockTimeout,
      'twoFactorAuth': twoFactorAuth,
      'allowDataCollection': allowDataCollection,
    };
  }

  PrivacySettings copyWith({
    bool? locationServices,
    bool? saveSearchHistory,
    bool? shareUsageData,
    bool? personalizedAds,
    bool? biometricAuth,
    bool? autoLock,
    int? lockTimeout,
    bool? twoFactorAuth,
    bool? allowDataCollection,
  }) {
    return PrivacySettings(
      locationServices: locationServices ?? this.locationServices,
      saveSearchHistory: saveSearchHistory ?? this.saveSearchHistory,
      shareUsageData: shareUsageData ?? this.shareUsageData,
      personalizedAds: personalizedAds ?? this.personalizedAds,
      biometricAuth: biometricAuth ?? this.biometricAuth,
      autoLock: autoLock ?? this.autoLock,
      lockTimeout: lockTimeout ?? this.lockTimeout,
      twoFactorAuth: twoFactorAuth ?? this.twoFactorAuth,
      allowDataCollection: allowDataCollection ?? this.allowDataCollection,
    );
  }
}

/// إعدادات الأداء
class PerformanceSettings {
  final bool autoSync;
  final bool offlineMode;
  final String dataUsageMode; // 'low', 'medium', 'high'
  final String imageQuality; // 'low', 'medium', 'high'
  final bool cacheEnabled;
  final bool analyticsEnabled;
  final bool crashReporting;
  final bool performanceMode;
  final bool autoBackup;
  final int cacheSize; // بالميجابايت

  const PerformanceSettings({
    required this.autoSync,
    required this.offlineMode,
    required this.dataUsageMode,
    required this.imageQuality,
    required this.cacheEnabled,
    required this.analyticsEnabled,
    required this.crashReporting,
    required this.performanceMode,
    required this.autoBackup,
    required this.cacheSize,
  });

  factory PerformanceSettings.defaultSettings() {
    return const PerformanceSettings(
      autoSync: true,
      offlineMode: false,
      dataUsageMode: 'medium',
      imageQuality: 'medium',
      cacheEnabled: true,
      analyticsEnabled: true,
      crashReporting: true,
      performanceMode: false,
      autoBackup: true,
      cacheSize: 100,
    );
  }

  factory PerformanceSettings.fromJson(Map<String, dynamic> json) {
    return PerformanceSettings(
      autoSync: json['autoSync'] ?? true,
      offlineMode: json['offlineMode'] ?? false,
      dataUsageMode: json['dataUsageMode'] ?? 'medium',
      imageQuality: json['imageQuality'] ?? 'medium',
      cacheEnabled: json['cacheEnabled'] ?? true,
      analyticsEnabled: json['analyticsEnabled'] ?? true,
      crashReporting: json['crashReporting'] ?? true,
      performanceMode: json['performanceMode'] ?? false,
      autoBackup: json['autoBackup'] ?? true,
      cacheSize: json['cacheSize'] ?? 100,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'autoSync': autoSync,
      'offlineMode': offlineMode,
      'dataUsageMode': dataUsageMode,
      'imageQuality': imageQuality,
      'cacheEnabled': cacheEnabled,
      'analyticsEnabled': analyticsEnabled,
      'crashReporting': crashReporting,
      'performanceMode': performanceMode,
      'autoBackup': autoBackup,
      'cacheSize': cacheSize,
    };
  }

  PerformanceSettings copyWith({
    bool? autoSync,
    bool? offlineMode,
    String? dataUsageMode,
    String? imageQuality,
    bool? cacheEnabled,
    bool? analyticsEnabled,
    bool? crashReporting,
    bool? performanceMode,
    bool? autoBackup,
    int? cacheSize,
  }) {
    return PerformanceSettings(
      autoSync: autoSync ?? this.autoSync,
      offlineMode: offlineMode ?? this.offlineMode,
      dataUsageMode: dataUsageMode ?? this.dataUsageMode,
      imageQuality: imageQuality ?? this.imageQuality,
      cacheEnabled: cacheEnabled ?? this.cacheEnabled,
      analyticsEnabled: analyticsEnabled ?? this.analyticsEnabled,
      crashReporting: crashReporting ?? this.crashReporting,
      performanceMode: performanceMode ?? this.performanceMode,
      autoBackup: autoBackup ?? this.autoBackup,
      cacheSize: cacheSize ?? this.cacheSize,
    );
  }
}

/// الإعدادات العامة
class GeneralSettings {
  final String language;
  final String region;
  final String currency;
  final String dateFormat;
  final String timeFormat;
  final bool autoUpdate;
  final bool betaFeatures;
  final bool hapticFeedback;
  final bool soundEffects;
  final double animationSpeed;

  const GeneralSettings({
    required this.language,
    required this.region,
    required this.currency,
    required this.dateFormat,
    required this.timeFormat,
    required this.autoUpdate,
    required this.betaFeatures,
    required this.hapticFeedback,
    required this.soundEffects,
    required this.animationSpeed,
  });

  factory GeneralSettings.defaultSettings() {
    return const GeneralSettings(
      language: 'ar',
      region: 'KW',
      currency: 'KWD',
      dateFormat: 'dd/MM/yyyy',
      timeFormat: '24h',
      autoUpdate: true,
      betaFeatures: false,
      hapticFeedback: true,
      soundEffects: true,
      animationSpeed: 1.0,
    );
  }

  factory GeneralSettings.fromJson(Map<String, dynamic> json) {
    return GeneralSettings(
      language: json['language'] ?? 'ar',
      region: json['region'] ?? 'KW',
      currency: json['currency'] ?? 'KWD',
      dateFormat: json['dateFormat'] ?? 'dd/MM/yyyy',
      timeFormat: json['timeFormat'] ?? '24h',
      autoUpdate: json['autoUpdate'] ?? true,
      betaFeatures: json['betaFeatures'] ?? false,
      hapticFeedback: json['hapticFeedback'] ?? true,
      soundEffects: json['soundEffects'] ?? true,
      animationSpeed: (json['animationSpeed'] ?? 1.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'language': language,
      'region': region,
      'currency': currency,
      'dateFormat': dateFormat,
      'timeFormat': timeFormat,
      'autoUpdate': autoUpdate,
      'betaFeatures': betaFeatures,
      'hapticFeedback': hapticFeedback,
      'soundEffects': soundEffects,
      'animationSpeed': animationSpeed,
    };
  }

  GeneralSettings copyWith({
    String? language,
    String? region,
    String? currency,
    String? dateFormat,
    String? timeFormat,
    bool? autoUpdate,
    bool? betaFeatures,
    bool? hapticFeedback,
    bool? soundEffects,
    double? animationSpeed,
  }) {
    return GeneralSettings(
      language: language ?? this.language,
      region: region ?? this.region,
      currency: currency ?? this.currency,
      dateFormat: dateFormat ?? this.dateFormat,
      timeFormat: timeFormat ?? this.timeFormat,
      autoUpdate: autoUpdate ?? this.autoUpdate,
      betaFeatures: betaFeatures ?? this.betaFeatures,
      hapticFeedback: hapticFeedback ?? this.hapticFeedback,
      soundEffects: soundEffects ?? this.soundEffects,
      animationSpeed: animationSpeed ?? this.animationSpeed,
    );
  }
}
