// lib/presentation/pages/property_request/property_requests_page.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../../core/theme/app_colors.dart';
import '../../providers/property_request_provider.dart';
import '../../utils/loading_state.dart';
import '../../widgets/property_request/property_request_card.dart';
import '../../widgets/property_request/property_request_filter_dialog.dart';
import '../../widgets/property_request/property_request_comments_sheet.dart';
import '../../../domain/services/user_interface_customization_service.dart';
import '../../../core/constants/user_types.dart';
import '../../../core/services/messaging_service.dart';
import '../../../data/repositories_impl/messaging_repository_impl.dart';
import '../../../domain/models/property_request/property_request_model.dart';
import 'create_property_request_page.dart';
import 'property_request_details_page.dart';
import 'create_property_offer_page.dart';
import '../messaging/conversation_details_page.dart';

/// صفحة طلبات العقارات
class PropertyRequestsPage extends StatefulWidget {
  const PropertyRequestsPage({super.key});

  @override
  State<PropertyRequestsPage> createState() => _PropertyRequestsPageState();
}

class _PropertyRequestsPageState extends State<PropertyRequestsPage> {
  final ScrollController _scrollController = ScrollController();
  bool _isFilterVisible = false;
  String? _selectedPropertyType;
  String? _selectedLocation;
  RangeValues? _priceRange;
  int? _minRooms;
  int? _minBathrooms;
  double? _minArea;
  bool? _hasCentralAC;
  bool? _hasMaidRoom;
  bool? _hasGarage;
  bool? _hasSwimmingPool;
  bool? _hasElevator;
  bool? _isFullyFurnished;
  String _sortBy = 'createdAt';
  bool _descending = true;
  bool _isUserSeeker = false; // لتحديد ما إذا كان المستخدم باحث عن عقار
  bool _isFiltered = false; // لتتبع حالة الفلاتر

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // التحقق من نوع المستخدم
    _checkUserType();

    // تحميل طلبات العقارات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<PropertyRequestProvider>(context, listen: false);
      provider.loadPropertyRequests(refresh: true);
    });

    // تحميل فوري إضافي للتأكد
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        final provider = Provider.of<PropertyRequestProvider>(context, listen: false);
        if (provider.requests.isEmpty) {
          provider.loadPropertyRequests(refresh: true);
        }
      }
    });
  }

  /// التحقق من نوع المستخدم الحالي باستخدام UserInterfaceCustomizationService
  Future<void> _checkUserType() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      return;
    }

    try {
      final uiService = UserInterfaceCustomizationService();
      final userType = await uiService.getCurrentUserType();
      final userTypeString = userType.toString().split('.').last;

      setState(() {
        _isUserSeeker = UserTypeConstants.canCreatePropertyRequests(userTypeString);
      });
    } catch (e) {
      print('Error checking user type: $e');
      setState(() {
        _isUserSeeker = false; // افتراضي آمن
      });
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  /// التمرير للأسفل
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final provider = Provider.of<PropertyRequestProvider>(context, listen: false);
      if (provider.hasMoreRequests && !provider.isLoadingMoreRequests) {
        provider.loadMorePropertyRequests();
      }
    }
  }

  /// تطبيق الفلاتر بكفاءة عالية
  void _applyFilters() {
    final provider = Provider.of<PropertyRequestProvider>(context, listen: false);

    // بناء الفلاتر بطريقة محسنة
    final filters = <String, dynamic>{};

    if (_selectedPropertyType != null) {
      filters['propertyType'] = _selectedPropertyType;
    }

    if (_selectedLocation != null) {
      filters['preferredLocations'] = [_selectedLocation];
    }

    if (_priceRange != null) {
      filters['minPrice'] = _priceRange!.start;
      filters['maxPrice'] = _priceRange!.end;
    }

    if (_minRooms != null) {
      filters['minRooms'] = _minRooms;
    }

    if (_minBathrooms != null) {
      filters['minBathrooms'] = _minBathrooms;
    }

    if (_minArea != null) {
      filters['minArea'] = _minArea;
    }

    if (_hasCentralAC != null) {
      filters['hasCentralAC'] = _hasCentralAC;
    }

    if (_hasMaidRoom != null) {
      filters['hasMaidRoom'] = _hasMaidRoom;
    }

    if (_hasGarage != null) {
      filters['hasGarage'] = _hasGarage;
    }

    if (_hasSwimmingPool != null) {
      filters['hasSwimmingPool'] = _hasSwimmingPool;
    }

    if (_hasElevator != null) {
      filters['hasElevator'] = _hasElevator;
    }

    if (_isFullyFurnished != null) {
      filters['isFullyFurnished'] = _isFullyFurnished;
    }

    // تطبيق الفلاتر والترتيب
    provider.setFilters(filters);
    provider.setSorting(_sortBy, _descending);
    provider.loadPropertyRequests(refresh: true);

    setState(() {
      _isFilterVisible = false;
      _isFiltered = _hasActiveFilters();
    });

    // إظهار رسالة تأكيد
    _showFilterAppliedMessage();
  }

  /// التحقق من وجود فلاتر نشطة
  bool _hasActiveFilters() {
    return _selectedPropertyType != null ||
           _selectedLocation != null ||
           _priceRange != null ||
           _minRooms != null ||
           _minBathrooms != null ||
           _minArea != null ||
           _hasCentralAC != null ||
           _hasMaidRoom != null ||
           _hasGarage != null ||
           _hasSwimmingPool != null ||
           _hasElevator != null ||
           _isFullyFurnished != null;
  }

  /// إظهار رسالة تطبيق الفلاتر
  void _showFilterAppliedMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(
              'تم تطبيق الفلاتر بنجاح',
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white)),
          ]),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.all(16),
        duration: const Duration(seconds: 2)));
  }

  /// إعادة تعيين الفلاتر
  void _resetFilters() {
    setState(() {
      _selectedPropertyType = null;
      _selectedLocation = null;
      _priceRange = null;
      _minRooms = null;
      _minBathrooms = null;
      _minArea = null;
      _hasCentralAC = null;
      _hasMaidRoom = null;
      _hasGarage = null;
      _hasSwimmingPool = null;
      _hasElevator = null;
      _isFullyFurnished = null;
      _sortBy = 'createdAt';
      _descending = true;
    });

    final provider = Provider.of<PropertyRequestProvider>(context, listen: false);
    provider.setFilters({});
    provider.setSorting('createdAt', true);
    provider.loadPropertyRequests(refresh: true);

    setState(() {
      _isFilterVisible = false;
    });
  }

  /// عرض حوار البحث الذكي المحسن
  void _showSearchDialog() {
    final TextEditingController searchController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.white, AppColors.orangeCardBackground.withValues(alpha: 0.3)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: AppColors.primaryOrange.withValues(alpha: 0.2),
                blurRadius: 20,
                offset: const Offset(0, 10)),
            ]),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان البحث
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: AppColors.orangeGradient,
                      borderRadius: BorderRadius.circular(12)),
                    child: const Icon(Icons.search_rounded, color: Colors.white, size: 24)),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'البحث الذكي',
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary)),
                        Text(
                          'ابحث في جميع تفاصيل الطلبات',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: AppColors.textSecondary)),
                      ])),
                ]),
              const SizedBox(height: 24),

              // حقل البحث المحسن
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: AppColors.primaryOrange.withValues(alpha: 0.3)),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primaryOrange.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2)),
                  ]),
                child: TextField(
                  controller: searchController,
                  decoration: InputDecoration(
                    hintText: 'مثال: شقة في الجابرية، فيلا مع مسبح، مكتب تجاري...',
                    hintStyle: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey[500]),
                    prefixIcon: Icon(Icons.search_rounded,
                      color: AppColors.primaryOrange, size: 22),
                    suffixIcon: IconButton(
                      onPressed: () => searchController.clear(),
                      icon: Icon(Icons.clear, color: Colors.grey[400], size: 20)),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16, vertical: 16)),
                  style: GoogleFonts.cairo(fontSize: 14),
                  onSubmitted: (query) {
                    Navigator.pop(context);
                    _performSmartSearch(query);
                  })),
              const SizedBox(height: 16),

              // اقتراحات البحث
              Text(
                'اقتراحات البحث:',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary)),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildSearchSuggestion('شقة للإيجار', searchController),
                  _buildSearchSuggestion('فيلا للبيع', searchController),
                  _buildSearchSuggestion('مكتب تجاري', searchController),
                  _buildSearchSuggestion('أرض استثمارية', searchController),
                  _buildSearchSuggestion('مع مسبح', searchController),
                  _buildSearchSuggestion('مفروش', searchController),
                ]),
              const SizedBox(height: 24),

              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12)),
                        side: BorderSide(color: AppColors.primaryOrange.withValues(alpha: 0.5))),
                      child: Text(
                        'إلغاء',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.primaryOrange)))),
                  const SizedBox(width: 12),
                  Expanded(
                    flex: 2,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: AppColors.orangeGradient,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primaryOrange.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 4)),
                        ]),
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _performSmartSearch(searchController.text);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          shadowColor: Colors.transparent,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12))),
                        child: Text(
                          'بحث',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.white))))),
                ]),
            ])),
        ));
  }

  /// بناء اقتراح البحث
  Widget _buildSearchSuggestion(String suggestion, TextEditingController controller) {
    return InkWell(
      onTap: () {
        controller.text = suggestion;
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: AppColors.primaryOrange.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: AppColors.primaryOrange.withValues(alpha: 0.3),
            width: 1)),
        child: Text(
          suggestion,
          style: GoogleFonts.cairo(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: AppColors.primaryOrange))));
  }

  /// تنفيذ البحث الذكي المحسن
  void _performSmartSearch(String query) {
    if (query.trim().isEmpty) return;

    final provider = Provider.of<PropertyRequestProvider>(context, listen: false);

    // تطبيق البحث الذكي مع فلاتر متقدمة
    final searchFilters = <String, dynamic>{};

    // تحليل النص للبحث الذكي
    final lowerQuery = query.toLowerCase();

    // البحث في نوع العقار
    if (lowerQuery.contains('شقة') || lowerQuery.contains('apartment')) {
      searchFilters['propertyType'] = 'شقة';
    } else if (lowerQuery.contains('فيلا') || lowerQuery.contains('villa')) {
      searchFilters['propertyType'] = 'فيلا';
    } else if (lowerQuery.contains('مكتب') || lowerQuery.contains('office')) {
      searchFilters['propertyType'] = 'مكتب';
    } else if (lowerQuery.contains('أرض') || lowerQuery.contains('land')) {
      searchFilters['propertyType'] = 'أرض';
    } else if (lowerQuery.contains('محل') || lowerQuery.contains('shop')) {
      searchFilters['propertyType'] = 'محل تجاري';
    }

    // البحث في نوع الإعلان
    if (lowerQuery.contains('إيجار') || lowerQuery.contains('rent')) {
      searchFilters['adType'] = 'للإيجار';
    } else if (lowerQuery.contains('بيع') || lowerQuery.contains('sale')) {
      searchFilters['adType'] = 'للبيع';
    }

    // البحث في الميزات
    if (lowerQuery.contains('مسبح') || lowerQuery.contains('pool')) {
      searchFilters['hasSwimmingPool'] = true;
    }
    if (lowerQuery.contains('مفروش') || lowerQuery.contains('furnished')) {
      searchFilters['isFullyFurnished'] = true;
    }
    if (lowerQuery.contains('مصعد') || lowerQuery.contains('elevator')) {
      searchFilters['hasElevator'] = true;
    }
    if (lowerQuery.contains('مرآب') || lowerQuery.contains('garage')) {
      searchFilters['hasGarage'] = true;
    }
    if (lowerQuery.contains('تكييف مركزي') || lowerQuery.contains('central ac')) {
      searchFilters['hasCentralAC'] = true;
    }

    // إضافة البحث النصي العام
    searchFilters['searchText'] = query;

    // تطبيق الفلاتر
    provider.setFilters(searchFilters);
    provider.loadPropertyRequests(refresh: true);

    // إظهار رسالة نجاح البحث
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.search_rounded, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'تم البحث عن: "$query"',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white))),
          ]),
        backgroundColor: AppColors.primaryOrange,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.all(16),
        duration: const Duration(seconds: 3)));
  }

  /// عرض حوار الفلاتر
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => PropertyRequestFilterDialog(
        selectedPropertyType: _selectedPropertyType,
        selectedLocation: _selectedLocation,
        priceRange: _priceRange,
        minRooms: _minRooms,
        minBathrooms: _minBathrooms,
        minArea: _minArea,
        hasCentralAC: _hasCentralAC,
        hasMaidRoom: _hasMaidRoom,
        hasGarage: _hasGarage,
        hasSwimmingPool: _hasSwimmingPool,
        hasElevator: _hasElevator,
        isFullyFurnished: _isFullyFurnished,
        sortBy: _sortBy,
        descending: _descending,
        onApply: (
          propertyType,
          location,
          priceRange,
          minRooms,
          minBathrooms,
          minArea,
          hasCentralAC,
          hasMaidRoom,
          hasGarage,
          hasSwimmingPool,
          hasElevator,
          isFullyFurnished,
          sortBy,
          descending) {
          setState(() {
            _selectedPropertyType = propertyType;
            _selectedLocation = location;
            _priceRange = priceRange;
            _minRooms = minRooms;
            _minBathrooms = minBathrooms;
            _minArea = minArea;
            _hasCentralAC = hasCentralAC;
            _hasMaidRoom = hasMaidRoom;
            _hasGarage = hasGarage;
            _hasSwimmingPool = hasSwimmingPool;
            _hasElevator = hasElevator;
            _isFullyFurnished = isFullyFurnished;
            _sortBy = sortBy;
            _descending = descending;
          });

          _applyFilters();
        },
        onReset: _resetFilters));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.orangeBackground,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.greenOrangeGradient)),
        title: Text(
          'طلبات العقارات',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: Colors.white)),
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          // أزرار الإجراءات العصرية
          Row(
            children: [
              // زر البحث
              _buildModernHeaderButton(
                icon: Icons.search_rounded,
                label: '',
                onPressed: _showSearchDialog,
              ),
              const SizedBox(width: 8),

              // زر الفلتر
              _buildModernHeaderButton(
                icon: Icons.tune_rounded,
                label: '',
                onPressed: _showFilterDialog,
                showBadge: _isFiltered,
              ),
              const SizedBox(width: 8),

              // زر المزيد
              _buildModernHeaderButton(
                icon: Icons.more_vert_rounded,
                label: '',
                onPressed: _showMoreOptionsMenu,
              ),
              const SizedBox(width: 12),
            ],
          ),
        ]),
      body: Consumer<PropertyRequestProvider>(
        builder: (context, provider, child) {
          final state = provider.requestsState;
          final requests = provider.requests;

          if (state == LoadingState.loading && requests.isEmpty) {
            return Container(
              decoration: const BoxDecoration(
                gradient: AppColors.lightOrangeGradient),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(32),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(30),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primaryOrange.withValues(alpha: 0.2),
                            blurRadius: 30,
                            offset: const Offset(0, 15)),
                        ]),
                      child: Column(
                        children: [
                          // مؤشر التحميل العصري
                          Stack(
                            alignment: Alignment.center,
                            children: [
                              Container(
                                width: 80,
                                height: 80,
                                decoration: BoxDecoration(
                                  gradient: AppColors.orangeGradient,
                                  borderRadius: BorderRadius.circular(40),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.primaryOrange.withValues(alpha: 0.3),
                                      blurRadius: 15,
                                      offset: const Offset(0, 8)),
                                  ]),
                                child: const Icon(
                                  Icons.search_rounded,
                                  color: Colors.white,
                                  size: 36)),
                              SizedBox(
                                width: 100,
                                height: 100,
                                child: CircularProgressIndicator(
                                  strokeWidth: 3,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.primaryOrange.withValues(alpha: 0.7)),
                                  backgroundColor: AppColors.primaryOrange.withValues(alpha: 0.1))),
                            ]),
                          const SizedBox(height: 24),
                          Text(
                            'جاري البحث عن الطلبات',
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary)),
                          const SizedBox(height: 8),
                          Text(
                            'يرجى الانتظار قليلاً...',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: AppColors.textSecondary)),
                        ])),
                  ])));
          }

          if (state == LoadingState.error && requests.isEmpty) {
            return Container(
              decoration: const BoxDecoration(
                gradient: AppColors.lightOrangeGradient),
              child: Center(
                child: Container(
                  margin: const EdgeInsets.all(20),
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.red.withValues(alpha: 0.15),
                        blurRadius: 20,
                        offset: const Offset(0, 10)),
                    ]),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // أيقونة الخطأ العصرية
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: Colors.red.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(40)),
                            child: const Icon(
                              Icons.wifi_off_rounded,
                              size: 40,
                              color: Colors.red)),
                          Positioned(
                            top: 0,
                            right: 0,
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle),
                              child: const Icon(
                                Icons.close,
                                size: 16,
                                color: Colors.white))),
                        ]),
                      const SizedBox(height: 20),
                      Text(
                        'مشكلة في الاتصال',
                        style: GoogleFonts.cairo(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary),
                        textAlign: TextAlign.center),
                      const SizedBox(height: 8),
                      Text(
                        provider.errorMessage ?? 'حدث خطأ أثناء تحميل الطلبات',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                          height: 1.5),
                        textAlign: TextAlign.center),
                      const SizedBox(height: 24),

                      // أزرار الإجراءات
                      Row(
                        children: [
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.primaryOrange),
                                borderRadius: BorderRadius.circular(16)),
                              child: OutlinedButton.icon(
                                onPressed: () {
                                  // فتح إعدادات الشبكة أو نصائح الاتصال
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'تحقق من اتصال الإنترنت وحاول مرة أخرى',
                                        style: GoogleFonts.cairo()),
                                      backgroundColor: AppColors.primaryOrange));
                                },
                                style: OutlinedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16)),
                                  side: BorderSide.none),
                                icon: const Icon(Icons.help_outline, size: 18),
                                label: Text(
                                  'مساعدة',
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.primaryOrange))))),
                          const SizedBox(width: 12),
                          Expanded(
                            flex: 2,
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: AppColors.orangeGradient,
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.primaryOrange.withValues(alpha: 0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4)),
                                ]),
                              child: ElevatedButton.icon(
                                onPressed: () {
                                  provider.loadPropertyRequests(refresh: true);
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.transparent,
                                  shadowColor: Colors.transparent,
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16))),
                                icon: const Icon(Icons.refresh, color: Colors.white, size: 18),
                                label: Text(
                                  'إعادة المحاولة',
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white))))),
                        ]),
                    ]))));
          }

          if (state == LoadingState.empty || requests.isEmpty) {
            return Container(
              decoration: const BoxDecoration(
                gradient: AppColors.lightOrangeGradient),
              child: Center(
                child: Container(
                  margin: const EdgeInsets.all(20),
                  padding: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryOrange.withValues(alpha: 0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10)),
                    ]),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          gradient: _isUserSeeker
                              ? AppColors.orangeGradient
                              : LinearGradient(
                                  colors: [AppColors.primary, AppColors.primaryLight],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight),
                          borderRadius: BorderRadius.circular(60)),
                        child: Icon(
                          _isUserSeeker ? Icons.add_home : Icons.search_off,
                          color: Colors.white,
                          size: 40)),
                      const SizedBox(height: 24),
                      Text(
                        _isUserSeeker
                            ? 'ابدأ رحلة البحث عن منزل أحلامك'
                            : 'طلبات الباحثين عن العقارات',
                        style: GoogleFonts.cairo(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary),
                        textAlign: TextAlign.center),
                      const SizedBox(height: 12),
                      Text(
                        _isUserSeeker
                            ? 'أنشئ طلبك الأول وحدد مواصفات العقار المثالي'
                            : 'ستظهر هنا طلبات الباحثين عن عقارات لتتمكن من تقديم عروضك',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                          height: 1.5),
                        textAlign: TextAlign.center),
                      const SizedBox(height: 28),
                      if (_isUserSeeker)
                        Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            gradient: AppColors.orangeGradient,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.primaryOrange.withValues(alpha: 0.3),
                                blurRadius: 12,
                                offset: const Offset(0, 6)),
                            ]),
                          child: ElevatedButton.icon(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const CreatePropertyRequestPage()));
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              shadowColor: Colors.transparent,
                              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16))),
                            icon: const Icon(
                              Icons.add_circle_outline,
                              color: Colors.white,
                              size: 24),
                            label: Text(
                              'إنشاء طلب جديد',
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.white)))),
                    ]))));
          }

          return Container(
            decoration: const BoxDecoration(
              gradient: AppColors.lightOrangeGradient),
            child: RefreshIndicator(
              onRefresh: () async {
                await provider.loadPropertyRequests(refresh: true);
              },
              color: AppColors.primaryOrange,
              backgroundColor: Colors.white,
              child: ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(16),
                itemCount: requests.length + (provider.isLoadingMoreRequests ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index == requests.length) {
                    return Container(
                      margin: const EdgeInsets.symmetric(vertical: 20),
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.white, AppColors.orangeCardBackground],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primaryOrange.withValues(alpha: 0.15),
                            blurRadius: 15,
                            offset: const Offset(0, 8)),
                        ],
                        border: Border.all(
                          color: AppColors.primaryOrange.withValues(alpha: 0.2),
                          width: 1)),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Stack(
                            alignment: Alignment.center,
                            children: [
                              Container(
                                width: 50,
                                height: 50,
                                decoration: BoxDecoration(
                                  gradient: AppColors.orangeGradient,
                                  borderRadius: BorderRadius.circular(25),
                                  boxShadow: [
                                    BoxShadow(
                                      color: AppColors.primaryOrange.withValues(alpha: 0.3),
                                      blurRadius: 10,
                                      offset: const Offset(0, 4)),
                                  ]),
                                child: const Icon(
                                  Icons.more_horiz_rounded,
                                  color: Colors.white,
                                  size: 24)),
                              SizedBox(
                                width: 70,
                                height: 70,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.primaryOrange.withValues(alpha: 0.6)),
                                  backgroundColor: AppColors.primaryOrange.withValues(alpha: 0.1))),
                            ]),
                          const SizedBox(width: 16),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'جاري تحميل المزيد',
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary)),
                              const SizedBox(height: 4),
                              Text(
                                'يرجى الانتظار...',
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: AppColors.textSecondary)),
                            ]),
                        ]));
                  }

                  final request = requests[index];
                  return Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: PropertyRequestCard(
                      request: request,
                      onTap: () async {
                        // عرض مؤشر التحميل العصري
                        showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (context) => Dialog(
                            backgroundColor: Colors.transparent,
                            child: Container(
                              padding: const EdgeInsets.all(32),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [Colors.white, AppColors.orangeCardBackground],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight),
                                borderRadius: BorderRadius.circular(24),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.primaryOrange.withValues(alpha: 0.3),
                                    blurRadius: 20,
                                    offset: const Offset(0, 10)),
                                ]),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // مؤشر التحميل العصري
                                  Stack(
                                    alignment: Alignment.center,
                                    children: [
                                      Container(
                                        width: 80,
                                        height: 80,
                                        decoration: BoxDecoration(
                                          gradient: AppColors.orangeGradient,
                                          borderRadius: BorderRadius.circular(40),
                                          boxShadow: [
                                            BoxShadow(
                                              color: AppColors.primaryOrange.withValues(alpha: 0.3),
                                              blurRadius: 15,
                                              offset: const Offset(0, 8)),
                                          ]),
                                        child: const Icon(
                                          Icons.description_rounded,
                                          color: Colors.white,
                                          size: 32)),
                                      SizedBox(
                                        width: 100,
                                        height: 100,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 3,
                                          valueColor: AlwaysStoppedAnimation<Color>(
                                            AppColors.primaryOrange),
                                          backgroundColor: AppColors.primaryOrange.withValues(alpha: 0.1))),
                                    ]),
                                  const SizedBox(height: 24),
                                  Text(
                                    'جاري فتح تفاصيل الطلب',
                                    style: GoogleFonts.cairo(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.textPrimary)),
                                  const SizedBox(height: 8),
                                  Text(
                                    'يرجى الانتظار...',
                                    style: GoogleFonts.cairo(
                                      fontSize: 14,
                                      color: AppColors.textSecondary)),
                                ]))));

                        // تأخير قصير لإظهار التحميل
                        await Future.delayed(const Duration(milliseconds: 500));

                        // إغلاق مؤشر التحميل
                        if (mounted) {
                          Navigator.pop(context);
                        }

                        // الانتقال إلى صفحة التفاصيل
                        if (mounted) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => PropertyRequestDetailsPage(
                                requestId: request.id)));
                        }
                      },
                      // دوال الإجراءات
                      onMessageTap: () async {
                        await _startConversation(request);
                      },
                      onCommentTap: () {
                        _navigateToComments(request);
                      },
                      onOfferTap: () {
                        _navigateToCreateOffer(request);
                      }));
                })));
        }),
      floatingActionButton: _isUserSeeker ? Container(
        decoration: BoxDecoration(
          gradient: AppColors.orangeGradient,
          borderRadius: BorderRadius.circular(28),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryOrange.withValues(alpha: 0.4),
              blurRadius: 16,
              offset: const Offset(0, 8)),
          ]),
        child: FloatingActionButton.extended(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const CreatePropertyRequestPage()));
          },
          backgroundColor: Colors.transparent,
          elevation: 0,
          icon: const Icon(
            Icons.add_home_outlined,
            color: Colors.white,
            size: 24),
          label: Text(
            'طلب جديد',
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white)))) : null);
  }

  /// بدء محادثة مع صاحب الطلب
  Future<void> _startConversation(PropertyRequestModel request) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'يجب تسجيل الدخول أولاً',
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.red));
      return;
    }

    if (currentUser.uid == request.userId) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'لا يمكنك مراسلة نفسك',
            style: GoogleFonts.cairo()),
          backgroundColor: Colors.orange));
      return;
    }

    // عرض مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withValues(alpha: 0.2),
                blurRadius: 20,
                offset: const Offset(0, 10)),
            ]),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // مؤشر التحميل العصري
              Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(40))),
                  const SizedBox(
                    width: 100,
                    height: 100,
                    child: CircularProgressIndicator(
                      strokeWidth: 4,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.blue))),
                  const Icon(
                    Icons.message_rounded,
                    color: Colors.white,
                    size: 32),
                ]),
              const SizedBox(height: 24),
              Text(
                'جاري إنشاء المحادثة',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary)),
              const SizedBox(height: 8),
              Text(
                'يرجى الانتظار قليلاً...',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: AppColors.textSecondary)),
            ]))));

    try {
      // إنشاء خدمة المراسلة
      final messagingRepository = MessagingRepositoryImpl();
      final messagingService = MessagingService(messagingRepository: messagingRepository);

      // إنشاء محادثة متعلقة بطلب العقار
      final conversationId = await messagingService.createConversationForPropertyRequest(
        request.id,
        request.userId,
        request.title);

      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل
      }

      if (conversationId != null && mounted) {
        // الانتقال إلى صفحة المحادثة
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ConversationDetailsPage(
              conversationId: conversationId)));
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء إنشاء المحادثة',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.red));
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء إنشاء المحادثة: $e',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.red));
      }
    }
  }

  /// عرض Bottom Sheet للتعليقات
  void _navigateToComments(PropertyRequestModel request) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => PropertyRequestCommentsSheet(
        requestId: request.id,
        requestTitle: request.title));
  }

  /// الانتقال إلى صفحة إنشاء عرض
  void _navigateToCreateOffer(PropertyRequestModel request) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreatePropertyOfferPage(
          requestId: request.id,
          requestTitle: request.title)));
  }

  /// بناء زر إجراء عصري في الهيدر
  Widget _buildModernHeaderButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    bool showBadge = false,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 6,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
            // شارة التنبيه
            if (showBadge)
              Positioned(
                top: -2,
                right: -2,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: Colors.white,
                      width: 1,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// عرض قائمة خيارات إضافية
  void _showMoreOptionsMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        margin: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مقبض السحب
            Container(
              margin: EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            SizedBox(height: 20),

            // العنوان
            Text(
              'خيارات إضافية',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),

            SizedBox(height: 20),

            // الخيارات
            _buildMenuOption(
              icon: Icons.refresh_rounded,
              title: 'تحديث البيانات',
              subtitle: 'إعادة تحميل الطلبات',
              onTap: () {
                Navigator.pop(context);
                final provider = Provider.of<PropertyRequestProvider>(context, listen: false);
                provider.loadPropertyRequests(refresh: true);
              },
            ),

            _buildMenuOption(
              icon: Icons.clear_all_rounded,
              title: 'مسح الفلاتر',
              subtitle: 'إزالة جميع الفلاتر المطبقة',
              onTap: () {
                Navigator.pop(context);
                _resetFilters();
              },
            ),

            _buildMenuOption(
              icon: Icons.sort_rounded,
              title: 'ترتيب سريع',
              subtitle: 'ترتيب حسب الأحدث',
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  _sortBy = 'createdAt';
                  _descending = true;
                });
                _applyFilters();
              },
            ),

            _buildMenuOption(
              icon: Icons.share_rounded,
              title: 'مشاركة الصفحة',
              subtitle: 'شارك رابط طلبات العقارات',
              onTap: () {
                Navigator.pop(context);
                // مشاركة الصفحة
              },
            ),

            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// بناء خيار في القائمة
  Widget _buildMenuOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primaryOrange.withValues(alpha: 0.1),
                    AppColors.primaryOrange.withValues(alpha: 0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: AppColors.primaryOrange,
                size: 24,
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: GoogleFonts.cairo(
                      fontSize: 13,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios_rounded,
              color: Colors.grey.shade400,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
