import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/presentation/widgets/loading_widget.dart';
import 'package:kuwait_corners/presentation/widgets/project_background_widget.dart';

class MemberDetailsPage extends StatefulWidget {
  final String memberId;

  const MemberDetailsPage({super.key, required this.memberId});

  @override
  State<MemberDetailsPage> createState() => _MemberDetailsPageState();
}

class _MemberDetailsPageState extends State<MemberDetailsPage> {
  Map<String, dynamic>? memberData;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadMemberData();
  }

  Future<void> _loadMemberData() async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('team_members')
          .doc(widget.memberId)
          .get();

      if (doc.exists) {
        setState(() {
          memberData = doc.data();
          isLoading = false;
        });
      } else {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'تفاصيل العضو',
          style: CairoTextStyles.appBarTitle,
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          if (memberData != null)
            IconButton(
              onPressed: () => _editMember(),
              icon: const Icon(Icons.edit, color: Colors.white),
              tooltip: 'تعديل العضو',
            ),
        ],
      ),
      body: ProjectBackgroundWidget(
        child: isLoading
            ? const Center(child: LoadingWidget())
            : memberData == null
                ? _buildNotFoundWidget()
                : _buildMemberDetails(),
      ),
    );
  }

  Widget _buildNotFoundWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_off,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لم يتم العثور على العضو',
            style: CairoTextStyles.headlineSmall.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: const Text('العودة'),
          ),
        ],
      ),
    );
  }

  Widget _buildMemberDetails() {
    final name = memberData!['fullName'] ?? 'غير محدد';
    final role = _getRoleDisplayName(memberData!['role'] ?? 'salesAgent');
    final email = memberData!['email'] ?? '';
    final phone = memberData!['phoneNumber'] ?? '';
    final status = memberData!['status'] ?? 'active';
    final totalSales = (memberData!['totalSales'] ?? 0.0).toDouble();
    final soldCount = memberData!['soldRentedCount'] ?? 0;
    final clientsCount = memberData!['clientsCount'] ?? 0;
    final rating = (memberData!['averageRating'] ?? 0.0).toDouble();
    final joinDate = memberData!['joinDate'] as Timestamp?;
    final imageUrl = memberData!['profileImage'] ?? '';

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة المعلومات الأساسية
          _buildBasicInfoCard(name, role, email, phone, status, imageUrl, joinDate),
          const SizedBox(height: 16),

          // بطاقة الإحصائيات
          _buildStatsCard(totalSales, soldCount, clientsCount, rating),
          const SizedBox(height: 16),

          // بطاقة معلومات إضافية
          _buildAdditionalInfoCard(),
          const SizedBox(height: 16),

          // أزرار الإجراءات
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildBasicInfoCard(String name, String role, String email, String phone,
      String status, String imageUrl, Timestamp? joinDate) {
    final isActive = status == 'active';

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        children: [
          // الصورة والاسم
          Row(
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.7)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: imageUrl.isNotEmpty
                    ? ClipOval(
                        child: Image.network(
                          imageUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              Icon(
                                Icons.person,
                                color: Colors.white,
                                size: 40,
                              ),
                        ),
                      )
                    : Icon(
                        Icons.person,
                        color: Colors.white,
                        size: 40,
                      ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: CairoTextStyles.headlineSmall.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      role,
                      style: CairoTextStyles.bodyLarge.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(status).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: _getStatusColor(status)),
                      ),
                      child: Text(
                        _getStatusDisplayName(status),
                        style: CairoTextStyles.bodySmall.copyWith(
                          color: _getStatusColor(status),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // معلومات الاتصال
          if (email.isNotEmpty || phone.isNotEmpty) ...[
            const Divider(),
            const SizedBox(height: 12),
            if (email.isNotEmpty)
              _buildContactInfo(Icons.email, 'البريد الإلكتروني', email),
            if (phone.isNotEmpty)
              _buildContactInfo(Icons.phone, 'رقم الهاتف', phone),
            if (joinDate != null)
              _buildContactInfo(Icons.calendar_today, 'تاريخ الانضمام',
                  _formatDate(joinDate.toDate())),
          ],
        ],
      ),
    );
  }

  Widget _buildContactInfo(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: AppColors.primary),
          const SizedBox(width: 12),
          Text(
            '$label: ',
            style: CairoTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: CairoTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCard(double totalSales, int soldCount, int clientsCount, double rating) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات الأداء',
            style: CairoTextStyles.headlineSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'إجمالي المبيعات',
                  '${totalSales.toStringAsFixed(0)} د.ك',
                  Icons.attach_money,
                  AppColors.primary,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatItem(
                  'العقارات المباعة',
                  '$soldCount',
                  Icons.home_work,
                  Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'العملاء',
                  '$clientsCount',
                  Icons.people,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatItem(
                  'متوسط التقييم',
                  rating > 0 ? rating.toStringAsFixed(1) : 'لا يوجد',
                  Icons.star,
                  Colors.amber,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: CairoTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: CairoTextStyles.bodySmall.copyWith(
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات إضافية',
            style: CairoTextStyles.headlineSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildInfoRow('التخصص', memberData!['specialty'] ?? 'غير محدد'),
          _buildInfoRow('سنوات الخبرة', '${memberData!['experienceYears'] ?? 0} سنة'),
          _buildInfoRow('الملاحظات', memberData!['notes'] ?? 'لا توجد ملاحظات'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: CairoTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: CairoTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _editMember(),
            icon: const Icon(Icons.edit),
            label: const Text('تعديل البيانات'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _showAnalytics(),
            icon: const Icon(Icons.analytics),
            label: const Text('عرض التحليلات'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: BorderSide(color: AppColors.primary),
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Helper Methods
  String _getRoleDisplayName(String role) {
    const roleNames = {
      'manager': 'مدير',
      'supervisor': 'مشرف',
      'salesAgent': 'وكيل مبيعات',
      'marketer': 'مسوق',
      'customerService': 'خدمة العملاء',
      'admin': 'مسؤول إداري',
    };
    return roleNames[role] ?? 'غير محدد';
  }

  String _getStatusDisplayName(String status) {
    const statusNames = {
      'active': 'نشط',
      'inactive': 'غير نشط',
      'onLeave': 'في إجازة',
      'suspended': 'معلق',
    };
    return statusNames[status] ?? 'غير محدد';
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'active':
        return Colors.green;
      case 'inactive':
        return Colors.grey;
      case 'onLeave':
        return Colors.orange;
      case 'suspended':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _editMember() {
    Navigator.pushNamed(context, '/edit-member', arguments: widget.memberId)
        .then((result) {
      if (result == true) {
        _loadMemberData(); // إعادة تحميل البيانات بعد التعديل
      }
    });
  }

  void _showAnalytics() {
    // يمكن إضافة صفحة تحليلات مفصلة هنا
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'صفحة التحليلات المفصلة قيد التطوير',
          style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
        ),
        backgroundColor: AppColors.primary,
      ),
    );
  }
}