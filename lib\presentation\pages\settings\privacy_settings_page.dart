import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/core/services/settings_service.dart';
import 'package:kuwait_corners/core/models/app_settings.dart';

class PrivacySettingsPage extends StatefulWidget {
  const PrivacySettingsPage({super.key});

  @override
  State<PrivacySettingsPage> createState() => _PrivacySettingsPageState();
}

class _PrivacySettingsPageState extends State<PrivacySettingsPage> {
  final SettingsService _settingsService = SettingsService();
  late PrivacySettings _settings;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    await _settingsService.initialize();
    setState(() {
      _settings = _settingsService.currentSettings.privacySettings;
      _isLoading = false;
    });
  }

  Future<void> _updateSetting(String key, dynamic value) async {
    setState(() {
      switch (key) {
        case 'locationServices':
          _settings = _settings.copyWith(locationServices: value as bool);
          break;
        case 'saveSearchHistory':
          _settings = _settings.copyWith(saveSearchHistory: value as bool);
          break;
        case 'shareUsageData':
          _settings = _settings.copyWith(shareUsageData: value as bool);
          break;
        case 'personalizedAds':
          _settings = _settings.copyWith(personalizedAds: value as bool);
          break;
        case 'biometricAuth':
          _settings = _settings.copyWith(biometricAuth: value as bool);
          break;
        case 'autoLock':
          _settings = _settings.copyWith(autoLock: value as bool);
          break;
        case 'lockTimeout':
          _settings = _settings.copyWith(lockTimeout: value as int);
          break;
        case 'twoFactorAuth':
          _settings = _settings.copyWith(twoFactorAuth: value as bool);
          break;
        // تم إزالة shareProfileData لأنه غير موجود في النموذج
        case 'allowDataCollection':
          _settings = _settings.copyWith(allowDataCollection: value as bool);
          break;
      }
    });

    await _settingsService.updatePrivacySettings(
      locationServices: _settings.locationServices,
      saveSearchHistory: _settings.saveSearchHistory,
      shareUsageData: _settings.shareUsageData,
      personalizedAds: _settings.personalizedAds,
      biometricAuth: _settings.biometricAuth,
      autoLock: _settings.autoLock,
      lockTimeout: _settings.lockTimeout,
      twoFactorAuth: _settings.twoFactorAuth,
      allowDataCollection: _settings.allowDataCollection,
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        title,
        style: CairoTextStyles.headlineSmall.copyWith(color: AppColors.primary),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required String key,
    required IconData icon,
    Color? iconColor,
  }) {
    return SwitchListTile(
      title: Text(title, style: CairoTextStyles.titleMedium),
      subtitle: Text(
        subtitle,
        style: CairoTextStyles.bodySmall.copyWith(color: Colors.grey.shade600),
      ),
      value: value,
      onChanged: (newValue) => _updateSetting(key, newValue),
      secondary: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: (iconColor ?? AppColors.primary).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: iconColor ?? AppColors.primary),
      ),
    );
  }

  Widget _buildListTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    Color? iconColor,
    Widget? trailing,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: (iconColor ?? AppColors.primary).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: iconColor ?? AppColors.primary),
      ),
      title: Text(title, style: CairoTextStyles.titleMedium),
      subtitle: Text(
        subtitle,
        style: CairoTextStyles.bodySmall.copyWith(color: Colors.grey.shade600),
      ),
      trailing: trailing ?? const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  void _showLockTimeoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('مهلة القفل التلقائي', style: CairoTextStyles.titleLarge),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<int>(
              title: const Text('دقيقة واحدة'),
              value: 1,
              groupValue: _settings.lockTimeout,
              onChanged: (value) {
                if (value != null) {
                  _updateSetting('lockTimeout', value);
                  Navigator.pop(context);
                }
              },
            ),
            RadioListTile<int>(
              title: const Text('5 دقائق'),
              value: 5,
              groupValue: _settings.lockTimeout,
              onChanged: (value) {
                if (value != null) {
                  _updateSetting('lockTimeout', value);
                  Navigator.pop(context);
                }
              },
            ),
            RadioListTile<int>(
              title: const Text('15 دقيقة'),
              value: 15,
              groupValue: _settings.lockTimeout,
              onChanged: (value) {
                if (value != null) {
                  _updateSetting('lockTimeout', value);
                  Navigator.pop(context);
                }
              },
            ),
            RadioListTile<int>(
              title: const Text('30 دقيقة'),
              value: 30,
              groupValue: _settings.lockTimeout,
              onChanged: (value) {
                if (value != null) {
                  _updateSetting('lockTimeout', value);
                  Navigator.pop(context);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('الخصوصية والأمان', style: CairoTextStyles.appBarTitle),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              children: [
                _buildSectionTitle('الخصوصية'),
                _buildSwitchTile(
                  title: 'خدمات الموقع',
                  subtitle: 'السماح للتطبيق بالوصول إلى موقعك',
                  value: _settings.locationServices,
                  key: 'locationServices',
                  icon: Icons.location_on,
                  iconColor: AppColors.info,
                ),
                _buildSwitchTile(
                  title: 'حفظ سجل البحث',
                  subtitle: 'حفظ عمليات البحث لتحسين التجربة',
                  value: _settings.saveSearchHistory,
                  key: 'saveSearchHistory',
                  icon: Icons.history,
                  iconColor: AppColors.secondary,
                ),
                _buildSwitchTile(
                  title: 'مشاركة بيانات الاستخدام',
                  subtitle: 'مساعدة في تحسين التطبيق',
                  value: _settings.shareUsageData,
                  key: 'shareUsageData',
                  icon: Icons.analytics,
                  iconColor: AppColors.warning,
                ),
                _buildSwitchTile(
                  title: 'الإعلانات المخصصة',
                  subtitle: 'عرض إعلانات مناسبة لاهتماماتك',
                  value: _settings.personalizedAds,
                  key: 'personalizedAds',
                  icon: Icons.ads_click,
                  iconColor: AppColors.error,
                ),

                const Divider(),

                _buildSectionTitle('الأمان'),
                _buildSwitchTile(
                  title: 'المصادقة البيومترية',
                  subtitle: 'استخدام بصمة الإصبع أو الوجه',
                  value: _settings.biometricAuth,
                  key: 'biometricAuth',
                  icon: Icons.fingerprint,
                  iconColor: AppColors.success,
                ),
                _buildSwitchTile(
                  title: 'القفل التلقائي',
                  subtitle: 'قفل التطبيق تلقائياً بعد فترة عدم نشاط',
                  value: _settings.autoLock,
                  key: 'autoLock',
                  icon: Icons.lock,
                  iconColor: AppColors.primary,
                ),
                if (_settings.autoLock)
                  _buildListTile(
                    title: 'مهلة القفل التلقائي',
                    subtitle: '${_settings.lockTimeout} دقيقة',
                    icon: Icons.timer,
                    iconColor: AppColors.warning,
                    onTap: _showLockTimeoutDialog,
                  ),
                _buildSwitchTile(
                  title: 'المصادقة الثنائية',
                  subtitle: 'طبقة حماية إضافية للحساب',
                  value: _settings.twoFactorAuth,
                  key: 'twoFactorAuth',
                  icon: Icons.security,
                  iconColor: AppColors.error,
                ),

                const Divider(),

                _buildSectionTitle('مشاركة البيانات'),
                _buildSwitchTile(
                  title: 'السماح بجمع البيانات',
                  subtitle: 'لتحسين الخدمات والميزات',
                  value: _settings.allowDataCollection,
                  key: 'allowDataCollection',
                  icon: Icons.data_usage,
                  iconColor: AppColors.secondary,
                ),

                const SizedBox(height: 20),
              ],
            ),
    );
  }
}
