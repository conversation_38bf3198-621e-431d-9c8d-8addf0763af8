{"database": {"rules": "database.rules.json"}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "hosting": {"public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/referral", "destination": "/referral.html"}, {"source": "/referral/**", "destination": "/referral.html"}]}, "functions": [{"source": "functions", "codebase": "default", "runtime": "nodejs18", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}], "emulators": {"functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "storage": {"port": 9199}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}, "flutter": {"platforms": {"android": {"default": {"projectId": "real-estate-998a9", "appId": "1:951329683889:android:b7de9803cfd70cefaa2590", "fileOutput": "android/app/google-services.json"}}, "dart": {"lib/firebase_options.dart": {"projectId": "real-estate-998a9", "configurations": {"android": "1:951329683889:android:b7de9803cfd70cefaa2590", "ios": "1:951329683889:ios:283721b99a184d2caa2590"}}}}}}