import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/constants/user_types.dart';
import 'package:kuwait_corners/presentation/pages/login_page.dart';
import 'package:kuwait_corners/presentation/pages/register_page.dart';

/// صفحة تتيح للمستخدم اختيار نوع الحساب قبل التسجيل.
/// تعرض بطاقات مصممة بشكل احترافي لكل نوع من أنواع المستخدمين.
/// عند النقر على البطاقة يتم الانتقال إلى صفحة تسجيل التفاصيل مع تحديد نوع المستخدم.
class UserTypeSelectionPage extends StatefulWidget {
  const UserTypeSelectionPage({super.key});

  @override
  State<UserTypeSelectionPage> createState() => _UserTypeSelectionPageState();
}

class _UserTypeSelectionPageState extends State<UserTypeSelectionPage>
    with SingleTickerProviderStateMixin {
  // متغيرات للتحكم بالرسوم المتحركة
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // متغير لتتبع البطاقة المحددة
  String? _selectedUserType;

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 800));

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: _animationController, curve: Curves.easeIn));

    // بدء الرسوم المتحركة
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// يبني بطاقة لنوع مستخدم معين مع أيقونة وعنوان ووصف اختياري.
  /// عند النقر، يتم الانتقال إلى صفحة تسجيل التفاصيل مع النوع المحدد.
  Widget _buildUserTypeCard({
    required BuildContext context,
    required String title,
    required String userType,
    required IconData iconData,
    String? description,
  }) {
    final bool isSelected = _selectedUserType == userType;

    return GestureDetector(
        onTap: () {
          setState(() {
            _selectedUserType = userType;
          });

          // تأخير قصير قبل الانتقال للصفحة التالية لإظهار تأثير التحديد
          Future.delayed(const Duration(milliseconds: 300), () {
            if (mounted) {
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (_) => RegisterDetailsPage(userType: userType)));
            }
          });
        },
        child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            margin: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                color: isSelected
                    ? AppColors.primary.withOpacity(0.05)
                    : Colors.white,
                border: Border.all(
                    color: isSelected ? AppColors.primary : Colors.transparent,
                    width: 2),
                boxShadow: [
                  BoxShadow(
                      color: isSelected
                          ? AppColors.primary.withOpacity(0.2)
                          : Colors.black.withOpacity(0.08),
                      blurRadius: isSelected ? 12 : 8,
                      offset: const Offset(0, 4)),
                ]),
            child: Row(children: [
              // أيقونة داخل حاوية دائرية.
              Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected
                          ? AppColors.primary.withOpacity(0.2)
                          : AppColors.primary.withOpacity(0.1)),
                  child: Icon(iconData,
                      size: 30,
                      color: isSelected
                          ? AppColors.primary
                          : AppColors.primary.withOpacity(0.7))),
              const SizedBox(width: 20),
              // العنوان والوصف.
              Expanded(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                    Text(title,
                        style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            color: isSelected
                                ? AppColors.primary
                                : AppColors.primary.withOpacity(0.8))),
                    if (description != null)
                      Padding(
                          padding: const EdgeInsets.only(top: 4.0),
                          child: Text(description,
                              style: TextStyle(
                                  fontSize: 14,
                                  color: isSelected
                                      ? Colors.black87
                                      : Colors.grey.shade600,
                                  height: 1.3))),
                  ])),
              AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected
                          ? AppColors.primary
                          : Colors.grey.shade200),
                  padding: const EdgeInsets.all(8),
                  child: Icon(
                      isSelected ? Icons.check : Icons.arrow_forward_ios,
                      size: isSelected ? 16 : 14,
                      color: isSelected ? Colors.white : Colors.grey)),
            ])));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
            title: const Text("اختر نوع الحساب"),
            centerTitle: true,
            elevation: 0,
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  Navigator.pushReplacement(context,
                      MaterialPageRoute(builder: (_) => const LoginPage()));
                })),
        // خلفية متدرجة للصفحة
        body: Container(
            width: double.infinity,
            decoration: BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                  Colors.white,
                  Colors.grey.shade50,
                  Colors.grey.shade100,
                ])),
            child: SafeArea(
                child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const SizedBox(height: 20),
                          // شعار التطبيق - بدون دائرة
                          SizedBox(
                            width: 120,
                            height: 120,
                            child: Image.asset(
                              'assets/images/logo.png',
                              fit: BoxFit.contain,
                            ),
                          ),
                          const SizedBox(height: 20),
                          // عنوان الصفحة
                          const Text("اختر نوع الحساب",
                              style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primary)),
                          const SizedBox(height: 8),
                          // وصف الصفحة
                          Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 32),
                              child: Text(
                                  "اختر نوع الحساب الذي يناسب احتياجاتك",
                                  style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey.shade600,
                                      height: 1.3),
                                  textAlign: TextAlign.center)),
                          const SizedBox(height: 30),
                          // قائمة أنواع الحسابات
                          Expanded(
                              child: SingleChildScrollView(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 8),
                                  physics: const BouncingScrollPhysics(),
                                  child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        _buildUserTypeCard(
                                            context: context,
                                            title: 'باحث عن عقار',
                                            userType: UserTypeConstants.seeker,
                                            iconData: Icons.search,
                                            description:
                                                'ابحث عن أفضل العقارات المناسبة لك.'),
                                        _buildUserTypeCard(
                                            context: context,
                                            title: 'مالك عقار',
                                            userType: UserTypeConstants.owner,
                                            iconData: Icons.home,
                                            description:
                                                'اعرض عقارك للإيجار أو البيع بسهولة.'),
                                        _buildUserTypeCard(
                                            context: context,
                                            title: 'مستثمر',
                                            userType: UserTypeConstants.agent,
                                            iconData: Icons.handshake,
                                            description:
                                                'استثمر في العقارات للإيجار.'),
                                        _buildUserTypeCard(
                                            context: context,
                                            title: 'شركة عقار',
                                            userType: UserTypeConstants.company,
                                            iconData: Icons.business,
                                            description:
                                                'سجل شركتك لإدارة العقارات بخبرة.'),
                                        const SizedBox(height: 20),
                                      ]))),
                          // زر العودة إلى صفحة تسجيل الدخول
                          Padding(
                              padding: const EdgeInsets.all(16),
                              child: TextButton.icon(
                                  onPressed: () {
                                    Navigator.pushReplacement(
                                        context,
                                        MaterialPageRoute(
                                            builder: (_) => const LoginPage()));
                                  },
                                  icon: const Icon(Icons.arrow_back, size: 18),
                                  label: const Text("العودة إلى تسجيل الدخول",
                                      style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500)),
                                  style: TextButton.styleFrom(
                                      foregroundColor: AppColors.primary))),
                        ])))));
  }
}
