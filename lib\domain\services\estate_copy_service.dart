import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../entities/estate.dart';

/// خدمة نسخ العقارات للمستثمرين
class EstateCopyService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// نسخ عقار موجود
  Future<Estate> copyEstate({
    required String originalEstateId,
    required String investorId,
    required String adType,
    required DateTime adExpiryDate,
    double? newPrice,
    String? newDescription,
    String? newTitle,
  }) async {
    try {
      // التحقق من صلاحية المستخدم
      final canCopy = await canCopyEstate(originalEstateId, investorId);
      if (!canCopy) {
        throw Exception('غير مسموح لك بنسخ هذا العقار');
      }

      // الحصول على العقار الأصلي
      final originalEstate = await getEstateById(originalEstateId);
      if (originalEstate == null) {
        throw Exception('العقار الأصلي غير موجود');
      }

      // إنشاء معرف جديد للعقار المنسوخ
      final newEstateId = _firestore.collection('estates').doc().id;

      // إنشاء العقار المنسوخ - مجاني ونشط
      final copiedEstate = originalEstate.copyWith(
        id: newEstateId,
        title: newTitle ?? originalEstate.title,
        description: newDescription ?? originalEstate.description,
        price: newPrice ?? originalEstate.price,
        ownerId: investorId,
        isCopied: true,
        originalEstateId: originalEstateId,
        copiedAt: DateTime.now(),
        isPaidAd: false, // مجاني
        adType: 'free', // نوع مجاني
        adExpiryDate: adExpiryDate,
        createdAt: DateTime.now(),
        startDate: DateTime.now(),
        endDate: adExpiryDate,
        isFeatured: false, // بدون مميزات مدفوعة
        pinnedOnHome: false, // بدون تثبيت مدفوع
        planType: 'free');

      // حفظ العقار المنسوخ في قاعدة البيانات
      await _firestore.collection('estates').doc(newEstateId).set({
        'id': copiedEstate.id,
        'title': copiedEstate.title,
        'description': copiedEstate.description,
        'price': copiedEstate.price,
        'location': copiedEstate.location,
        'photoUrls': copiedEstate.photoUrls,
        'isFeatured': copiedEstate.isFeatured,
        'planType': copiedEstate.planType,
        'startDate': copiedEstate.startDate != null ? Timestamp.fromDate(copiedEstate.startDate!) : null,
        'endDate': copiedEstate.endDate != null ? Timestamp.fromDate(copiedEstate.endDate!) : null,
        'createdAt': Timestamp.fromDate(copiedEstate.createdAt),
        'mainCategory': copiedEstate.mainCategory,
        'subCategory': copiedEstate.subCategory,
        'ownerId': copiedEstate.ownerId,
        'isCopied': copiedEstate.isCopied,
        'originalEstateId': copiedEstate.originalEstateId,
        'copiedAt': Timestamp.fromDate(copiedEstate.copiedAt!),
        'isPaidAd': copiedEstate.isPaidAd,
        'adType': copiedEstate.adType,
        'adExpiryDate': Timestamp.fromDate(copiedEstate.adExpiryDate!),
        'copyCount': 0,
        'numberOfRooms': copiedEstate.numberOfRooms,
        'numberOfBathrooms': copiedEstate.numberOfBathrooms,
        'area': copiedEstate.area,
        'propertyType': copiedEstate.propertyType,
        'purpose': copiedEstate.purpose,
        'isAvailable': true,
        'pinnedOnHome': copiedEstate.pinnedOnHome,
        // حالة الإعلان المنسوخ - مجاني ونشط
        'isPaid': true, // مجاني - لا يحتاج دفع
        'isPaymentVerified': true, // مفعل تلقائياً
        'isActive': true, // نشط فوراً
        'isVerified': true,
        'paymentStatus': 'free', // مجاني
      });

      // تحديث عداد النسخ في العقار الأصلي
      await _updateOriginalEstateCopyCount(originalEstateId);

      // إضافة سجل في مجموعة العقارات المنسوخة
      await _firestore.collection('copied_estates').doc(newEstateId).set({
        'copiedEstateId': newEstateId,
        'originalEstateId': originalEstateId,
        'investorId': investorId,
        'copiedAt': Timestamp.fromDate(DateTime.now()),
        'adType': adType,
        'adExpiryDate': Timestamp.fromDate(adExpiryDate),
        'isActive': true,
      });

      return copiedEstate;
    } catch (e) {
      throw Exception('خطأ في نسخ العقار: $e');
    }
  }

  /// التحقق من إمكانية نسخ العقار
  Future<bool> canCopyEstate(String estateId, String userId) async {
    try {
      // التحقق من نوع المستخدم
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) return false;

      final userData = userDoc.data()!;
      final userType = userData['type'] as int?;

      // فقط المستثمرين (الوكلاء) يمكنهم النسخ
      if (userType != 1) return false; // 1 = agent/investor

      // التحقق من وجود العقار
      final estateDoc = await _firestore.collection('estates').doc(estateId).get();
      if (!estateDoc.exists) return false;

      final estateData = estateDoc.data()!;

      // لا يمكن نسخ العقارات المنسوخة
      if (estateData['isCopied'] == true) return false;

      // التحقق من نوع العقار - فقط العقارات للإيجار يمكن نسخها
      final mainCategory = estateData['mainCategory'] as String?;
      if (mainCategory != 'عقار للايجار') return false;

      // لا يمكن نسخ العقارات الخاصة بنفس المستخدم
      if (estateData['ownerId'] == userId) return false;

      // التحقق من عدم نسخ نفس العقار مسبقاً من نفس المستثمر
      final existingCopy = await _firestore
          .collection('copied_estates')
          .where('originalEstateId', isEqualTo: estateId)
          .where('investorId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .get();

      return existingCopy.docs.isEmpty;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على العقارات المنسوخة للمستثمر
  Future<List<Estate>> getCopiedEstates(String investorId) async {
    try {
      final querySnapshot = await _firestore
          .collection('estates')
          .where('ownerId', isEqualTo: investorId)
          .where('isCopied', isEqualTo: true)
          .orderBy('copiedAt', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        return _mapToEstate(data);
      }).toList();
    } catch (e) {
      throw Exception('خطأ في جلب العقارات المنسوخة: $e');
    }
  }

  /// الحصول على العقار الأصلي
  Future<Estate?> getOriginalEstate(String copiedEstateId) async {
    try {
      final copiedEstateDoc = await _firestore
          .collection('estates')
          .doc(copiedEstateId)
          .get();

      if (!copiedEstateDoc.exists) return null;

      final copiedEstateData = copiedEstateDoc.data()!;
      final originalEstateId = copiedEstateData['originalEstateId'] as String?;

      if (originalEstateId == null) return null;

      return await getEstateById(originalEstateId);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على عقار بالمعرف
  Future<Estate?> getEstateById(String estateId) async {
    try {
      final doc = await _firestore.collection('estates').doc(estateId).get();
      if (!doc.exists) return null;

      final data = doc.data()!;
      return _mapToEstate(data);
    } catch (e) {
      return null;
    }
  }

  /// تحديث عداد النسخ في العقار الأصلي
  Future<void> _updateOriginalEstateCopyCount(String originalEstateId) async {
    try {
      await _firestore.runTransaction((transaction) async {
        final estateRef = _firestore.collection('estates').doc(originalEstateId);
        final estateDoc = await transaction.get(estateRef);

        if (estateDoc.exists) {
          final currentCount = estateDoc.data()?['copyCount'] as int? ?? 0;
          transaction.update(estateRef, {'copyCount': currentCount + 1});
        }
      });
    } catch (e) {
      // تجاهل الخطأ إذا فشل تحديث العداد
    }
  }

  /// تحويل البيانات إلى كائن Estate
  Estate _mapToEstate(Map<String, dynamic> data) {
    return Estate(
      id: data['id'] ?? '',
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      price: (data['price'] as num?)?.toDouble() ?? 0.0,
      location: data['location'] ?? '',
      photoUrls: List<String>.from(data['photoUrls'] ?? []),
      isFeatured: data['isFeatured'] ?? false,
      planType: data['planType'] ?? 'free',
      startDate: (data['startDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      endDate: (data['endDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      mainCategory: data['mainCategory'],
      subCategory: data['subCategory'],
      ownerId: data['ownerId'],
      numberOfRooms: data['numberOfRooms'],
      numberOfBathrooms: data['numberOfBathrooms'],
      area: (data['area'] as num?)?.toDouble(),
      propertyType: data['propertyType'],
      purpose: data['purpose'],
      isCopied: data['isCopied'] ?? false,
      originalEstateId: data['originalEstateId'],
      copiedAt: (data['copiedAt'] as Timestamp?)?.toDate(),
      isPaidAd: data['isPaidAd'] ?? false,
      adType: data['adType'],
      adExpiryDate: (data['adExpiryDate'] as Timestamp?)?.toDate(),
      copyCount: data['copyCount'] ?? 0,
      pinnedOnHome: data['pinnedOnHome'] ?? false);
  }

  /// حذف العقار المنسوخ
  Future<void> deleteCopiedEstate(String copiedEstateId) async {
    try {
      // حذف العقار من مجموعة العقارات
      await _firestore.collection('estates').doc(copiedEstateId).delete();

      // حذف السجل من مجموعة العقارات المنسوخة
      await _firestore.collection('copied_estates').doc(copiedEstateId).delete();
    } catch (e) {
      throw Exception('خطأ في حذف العقار المنسوخ: $e');
    }
  }

  /// الحصول على إحصائيات النسخ للمستثمر
  Future<Map<String, dynamic>> getCopyStatistics(String investorId) async {
    try {
      final copiedEstates = await getCopiedEstates(investorId);
      final activeAds = copiedEstates.where((estate) =>
        estate.adExpiryDate != null &&
        estate.adExpiryDate!.isAfter(DateTime.now())
      ).length;

      return {
        'totalCopied': copiedEstates.length,
        'activeAds': activeAds,
        'expiredAds': copiedEstates.length - activeAds,
        'totalRevenue': copiedEstates.length * 10.0, // افتراض 10 دنانير لكل إعلان
      };
    } catch (e) {
      return {
        'totalCopied': 0,
        'activeAds': 0,
        'expiredAds': 0,
        'totalRevenue': 0.0,
      };
    }
  }
}
