import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

/// فئات الإنجازات
enum AchievementCategory {
  topics,
  posts,
  interaction,
  activity,
}

/// نموذج الإنجاز
class AchievementModel {
  /// معرف الإنجاز
  final String id;

  /// اسم الإنجاز
  final String name;

  /// وصف الإنجاز
  final String description;

  /// أيقونة الإنجاز
  final String iconUrl;

  /// نوع الإنجاز
  final AchievementType type;

  /// القيمة المطلوبة لإكمال الإنجاز
  final int requiredValue;

  /// القيمة الحالية للمستخدم
  final int currentValue;

  /// ما إذا كان الإنجاز مكتملاً
  final bool isCompleted;

  /// ما إذا كان الإنجاز نادراً
  final bool isRare;

  /// النقاط المكتسبة عند إكمال الإنجاز
  final int points;

  /// فئة الإنجاز
  AchievementCategory get category {
    switch (type) {
      case AchievementType.posting:
        return AchievementCategory.posts;
      case AchievementType.social:
        return AchievementCategory.interaction;
      case AchievementType.engagement:
        return AchievementCategory.activity;
      default:
        return AchievementCategory.topics;
    }
  }

  /// القيمة المستهدفة (alias لـ requiredValue)
  num get targetValue => requiredValue;

  /// نقاط المكافأة (alias لـ points)
  num get rewardPoints => points;

  /// تاريخ إكمال الإنجاز (إذا كان مكتملاً)
  final DateTime? completedAt;

  /// تاريخ إنشاء الإنجاز
  final DateTime createdAt;

  /// لون الإنجاز
  Color get color => Colors.green.shade400;

  /// رمز الإنجاز
  IconData get icon => Icons.emoji_events;

  const AchievementModel({
    required this.id,
    required this.name,
    required this.description,
    required this.iconUrl,
    required this.type,
    required this.requiredValue,
    this.currentValue = 0,
    this.isCompleted = false,
    this.isRare = false,
    this.points = 0,
    this.completedAt,
    required this.createdAt,
  });

  /// إنشاء نموذج من خريطة البيانات
  factory AchievementModel.fromMap(Map<String, dynamic> map) {
    return AchievementModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      iconUrl: map['iconUrl'] ?? '',
      type: AchievementType.values.firstWhere(
        (e) => e.toString() == 'AchievementType.${map['type']}',
        orElse: () => AchievementType.general,
      ),
      requiredValue: map['requiredValue'] ?? 0,
      currentValue: map['currentValue'] ?? 0,
      isCompleted: map['isCompleted'] ?? false,
      isRare: map['isRare'] ?? false,
      points: map['points'] ?? 0,
      completedAt: map['completedAt'] != null
          ? (map['completedAt'] as Timestamp).toDate()
          : null,
      createdAt: map['createdAt'] != null
          ? (map['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }

  /// تحويل النموذج إلى خريطة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'iconUrl': iconUrl,
      'type': type.toString().split('.').last,
      'requiredValue': requiredValue,
      'currentValue': currentValue,
      'isCompleted': isCompleted,
      'isRare': isRare,
      'points': points,
      'completedAt': completedAt != null ? Timestamp.fromDate(completedAt!) : null,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  /// إنشاء نسخة محدثة من النموذج
  AchievementModel copyWith({
    String? id,
    String? name,
    String? description,
    String? iconUrl,
    AchievementType? type,
    int? requiredValue,
    int? currentValue,
    bool? isCompleted,
    bool? isRare,
    int? points,
    DateTime? completedAt,
    DateTime? createdAt,
  }) {
    return AchievementModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconUrl: iconUrl ?? this.iconUrl,
      type: type ?? this.type,
      requiredValue: requiredValue ?? this.requiredValue,
      currentValue: currentValue ?? this.currentValue,
      isCompleted: isCompleted ?? this.isCompleted,
      isRare: isRare ?? this.isRare,
      points: points ?? this.points,
      completedAt: completedAt ?? this.completedAt,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// حساب نسبة التقدم
  double get progressPercentage {
    if (requiredValue == 0) return 0.0;
    return (currentValue / requiredValue).clamp(0.0, 1.0);
  }

  /// التحقق من إمكانية إكمال الإنجاز
  bool get canComplete {
    return currentValue >= requiredValue && !isCompleted;
  }

  /// الحصول على الإنجازات المتاحة (قائمة افتراضية)
  static List<AchievementModel> getAvailableAchievements() {
    final now = DateTime.now();
    
    return [
      AchievementModel(
        id: 'first_post',
        name: 'أول مشاركة',
        description: 'قم بكتابة أول مشاركة لك في المنتدى',
        iconUrl: 'https://example.com/icons/first_post.png',
        type: AchievementType.posting,
        requiredValue: 1,
        points: 10,
        createdAt: now,
      ),
      AchievementModel(
        id: 'active_member',
        name: 'عضو نشط',
        description: 'اكتب 10 مشاركات في المنتدى',
        iconUrl: 'https://example.com/icons/active_member.png',
        type: AchievementType.posting,
        requiredValue: 10,
        points: 50,
        createdAt: now,
      ),
      AchievementModel(
        id: 'helpful_member',
        name: 'عضو مفيد',
        description: 'احصل على 5 إعجابات على مشاركاتك',
        iconUrl: 'https://example.com/icons/helpful_member.png',
        type: AchievementType.social,
        requiredValue: 5,
        points: 30,
        createdAt: now,
      ),
      AchievementModel(
        id: 'expert_advisor',
        name: 'مستشار خبير',
        description: 'احصل على 3 أفضل إجابات',
        iconUrl: 'https://example.com/icons/expert_advisor.png',
        type: AchievementType.expertise,
        requiredValue: 3,
        points: 100,
        isRare: true,
        createdAt: now,
      ),
      AchievementModel(
        id: 'daily_visitor',
        name: 'زائر يومي',
        description: 'قم بزيارة المنتدى لمدة 7 أيام متتالية',
        iconUrl: 'https://example.com/icons/daily_visitor.png',
        type: AchievementType.engagement,
        requiredValue: 7,
        points: 25,
        createdAt: now,
      ),
      AchievementModel(
        id: 'property_expert',
        name: 'خبير العقارات',
        description: 'أضف 5 عقارات إلى التطبيق',
        iconUrl: 'https://example.com/icons/property_expert.png',
        type: AchievementType.content,
        requiredValue: 5,
        points: 75,
        createdAt: now,
      ),
    ];
  }

  @override
  String toString() {
    return 'AchievementModel(id: $id, name: $name, isCompleted: $isCompleted, progress: ${(progressPercentage * 100).toStringAsFixed(1)}%)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AchievementModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// أنواع الإنجازات
enum AchievementType {
  /// إنجازات عامة
  general,
  
  /// إنجازات المشاركة
  posting,
  
  /// إنجازات اجتماعية
  social,
  
  /// إنجازات الخبرة
  expertise,
  
  /// إنجازات التفاعل
  engagement,
  
  /// إنجازات المحتوى
  content,
  
  /// إنجازات خاصة
  special,
}

/// امتدادات لنوع الإنجاز
extension AchievementTypeExtension on AchievementType {
  /// الحصول على اسم النوع باللغة العربية
  String get displayName {
    switch (this) {
      case AchievementType.general:
        return 'عام';
      case AchievementType.posting:
        return 'المشاركات';
      case AchievementType.social:
        return 'اجتماعي';
      case AchievementType.expertise:
        return 'الخبرة';
      case AchievementType.engagement:
        return 'التفاعل';
      case AchievementType.content:
        return 'المحتوى';
      case AchievementType.special:
        return 'خاص';
    }
  }

  /// الحصول على أيقونة النوع
  String get icon {
    switch (this) {
      case AchievementType.general:
        return '🏆';
      case AchievementType.posting:
        return '✍️';
      case AchievementType.social:
        return '👥';
      case AchievementType.expertise:
        return '🎓';
      case AchievementType.engagement:
        return '🔥';
      case AchievementType.content:
        return '📝';
      case AchievementType.special:
        return '⭐';
    }
  }
}
