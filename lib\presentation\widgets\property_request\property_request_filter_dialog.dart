// lib/presentation/widgets/property_request/property_request_filter_dialog.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/theme/app_colors.dart';
import '../../../data/kuwait_locations.dart';

/// حوار فلترة طلبات العقارات
class PropertyRequestFilterDialog extends StatefulWidget {
  /// نوع العقار المحدد
  final String? selectedPropertyType;

  /// المنطقة المحددة
  final String? selectedLocation;

  /// نطاق السعر
  final RangeValues? priceRange;

  /// الحد الأدنى لعدد الغرف
  final int? minRooms;

  /// الحد الأدنى لعدد الحمامات
  final int? minBathrooms;

  /// الحد الأدنى للمساحة
  final double? minArea;

  /// هل يحتوي على تكييف مركزي
  final bool? hasCentralAC;

  /// هل يحتوي على غرفة خادمة
  final bool? hasMaidRoom;

  /// هل يحتوي على مرآب
  final bool? hasGarage;

  /// هل يحتوي على مسبح
  final bool? hasSwimmingPool;

  /// هل يحتوي على مصعد
  final bool? hasElevator;

  /// هل مفروش بالكامل
  final bool? isFullyFurnished;

  /// ترتيب حسب
  final String sortBy;

  /// ترتيب تنازلي
  final bool descending;

  /// دالة عند تطبيق الفلاتر
  final Function(
    String? propertyType,
    String? location,
    RangeValues? priceRange,
    int? minRooms,
    int? minBathrooms,
    double? minArea,
    bool? hasCentralAC,
    bool? hasMaidRoom,
    bool? hasGarage,
    bool? hasSwimmingPool,
    bool? hasElevator,
    bool? isFullyFurnished,
    String sortBy,
    bool descending) onApply;

  /// دالة عند إعادة تعيين الفلاتر
  final VoidCallback onReset;

  const PropertyRequestFilterDialog({
    super.key,
    this.selectedPropertyType,
    this.selectedLocation,
    this.priceRange,
    this.minRooms,
    this.minBathrooms,
    this.minArea,
    this.hasCentralAC,
    this.hasMaidRoom,
    this.hasGarage,
    this.hasSwimmingPool,
    this.hasElevator,
    this.isFullyFurnished,
    required this.sortBy,
    required this.descending,
    required this.onApply,
    required this.onReset,
  });

  @override
  State<PropertyRequestFilterDialog> createState() => _PropertyRequestFilterDialogState();
}

class _PropertyRequestFilterDialogState extends State<PropertyRequestFilterDialog> {
  // فلاتر أساسية
  late String? _selectedPropertyType;
  late String? _selectedAdType;
  late String? _selectedGovernorate;
  late String? _selectedLocation;
  late RangeValues? _priceRange;

  // فلاتر ذكية إضافية
  String? _selectedUrgency;
  String? _selectedBudgetRange;
  bool _hasSpecialRequests = false;
  bool _isVerifiedUser = false;
  bool _hasContactInfo = false;

  // فلاتر زمنية
  DateTime? _dateFrom;
  DateTime? _dateTo;
  String? _selectedTimeRange;
  late TextEditingController _minRoomsController;
  late TextEditingController _minBathroomsController;
  late TextEditingController _minAreaController;
  late bool? _hasCentralAC;
  late bool? _hasMaidRoom;
  late bool? _hasGarage;
  late bool? _hasSwimmingPool;
  late bool? _hasElevator;
  late bool? _isFullyFurnished;
  late String _sortBy;
  late bool _descending;

  @override
  void initState() {
    super.initState();

    // تهيئة الفلاتر الأساسية
    _selectedPropertyType = widget.selectedPropertyType;
    _selectedLocation = widget.selectedLocation;
    _priceRange = widget.priceRange ?? const RangeValues(50, 2000);

    // تهيئة الفلاتر الذكية
    _selectedAdType = null;
    _selectedGovernorate = null;
    _selectedUrgency = null;
    _selectedBudgetRange = null;
    _selectedTimeRange = 'الكل';

    // تهيئة الفلاتر المنطقية
    _hasSpecialRequests = false;
    _isVerifiedUser = false;
    _hasContactInfo = false;
    _minRoomsController = TextEditingController(
      text: widget.minRooms?.toString() ?? '');
    _minBathroomsController = TextEditingController(
      text: widget.minBathrooms?.toString() ?? '');
    _minAreaController = TextEditingController(
      text: widget.minArea?.toString() ?? '');
    _hasCentralAC = widget.hasCentralAC;
    _hasMaidRoom = widget.hasMaidRoom;
    _hasGarage = widget.hasGarage;
    _hasSwimmingPool = widget.hasSwimmingPool;
    _hasElevator = widget.hasElevator;
    _isFullyFurnished = widget.isFullyFurnished;
    _sortBy = widget.sortBy;
    _descending = widget.descending;
  }

  @override
  void dispose() {
    _minRoomsController.dispose();
    _minBathroomsController.dispose();
    _minAreaController.dispose();
    super.dispose();
  }

  /// تطبيق الفلاتر
  void _applyFilters() {
    widget.onApply(
      _selectedPropertyType,
      _selectedLocation,
      _priceRange,
      _minRoomsController.text.isNotEmpty
          ? int.tryParse(_minRoomsController.text)
          : null,
      _minBathroomsController.text.isNotEmpty
          ? int.tryParse(_minBathroomsController.text)
          : null,
      _minAreaController.text.isNotEmpty
          ? double.tryParse(_minAreaController.text)
          : null,
      _hasCentralAC,
      _hasMaidRoom,
      _hasGarage,
      _hasSwimmingPool,
      _hasElevator,
      _isFullyFurnished,
      _sortBy,
      _descending);

    Navigator.pop(context);
  }

  /// إعادة تعيين الفلاتر
  void _resetFilters() {
    widget.onReset();
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(0, 5)),
          ]),
        child: Column(
          children: [
            // رأس الحوار
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.primaryOrange,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20))),
              child: Row(
                children: [
                  const Icon(
                    Icons.tune_rounded,
                    color: Colors.white,
                    size: 24),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'فلتر الطلبات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white))),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.pop(context)),
                ])),

            // محتوى الفلتر الذكي
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                physics: const BouncingScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // فلاتر سريعة ذكية
                    _buildQuickSmartFilters(),
                    const SizedBox(height: 24),

                    // نوع العقار والإعلان
                    _buildPropertyAndAdTypeSection(),
                    const SizedBox(height: 24),

                    // الموقع الذكي
                    _buildSmartLocationSection(),
                    const SizedBox(height: 24),

                    // نطاق السعر الذكي
                    _buildSmartPriceSection(),
                    const SizedBox(height: 24),

                    // الفلاتر الزمنية
                    _buildTimeFiltersSection(),
                    const SizedBox(height: 24),

                    // فلاتر الأولوية والجودة
                    _buildQualityFiltersSection(),
                    const SizedBox(height: 24),

                    // تفاصيل العقار
                    _buildPropertyDetailsSection(),
                    const SizedBox(height: 24),

                    // الترتيب الذكي
                    _buildSmartSortingSection(),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),

            // أزرار الحوار
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20))),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _resetFilters,
                      child: const Text('إعادة تعيين'))),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _applyFilters,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryOrange),
                      child: const Text(
                        'تطبيق',
                        style: TextStyle(color: Colors.white)))),
                ])),
          ])),
    );
  }

  /// بناء الفلاتر السريعة الذكية
  Widget _buildQuickSmartFilters() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryOrange.withValues(alpha: 0.1),
            AppColors.primaryOrange.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.primaryOrange.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [AppColors.primaryOrange, AppColors.primaryOrange.withValues(alpha: 0.8)],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.flash_on_rounded, color: Colors.white, size: 20),
              ),
              const SizedBox(width: 12),
              Text(
                'فلاتر ذكية سريعة',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الصف الأول من الفلاتر
          Row(
            children: [
              Expanded(child: _buildQuickFilterChip('طلبات عاجلة', Icons.priority_high, () {
                setState(() => _selectedUrgency = 'عاجل');
              })),
              const SizedBox(width: 12),
              Expanded(child: _buildQuickFilterChip('مستخدمين موثقين', Icons.verified_user, () {
                setState(() => _isVerifiedUser = true);
              })),
            ],
          ),
          const SizedBox(height: 12),

          // الصف الثاني من الفلاتر
          Row(
            children: [
              Expanded(child: _buildQuickFilterChip('مع معلومات التواصل', Icons.contact_phone, () {
                setState(() => _hasContactInfo = true);
              })),
              const SizedBox(width: 12),
              Expanded(child: _buildQuickFilterChip('طلبات مميزة', Icons.star, () {
                setState(() => _hasSpecialRequests = true);
              })),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء رقاقة فلتر سريع
  Widget _buildQuickFilterChip(String label, IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.white,
              AppColors.primaryOrange.withValues(alpha: 0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.primaryOrange.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryOrange.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: AppColors.primaryOrange, size: 16),
            const SizedBox(width: 6),
            Expanded(
              child: Text(
                label,
                style: GoogleFonts.cairo(
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryOrange,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم نوع العقار والإعلان
  Widget _buildPropertyAndAdTypeSection() {
    return _buildSection(
      title: 'نوع العقار والإعلان',
      icon: Icons.home_work_rounded,
      child: Column(
        children: [
          // نوع العقار
          _buildSectionSubtitle('نوع العقار'),
          const SizedBox(height: 8),
          _buildDropdown(
            value: _selectedPropertyType,
            items: ['شقة', 'فيلا', 'بيت شعبي', 'مكتب', 'محل تجاري', 'مستودع', 'أرض سكنية', 'أرض تجارية', 'أرض زراعية'],
            hint: 'اختر نوع العقار',
            onChanged: (value) => setState(() => _selectedPropertyType = value),
          ),
          const SizedBox(height: 16),

          // نوع الإعلان
          _buildSectionSubtitle('نوع الطلب'),
          const SizedBox(height: 8),
          _buildDropdown(
            value: _selectedAdType,
            items: ['للإيجار', 'للبيع', 'للاستثمار', 'للمشاركة'],
            hint: 'اختر نوع الطلب',
            onChanged: (value) => setState(() => _selectedAdType = value),
          ),
        ],
      ),
    );
  }

  /// بناء قسم الموقع الذكي
  Widget _buildSmartLocationSection() {
    return _buildSection(
      title: 'الموقع الذكي',
      icon: Icons.location_on_rounded,
      child: Column(
        children: [
          // المحافظة
          _buildSectionSubtitle('المحافظة'),
          const SizedBox(height: 8),
          _buildDropdown(
            value: _selectedGovernorate,
            items: KuwaitLocations.governorates,
            hint: 'اختر المحافظة',
            onChanged: (value) {
              setState(() {
                _selectedGovernorate = value;
                _selectedLocation = null; // إعادة تعيين المنطقة
              });
            },
          ),

          // المنطقة (تظهر فقط عند اختيار محافظة)
          if (_selectedGovernorate != null) ...[
            const SizedBox(height: 16),
            _buildSectionSubtitle('المنطقة'),
            const SizedBox(height: 8),
            _buildDropdown(
              value: _selectedLocation,
              items: KuwaitLocations.getAreasByGovernorate(_selectedGovernorate!),
              hint: 'اختر المنطقة',
              onChanged: (value) => setState(() => _selectedLocation = value),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء قسم مع تصميم موحد
  Widget _buildSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.primaryOrange.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryOrange.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [AppColors.primaryOrange, AppColors.primaryOrange.withValues(alpha: 0.8)],
                  ),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(icon, color: Colors.white, size: 18),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  /// بناء عنوان فرعي
  Widget _buildSectionSubtitle(String title) {
    return Text(
      title,
      style: GoogleFonts.cairo(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: AppColors.textPrimary,
      ),
    );
  }

  /// بناء قائمة منسدلة
  Widget _buildDropdown({
    required String? value,
    required List<String> items,
    required String hint,
    required Function(String?) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primaryOrange.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: DropdownButtonFormField<String>(
        value: value,
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          hintText: hint,
          hintStyle: GoogleFonts.cairo(
            color: Colors.grey.shade500,
            fontSize: 14,
          ),
        ),
        items: items.map((item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(
              item,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: AppColors.textPrimary,
              ),
            ),
          );
        }).toList(),
        onChanged: onChanged,
        style: GoogleFonts.cairo(color: AppColors.textPrimary),
        dropdownColor: Colors.white,
        icon: Icon(
          Icons.keyboard_arrow_down_rounded,
          color: AppColors.primaryOrange,
        ),
      ),
    );
  }

  /// بناء قسم السعر الذكي
  Widget _buildSmartPriceSection() {
    return _buildSection(
      title: 'نطاق السعر الذكي',
      icon: Icons.attach_money_rounded,
      child: Column(
        children: [
          // نطاقات سعر سريعة
          _buildSectionSubtitle('نطاقات سريعة'),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildPriceRangeChip('أقل من 200 د.ك', const RangeValues(0, 200)),
              _buildPriceRangeChip('200 - 500 د.ك', const RangeValues(200, 500)),
              _buildPriceRangeChip('500 - 1000 د.ك', const RangeValues(500, 1000)),
              _buildPriceRangeChip('1000 - 2000 د.ك', const RangeValues(1000, 2000)),
              _buildPriceRangeChip('أكثر من 2000 د.ك', const RangeValues(2000, 5000)),
            ],
          ),
          const SizedBox(height: 16),

          // شريط السعر المخصص
          _buildSectionSubtitle('نطاق مخصص'),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'من: ${(_priceRange?.start ?? 50).toInt()} د.ك',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryOrange,
                ),
              ),
              Text(
                'إلى: ${(_priceRange?.end ?? 2000).toInt()} د.ك',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryOrange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          RangeSlider(
            values: _priceRange ?? const RangeValues(50, 2000),
            min: 0,
            max: 5000,
            divisions: 100,
            activeColor: AppColors.primaryOrange,
            inactiveColor: AppColors.primaryOrange.withValues(alpha: 0.2),
            labels: RangeLabels(
              '${(_priceRange?.start ?? 50).toInt()}',
              '${(_priceRange?.end ?? 2000).toInt()}',
            ),
            onChanged: (values) {
              setState(() {
                _priceRange = values;
              });
            },
          ),
        ],
      ),
    );
  }

  /// بناء رقاقة نطاق السعر
  Widget _buildPriceRangeChip(String label, RangeValues range) {
    final isSelected = _priceRange?.start == range.start && _priceRange?.end == range.end;

    return InkWell(
      onTap: () {
        setState(() {
          _priceRange = range;
        });
      },
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [AppColors.primaryOrange, AppColors.primaryOrange.withValues(alpha: 0.8)],
                )
              : null,
          color: isSelected ? null : Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppColors.primaryOrange : AppColors.primaryOrange.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected ? Colors.white : AppColors.primaryOrange,
          ),
        ),
      ),
    );
  }

  /// بناء قسم الفلاتر الزمنية
  Widget _buildTimeFiltersSection() {
    return _buildSection(
      title: 'الفلاتر الزمنية',
      icon: Icons.schedule_rounded,
      child: Column(
        children: [
          _buildSectionSubtitle('فترة زمنية سريعة'),
          const SizedBox(height: 8),
          _buildDropdown(
            value: _selectedTimeRange,
            items: ['الكل', 'اليوم', 'أمس', 'آخر 3 أيام', 'آخر أسبوع', 'آخر شهر', 'آخر 3 شهور'],
            hint: 'اختر الفترة الزمنية',
            onChanged: (value) => setState(() => _selectedTimeRange = value),
          ),
          const SizedBox(height: 16),

          // تواريخ مخصصة
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionSubtitle('من تاريخ'),
                    const SizedBox(height: 8),
                    _buildDatePicker(
                      date: _dateFrom,
                      hint: 'اختر التاريخ',
                      onDateSelected: (date) => setState(() => _dateFrom = date),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionSubtitle('إلى تاريخ'),
                    const SizedBox(height: 8),
                    _buildDatePicker(
                      date: _dateTo,
                      hint: 'اختر التاريخ',
                      onDateSelected: (date) => setState(() => _dateTo = date),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء منتقي التاريخ
  Widget _buildDatePicker({
    required DateTime? date,
    required String hint,
    required Function(DateTime) onDateSelected,
  }) {
    return InkWell(
      onTap: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: date ?? DateTime.now(),
          firstDate: DateTime.now().subtract(const Duration(days: 365)),
          lastDate: DateTime.now(),
        );
        if (selectedDate != null) {
          onDateSelected(selectedDate);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.primaryOrange.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today,
              color: AppColors.primaryOrange,
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                date != null
                    ? '${date.day}/${date.month}/${date.year}'
                    : hint,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: date != null ? AppColors.textPrimary : Colors.grey.shade500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم فلاتر الجودة والأولوية
  Widget _buildQualityFiltersSection() {
    return _buildSection(
      title: 'فلاتر الجودة والأولوية',
      icon: Icons.star_rounded,
      child: Column(
        children: [
          // مستوى الأولوية
          _buildSectionSubtitle('مستوى الأولوية'),
          const SizedBox(height: 8),
          _buildDropdown(
            value: _selectedUrgency,
            items: ['الكل', 'عادي', 'مهم', 'عاجل', 'عاجل جداً'],
            hint: 'اختر مستوى الأولوية',
            onChanged: (value) => setState(() => _selectedUrgency = value),
          ),
          const SizedBox(height: 16),

          // فلاتر الجودة
          _buildSectionSubtitle('معايير الجودة'),
          const SizedBox(height: 12),
          _buildQualityCheckboxes(),
        ],
      ),
    );
  }

  /// بناء مربعات اختيار الجودة
  Widget _buildQualityCheckboxes() {
    return Column(
      children: [
        _buildCheckboxTile(
          title: 'مستخدمين موثقين فقط',
          subtitle: 'إظهار طلبات المستخدمين الموثقين',
          value: _isVerifiedUser,
          onChanged: (value) => setState(() => _isVerifiedUser = value ?? false),
          icon: Icons.verified_user,
        ),
        const SizedBox(height: 8),
        _buildCheckboxTile(
          title: 'مع معلومات التواصل',
          subtitle: 'طلبات تحتوي على رقم هاتف أو إيميل',
          value: _hasContactInfo,
          onChanged: (value) => setState(() => _hasContactInfo = value ?? false),
          icon: Icons.contact_phone,
        ),
        const SizedBox(height: 8),
        _buildCheckboxTile(
          title: 'طلبات مميزة',
          subtitle: 'طلبات تحتوي على متطلبات خاصة',
          value: _hasSpecialRequests,
          onChanged: (value) => setState(() => _hasSpecialRequests = value ?? false),
          icon: Icons.star,
        ),
      ],
    );
  }

  /// بناء مربع اختيار مخصص
  Widget _buildCheckboxTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool?) onChanged,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: value ? AppColors.primaryOrange.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: value ? AppColors.primaryOrange : AppColors.primaryOrange.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: value ? AppColors.primaryOrange : Colors.grey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: value ? Colors.white : Colors.grey.shade600,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: value ? AppColors.primaryOrange : AppColors.textPrimary,
                  ),
                ),
                Text(
                  subtitle,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Checkbox(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primaryOrange,
          ),
        ],
      ),
    );
  }

  /// بناء قسم تفاصيل العقار
  Widget _buildPropertyDetailsSection() {
    return _buildSection(
      title: 'تفاصيل العقار المطلوب',
      icon: Icons.home_rounded,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionSubtitle('عدد الغرف'),
                    const SizedBox(height: 8),
                    _buildNumberField(
                      controller: _minRoomsController,
                      hint: 'أي عدد',
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionSubtitle('عدد الحمامات'),
                    const SizedBox(height: 8),
                    _buildNumberField(
                      controller: _minBathroomsController,
                      hint: 'أي عدد',
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // المساحة
          _buildSectionSubtitle('المساحة (متر مربع)'),
          const SizedBox(height: 8),
          _buildNumberField(
            controller: _minAreaController,
            hint: 'أي مساحة',
          ),
        ],
      ),
    );
  }

  /// بناء حقل رقمي
  Widget _buildNumberField({
    required TextEditingController controller,
    required String hint,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primaryOrange.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: TextField(
        controller: controller,
        keyboardType: TextInputType.number,
        style: GoogleFonts.cairo(fontSize: 14),
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          hintText: hint,
          hintStyle: GoogleFonts.cairo(
            color: Colors.grey.shade500,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  /// بناء قسم الترتيب الذكي
  Widget _buildSmartSortingSection() {
    return _buildSection(
      title: 'الترتيب الذكي',
      icon: Icons.sort_rounded,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionSubtitle('ترتيب حسب'),
                    const SizedBox(height: 8),
                    _buildDropdown(
                      value: _sortBy == 'createdAt' ? 'تاريخ النشر' : _sortBy,
                      items: [
                        'تاريخ النشر',
                        'عدد المشاهدات',
                        'عدد العروض',
                        'الأولوية',
                        'المستخدمين الموثقين',
                      ],
                      hint: 'اختر طريقة الترتيب',
                      onChanged: (value) {
                        setState(() {
                          switch (value) {
                            case 'تاريخ النشر':
                              _sortBy = 'createdAt';
                              break;
                            case 'عدد المشاهدات':
                              _sortBy = 'viewsCount';
                              break;
                            case 'عدد العروض':
                              _sortBy = 'offersCount';
                              break;
                            case 'الأولوية':
                              _sortBy = 'priority';
                              break;
                            case 'المستخدمين الموثقين':
                              _sortBy = 'verifiedUsers';
                              break;
                            default:
                              _sortBy = 'createdAt';
                          }
                        });
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionSubtitle('اتجاه الترتيب'),
                    const SizedBox(height: 8),
                    _buildDropdown(
                      value: _descending ? 'تنازلي' : 'تصاعدي',
                      items: ['تنازلي', 'تصاعدي'],
                      hint: 'اختر الاتجاه',
                      onChanged: (value) => setState(() => _descending = value == 'تنازلي'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
