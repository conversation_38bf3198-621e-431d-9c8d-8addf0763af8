import 'package:flutter/material.dart';

/// نوع الإشعار
enum NotificationType {
  /// إشعار عقار جديد
  newEstate,
  
  /// إشعار رسالة جديدة
  newMessage,
  
  /// إشعار تحقق من الحساب
  accountVerification,
  
  /// إشعار آخر
  other,
}

/// نموذج الإشعار البسيط
class NotificationModel {
  /// معرف الإشعار
  final String id;
  
  /// عنوان الإشعار
  final String title;
  
  /// نص الإشعار
  final String body;
  
  /// نوع الإشعار
  final NotificationType type;
  
  /// بيانات إضافية للإشعار
  final Map<String, dynamic> data;
  
  /// تاريخ الإشعار
  final DateTime timestamp;
  
  /// ما إذا كان الإشعار مقروء
  bool isRead;

  /// إنشاء نموذج إشعار
  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.data,
    required this.timestamp,
    this.isRead = false,
  });

  /// الحصول على أيقونة الإشعار
  IconData getNotificationIcon() {
    switch (type) {
      case NotificationType.newEstate:
        return Icons.home;
      case NotificationType.newMessage:
        return Icons.message;
      case NotificationType.accountVerification:
        return Icons.verified_user;
      case NotificationType.other:
        return Icons.notifications;
    }
  }

  /// الحصول على لون الإشعار
  Color getNotificationColor() {
    switch (type) {
      case NotificationType.newEstate:
        return Colors.blue;
      case NotificationType.newMessage:
        return Colors.green;
      case NotificationType.accountVerification:
        return Colors.purple;
      case NotificationType.other:
        return Colors.grey;
    }
  }
}

/// نموذج الإشعار في الوقت الحقيقي
class RealtimeNotificationModel {
  /// معرف الإشعار
  final String id;
  
  /// معرف المستخدم المرسل إليه
  final String userId;
  
  /// عنوان الإشعار
  final String title;
  
  /// نص الإشعار
  final String body;
  
  /// نوع الإشعار
  final String type;
  
  /// بيانات إضافية للإشعار
  final Map<String, dynamic> data;
  
  /// تاريخ الإشعار
  final DateTime timestamp;
  
  /// ما إذا كان الإشعار مقروء
  bool isRead;

  /// إنشاء نموذج إشعار في الوقت الحقيقي
  RealtimeNotificationModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.body,
    required this.type,
    required this.data,
    required this.timestamp,
    this.isRead = false,
  });

  /// الحصول على أيقونة الإشعار
  IconData getNotificationIcon() {
    switch (type) {
      case 'newEstate':
        return Icons.home;
      case 'newMessage':
        return Icons.message;
      case 'accountVerification':
        return Icons.verified_user;
      default:
        return Icons.notifications;
    }
  }

  /// الحصول على لون الإشعار
  Color getNotificationColor() {
    switch (type) {
      case 'newEstate':
        return Colors.blue;
      case 'newMessage':
        return Colors.green;
      case 'accountVerification':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }
}
