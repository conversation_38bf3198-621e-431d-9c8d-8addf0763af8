import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/core/services/notification_service.dart';
import 'package:kuwait_corners/domain/models/notification_model.dart' as domain;
import 'package:syncfusion_flutter_xlsio/xlsio.dart' as xlsio;
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';
import 'package:fl_chart/fl_chart.dart';
import 'package:kuwait_corners/presentation/widgets/loading_widget.dart';
import 'package:intl/intl.dart';
import 'package:kuwait_corners/domain/entities/client_interaction.dart';

/// صفحة إدارة العملاء للوكلاء والشركات العقارية
class ClientsManagementPage extends StatefulWidget {
  const ClientsManagementPage({super.key});

  @override
  State<ClientsManagementPage> createState() => _ClientsManagementPageState();
}

class _ClientsManagementPageState extends State<ClientsManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'الكل';
  String _sortBy = 'الأحدث';
  bool _isLoading = false;

  final List<String> _filterOptions = [
    'الكل',
    'عملاء جدد',
    'عملاء نشطين',
    'عملاء سابقين',
    'مهتمين بالشراء',
    'مهتمين بالإيجار',
    'عملاء VIP',
    'عملاء غير نشطين',
  ];

  final List<String> _sortOptions = [
    'الأحدث',
    'الأقدم',
    'الاسم (أ-ي)',
    'الاسم (ي-أ)',
    'آخر تواصل',
    'الأكثر نشاطاً',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: Stack(
        children: [
          // خلفية بأشكال هندسية
          _buildBackgroundShapes(),

          // المحتوى الرئيسي
          Column(
            children: [
              _buildSearchAndFilters(),
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildAllClientsTab(),
                    _buildLeadsTab(),
                    _buildActiveClientsTab(),
                    _buildArchivedClientsTab(),
                  ])),
            ]),
        ]),
      floatingActionButton: _buildFloatingActionButton());
  }

  Widget _buildBackgroundShapes() {
    return Positioned.fill(
      child: Stack(
        children: [
          // أيقونة عملاء - أصغر وأكثر دقة
          Positioned(
            top: 120,
            right: -25,
            child: Opacity(
              opacity: 0.025,
              child: Icon(
                Icons.people_outline,
                size: 120,
                color: AppColors.primary))),

          // أيقونة إدارة - موضعة بشكل أفضل
          Positioned(
            top: 280,
            left: -15,
            child: Opacity(
              opacity: 0.03,
              child: Icon(
                Icons.manage_accounts_outlined,
                size: 90,
                color: AppColors.primary))),

          // أيقونة تحليلات - أصغر وأكثر توازناً
          Positioned(
            bottom: 180,
            right: -20,
            child: Opacity(
              opacity: 0.025,
              child: Icon(
                Icons.analytics_outlined,
                size: 80,
                color: AppColors.primary))),

          // أيقونة اتصال - موضعة بدقة
          Positioned(
            bottom: 320,
            left: -10,
            child: Opacity(
              opacity: 0.03,
              child: Icon(
                Icons.contact_phone_outlined,
                size: 70,
                color: AppColors.primary))),

          // أيقونة بحث - إضافة جديدة
          Positioned(
            top: 450,
            right: -15,
            child: Opacity(
              opacity: 0.02,
              child: Icon(
                Icons.search,
                size: 60,
                color: AppColors.primary))),

          // دوائر هندسية - أصغر وأكثر توزيعاً
          Positioned(
            top: 80,
            left: 40,
            child: Container(
              width: 35,
              height: 35,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.withValues(alpha: 0.015)))),

          Positioned(
            top: 200,
            right: 60,
            child: Container(
              width: 25,
              height: 25,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.withValues(alpha: 0.02)))),

          Positioned(
            bottom: 120,
            right: 90,
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.withValues(alpha: 0.018)))),

          Positioned(
            bottom: 250,
            left: 70,
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.withValues(alpha: 0.025)))),

          // مربعات مدورة - أصغر وأكثر انتشاراً
          Positioned(
            top: 350,
            right: 30,
            child: Container(
              width: 18,
              height: 18,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: AppColors.primary.withValues(alpha: 0.015)))),

          Positioned(
            top: 180,
            left: 25,
            child: Container(
              width: 22,
              height: 22,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                color: AppColors.primary.withValues(alpha: 0.02)))),

          Positioned(
            bottom: 160,
            left: 40,
            child: Container(
              width: 28,
              height: 28,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: AppColors.primary.withValues(alpha: 0.018)))),

          Positioned(
            bottom: 400,
            right: 50,
            child: Container(
              width: 15,
              height: 15,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(3),
                color: AppColors.primary.withValues(alpha: 0.022)))),
        ]));
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'إدارة العملاء',
        style: CairoTextStyles.appBarTitle),
      backgroundColor: AppColors.primary,
      elevation: 0,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          icon: const Icon(Icons.analytics),
          onPressed: _showClientAnalytics),
        IconButton(
          icon: const Icon(Icons.download),
          onPressed: _exportClientData),
      ]);
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1)),
        ]),
      child: Column(
        children: [
          // شريط البحث مع خيارات متقدمة
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  style: CairoTextStyles.bodyLarge,
                  decoration: InputDecoration(
                    hintText: 'البحث بالاسم، الهاتف، أو البريد الإلكتروني...',
                    hintStyle: CairoTextStyles.hint,
                    prefixIcon: Icon(Icons.search, color: AppColors.primary),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            })
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!)),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColors.primary, width: 2)),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!)),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12)),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  })),
              const SizedBox(width: 12),
              // زر الفلاتر المتقدمة
              Container(
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(12)),
                child: IconButton(
                  icon: const Icon(Icons.tune, color: Colors.white),
                  onPressed: _showAdvancedFilters,
                  tooltip: 'فلاتر متقدمة')),
            ]),
          const SizedBox(height: 16),
          // الفلاتر السريعة
          Row(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: _filterOptions.map((filter) {
                      final isSelected = _selectedFilter == filter;
                      return Padding(
                        padding: const EdgeInsets.only(left: 8),
                        child: FilterChip(
                          label: Text(
                            filter,
                            style: CairoTextStyles.labelMedium.copyWith(
                              color: isSelected ? Colors.white : Colors.grey[700],
                              fontSize: 12,
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal)),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              _selectedFilter = filter;
                            });
                          },
                          backgroundColor: Colors.grey[100],
                          selectedColor: AppColors.primary,
                          checkmarkColor: Colors.white,
                          elevation: isSelected ? 2 : 0,
                          pressElevation: 4));
                    }).toList()))),
              const SizedBox(width: 8),
              // زر الترتيب
              PopupMenuButton<String>(
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8)),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.sort, size: 16, color: Colors.grey[700]),
                      const SizedBox(width: 4),
                      Text(
                        'ترتيب',
                        style: CairoTextStyles.labelMedium.copyWith(
                          fontSize: 12,
                          color: Colors.grey[700])),
                    ])),
                onSelected: (value) {
                  setState(() {
                    _sortBy = value;
                  });
                },
                itemBuilder: (context) => _sortOptions.map((option) {
                  return PopupMenuItem<String>(
                    value: option,
                    child: Row(
                      children: [
                        if (_sortBy == option)
                          Icon(Icons.check, size: 16, color: AppColors.primary),
                        if (_sortBy == option) const SizedBox(width: 8),
                        Text(
                          option,
                          style: CairoTextStyles.bodyMedium.copyWith(
                            color: _sortBy == option ? AppColors.primary : Colors.black87,
                            fontWeight: _sortBy == option ? FontWeight.w600 : FontWeight.normal)),
                      ]));
                }).toList()),
            ]),
          // إحصائيات سريعة
          if (_searchQuery.isNotEmpty || _selectedFilter != 'الكل')
            Container(
              margin: const EdgeInsets.only(top: 12),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.primary.withValues(alpha: 0.3))),
              child: Row(
                children: [
                  Icon(Icons.info_outline, size: 16, color: AppColors.primary),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getFilterSummary(),
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500))),
                  TextButton(
                    onPressed: _clearAllFilters,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      minimumSize: Size.zero),
                    child: Text(
                      'مسح الكل',
                      style: CairoTextStyles.labelSmall.copyWith(
                        fontSize: 11,
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600))),
                ])),
        ]));
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: AppColors.primary,
        isScrollable: true,
        labelStyle: CairoTextStyles.bodyMedium.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 14),
        unselectedLabelStyle: CairoTextStyles.bodyMedium.copyWith(
          fontWeight: FontWeight.normal,
          fontSize: 13),
        tabs: const [
          Tab(text: 'الكل'),
          Tab(text: 'محتملين'),
          Tab(text: 'نشطين'),
          Tab(text: 'أرشيف'),
        ]));
  }

  Widget _buildAllClientsTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getAllClientsStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل العملاء');
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyState('لا يوجد عملاء حالياً');
        }

        final clients = snapshot.data!.docs;
        final filteredClients = _filterClients(clients);

        return RefreshIndicator(
          onRefresh: () async {
            setState(() {});
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredClients.length,
            itemBuilder: (context, index) {
              final clientDoc = filteredClients[index];
              final clientData = clientDoc.data() as Map<String, dynamic>;
              return _buildClientCard(clientData, clientDoc.id);
            }));
      });
  }

  Widget _buildLeadsTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getLeadsStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل العملاء المحتملين');
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyState('لا يوجد عملاء محتملين');
        }

        final leads = snapshot.data!.docs;

        return RefreshIndicator(
          onRefresh: () async {
            setState(() {});
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: leads.length,
            itemBuilder: (context, index) {
              final leadDoc = leads[index];
              final leadData = leadDoc.data() as Map<String, dynamic>;
              return _buildLeadCard(leadData, leadDoc.id);
            }));
      });
  }

  Widget _buildActiveClientsTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getActiveClientsStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل العملاء النشطين');
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyState('لا يوجد عملاء نشطين');
        }

        final activeClients = snapshot.data!.docs;

        return RefreshIndicator(
          onRefresh: () async {
            setState(() {});
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: activeClients.length,
            itemBuilder: (context, index) {
              final clientDoc = activeClients[index];
              final clientData = clientDoc.data() as Map<String, dynamic>;
              return _buildActiveClientCard(clientData, clientDoc.id);
            }));
      });
  }

  Widget _buildArchivedClientsTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getArchivedClientsStream(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingWidget();
        }

        if (snapshot.hasError) {
          return _buildErrorWidget('حدث خطأ في تحميل الأرشيف');
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyState('لا يوجد عملاء في الأرشيف');
        }

        final archivedClients = snapshot.data!.docs;

        return RefreshIndicator(
          onRefresh: () async {
            setState(() {});
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: archivedClients.length,
            itemBuilder: (context, index) {
              final clientDoc = archivedClients[index];
              final clientData = clientDoc.data() as Map<String, dynamic>;
              return _buildArchivedClientCard(clientData, clientDoc.id);
            }));
      });
  }

  Widget _buildClientCard(Map<String, dynamic> clientData, String clientId) {
    final name = clientData['name'] ?? 'غير محدد';
    final phone = clientData['phone'] ?? '';
    final status = clientData['status'] ?? 'جديد';
    final interest = clientData['interest'] ?? 'غير محدد';
    final isVip = clientData['isVip'] ?? false;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: isVip
            ? Border.all(color: Colors.amber.withValues(alpha: 0.4), width: 1.5)
            : Border.all(color: Colors.grey.withValues(alpha: 0.15), width: 1)),
      child: InkWell(
        onTap: () => _viewClientDetails(clientId),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // صورة العميل
              Stack(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppColors.primary.withValues(alpha: 0.2),
                        width: 1.5)),
                    child: ClipOval(
                      child: clientData['imageUrl'] != null && clientData['imageUrl'].toString().isNotEmpty
                          ? Image.network(
                              clientData['imageUrl'],
                              width: 50,
                              height: 50,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Center(
                                  child: Text(
                                    name.isNotEmpty ? name[0].toUpperCase() : '؟',
                                    style: CairoTextStyles.headlineSmall.copyWith(
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.bold)));
                              },
                              loadingBuilder: (context, child, loadingProgress) {
                                if (loadingProgress == null) return child;
                                return Center(
                                  child: SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary))));
                              })
                          : Center(
                              child: Text(
                                name.isNotEmpty ? name[0].toUpperCase() : '؟',
                                style: CairoTextStyles.headlineSmall.copyWith(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.bold))))),
                  if (isVip)
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(3),
                        decoration: BoxDecoration(
                          color: Colors.amber,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 1.5)),
                        child: const Icon(
                          Icons.star,
                          color: Colors.white,
                          size: 10))),
                ]),
              const SizedBox(width: 16),

              // معلومات العميل
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // الاسم والحالة
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            name,
                            style: CairoTextStyles.cardTitle.copyWith(
                              fontSize: 16,
                              fontWeight: FontWeight.w600))),
                        _buildCompactStatusChip(status),
                      ]),
                    const SizedBox(height: 6),

                    // الاهتمام
                    Text(
                      interest,
                      style: CairoTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600]),
                      overflow: TextOverflow.ellipsis),

                    // رقم الهاتف (إذا كان متوفراً)
                    if (phone.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.phone, size: 14, color: Colors.grey[500]),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Text(
                              phone,
                              style: CairoTextStyles.bodySmall.copyWith(
                                color: Colors.grey[600]),
                              overflow: TextOverflow.ellipsis)),
                        ]),
                    ],
                  ])),

              // أزرار الإجراءات
              Column(
                children: [
                  // زر التفاصيل
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8)),
                    child: IconButton(
                      onPressed: () => _viewClientDetails(clientId),
                      icon: Icon(Icons.visibility,
                        size: 18,
                        color: AppColors.primary),
                      padding: EdgeInsets.zero)),
                  const SizedBox(height: 8),

                  // قائمة الخيارات
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8)),
                    child: PopupMenuButton<String>(
                      onSelected: (value) => _handleClientAction(value, clientId),
                      icon: Icon(Icons.more_vert,
                        size: 18,
                        color: Colors.grey[600]),
                      padding: EdgeInsets.zero,
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 16, color: Colors.grey[700]),
                              const SizedBox(width: 8),
                              Text('تعديل', style: CairoTextStyles.bodyMedium),
                            ])),
                        PopupMenuItem(
                          value: 'interactions',
                          child: Row(
                            children: [
                              Icon(Icons.history, size: 16, color: AppColors.primary),
                              const SizedBox(width: 8),
                              Text('التفاعلات', style: CairoTextStyles.bodyMedium),
                            ])),
                        PopupMenuItem(
                          value: 'appointment',
                          child: Row(
                            children: [
                              Icon(Icons.schedule, size: 16, color: Colors.blue),
                              const SizedBox(width: 8),
                              Text('حجز موعد', style: CairoTextStyles.bodyMedium),
                            ])),
                        if (phone.isNotEmpty)
                          PopupMenuItem(
                            value: 'call',
                            child: Row(
                              children: [
                                Icon(Icons.phone, size: 16, color: Colors.green),
                                const SizedBox(width: 8),
                                Text('اتصال', style: CairoTextStyles.bodyMedium),
                              ])),
                        PopupMenuItem(
                          value: 'archive',
                          child: Row(
                            children: [
                              Icon(Icons.archive, size: 16, color: Colors.orange),
                              const SizedBox(width: 8),
                              Text('أرشفة', style: CairoTextStyles.bodyMedium),
                            ])),
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 16, color: Colors.red),
                              const SizedBox(width: 8),
                              Text('حذف', style: CairoTextStyles.bodyMedium.copyWith(color: Colors.red)),
                            ])),
                      ])),
                ]),
            ]))));
  }

  Widget _buildLeadCard(Map<String, dynamic> leadData, String leadId) {
    final name = leadData['name'] ?? 'غير محدد';
    final source = leadData['source'] ?? 'غير محدد';
    final score = leadData['score'] ?? 0;
    final createdAt = leadData['createdAt'] as Timestamp?;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.15), width: 1)),
      child: InkWell(
        onTap: () => _viewClientDetails(leadId),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // أيقونة العميل المحتمل
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.2),
                    width: 1.5)),
                child: ClipOval(
                  child: leadData['imageUrl'] != null && leadData['imageUrl'].toString().isNotEmpty
                      ? Image.network(
                          leadData['imageUrl'],
                          width: 50,
                          height: 50,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Center(
                              child: Icon(
                                Icons.person_add,
                                color: Colors.orange,
                                size: 24));
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Center(
                              child: SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.orange))));
                          })
                      : Center(
                          child: Icon(
                            Icons.person_add,
                            color: Colors.orange,
                            size: 24)))),
              const SizedBox(width: 16),

              // معلومات العميل المحتمل
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // الاسم ونقاط التقييم
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            name,
                            style: CairoTextStyles.cardTitle.copyWith(
                              fontSize: 16,
                              fontWeight: FontWeight.w600))),
                        _buildLeadScoreChip(score),
                      ]),
                    const SizedBox(height: 6),

                    // المصدر
                    Row(
                      children: [
                        Icon(Icons.source, size: 14, color: Colors.grey[500]),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            source,
                            style: CairoTextStyles.bodySmall.copyWith(
                              color: Colors.grey[600]),
                            overflow: TextOverflow.ellipsis)),
                      ]),

                    // تاريخ الإضافة
                    if (createdAt != null) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.schedule, size: 14, color: Colors.grey[500]),
                          const SizedBox(width: 6),
                          Text(
                            _formatDate(createdAt.toDate()),
                            style: CairoTextStyles.bodySmall.copyWith(
                              color: Colors.grey[600])),
                        ]),
                    ],
                  ])),

              // زر التحويل
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: IconButton(
                  onPressed: () => _convertToClient(leadId),
                  icon: Icon(Icons.person_add,
                    size: 18,
                    color: AppColors.primary),
                  padding: EdgeInsets.zero,
                  tooltip: 'تحويل لعميل')),
            ]))));
  }

  Widget _buildActiveClientCard(Map<String, dynamic> clientData, String clientId) {
    return _buildClientCard(clientData, clientId);
  }

  Widget _buildArchivedClientCard(Map<String, dynamic> clientData, String clientId) {
    return _buildClientCard(clientData, clientId);
  }

  Widget _buildCompactStatusChip(String status) {
    Color color;
    switch (status.toLowerCase()) {
      case 'نشط':
        color = Colors.green;
        break;
      case 'محتمل':
        color = Colors.orange;
        break;
      case 'مؤرشف':
        color = Colors.grey;
        break;
      case 'جديد':
        color = Colors.blue;
        break;
      default:
        color = Colors.blue;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8)),
      child: Text(
        status,
        style: CairoTextStyles.labelSmall.copyWith(
          color: color,
          fontSize: 11,
          fontWeight: FontWeight.w500)));
  }

  Widget _buildLeadScoreChip(int score) {
    Color color;
    if (score >= 80) {
      color = Colors.green;
    } else if (score >= 60) {
      color = Colors.orange;
    } else {
      color = Colors.red;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8)),
      child: Text(
        '$score%',
        style: CairoTextStyles.labelSmall.copyWith(
          color: color,
          fontSize: 11,
          fontWeight: FontWeight.w500)));
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 80,
            color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: CairoTextStyles.headlineSmall.copyWith(
              color: Colors.grey[600])),
        ]));
  }

  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: CairoTextStyles.headlineSmall.copyWith(
              color: Colors.red[600])),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              setState(() {});
            },
            child: Text('إعادة المحاولة', style: CairoTextStyles.button)),
        ]));
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _addNewClient,
      backgroundColor: AppColors.primary,
      child: const Icon(Icons.person_add, color: Colors.white));
  }

  // Firebase Streams
  Stream<QuerySnapshot> _getAllClientsStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    return FirebaseFirestore.instance
        .collection('clients')
        .where('agentId', isEqualTo: currentUser.uid)
        .orderBy('createdAt', descending: true)
        .snapshots();
  }

  Stream<QuerySnapshot> _getLeadsStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    return FirebaseFirestore.instance
        .collection('leads')
        .where('agentId', isEqualTo: currentUser.uid)
        .orderBy('score', descending: true)
        .snapshots();
  }

  Stream<QuerySnapshot> _getActiveClientsStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    return FirebaseFirestore.instance
        .collection('clients')
        .where('agentId', isEqualTo: currentUser.uid)
        .where('status', isEqualTo: 'نشط')
        .orderBy('lastContact', descending: true)
        .snapshots();
  }

  Stream<QuerySnapshot> _getArchivedClientsStream() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return const Stream.empty();

    return FirebaseFirestore.instance
        .collection('clients')
        .where('agentId', isEqualTo: currentUser.uid)
        .where('status', isEqualTo: 'مؤرشف')
        .orderBy('archivedAt', descending: true)
        .snapshots();
  }

  List<QueryDocumentSnapshot> _filterClients(List<QueryDocumentSnapshot> clients) {
    List<QueryDocumentSnapshot> filtered = clients;

    // تطبيق فلتر الحالة
    if (_selectedFilter != 'الكل') {
      filtered = filtered.where((client) {
        final data = client.data() as Map<String, dynamic>;
        final status = data['status'] ?? '';
        final interest = data['interest'] ?? '';

        switch (_selectedFilter) {
          case 'عملاء جدد':
            return status == 'جديد';
          case 'عملاء نشطين':
            return status == 'نشط';
          case 'عملاء سابقين':
            return status == 'مؤرشف';
          case 'مهتمين بالشراء':
            return interest.contains('شراء');
          case 'مهتمين بالإيجار':
            return interest.contains('إيجار');
          default:
            return true;
        }
      }).toList();
    }

    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      final searchTerm = _searchQuery.toLowerCase();
      filtered = filtered.where((client) {
        final data = client.data() as Map<String, dynamic>;
        final name = (data['name'] ?? '').toString().toLowerCase();
        final email = (data['email'] ?? '').toString().toLowerCase();
        final phone = (data['phone'] ?? '').toString().toLowerCase();

        return name.contains(searchTerm) ||
               email.contains(searchTerm) ||
               phone.contains(searchTerm);
      }).toList();
    }

    return filtered;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  // Action Methods
  void _addNewClient() {
    // Navigate to add client page
    Navigator.pushNamed(context, '/add-client');
  }

  void _contactClient(String clientId, String phone) {
    // Implement contact functionality
    if (phone.isNotEmpty) {
      // Launch phone dialer or WhatsApp
    }
  }

  void _viewClientDetails(String clientId) {
    // Navigate to client details page
    Navigator.pushNamed(context, '/client-details', arguments: clientId);
  }

  void _handleClientAction(String action, String clientId) async {
    switch (action) {
      case 'edit':
        final result = await Navigator.pushNamed(context, '/edit-client', arguments: clientId);
        if (result == true) {
          setState(() {}); // إعادة تحميل البيانات بعد التعديل
        }
        break;
      case 'interactions':
        _showClientInteractions(clientId);
        break;
      case 'appointment':
        _scheduleAppointment(clientId);
        break;
      case 'call':
        _contactClient(clientId, '');
        break;
      case 'archive':
        _archiveClient(clientId);
        break;
      case 'delete':
        _deleteClient(clientId);
        break;
    }
  }

  void _convertToClient(String leadId) async {
    try {
      final leadDoc = await FirebaseFirestore.instance
          .collection('leads')
          .doc(leadId)
          .get();

      if (leadDoc.exists) {
        final leadData = leadDoc.data() as Map<String, dynamic>;

        // Create new client from lead data
        await FirebaseFirestore.instance.collection('clients').add({
          ...leadData,
          'status': 'جديد',
          'convertedAt': FieldValue.serverTimestamp(),
          'convertedFrom': 'lead',
        });

        // Delete the lead
        await leadDoc.reference.delete();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('تم تحويل العميل المحتمل بنجاح', style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white))));
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ في تحويل العميل', style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white))));
      }
    }
  }

  void _archiveClient(String clientId) async {
    try {
      await FirebaseFirestore.instance
          .collection('clients')
          .doc(clientId)
          .update({
        'status': 'مؤرشف',
        'archivedAt': FieldValue.serverTimestamp(),
      });

      if (mounted) {
        // عرض إشعار فوري
        NotificationService.showInAppNotification(
          context,
          title: 'تم أرشفة العميل',
          body: 'تم نقل العميل إلى الأرشيف بنجاح',
          type: domain.NotificationType.system,
          duration: const Duration(seconds: 3),
        );

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم أرشفة العميل بنجاح', style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white))));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ في أرشفة العميل', style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white))));
      }
    }
  }

  void _deleteClient(String clientId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد الحذف', style: CairoTextStyles.headlineSmall),
        content: Text('هل أنت متأكد من حذف هذا العميل؟', style: CairoTextStyles.bodyMedium),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء', style: CairoTextStyles.button.copyWith(color: Colors.grey))),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text('حذف', style: CairoTextStyles.button.copyWith(color: Colors.red))),
        ]));

    if (confirmed == true) {
      try {
        await FirebaseFirestore.instance
            .collection('clients')
            .doc(clientId)
            .delete();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('تم حذف العميل بنجاح', style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white))));
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('حدث خطأ في حذف العميل', style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white))));
        }
      }
    }
  }

  // الدوال المفقودة
  void _showAdvancedFilters() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildAdvancedFiltersSheet());
  }

  Widget _buildAdvancedFiltersSheet() {
    // متغيرات محلية للفلاتر المتقدمة
    String? selectedStatus;
    String? selectedClientType;
    DateTime? fromDate;
    DateTime? toDate;
    bool? isVipFilter;
    String? selectedInterest;

    return StatefulBuilder(
      builder: (context, setModalState) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.85,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.white, AppColors.primary.withValues(alpha: 0.02)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24)),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, -5)),
            ]),
          child: Column(
            children: [
              // Handle bar عصري
              Container(
                margin: const EdgeInsets.only(top: 12),
                width: 50,
                height: 5,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [AppColors.primary.withValues(alpha: 0.3), AppColors.primary.withValues(alpha: 0.6)]),
                  borderRadius: BorderRadius.circular(3))),

              // Header محسن
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [AppColors.primary.withValues(alpha: 0.05), Colors.transparent]),
                  border: Border(
                    bottom: BorderSide(color: AppColors.primary.withValues(alpha: 0.1)))),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)]),
                        borderRadius: BorderRadius.circular(12)),
                      child: const Icon(Icons.tune_rounded, color: Colors.white, size: 24)),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'فلاتر متقدمة',
                            style: CairoTextStyles.headlineMedium.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary)),
                          Text(
                            'اختر المعايير المطلوبة للبحث',
                            style: CairoTextStyles.bodySmall.copyWith(
                              color: Colors.grey[600])),
                        ])),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.grey.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20)),
                      child: IconButton(
                        icon: const Icon(Icons.close_rounded, color: Colors.grey),
                        onPressed: () => Navigator.pop(context))),
                  ])),

              // محتوى الفلاتر المحسن
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // حالة العميل
                      _buildAdvancedFilterSection(
                        'حالة العميل',
                        Icons.person_outline,
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: ['الكل', 'جديد', 'نشط', 'محتمل', 'مؤرشف'].map((status) {
                            final isSelected = selectedStatus == status;
                            return _buildAdvancedFilterChip(
                              status,
                              isSelected,
                              () {
                                setModalState(() {
                                  selectedStatus = isSelected ? null : status;
                                });
                              });
                          }).toList())),
                      const SizedBox(height: 24),

                      // نوع العميل
                      _buildAdvancedFilterSection(
                        'نوع العميل',
                        Icons.category_outlined,
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: ['الكل', 'مشتري', 'مستأجر', 'بائع', 'مؤجر'].map((type) {
                            final isSelected = selectedClientType == type;
                            return _buildAdvancedFilterChip(
                              type,
                              isSelected,
                              () {
                                setModalState(() {
                                  selectedClientType = isSelected ? null : type;
                                });
                              });
                          }).toList())),
                      const SizedBox(height: 24),

                      // الاهتمامات
                      _buildAdvancedFilterSection(
                        'نوع الاهتمام',
                        Icons.favorite_outline,
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: ['الكل', 'شقق', 'فلل', 'مكاتب', 'محلات', 'أراضي'].map((interest) {
                            final isSelected = selectedInterest == interest;
                            return _buildAdvancedFilterChip(
                              interest,
                              isSelected,
                              () {
                                setModalState(() {
                                  selectedInterest = isSelected ? null : interest;
                                });
                              });
                          }).toList())),
                      const SizedBox(height: 24),

                      // عملاء VIP
                      _buildAdvancedFilterSection(
                        'تصنيف العميل',
                        Icons.star_outline,
                        Column(
                          children: [
                            _buildAdvancedSwitchTile(
                              'عملاء VIP فقط',
                              'إظهار العملاء المميزين فقط',
                              isVipFilter ?? false,
                              (value) {
                                setModalState(() {
                                  isVipFilter = value;
                                });
                              }),
                          ])),
                      const SizedBox(height: 24),

                      // تاريخ الإضافة
                      _buildAdvancedFilterSection(
                        'تاريخ الإضافة',
                        Icons.date_range_outlined,
                        Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: _buildDateButton(
                                    'من تاريخ',
                                    fromDate,
                                    () async {
                                      final date = await showDatePicker(
                                        context: context,
                                        initialDate: fromDate ?? DateTime.now(),
                                        firstDate: DateTime(2020),
                                        lastDate: DateTime.now());
                                      if (date != null) {
                                        setModalState(() {
                                          fromDate = date;
                                        });
                                      }
                                    })),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: _buildDateButton(
                                    'إلى تاريخ',
                                    toDate,
                                    () async {
                                      final date = await showDatePicker(
                                        context: context,
                                        initialDate: toDate ?? DateTime.now(),
                                        firstDate: fromDate ?? DateTime(2020),
                                        lastDate: DateTime.now());
                                      if (date != null) {
                                        setModalState(() {
                                          toDate = date;
                                        });
                                      }
                                    })),
                              ]),
                          ])),
                    ]))),

              // أزرار الإجراءات المحسنة
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    top: BorderSide(color: AppColors.primary.withValues(alpha: 0.1))),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(24),
                    bottomRight: Radius.circular(24))),
                child: Row(
                  children: [
                    // زر إعادة التعيين
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                          borderRadius: BorderRadius.circular(16)),
                        child: OutlinedButton.icon(
                          onPressed: () {
                            setModalState(() {
                              selectedStatus = null;
                              selectedClientType = null;
                              selectedInterest = null;
                              isVipFilter = null;
                              fromDate = null;
                              toDate = null;
                            });
                          },
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16)),
                            side: BorderSide.none),
                          icon: Icon(Icons.refresh, color: AppColors.primary, size: 20),
                          label: Text(
                            'إعادة تعيين',
                            style: CairoTextStyles.button.copyWith(
                              color: AppColors.primary,
                              fontWeight: FontWeight.w600))))),
                    const SizedBox(width: 16),

                    // زر التطبيق
                    Expanded(
                      flex: 2,
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)]),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primary.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 4)),
                          ]),
                        child: ElevatedButton.icon(
                          onPressed: () {
                            // تطبيق الفلاتر
                            setState(() {
                              if (selectedStatus != null && selectedStatus != 'الكل') {
                                _selectedFilter = selectedStatus!;
                              }
                              // يمكن إضافة المزيد من منطق الفلاتر هنا
                            });
                            Navigator.pop(context);

                            // إظهار رسالة نجاح
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Row(
                                  children: [
                                    const Icon(Icons.check_circle, color: Colors.white, size: 20),
                                    const SizedBox(width: 8),
                                    Text(
                                      'تم تطبيق الفلاتر بنجاح',
                                      style: CairoTextStyles.bodyMedium.copyWith(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w600)),
                                  ]),
                                backgroundColor: Colors.green,
                                behavior: SnackBarBehavior.floating,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12)),
                                margin: const EdgeInsets.all(16),
                                duration: const Duration(seconds: 2)));
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            shadowColor: Colors.transparent,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16))),
                          icon: const Icon(Icons.check, color: Colors.white, size: 20),
                          label: Text(
                            'تطبيق الفلاتر',
                            style: CairoTextStyles.button.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold))))),
                  ])),
            ]));
      });
  }

  String _getFilterSummary() {
    List<String> activeFilers = [];

    if (_searchQuery.isNotEmpty) {
      activeFilers.add('البحث: "$_searchQuery"');
    }

    if (_selectedFilter != 'الكل') {
      activeFilers.add('الفلتر: $_selectedFilter');
    }

    if (_sortBy != 'الأحدث') {
      activeFilers.add('الترتيب: $_sortBy');
    }

    return activeFilers.isEmpty
        ? 'لا توجد فلاتر نشطة'
        : 'الفلاتر النشطة: ${activeFilers.join(' • ')}';
  }

  void _clearAllFilters() {
    setState(() {
      _searchQuery = '';
      _selectedFilter = 'الكل';
      _sortBy = 'الأحدث';
      _searchController.clear();
    });
  }

  void _showClientAnalytics() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'تحليلات العملاء',
                    style: CairoTextStyles.headlineMedium,
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(),
              Expanded(
                child: FutureBuilder<Map<String, dynamic>>(
                  future: _getClientAnalytics(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (snapshot.hasError) {
                      return Center(
                        child: Text('حدث خطأ في تحميل التحليلات: ${snapshot.error}'),
                      );
                    }

                    final analytics = snapshot.data!;
                    return _buildAnalyticsContent(analytics);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<Map<String, dynamic>> _getClientAnalytics() async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return {};

    try {
      final clientsSnapshot = await FirebaseFirestore.instance
          .collection('clients')
          .where('agentId', isEqualTo: currentUser.uid)
          .get();

      final leadsSnapshot = await FirebaseFirestore.instance
          .collection('leads')
          .where('agentId', isEqualTo: currentUser.uid)
          .get();

      final clients = clientsSnapshot.docs;
      final leads = leadsSnapshot.docs;

      // إحصائيات أساسية
      final totalClients = clients.length;
      final totalLeads = leads.length;
      final activeClients = clients.where((c) =>
          (c.data())['status'] == 'نشط').length;
      final newClients = clients.where((c) =>
          (c.data())['status'] == 'جديد').length;
      final vipClients = clients.where((c) =>
          (c.data())['isVip'] == true).length;

      // تحليل الاهتمامات
      Map<String, int> interestAnalysis = {};
      for (var client in clients) {
        final data = client.data();
        final interest = data['interest'] ?? 'غير محدد';
        interestAnalysis[interest] = (interestAnalysis[interest] ?? 0) + 1;
      }

      // تحليل المصادر
      Map<String, int> sourceAnalysis = {};
      for (var lead in leads) {
        final data = lead.data();
        final source = data['source'] ?? 'غير محدد';
        sourceAnalysis[source] = (sourceAnalysis[source] ?? 0) + 1;
      }

      // تحليل شهري
      Map<String, int> monthlyAnalysis = {};
      final now = DateTime.now();
      for (int i = 5; i >= 0; i--) {
        final month = DateTime(now.year, now.month - i, 1);
        final monthKey = DateFormat('yyyy-MM').format(month);
        monthlyAnalysis[monthKey] = 0;
      }

      for (var client in clients) {
        final data = client.data();
        final createdAt = data['createdAt'] as Timestamp?;
        if (createdAt != null) {
          final monthKey = DateFormat('yyyy-MM').format(createdAt.toDate());
          if (monthlyAnalysis.containsKey(monthKey)) {
            monthlyAnalysis[monthKey] = monthlyAnalysis[monthKey]! + 1;
          }
        }
      }

      return {
        'totalClients': totalClients,
        'totalLeads': totalLeads,
        'activeClients': activeClients,
        'newClients': newClients,
        'vipClients': vipClients,
        'interestAnalysis': interestAnalysis,
        'sourceAnalysis': sourceAnalysis,
        'monthlyAnalysis': monthlyAnalysis,
      };
    } catch (e) {
      throw Exception('فشل في تحميل التحليلات: $e');
    }
  }

  Widget _buildAnalyticsContent(Map<String, dynamic> analytics) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إحصائيات سريعة
          _buildQuickStats(analytics),
          const SizedBox(height: 24),

          // تحليل الاهتمامات
          _buildInterestChart(analytics['interestAnalysis']),
          const SizedBox(height: 24),

          // تحليل المصادر
          _buildSourceChart(analytics['sourceAnalysis']),
          const SizedBox(height: 24),

          // التحليل الشهري
          _buildMonthlyChart(analytics['monthlyAnalysis']),
        ],
      ),
    );
  }

  Widget _buildQuickStats(Map<String, dynamic> analytics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإحصائيات السريعة',
          style: CairoTextStyles.headlineSmall,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي العملاء',
                analytics['totalClients'].toString(),
                Icons.people,
                AppColors.primary,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'العملاء النشطين',
                analytics['activeClients'].toString(),
                Icons.person_outline,
                Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'عملاء جدد',
                analytics['newClients'].toString(),
                Icons.person_add,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'عملاء VIP',
                analytics['vipClients'].toString(),
                Icons.star,
                Colors.amber,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: CairoTextStyles.headlineMedium.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: CairoTextStyles.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInterestChart(Map<String, int> interestData) {
    if (interestData.isEmpty) {
      return const SizedBox();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تحليل اهتمامات العملاء',
          style: CairoTextStyles.headlineSmall,
        ),
        const SizedBox(height: 16),
        Container(
          height: 200,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
          ),
          child: PieChart(
            PieChartData(
              sections: interestData.entries.map((entry) {
                final total = interestData.values.reduce((a, b) => a + b);
                final percentage = (entry.value / total * 100).round();
                return PieChartSectionData(
                  value: entry.value.toDouble(),
                  title: '$percentage%',
                  color: _getColorForIndex(interestData.keys.toList().indexOf(entry.key)),
                  radius: 60,
                );
              }).toList(),
            ),
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 8,
          children: interestData.entries.map((entry) {
            final index = interestData.keys.toList().indexOf(entry.key);
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: _getColorForIndex(index),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 4),
                Text('${entry.key}: ${entry.value}', style: CairoTextStyles.bodySmall),
              ],
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSourceChart(Map<String, int> sourceData) {
    if (sourceData.isEmpty) {
      return const SizedBox();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تحليل مصادر العملاء المحتملين',
          style: CairoTextStyles.headlineSmall,
        ),
        const SizedBox(height: 16),
        ...sourceData.entries.map((entry) {
          final total = sourceData.values.reduce((a, b) => a + b);
          final percentage = (entry.value / total * 100).round();
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(entry.key, style: CairoTextStyles.bodyMedium),
                    Text('${entry.value} ($percentage%)', style: CairoTextStyles.bodyMedium),
                  ],
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: entry.value / total,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation(AppColors.primary),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildMonthlyChart(Map<String, int> monthlyData) {
    if (monthlyData.isEmpty) {
      return const SizedBox();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نمو العملاء الشهري',
          style: CairoTextStyles.headlineSmall,
        ),
        const SizedBox(height: 16),
        Container(
          height: 200,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
          ),
          child: LineChart(
            LineChartData(
              gridData: FlGridData(show: true),
              titlesData: FlTitlesData(
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(showTitles: true),
                ),
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      final months = monthlyData.keys.toList();
                      if (value.toInt() < months.length) {
                        final month = months[value.toInt()];
                        return Text(month.substring(5)); // عرض الشهر فقط
                      }
                      return const Text('');
                    },
                  ),
                ),
                topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
              ),
              borderData: FlBorderData(show: true),
              lineBarsData: [
                LineChartBarData(
                  spots: monthlyData.entries.toList().asMap().entries.map((entry) {
                    return FlSpot(entry.key.toDouble(), entry.value.value.toDouble());
                  }).toList(),
                  isCurved: true,
                  color: AppColors.primary,
                  barWidth: 3,
                  dotData: FlDotData(show: true),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Color _getColorForIndex(int index) {
    final colors = [
      AppColors.primary,
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.amber,
    ];
    return colors[index % colors.length];
  }

  void _exportClientData() async {
    try {
      setState(() => _isLoading = true);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('جاري تصدير بيانات العملاء...', style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white))));

      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      // جلب بيانات العملاء
      final clientsSnapshot = await FirebaseFirestore.instance
          .collection('clients')
          .where('agentId', isEqualTo: currentUser.uid)
          .get();

      // إنشاء ملف Excel
      final xlsio.Workbook workbook = xlsio.Workbook();
      final xlsio.Worksheet worksheet = workbook.worksheets[0];

      // إعداد العناوين
      worksheet.getRangeByName('A1').setText('الاسم');
      worksheet.getRangeByName('B1').setText('الهاتف');
      worksheet.getRangeByName('C1').setText('البريد الإلكتروني');
      worksheet.getRangeByName('D1').setText('الحالة');
      worksheet.getRangeByName('E1').setText('الاهتمام');
      worksheet.getRangeByName('F1').setText('الميزانية');
      worksheet.getRangeByName('G1').setText('تاريخ الإضافة');
      worksheet.getRangeByName('H1').setText('VIP');

      // تنسيق العناوين
      final xlsio.Range headerRange = worksheet.getRangeByName('A1:H1');
      headerRange.cellStyle.bold = true;
      headerRange.cellStyle.backColor = '#4CAF50';
      headerRange.cellStyle.fontColor = '#FFFFFF';

      // إضافة البيانات
      int row = 2;
      for (var doc in clientsSnapshot.docs) {
        final data = doc.data();

        worksheet.getRangeByName('A$row').setText(data['name'] ?? '');
        worksheet.getRangeByName('B$row').setText(data['phone'] ?? '');
        worksheet.getRangeByName('C$row').setText(data['email'] ?? '');
        worksheet.getRangeByName('D$row').setText(data['status'] ?? '');
        worksheet.getRangeByName('E$row').setText(data['interest'] ?? '');
        worksheet.getRangeByName('F$row').setNumber(data['budget']?.toDouble() ?? 0);

        final createdAt = data['createdAt'] as Timestamp?;
        if (createdAt != null) {
          worksheet.getRangeByName('G$row').setText(
            DateFormat('yyyy-MM-dd').format(createdAt.toDate()));
        }

        worksheet.getRangeByName('H$row').setText(
          (data['isVip'] == true) ? 'نعم' : 'لا');

        row++;
      }

      // ضبط عرض الأعمدة
      worksheet.autoFitColumn(1);
      worksheet.autoFitColumn(2);
      worksheet.autoFitColumn(3);
      worksheet.autoFitColumn(4);
      worksheet.autoFitColumn(5);
      worksheet.autoFitColumn(6);
      worksheet.autoFitColumn(7);
      worksheet.autoFitColumn(8);

      // حفظ الملف
      final List<int> bytes = workbook.saveAsStream();
      workbook.dispose();

      // مشاركة الملف
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/clients_data_${DateTime.now().millisecondsSinceEpoch}.xlsx');
      await file.writeAsBytes(bytes);

      await Share.shareXFiles([XFile(file.path)],
        text: 'بيانات العملاء - ${DateFormat('yyyy-MM-dd').format(DateTime.now())}');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم تصدير البيانات بنجاح!', style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white))));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ في التصدير: $e', style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white))));
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// عرض تفاعلات العميل باستخدام النظام الجديد
  void _showClientInteractions(String clientId) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20))),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2))),
            // Header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'تفاعلات العميل',
                      style: CairoTextStyles.headlineMedium)),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context)),
                ])),
            const Divider(),
            // Content
            Expanded(
              child: FutureBuilder<List<ClientInteraction>>(
                future: _getClientInteractionsFromProvider(clientId),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const LoadingWidget();
                  }

                  if (snapshot.hasError) {
                    return Center(
                      child: Text('حدث خطأ في تحميل التفاعلات',
                        style: CairoTextStyles.bodyMedium));
                  }

                  final interactions = snapshot.data ?? [];

                  if (interactions.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.history, size: 64, color: Colors.grey[400]),
                          const SizedBox(height: 16),
                          Text('لا توجد تفاعلات مع هذا العميل',
                            style: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[600])),
                        ]));
                  }

                  return ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: interactions.length,
                    itemBuilder: (context, index) {
                      final interaction = interactions[index];
                      return _buildInteractionCard(interaction);
                    });
                })),
            // Add interaction button
            Padding(
              padding: const EdgeInsets.all(16),
              child: ElevatedButton.icon(
                onPressed: () => _addNewInteraction(clientId),
                icon: const Icon(Icons.add),
                label: Text('إضافة تفاعل جديد', style: CairoTextStyles.button),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  minimumSize: const Size(double.infinity, 48)))),
          ])));
  }

  /// جدولة موعد جديد
  void _scheduleAppointment(String clientId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حجز موعد', style: CairoTextStyles.headlineSmall),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('سيتم إضافة ميزة حجز المواعيد قريباً',
              style: CairoTextStyles.bodyMedium),
            const SizedBox(height: 16),
            Text('يمكنك حالياً إضافة تفاعل من نوع "موعد" في قسم التفاعلات',
              style: CairoTextStyles.bodySmall.copyWith(color: Colors.grey[600])),
          ]),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('حسناً', style: CairoTextStyles.button.copyWith(color: AppColors.primary))),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _showClientInteractions(clientId);
            },
            child: Text('عرض التفاعلات', style: CairoTextStyles.button.copyWith(color: AppColors.primary))),
        ]));
  }

  /// الحصول على تفاعلات العميل من المزود
  Future<List<ClientInteraction>> _getClientInteractionsFromProvider(String clientId) async {
    // هذه دالة مؤقتة - يجب ربطها بالمزود الفعلي
    return [];
  }

  /// إضافة تفاعل جديد
  void _addNewInteraction(String clientId) {
    Navigator.pop(context); // إغلاق النافذة الحالية
    // يمكن إضافة صفحة إضافة تفاعل جديد هنا
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('سيتم إضافة صفحة إضافة التفاعل قريباً',
        style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white))));
  }

  /// بناء كارت التفاعل
  Widget _buildInteractionCard(ClientInteraction interaction) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.15), width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(_getInteractionIcon(interaction.type),
                size: 20, color: AppColors.primary),
              const SizedBox(width: 8),
              Expanded(
                child: Text(interaction.title,
                  style: CairoTextStyles.cardTitle.copyWith(fontSize: 14))),
              Text(_formatDate(interaction.interactionDate),
                style: CairoTextStyles.bodySmall.copyWith(color: Colors.grey[600])),
            ]),
          const SizedBox(height: 8),
          Text(interaction.description,
            style: CairoTextStyles.bodySmall,
            maxLines: 2,
            overflow: TextOverflow.ellipsis),
          if (interaction.outcome != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getOutcomeColor(interaction.outcome!).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8)),
              child: Text(_getOutcomeText(interaction.outcome!),
                style: CairoTextStyles.labelSmall.copyWith(
                  color: _getOutcomeColor(interaction.outcome!),
                  fontSize: 11))),
          ],
        ]));
  }

  /// الحصول على أيقونة نوع التفاعل
  IconData _getInteractionIcon(InteractionType type) {
    switch (type) {
      case InteractionType.call:
        return Icons.phone;
      case InteractionType.meeting:
        return Icons.meeting_room;
      case InteractionType.email:
        return Icons.email;
      case InteractionType.whatsapp:
        return Icons.chat;
      case InteractionType.visit:
        return Icons.location_on;
      default:
        return Icons.chat_bubble;
    }
  }

  /// الحصول على لون نتيجة التفاعل
  Color _getOutcomeColor(InteractionOutcome outcome) {
    switch (outcome) {
      case InteractionOutcome.positive:
        return Colors.green;
      case InteractionOutcome.negative:
        return Colors.red;
      case InteractionOutcome.converted:
        return Colors.blue;
      default:
        return Colors.orange;
    }
  }

  /// الحصول على نص نتيجة التفاعل
  String _getOutcomeText(InteractionOutcome outcome) {
    switch (outcome) {
      case InteractionOutcome.positive:
        return 'إيجابي';
      case InteractionOutcome.negative:
        return 'سلبي';
      case InteractionOutcome.converted:
        return 'تم التحويل';
      case InteractionOutcome.interested:
        return 'مهتم';
      case InteractionOutcome.notInterested:
        return 'غير مهتم';
      default:
        return 'محايد';
    }
  }

  /// بناء قسم فلتر متقدم
  Widget _buildAdvancedFilterSection(String title, IconData icon, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)]),
                borderRadius: BorderRadius.circular(10)),
              child: Icon(icon, color: Colors.white, size: 18)),
            const SizedBox(width: 12),
            Text(
              title,
              style: CairoTextStyles.headlineSmall.copyWith(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.primary)),
          ]),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.2),
              width: 1),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2)),
            ]),
          child: content),
      ]);
  }

  /// بناء رقاقة فلتر متقدم
  Widget _buildAdvancedFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: isSelected
            ? LinearGradient(
                colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)])
            : null,
          color: isSelected ? null : Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
              ? AppColors.primary
              : AppColors.primary.withValues(alpha: 0.3),
            width: 1),
          boxShadow: isSelected ? [
            BoxShadow(
              color: AppColors.primary.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2)),
          ] : null),
        child: Text(
          label,
          style: CairoTextStyles.bodyMedium.copyWith(
            fontSize: 13,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected ? Colors.white : AppColors.primary))));
  }

  /// بناء مفتاح تبديل متقدم
  Widget _buildAdvancedSwitchTile(String title, String subtitle, bool value, ValueChanged<bool> onChanged) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: value ? AppColors.primary.withValues(alpha: 0.05) : Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: value ? AppColors.primary.withValues(alpha: 0.3) : Colors.grey.withValues(alpha: 0.2),
          width: 1)),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: CairoTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: value ? AppColors.primary : Colors.grey[700])),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: CairoTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600])),
              ])),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primary,
            activeTrackColor: AppColors.primary.withValues(alpha: 0.3)),
        ]));
  }

  /// بناء زر التاريخ
  Widget _buildDateButton(String label, DateTime? date, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: date != null ? AppColors.primary.withValues(alpha: 0.05) : Colors.grey.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: date != null ? AppColors.primary.withValues(alpha: 0.3) : Colors.grey.withValues(alpha: 0.3),
            width: 1)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: CairoTextStyles.bodySmall.copyWith(
                color: Colors.grey[600])),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: date != null ? AppColors.primary : Colors.grey[500]),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    date != null ? _formatDate(date) : 'اختر التاريخ',
                    style: CairoTextStyles.bodyMedium.copyWith(
                      color: date != null ? AppColors.primary : Colors.grey[500],
                      fontWeight: date != null ? FontWeight.w600 : FontWeight.normal))),
              ]),
          ])));
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }
}