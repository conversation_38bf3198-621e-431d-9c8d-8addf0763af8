# دليل النشر والتوزيع - تطبيق Krea

## 📋 نظرة عامة

هذا الدليل يوضح خطوات نشر وتوزيع تطبيق Krea على المنصات المختلفة.

---

## 🔧 الإعداد الأولي

### متطلبات النشر
- **Flutter SDK**: 3.6.1 أو أحدث
- **Android Studio**: مع Android SDK
- **Xcode**: للنشر على iOS (macOS فقط)
- **Firebase Project**: مُعد ومُفعل
- **حسابات المطورين**: Google Play Console و Apple Developer

### إعداد المتغيرات البيئية
```bash
# ملف .env
FIREBASE_API_KEY=your_api_key
FIREBASE_PROJECT_ID=your_project_id
WAMDA_API_KEY=your_wamda_key
GOOGLE_MAPS_API_KEY=your_maps_key
ENCRYPTION_KEY=your_encryption_key
```

---

## 🤖 النشر على Android

### 1. إعداد التوقيع
```bash
# إنشاء مفتاح التوقيع
keytool -genkey -v -keystore ~/upload-keystore.jks \
  -keyalg RSA -keysize 2048 -validity 10000 \
  -alias upload

# إنشاء ملف key.properties
echo "storePassword=your_store_password
keyPassword=your_key_password
keyAlias=upload
storeFile=../upload-keystore.jks" > android/key.properties
```

### 2. تحديث build.gradle
```gradle
// android/app/build.gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        applicationId "com.codnet.krea"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 1
        versionName "1.0.0"
    }
    
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
```

### 3. بناء التطبيق
```bash
# تنظيف المشروع
flutter clean
flutter pub get

# بناء App Bundle (مُفضل)
flutter build appbundle --release

# أو بناء APK
flutter build apk --release --split-per-abi
```

### 4. الرفع على Google Play Console
1. تسجيل الدخول إلى Google Play Console
2. إنشاء تطبيق جديد أو تحديث موجود
3. رفع ملف AAB في قسم "App bundles"
4. ملء معلومات التطبيق والوصف
5. إعداد السعر والتوزيع
6. مراجعة وإرسال للمراجعة

---

## 🍎 النشر على iOS

### 1. إعداد Xcode
```bash
# فتح مشروع iOS
open ios/Runner.xcworkspace
```

### 2. تحديث إعدادات المشروع
- **Bundle Identifier**: com.codnet.krea
- **Version**: 1.0.0
- **Build**: 1
- **Deployment Target**: iOS 12.0
- **Team**: اختيار فريق المطور

### 3. إعداد الشهادات والملفات الشخصية
1. تسجيل الدخول إلى Apple Developer Portal
2. إنشاء App ID للتطبيق
3. إنشاء Distribution Certificate
4. إنشاء Provisioning Profile للتوزيع
5. تحميل وتثبيت الشهادات في Xcode

### 4. بناء التطبيق
```bash
# بناء للجهاز
flutter build ios --release

# أو استخدام Xcode
# Product > Archive
```

### 5. الرفع على App Store Connect
1. استخدام Xcode Organizer لرفع الأرشيف
2. تسجيل الدخول إلى App Store Connect
3. إنشاء تطبيق جديد أو إصدار جديد
4. ملء معلومات التطبيق
5. إضافة لقطات الشاشة والوصف
6. إرسال للمراجعة

---

## 🔥 إعداد Firebase للإنتاج

### 1. تكوين المشروع
```javascript
// firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد الأمان للمستخدمين
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد العقارات
    match /estates/{estateId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // قواعد المنتدى
    match /forum/{document=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

### 2. إعداد الفهارس
```json
// firestore.indexes.json
{
  "indexes": [
    {
      "collectionGroup": "estates",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "isActive", "order": "ASCENDING"},
        {"fieldPath": "createdAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "estates",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "location.governorate", "order": "ASCENDING"},
        {"fieldPath": "price", "order": "ASCENDING"}
      ]
    }
  ]
}
```

### 3. Cloud Functions
```javascript
// functions/index.js
const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

// إرسال إشعار عند إنشاء عقار جديد
exports.sendEstateNotification = functions.firestore
  .document('estates/{estateId}')
  .onCreate(async (snap, context) => {
    const estate = snap.data();
    
    // إرسال إشعار للمهتمين
    const message = {
      notification: {
        title: 'عقار جديد',
        body: `تم إضافة ${estate.title} في ${estate.location.area}`,
      },
      topic: 'new_estates'
    };
    
    return admin.messaging().send(message);
  });
```

---

## 🌐 إعداد الخدمات السحابية

### 1. خدمة ومض للدفع
```dart
// lib/core/config/wamda_config.dart
class WamdaConfig {
  static const String baseUrl = 'https://api.wamda.kw';
  static const String apiKey = String.fromEnvironment('WAMDA_API_KEY');
  static const String merchantId = 'your_merchant_id';
  
  static Map<String, String> get headers => {
    'Authorization': 'Bearer $apiKey',
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
}
```

### 2. Google Maps API
```yaml
# android/app/src/main/AndroidManifest.xml
<meta-data
    android:name="com.google.android.geo.API_KEY"
    android:value="${GOOGLE_MAPS_API_KEY}" />
```

```xml
<!-- ios/Runner/Info.plist -->
<key>GMSApiKey</key>
<string>${GOOGLE_MAPS_API_KEY}</string>
```

---

## 📊 مراقبة الأداء

### 1. Firebase Crashlytics
```dart
// lib/main.dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  await Firebase.initializeApp();
  
  // تفعيل Crashlytics في الإنتاج فقط
  if (kReleaseMode) {
    await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
  }
  
  FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;
  
  runApp(MyApp());
}
```

### 2. Firebase Performance
```dart
// مراقبة أداء الشبكة
final HttpMetric metric = FirebasePerformance.instance
    .newHttpMetric('https://api.example.com/data', HttpMethod.Get);

await metric.start();
final response = await http.get(url);
metric.responseCode = response.statusCode;
await metric.stop();
```

---

## 🔒 الأمان في الإنتاج

### 1. تشفير البيانات الحساسة
```dart
// lib/core/security/encryption.dart
class EncryptionService {
  static const String _key = String.fromEnvironment('ENCRYPTION_KEY');
  
  static String encrypt(String data) {
    final key = encrypt.Key.fromBase64(_key);
    final iv = encrypt.IV.fromSecureRandom(16);
    final encrypter = encrypt.Encrypter(encrypt.AES(key));
    
    return encrypter.encrypt(data, iv: iv).base64;
  }
}
```

### 2. إخفاء المفاتيح الحساسة
```dart
// استخدام flutter_dotenv
await dotenv.load(fileName: '.env');
final apiKey = dotenv.env['API_KEY'] ?? '';
```

### 3. تفعيل Certificate Pinning
```dart
// lib/core/network/certificate_pinning.dart
class CertificatePinning {
  static SecurityContext get securityContext {
    final context = SecurityContext.defaultContext;
    context.setTrustedCertificatesBytes(certificateBytes);
    return context;
  }
}
```

---

## 🚀 CI/CD Pipeline

### 1. GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Stores

on:
  push:
    tags:
      - 'v*'

jobs:
  deploy-android:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.6.1'
    
    - name: Install dependencies
      run: flutter pub get
    
    - name: Build APK
      run: flutter build apk --release
    
    - name: Upload to Play Store
      uses: r0adkll/upload-google-play@v1
      with:
        serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
        packageName: com.krea.app
        releaseFiles: build/app/outputs/bundle/release/app-release.aab
        track: production
```

### 2. Fastlane (iOS)
```ruby
# ios/fastlane/Fastfile
default_platform(:ios)

platform :ios do
  desc "Deploy to App Store"
  lane :deploy do
    build_app(
      scheme: "Runner",
      export_method: "app-store"
    )
    
    upload_to_app_store(
      skip_metadata: false,
      skip_screenshots: false,
      submit_for_review: true
    )
  end
end
```

---

## 📱 اختبار ما قبل النشر

### 1. اختبارات الوحدة
```bash
flutter test
```

### 2. اختبارات التكامل
```bash
flutter drive --target=test_driver/app.dart
```

### 3. اختبار الأداء
```bash
flutter run --profile
```

### 4. اختبار على أجهزة حقيقية
- اختبار على أجهزة Android مختلفة
- اختبار على أجهزة iOS مختلفة
- اختبار الشبكة البطيئة
- اختبار حالات عدم الاتصال

---

## 📈 مراقبة ما بعد النشر

### 1. مؤشرات الأداء الرئيسية
- معدل التحميل والتثبيت
- معدل الاحتفاظ بالمستخدمين
- معدل الأخطاء والتعطل
- أوقات الاستجابة
- استخدام الذاكرة والبطارية

### 2. تحليلات المستخدمين
```dart
// تتبع الأحداث المهمة
FirebaseAnalytics.instance.logEvent(
  name: 'estate_viewed',
  parameters: {
    'estate_id': estateId,
    'user_type': userType,
    'location': location,
  },
);
```

### 3. مراقبة الأخطاء
```dart
// تسجيل الأخطاء المخصصة
FirebaseCrashlytics.instance.recordError(
  error,
  stackTrace,
  reason: 'Custom error description',
);
```

---

## 🔄 التحديثات والصيانة

### 1. التحديثات التلقائية
```dart
// فحص التحديثات المتاحة
final updateInfo = await InAppUpdate.checkForUpdate();
if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
  await InAppUpdate.performImmediateUpdate();
}
```

### 2. الصيانة المجدولة
- إشعار المستخدمين مسبقاً
- تحديد نافزة زمنية مناسبة
- توفير صفحة صيانة مؤقتة
- مراقبة العودة للخدمة

### 3. خطة الطوارئ
- نسخ احتياطية للبيانات
- خطة استعادة الخدمة
- فريق دعم فني جاهز
- قنوات تواصل مع المستخدمين

---

## 👨‍💻 فريق النشر والدعم

### Codnet Moroccan DevOps Team
فريق **Codnet Moroccan** متخصص في عمليات النشر والتوزيع للتطبيقات المعقدة، مع خبرة واسعة في:

- **CI/CD Pipelines**: أتمتة عمليات البناء والنشر
- **Cloud Infrastructure**: إدارة البنية التحتية السحابية
- **Monitoring & Alerting**: مراقبة الأداء والتنبيهات
- **Security Compliance**: ضمان الامتثال الأمني
- **Performance Optimization**: تحسين الأداء والسرعة

### خدمات ما بعد النشر
- **مراقبة 24/7**: مراقبة مستمرة للتطبيق والخوادم
- **دعم فني سريع**: استجابة خلال ساعات للمشاكل الحرجة
- **تحديثات أمنية**: تطبيق التحديثات الأمنية فوراً
- **تحسين الأداء**: مراقبة وتحسين مستمر للأداء
- **نسخ احتياطية**: نسخ احتياطية تلقائية ومنتظمة

### التواصل مع فريق النشر
- **البريد الإلكتروني**: <EMAIL>
- **الطوارئ**: +212 XXX XXX XXX
- **Slack**: #codnet-devops
- **Monitoring Dashboard**: monitor.krea.app

### ضمانات الجودة
- **99.9% Uptime**: ضمان توفر الخدمة
- **< 2s Response Time**: أوقات استجابة سريعة
- **Zero Downtime Deployment**: نشر بدون انقطاع
- **Automated Rollback**: استعادة تلقائية عند المشاكل

---

*تم إعداد هذا الدليل بواسطة فريق Codnet Moroccan - تأكد من اتباع جميع الخطوات بعناية وإجراء اختبارات شاملة قبل النشر النهائي*
