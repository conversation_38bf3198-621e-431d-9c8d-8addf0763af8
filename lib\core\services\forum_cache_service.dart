import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../../domain/models/forum/category_model.dart';
import '../../domain/models/forum/topic_model.dart';

/// خدمة التخزين المؤقت للوبي
class ForumCacheService {
  /// مفتاح تخزين الفئات
  static const String _categoriesKey = 'lobby_categories';

  /// مفتاح تخزين المواضيع المميزة
  static const String _featuredTopicsKey = 'lobby_featured_topics';

  /// مفتاح تخزين المواضيع المثبتة
  static const String _pinnedTopicsKey = 'lobby_pinned_topics';

  /// مفتاح تخزين أحدث المواضيع
  static const String _latestTopicsKey = 'lobby_latest_topics';

  /// مفتاح تخزين المواضيع الأكثر مشاهدة
  static const String _mostViewedTopicsKey = 'lobby_most_viewed_topics';

  /// مفتاح تخزين المواضيع الأكثر إعجاباً
  static const String _mostLikedTopicsKey = 'lobby_most_liked_topics';

  /// مفتاح تخزين المواضيع الأكثر نشاطاً
  static const String _mostActiveTopicsKey = 'lobby_most_active_topics';

  /// مفتاح تخزين آخر تحديث للبيانات
  static const String _lastUpdateKey = 'forum_last_update';

  /// مدة صلاحية البيانات المخزنة (بالدقائق)
  static const int _cacheDuration = 30;

  /// مثيل التفضيلات المشتركة
  late SharedPreferences _prefs;

  /// تهيئة خدمة التخزين المؤقت
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// تخزين فئات المنتدى
  Future<void> cacheCategories(List<CategoryModel> categories) async {
    final categoriesJson =
        categories.map((category) => jsonEncode(category.toMap())).toList();
    await _prefs.setStringList(_categoriesKey, categoriesJson);
    await _updateLastCacheTime();
  }

  /// الحصول على فئات اللوبي المخزنة
  List<CategoryModel>? getCachedCategories() {
    if (!_isCacheValid()) return null;

    final categoriesJson = _prefs.getStringList(_categoriesKey);
    if (categoriesJson == null) return null;

    try {
      return categoriesJson
          .map(
              (categoryJson) => CategoryModel.fromMap(jsonDecode(categoryJson)))
          .toList();
    } catch (e) {
      return null;
    }
  }

  /// تخزين المواضيع المميزة
  Future<void> cacheFeaturedTopics(List<TopicModel> topics) async {
    final topicsJson =
        topics.map((topic) => jsonEncode(topic.toMap())).toList();
    await _prefs.setStringList(_featuredTopicsKey, topicsJson);
    await _updateLastCacheTime();
  }

  /// الحصول على المواضيع المميزة المخزنة
  List<TopicModel>? getCachedFeaturedTopics() {
    if (!_isCacheValid()) return null;

    final topicsJson = _prefs.getStringList(_featuredTopicsKey);
    if (topicsJson == null) return null;

    try {
      return topicsJson
          .map((topicJson) => TopicModel.fromMap(jsonDecode(topicJson)))
          .toList();
    } catch (e) {
      return null;
    }
  }

  /// تخزين المواضيع المثبتة
  Future<void> cachePinnedTopics(List<TopicModel> topics) async {
    final topicsJson =
        topics.map((topic) => jsonEncode(topic.toMap())).toList();
    await _prefs.setStringList(_pinnedTopicsKey, topicsJson);
    await _updateLastCacheTime();
  }

  /// الحصول على المواضيع المثبتة المخزنة
  List<TopicModel>? getCachedPinnedTopics() {
    if (!_isCacheValid()) return null;

    final topicsJson = _prefs.getStringList(_pinnedTopicsKey);
    if (topicsJson == null) return null;

    try {
      return topicsJson
          .map((topicJson) => TopicModel.fromMap(jsonDecode(topicJson)))
          .toList();
    } catch (e) {
      return null;
    }
  }

  /// تخزين أحدث المواضيع
  Future<void> cacheLatestTopics(List<TopicModel> topics) async {
    final topicsJson =
        topics.map((topic) => jsonEncode(topic.toMap())).toList();
    await _prefs.setStringList(_latestTopicsKey, topicsJson);
    await _updateLastCacheTime();
  }

  /// الحصول على أحدث المواضيع المخزنة
  List<TopicModel>? getCachedLatestTopics() {
    if (!_isCacheValid()) return null;

    final topicsJson = _prefs.getStringList(_latestTopicsKey);
    if (topicsJson == null) return null;

    try {
      return topicsJson
          .map((topicJson) => TopicModel.fromMap(jsonDecode(topicJson)))
          .toList();
    } catch (e) {
      return null;
    }
  }

  /// تخزين المواضيع الأكثر مشاهدة
  Future<void> cacheMostViewedTopics(List<TopicModel> topics) async {
    final topicsJson =
        topics.map((topic) => jsonEncode(topic.toMap())).toList();
    await _prefs.setStringList(_mostViewedTopicsKey, topicsJson);
    await _updateLastCacheTime();
  }

  /// الحصول على المواضيع الأكثر مشاهدة المخزنة
  List<TopicModel>? getCachedMostViewedTopics() {
    if (!_isCacheValid()) return null;

    final topicsJson = _prefs.getStringList(_mostViewedTopicsKey);
    if (topicsJson == null) return null;

    try {
      return topicsJson
          .map((topicJson) => TopicModel.fromMap(jsonDecode(topicJson)))
          .toList();
    } catch (e) {
      return null;
    }
  }

  /// تخزين المواضيع الأكثر إعجاباً
  Future<void> cacheMostLikedTopics(List<TopicModel> topics) async {
    final topicsJson =
        topics.map((topic) => jsonEncode(topic.toMap())).toList();
    await _prefs.setStringList(_mostLikedTopicsKey, topicsJson);
    await _updateLastCacheTime();
  }

  /// الحصول على المواضيع الأكثر إعجاباً المخزنة
  List<TopicModel>? getCachedMostLikedTopics() {
    if (!_isCacheValid()) return null;

    final topicsJson = _prefs.getStringList(_mostLikedTopicsKey);
    if (topicsJson == null) return null;

    try {
      return topicsJson
          .map((topicJson) => TopicModel.fromMap(jsonDecode(topicJson)))
          .toList();
    } catch (e) {
      return null;
    }
  }

  /// تخزين المواضيع الأكثر نشاطاً
  Future<void> cacheMostActiveTopics(List<TopicModel> topics) async {
    final topicsJson =
        topics.map((topic) => jsonEncode(topic.toMap())).toList();
    await _prefs.setStringList(_mostActiveTopicsKey, topicsJson);
    await _updateLastCacheTime();
  }

  /// الحصول على المواضيع الأكثر نشاطاً المخزنة
  List<TopicModel>? getCachedMostActiveTopics() {
    if (!_isCacheValid()) return null;

    final topicsJson = _prefs.getStringList(_mostActiveTopicsKey);
    if (topicsJson == null) return null;

    try {
      return topicsJson
          .map((topicJson) => TopicModel.fromMap(jsonDecode(topicJson)))
          .toList();
    } catch (e) {
      return null;
    }
  }

  /// مسح جميع البيانات المخزنة
  Future<void> clearCache() async {
    await _prefs.remove(_categoriesKey);
    await _prefs.remove(_featuredTopicsKey);
    await _prefs.remove(_pinnedTopicsKey);
    await _prefs.remove(_latestTopicsKey);
    await _prefs.remove(_mostViewedTopicsKey);
    await _prefs.remove(_mostLikedTopicsKey);
    await _prefs.remove(_mostActiveTopicsKey);
    await _prefs.remove(_lastUpdateKey);
  }

  /// تحديث وقت آخر تخزين
  Future<void> _updateLastCacheTime() async {
    await _prefs.setInt(_lastUpdateKey, DateTime.now().millisecondsSinceEpoch);
  }

  /// التحقق من صلاحية البيانات المخزنة
  bool _isCacheValid() {
    final lastUpdate = _prefs.getInt(_lastUpdateKey);
    if (lastUpdate == null) return false;

    final lastUpdateTime = DateTime.fromMillisecondsSinceEpoch(lastUpdate);
    final currentTime = DateTime.now();

    return currentTime.difference(lastUpdateTime).inMinutes < _cacheDuration;
  }
}
