// lib/domain/models/favorites/favorite_property_request_model.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// نموذج طلب عقار مفضل
class FavoritePropertyRequestModel extends Equatable {
  /// معرف المفضلة
  final String id;

  /// معرف المستخدم
  final String userId;

  /// معرف طلب العقار
  final String requestId;

  /// عنوان الطلب
  final String requestTitle;

  /// وصف الطلب
  final String requestDescription;

  /// نوع العقار
  final String propertyType;

  /// المناطق المفضلة
  final List<String> preferredLocations;

  /// الحد الأدنى للسعر
  final double? minPrice;

  /// الحد الأقصى للسعر
  final double? maxPrice;

  /// معرف صاحب الطلب
  final String requestOwnerId;

  /// اسم صاحب الطلب
  final String requestOwnerName;

  /// صورة صاحب الطلب
  final String? requestOwnerImage;

  /// تاريخ الإضافة للمفضلة
  final DateTime addedAt;

  /// تاريخ آخر تحديث
  final DateTime? updatedAt;

  /// هل الإشعارات مفعلة لهذا الطلب
  final bool isNotificationEnabled;

  /// ملاحظات شخصية
  final String? personalNotes;

  /// علامات مخصصة
  final List<String>? customTags;

  /// أولوية المفضلة (1-5)
  final int priority;

  /// هل تم عرض الطلب مؤخراً
  final bool isRecentlyViewed;

  /// عدد مرات المشاهدة من المفضلة
  final int viewCount;

  const FavoritePropertyRequestModel({
    required this.id,
    required this.userId,
    required this.requestId,
    required this.requestTitle,
    required this.requestDescription,
    required this.propertyType,
    required this.preferredLocations,
    this.minPrice,
    this.maxPrice,
    required this.requestOwnerId,
    required this.requestOwnerName,
    this.requestOwnerImage,
    required this.addedAt,
    this.updatedAt,
    this.isNotificationEnabled = true,
    this.personalNotes,
    this.customTags,
    this.priority = 3,
    this.isRecentlyViewed = false,
    this.viewCount = 0,
  });

  /// إنشاء من Firestore
  factory FavoritePropertyRequestModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return FavoritePropertyRequestModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      requestId: data['requestId'] ?? '',
      requestTitle: data['requestTitle'] ?? '',
      requestDescription: data['requestDescription'] ?? '',
      propertyType: data['propertyType'] ?? '',
      preferredLocations: data['preferredLocations'] != null
          ? List<String>.from(data['preferredLocations'])
          : [],
      minPrice: data['minPrice']?.toDouble(),
      maxPrice: data['maxPrice']?.toDouble(),
      requestOwnerId: data['requestOwnerId'] ?? '',
      requestOwnerName: data['requestOwnerName'] ?? '',
      requestOwnerImage: data['requestOwnerImage'],
      addedAt: (data['addedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
      isNotificationEnabled: data['isNotificationEnabled'] ?? true,
      personalNotes: data['personalNotes'],
      customTags: data['customTags'] != null
          ? List<String>.from(data['customTags'])
          : null,
      priority: data['priority'] ?? 3,
      isRecentlyViewed: data['isRecentlyViewed'] ?? false,
      viewCount: data['viewCount'] ?? 0,
    );
  }

  /// تحويل إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'requestId': requestId,
      'requestTitle': requestTitle,
      'requestDescription': requestDescription,
      'propertyType': propertyType,
      'preferredLocations': preferredLocations,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'requestOwnerId': requestOwnerId,
      'requestOwnerName': requestOwnerName,
      'requestOwnerImage': requestOwnerImage,
      'addedAt': Timestamp.fromDate(addedAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'isNotificationEnabled': isNotificationEnabled,
      'personalNotes': personalNotes,
      'customTags': customTags,
      'priority': priority,
      'isRecentlyViewed': isRecentlyViewed,
      'viewCount': viewCount,
    };
  }

  /// إنشاء نسخة محدثة
  FavoritePropertyRequestModel copyWith({
    String? id,
    String? userId,
    String? requestId,
    String? requestTitle,
    String? requestDescription,
    String? propertyType,
    List<String>? preferredLocations,
    double? minPrice,
    double? maxPrice,
    String? requestOwnerId,
    String? requestOwnerName,
    String? requestOwnerImage,
    DateTime? addedAt,
    DateTime? updatedAt,
    bool? isNotificationEnabled,
    String? personalNotes,
    List<String>? customTags,
    int? priority,
    bool? isRecentlyViewed,
    int? viewCount,
  }) {
    return FavoritePropertyRequestModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      requestId: requestId ?? this.requestId,
      requestTitle: requestTitle ?? this.requestTitle,
      requestDescription: requestDescription ?? this.requestDescription,
      propertyType: propertyType ?? this.propertyType,
      preferredLocations: preferredLocations ?? this.preferredLocations,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      requestOwnerId: requestOwnerId ?? this.requestOwnerId,
      requestOwnerName: requestOwnerName ?? this.requestOwnerName,
      requestOwnerImage: requestOwnerImage ?? this.requestOwnerImage,
      addedAt: addedAt ?? this.addedAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isNotificationEnabled: isNotificationEnabled ?? this.isNotificationEnabled,
      personalNotes: personalNotes ?? this.personalNotes,
      customTags: customTags ?? this.customTags,
      priority: priority ?? this.priority,
      isRecentlyViewed: isRecentlyViewed ?? this.isRecentlyViewed,
      viewCount: viewCount ?? this.viewCount,
    );
  }

  /// الحصول على نص نطاق السعر
  String get priceRangeText {
    if (minPrice == null && maxPrice == null) {
      return 'غير محدد';
    } else if (minPrice == null) {
      return 'حتى ${maxPrice!.toInt()} د.ك';
    } else if (maxPrice == null) {
      return 'من ${minPrice!.toInt()} د.ك';
    } else {
      return '${minPrice!.toInt()} - ${maxPrice!.toInt()} د.ك';
    }
  }

  /// الحصول على نص المناطق المفضلة
  String get locationsText {
    if (preferredLocations.isEmpty) {
      return 'جميع المناطق';
    } else if (preferredLocations.length == 1) {
      return preferredLocations.first;
    } else if (preferredLocations.length <= 3) {
      return preferredLocations.join('، ');
    } else {
      return '${preferredLocations.take(2).join('، ')} و ${preferredLocations.length - 2} أخرى';
    }
  }

  /// الحصول على لون الأولوية
  String get priorityColor {
    switch (priority) {
      case 5:
        return '#F44336'; // أحمر - أولوية عالية جداً
      case 4:
        return '#FF9800'; // برتقالي - أولوية عالية
      case 3:
        return '#FFC107'; // أصفر - أولوية متوسطة
      case 2:
        return '#4CAF50'; // أخضر - أولوية منخفضة
      case 1:
        return '#9E9E9E'; // رمادي - أولوية منخفضة جداً
      default:
        return '#FFC107'; // أصفر - افتراضي
    }
  }

  /// الحصول على نص الأولوية
  String get priorityText {
    switch (priority) {
      case 5:
        return 'عالية جداً';
      case 4:
        return 'عالية';
      case 3:
        return 'متوسطة';
      case 2:
        return 'منخفضة';
      case 1:
        return 'منخفضة جداً';
      default:
        return 'متوسطة';
    }
  }

  /// التحقق من كون المفضلة حديثة (أقل من أسبوع)
  bool get isRecent {
    return DateTime.now().difference(addedAt).inDays <= 7;
  }

  /// التحقق من كون المفضلة قديمة (أكثر من شهر)
  bool get isOld {
    return DateTime.now().difference(addedAt).inDays > 30;
  }

  /// الحصول على عمر المفضلة بالنص
  String get ageText {
    final difference = DateTime.now().difference(addedAt);
    
    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return 'منذ ${difference.inMinutes} دقيقة';
      } else {
        return 'منذ ${difference.inHours} ساعة';
      }
    } else if (difference.inDays == 1) {
      return 'منذ يوم واحد';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return weeks == 1 ? 'منذ أسبوع واحد' : 'منذ $weeks أسابيع';
    } else {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? 'منذ شهر واحد' : 'منذ $months أشهر';
    }
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        requestId,
        requestTitle,
        requestDescription,
        propertyType,
        preferredLocations,
        minPrice,
        maxPrice,
        requestOwnerId,
        requestOwnerName,
        requestOwnerImage,
        addedAt,
        updatedAt,
        isNotificationEnabled,
        personalNotes,
        customTags,
        priority,
        isRecentlyViewed,
        viewCount,
      ];

  @override
  String toString() {
    return 'FavoritePropertyRequestModel(id: $id, requestTitle: $requestTitle, propertyType: $propertyType)';
  }
}
