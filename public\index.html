<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Krea - تطبيق العقارات الأول في الكويت</title>

    <!-- Meta Tags for SEO -->
    <meta name="description" content="Krea - تطبيق العقارات الأول في الكويت. اكتشف أفضل العقارات للبيع والإيجار.">
    <meta name="keywords" content="عقارات الكويت، شقق للبيع، فلل للإيجار، عقارات، كريا، Krea">
    <meta name="author" content="Codnet Moroccan">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Krea - تطبيق العقارات الأول في الكويت">
    <meta property="og:description" content="اكتشف أفضل العقارات في الكويت مع تطبيق Krea">
    <meta property="og:image" content="https://real-estate-998a9.web.app/icons/Icon-512.png">
    <meta property="og:url" content="https://real-estate-998a9.web.app">
    <meta property="og:type" content="website">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="favicon.png">
    <link rel="apple-touch-icon" href="icons/Icon-192.png">

    <!-- Google Fonts - Cairo -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-nav {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-small {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
        }

        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
        }

        .nav-links {
            display: flex;
            gap: 30px;
            align-items: center;
        }

        .nav-link {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 24px;
            color: #2c3e50;
            cursor: pointer;
        }

        /* Main Content */
        .main-content {
            margin-top: 80px;
            padding: 60px 20px;
        }

        .hero-section {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
            padding: 80px 0;
        }

        .logo-main {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 30px;
            margin: 0 auto 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
            font-weight: bold;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
        }

        .hero-title {
            color: white;
            margin-bottom: 20px;
            font-size: 48px;
            font-weight: 700;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 50px;
            font-size: 20px;
            line-height: 1.6;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .download-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 60px;
        }

        .download-btn {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 18px 35px;
            background: rgba(255, 255, 255, 0.95);
            color: #2c3e50;
            text-decoration: none;
            border-radius: 15px;
            font-weight: 600;
            font-size: 18px;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }

        .download-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
            background: white;
        }

        .download-btn.primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .download-btn.primary:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a4190);
        }

        /* Features Section */
        .features-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 30px;
            padding: 60px 40px;
            margin: 40px auto;
            max-width: 1000px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        }

        .features-title {
            text-align: center;
            color: #2c3e50;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 50px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }

        .feature-card {
            text-align: center;
            padding: 30px 20px;
            border-radius: 20px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            color: white;
        }

        .feature-title {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .feature-description {
            color: #6c757d;
            font-size: 16px;
        }

        /* Links Section */
        .links-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 30px;
            padding: 60px 40px;
            margin: 40px auto;
            max-width: 1000px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        }

        .links-title {
            text-align: center;
            color: #2c3e50;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 50px;
        }

        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }

        .link-card {
            text-align: center;
            padding: 30px 20px;
            border-radius: 20px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }

        .link-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }

        .link-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            color: white;
        }

        .link-icon.referral {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .link-icon.privacy {
            background: linear-gradient(135deg, #4CAF50, #45a049);
        }

        .link-icon.terms {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .link-icon.delete {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .link-title {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .link-description {
            color: #6c757d;
            font-size: 16px;
        }

        /* Footer */
        .footer {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 40px 20px;
            text-align: center;
            color: #6c757d;
            margin-top: 40px;
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }

            .hero-title {
                font-size: 36px;
            }

            .hero-subtitle {
                font-size: 18px;
            }

            .download-buttons {
                flex-direction: column;
                align-items: center;
            }

            .features-section,
            .links-section {
                padding: 40px 20px;
                margin: 20px;
            }

            .features-title,
            .links-title {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <div class="logo-nav">
                <div class="logo-small">K</div>
                <div class="logo-text">Krea</div>
            </div>
            <nav class="nav-links">
                <a href="#features" class="nav-link">المميزات</a>
                <a href="#links" class="nav-link">الروابط المهمة</a>
                <a href="referral.html" class="nav-link">الإحالة</a>
                <a href="#download" class="nav-link">تحميل التطبيق</a>
            </nav>
            <button class="mobile-menu-btn">☰</button>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="hero-section" id="home">
            <div class="logo-main">K</div>
            <h1 class="hero-title">KREA</h1>
            <p class="hero-subtitle">
                تطبيق العقارات الأول في الكويت<br>
                اكتشف أفضل العقارات للبيع والإيجار مع أحدث التقنيات
            </p>

            <div class="download-buttons" id="download">
                <a href="#" class="download-btn primary" onclick="downloadApp()">
                    📱 تحميل التطبيق
                </a>
                <a href="referral.html" class="download-btn">
                    🎁 رابط الإحالة
                </a>
            </div>
        </section>

        <!-- Features Section -->
        <section class="features-section" id="features">
            <h2 class="features-title">مميزات التطبيق</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🏠</div>
                    <h3 class="feature-title">آلاف العقارات</h3>
                    <p class="feature-description">اكتشف مجموعة واسعة من العقارات للبيع والإيجار في جميع أنحاء الكويت</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔍</div>
                    <h3 class="feature-title">بحث ذكي</h3>
                    <p class="feature-description">فلاتر متقدمة وبحث ذكي للعثور على العقار المثالي بسهولة</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3 class="feature-title">واجهة حديثة</h3>
                    <p class="feature-description">تصميم عصري وسهل الاستخدام مع تجربة مستخدم متميزة</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⭐</div>
                    <h3 class="feature-title">نظام النقاط</h3>
                    <p class="feature-description">اكسب نقاط ومكافآت مع كل استخدام واستفد من العروض الحصرية</p>
                </div>
            </div>
        </section>

        <!-- Links Section -->
        <section class="links-section" id="links">
            <h2 class="links-title">الروابط المهمة</h2>
            <div class="links-grid">
                <a href="referral.html" class="link-card">
                    <div class="link-icon referral">🎁</div>
                    <h3 class="link-title">نظام الإحالة</h3>
                    <p class="link-description">ادع أصدقاءك واحصل على مكافآت ونقاط مجانية</p>
                </a>
                <a href="privacy-policy.html" class="link-card">
                    <div class="link-icon privacy">🔒</div>
                    <h3 class="link-title">سياسة الخصوصية</h3>
                    <p class="link-description">تعرف على كيفية حماية وإدارة بياناتك الشخصية</p>
                </a>
                <a href="terms-of-service.html" class="link-card">
                    <div class="link-icon terms">📋</div>
                    <h3 class="link-title">شروط الاستخدام</h3>
                    <p class="link-description">اقرأ شروط وأحكام استخدام تطبيق Krea</p>
                </a>
                <a href="delete-account.html" class="link-card">
                    <div class="link-icon delete">🗑️</div>
                    <h3 class="link-title">حذف الحساب</h3>
                    <p class="link-description">طلب حذف حسابك وجميع البيانات المرتبطة به</p>
                </a>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <p>© 2025 Krea. جميع الحقوق محفوظة.</p>
        <p>تطبيق Krea - منصة العقارات الرائدة في دولة الكويت</p>
        <p>تطوير: Codnet Moroccan</p>
    </footer>

    <script>
        // تحميل التطبيق
        function downloadApp() {
            const userAgent = navigator.userAgent || navigator.vendor || window.opera;

            if (/android/i.test(userAgent)) {
                // Android - توجيه إلى Google Play Store
                window.open('https://play.google.com/store/apps/details?id=com.codnet.krea', '_blank');
            } else if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
                // iOS - توجيه إلى App Store
                window.open('https://apps.apple.com/app/krea/id123456789', '_blank');
            } else {
                // أجهزة أخرى - عرض رسالة
                alert('يرجى تحميل تطبيق Krea من متجر التطبيقات على هاتفك المحمول');
            }
        }

        // التنقل السلس
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثير التنقل السلس للروابط
            const navLinks = document.querySelectorAll('a[href^="#"]');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // إضافة تأثير الشفافية للهيدر عند التمرير
            window.addEventListener('scroll', function() {
                const header = document.querySelector('.header');
                if (window.scrollY > 100) {
                    header.style.background = 'rgba(255, 255, 255, 0.98)';
                } else {
                    header.style.background = 'rgba(255, 255, 255, 0.95)';
                }
            });
        });
    </script>
</body>
</html>
