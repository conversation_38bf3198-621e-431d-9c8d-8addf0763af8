// lib/core/services/performance_optimization_service.dart
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// خدمة تحسين الأداء
class PerformanceOptimizationService {
  static const String _cachePrefix = 'cache_';
  static const int _maxCacheAge = 24 * 60 * 60 * 1000; // 24 ساعة بالميلي ثانية
  static const int _defaultPageSize = 10;
  static const int _preloadThreshold = 3;

  /// ضغط الصور
  static Future<File?> compressImage(
    File imageFile, {
    int quality = 85,
    int maxWidth = 1920,
    int maxHeight = 1080,
  }) async {
    try {
      final String targetPath = imageFile.path.replaceAll('.jpg', '_compressed.jpg');
      
      final XFile? compressedFile = await FlutterImageCompress.compressAndGetFile(
        imageFile.absolute.path,
        targetPath,
        quality: quality,
        minWidth: maxWidth,
        minHeight: maxHeight,
        format: CompressFormat.jpeg,
      );

      return compressedFile != null ? File(compressedFile.path) : null;
    } catch (e) {
      debugPrint('خطأ في ضغط الصورة: $e');
      return imageFile; // إرجاع الصورة الأصلية في حالة الخطأ
    }
  }

  /// ضغط صور متعددة
  static Future<List<File>> compressMultipleImages(
    List<File> imageFiles, {
    int quality = 85,
    int maxWidth = 1920,
    int maxHeight = 1080,
  }) async {
    final List<File> compressedFiles = [];
    
    for (final imageFile in imageFiles) {
      final compressedFile = await compressImage(
        imageFile,
        quality: quality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );
      
      if (compressedFile != null) {
        compressedFiles.add(compressedFile);
      }
    }
    
    return compressedFiles;
  }

  /// حفظ البيانات في التخزين المؤقت
  static Future<void> cacheData(String key, Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '$_cachePrefix$key';
      final cacheData = {
        'data': data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      
      await prefs.setString(cacheKey, cacheData.toString());
    } catch (e) {
      debugPrint('خطأ في حفظ البيانات في التخزين المؤقت: $e');
    }
  }

  /// استرجاع البيانات من التخزين المؤقت
  static Future<Map<String, dynamic>?> getCachedData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '$_cachePrefix$key';
      final cachedString = prefs.getString(cacheKey);
      
      if (cachedString == null) return null;
      
      // تحويل النص إلى Map (يحتاج تحسين للتحويل الآمن)
      // هذا مثال مبسط - في التطبيق الحقيقي يجب استخدام JSON
      
      return null; // مؤقتاً
    } catch (e) {
      debugPrint('خطأ في استرجاع البيانات من التخزين المؤقت: $e');
      return null;
    }
  }

  /// مسح التخزين المؤقت المنتهي الصلاحية
  static Future<void> clearExpiredCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      
      for (final key in keys) {
        if (key.startsWith(_cachePrefix)) {
          final cachedString = prefs.getString(key);
          if (cachedString != null) {
            // فحص انتهاء الصلاحية وحذف البيانات المنتهية
            // تحتاج تحسين للتحويل الآمن
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ في مسح التخزين المؤقت المنتهي الصلاحية: $e');
    }
  }

  /// تحسين استعلامات Firestore
  static Query optimizeFirestoreQuery(
    Query baseQuery, {
    int? limit,
    DocumentSnapshot? startAfter,
    bool enablePersistence = true,
  }) {
    Query optimizedQuery = baseQuery;
    
    // تطبيق الحد الأقصى
    if (limit != null) {
      optimizedQuery = optimizedQuery.limit(limit);
    } else {
      optimizedQuery = optimizedQuery.limit(_defaultPageSize);
    }
    
    // التحميل التدريجي
    if (startAfter != null) {
      optimizedQuery = optimizedQuery.startAfterDocument(startAfter);
    }
    
    return optimizedQuery;
  }

  /// تحسين التحميل التدريجي
  static bool shouldLoadMore(
    int currentIndex,
    int totalItems,
    int threshold,
  ) {
    return currentIndex >= totalItems - (threshold + 1);
  }

  /// حساب حجم الصفحة الديناميكي
  static int calculateDynamicPageSize({
    required int screenHeight,
    required int itemHeight,
    int minPageSize = 5,
    int maxPageSize = 20,
  }) {
    final itemsPerScreen = (screenHeight / itemHeight).ceil();
    final dynamicPageSize = (itemsPerScreen * 1.5).ceil(); // تحميل 1.5 شاشة
    
    return dynamicPageSize.clamp(minPageSize, maxPageSize);
  }

  /// تحسين الذاكرة
  static void optimizeMemory() {
    // تنظيف الذاكرة
    if (!kDebugMode) {
      // في وضع الإنتاج فقط
      System.gc(); // طلب تنظيف الذاكرة (Android)
    }
  }

  /// مراقبة الأداء
  static void trackPerformance(String operation, Duration duration) {
    // يمكن إضافة تتبع الأداء هنا (Firebase Performance, Analytics, etc.)
  }

  /// تحسين التحميل الكسول للصور
  static Widget buildOptimizedImage(
    String imageUrl, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    // استخدام مكتبة cached_network_image مع تحسينات
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(8),
      ),
      child: placeholder ?? const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// تحسين البحث
  static List<String> optimizeSearchKeywords(String query) {
    // تنظيف وتحسين كلمات البحث
    final cleanQuery = query.trim().toLowerCase();
    
    // إزالة الكلمات الشائعة
    final stopWords = ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك'];
    
    final words = cleanQuery.split(' ')
        .where((word) => word.length > 2 && !stopWords.contains(word))
        .toList();
    
    return words;
  }

  /// تحسين التحديثات المباشرة
  static Stream<T> optimizeRealtimeUpdates<T>(
    Stream<T> originalStream, {
    Duration throttleDuration = const Duration(milliseconds: 500),
  }) {
    // تقليل تكرار التحديثات
    return originalStream.distinct().throttle(throttleDuration);
  }

  /// تحسين حجم البيانات المنقولة
  static Map<String, dynamic> optimizeDataTransfer(Map<String, dynamic> data) {
    final optimizedData = <String, dynamic>{};
    
    for (final entry in data.entries) {
      final value = entry.value;
      
      // تجاهل القيم الفارغة أو null
      if (value != null && value != '' && value != []) {
        optimizedData[entry.key] = value;
      }
    }
    
    return optimizedData;
  }

  /// تحسين الاستعلامات المركبة
  static Future<List<T>> optimizeCompoundQueries<T>(
    List<Future<List<T>>> queries, {
    int maxConcurrency = 3,
  }) async {
    final results = <T>[];
    
    // تنفيذ الاستعلامات بشكل متوازي مع حد أقصى للتزامن
    for (int i = 0; i < queries.length; i += maxConcurrency) {
      final batch = queries.skip(i).take(maxConcurrency);
      final batchResults = await Future.wait(batch);
      
      for (final result in batchResults) {
        results.addAll(result);
      }
    }
    
    return results;
  }

  /// تحسين التخزين المحلي
  static Future<void> optimizeLocalStorage() async {
    try {
      // مسح البيانات المؤقتة القديمة
      await clearExpiredCache();
      
      // تحسين قاعدة البيانات المحلية
      // يمكن إضافة تحسينات أخرى هنا
      
    } catch (e) {
      // خطأ في تحسين التخزين المحلي
    }
  }
}

/// امتداد للتحكم في معدل التحديثات
extension StreamThrottle<T> on Stream<T> {
  Stream<T> throttle(Duration duration) {
    T? lastValue;
    DateTime? lastEmission;
    
    return where((value) {
      final now = DateTime.now();
      final shouldEmit = lastEmission == null || 
          now.difference(lastEmission!).compareTo(duration) >= 0;
      
      if (shouldEmit) {
        lastValue = value;
        lastEmission = now;
        return true;
      }
      
      return false;
    });
  }
}
