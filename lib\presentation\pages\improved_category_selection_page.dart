// lib/presentation/pages/improved_category_selection_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:kuwait_corners/core/config/app_mode_config.dart';
import 'package:kuwait_corners/core/services/enhanced_ad_draft_service.dart';
import 'package:kuwait_corners/core/services/enhanced_subscription_service.dart';
import 'package:kuwait_corners/core/services/category_count_service.dart';
import 'package:kuwait_corners/domain/entities/estate.dart';
import 'package:kuwait_corners/presentation/bloc/improved_ad_bloc.dart';
import 'package:kuwait_corners/presentation/pages/improved_media_upload_page.dart';
import 'package:kuwait_corners/presentation/pages/in_flow_upgrade_page.dart';
import 'package:kuwait_corners/presentation/widgets/ad_creation_navigation_buttons.dart';
import 'package:kuwait_corners/presentation/widgets/improved_ad_creation_progress.dart';

/// صفحة اختيار التصنيف المحسنة
/// تجمع بين اختيار التصنيف الرئيسي والفرعي في صفحة واحدة
class ImprovedCategorySelectionPage extends StatefulWidget {
  const ImprovedCategorySelectionPage({super.key});

  @override
  State<ImprovedCategorySelectionPage> createState() =>
      _ImprovedCategorySelectionPageState();
}

class _ImprovedCategorySelectionPageState
    extends State<ImprovedCategorySelectionPage>
    with SingleTickerProviderStateMixin {
  // خدمة المسودات المحسنة
  final _draftService = EnhancedAdDraftService();

  // خدمة الاشتراكات
  final _subscriptionService = EnhancedSubscriptionService();

  // متغيرات التصنيف
  String _selectedMainCategory = "";
  String _selectedSubCategory = "";

  // متغيرات العرض
  late TabController _tabController;
  bool _showSubCategories = false;

  // متغيرات التحقق من الصلاحيات
  bool _isCheckingPermissions = true;
  bool _canCreateAd = false;
  int _remainingAds = 0;

  // خدمة حساب الأعداد
  final CategoryCountService _countService = CategoryCountService();

  // خريطة لتخزين الأعداد الحقيقية
  Map<String, int> _realCounts = {};

  // قائمة التصنيفات الرئيسية
  final List<Map<String, dynamic>> _mainCategories = [
    {
      "id": "sale",
      "title": "عقار للبيع",
      "icon": Icons.sell,
      "color": Colors.blue,
      "description": "اختر هذا القسم إذا كنت ترغب في بيع عقار",
    },
    {
      "id": "rent",
      "title": "عقار للايجار",
      "icon": Icons.home,
      "color": Colors.green,
      "description": "اختر هذا القسم إذا كنت ترغب في تأجير عقار",
    },
    {
      "id": "swap",
      "title": "عقار للبدل",
      "icon": Icons.swap_horiz,
      "color": Colors.orange,
      "description": "اختر هذا القسم إذا كنت ترغب في مبادلة عقار",
    },
    {
      "id": "international",
      "title": "عقار دولي",
      "icon": Icons.public,
      "color": Colors.purple,
      "description": "اختر هذا القسم للعقارات خارج الكويت",
    },
    {
      "id": "manage",
      "title": "ادارة أملاك الغير",
      "icon": Icons.business,
      "color": Colors.teal,
      "description": "اختر هذا القسم لخدمات إدارة العقارات",
    },
  ];

  // قواميس التصنيفات الفرعية
  final Map<String, List<Map<String, dynamic>>> _subCategories = {
    "sale": [
      {"id": "house", "title": "بيت للبيع", "icon": Icons.home, "count": 450},
      {
        "id": "building",
        "title": "عمارة او ادوار",
        "icon": Icons.apartment,
        "count": 320
      },
      {
        "id": "apartment",
        "title": "شقة للبيع",
        "icon": Icons.meeting_room,
        "count": 580
      },
      {
        "id": "farm",
        "title": "مزارع للبيع",
        "icon": Icons.nature,
        "count": 120
      },
      {
        "id": "land",
        "title": "اراضي للبيع",
        "icon": Icons.landscape,
        "count": 290
      },
      {
        "id": "commercial",
        "title": "محل للبيع",
        "icon": Icons.store,
        "count": 180
      },
    ],
    "rent": [
      {
        "id": "house_rent",
        "title": "بيت للايجار",
        "icon": Icons.home,
        "count": 380
      },
      {"id": "floor", "title": "دور كامل", "icon": Icons.layers, "count": 240},
      {
        "id": "furnished",
        "title": "شقة مفروشة للايجار",
        "icon": Icons.chair,
        "count": 420
      },
      {
        "id": "apartment_rent",
        "title": "شقة للايجار",
        "icon": Icons.meeting_room,
        "count": 650
      },
      {
        "id": "office",
        "title": "مكتب",
        "icon": Icons.business_center,
        "count": 150
      },
      {
        "id": "store",
        "title": "محل للايجار",
        "icon": Icons.store,
        "count": 210
      },
    ],
    "swap": [
      {
        "id": "residential",
        "title": "قسائم سكنية",
        "icon": Icons.home_work,
        "count": 180
      },
      {
        "id": "houses",
        "title": "منازل سكنية",
        "icon": Icons.home,
        "count": 220
      },
      {
        "id": "housing",
        "title": "طلبات اسكانية",
        "icon": Icons.assignment,
        "count": 150
      },
      {
        "id": "commercial_land",
        "title": "اراضي تجارية",
        "icon": Icons.business,
        "count": 90
      },
    ],
    "international": [
      {"id": "saudi", "title": "السعودية", "icon": Icons.flag, "count": 280},
      {"id": "turkey", "title": "تركيا", "icon": Icons.flag, "count": 350},
      {
        "id": "uae",
        "title": "الامارات العربية المتحدة",
        "icon": Icons.flag,
        "count": 220
      },
      {"id": "egypt", "title": "مصر", "icon": Icons.flag, "count": 190},
      {"id": "morocco", "title": "المغرب", "icon": Icons.flag, "count": 120},
      {"id": "other", "title": "دول اخرى", "icon": Icons.flag, "count": 180},
    ],
    "manage": [
      {
        "id": "property_management",
        "title": "إدارة عقارات",
        "icon": Icons.business,
        "count": 120
      },
      {
        "id": "maintenance",
        "title": "صيانة عقارات",
        "icon": Icons.build,
        "count": 90
      },
      {
        "id": "consulting",
        "title": "استشارات عقارية",
        "icon": Icons.support_agent,
        "count": 70
      },
    ],
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _mainCategories.length, vsync: this);
    _tabController.addListener(_handleTabChange);

    // تعيين التصنيف الرئيسي الافتراضي وإظهار التصنيفات الفرعية
    setState(() {
      _selectedMainCategory = _mainCategories[0]['id'] as String;
      _showSubCategories = true;
    });

    // استرجاع آخر مسودة
    _loadLastDraft();

    // التحقق من صلاحيات المستخدم
    _checkPermissions();

    // تحميل الأعداد الحقيقية
    _loadRealCounts();
  }

  /// التحقق من صلاحيات المستخدم - معطل في الوضع المعلوماتي
  Future<void> _checkPermissions() async {
    setState(() {
      _isCheckingPermissions = true;
    });

    // في الوضع المعلوماتي، السماح بإنشاء الإعلانات بدون قيود
    if (AppModeConfig.isInformationalOnly) {
      setState(() {
        _canCreateAd = true;
        _remainingAds = 999; // عدد غير محدود
        _isCheckingPermissions = false;
      });
      return;
    }

    try {
      // التحقق مما إذا كان المستخدم يمكنه إنشاء إعلان
      final canCreate = await _subscriptionService.canCreateAd(null);

      // الحصول على عدد الإعلانات المتبقية
      final remaining = await _subscriptionService.getRemainingAds(null);

      setState(() {
        _canCreateAd = canCreate;
        _remainingAds = remaining;
        _isCheckingPermissions = false;
      });

      // إذا لم يكن المستخدم يمكنه إنشاء إعلان، عرض صفحة الترقية
      if (!canCreate && mounted) {
        _showUpgradeDialog();
      }
    } catch (e) {
      setState(() {
        _isCheckingPermissions = false;
        _canCreateAd = true; // السماح بالإنشاء في حالة الخطأ
      });
    }
  }

  /// عرض حوار الترقية
  void _showUpgradeDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(
          "لا يمكن إنشاء إعلان جديد",
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold)),
        content: Text(
          "لقد وصلت إلى الحد الأقصى لعدد الإعلانات المسموح بها في باقتك الحالية. يرجى ترقية باقتك للاستمرار.",
          style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: Text(
              "إلغاء",
              style: GoogleFonts.cairo())),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _navigateToUpgrade();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white),
            child: Text(
              "ترقية الباقة",
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.bold))),
        ]));
  }

  /// الانتقال إلى صفحة الترقية
  void _navigateToUpgrade() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InFlowUpgradePage(
          onUpgradeComplete: () {
            // إعادة التحقق من الصلاحيات بعد الترقية
            _checkPermissions();
            Navigator.pop(context);
          },
          onCancel: () {
            Navigator.pop(context);
            Navigator.pop(context);
          })));
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل الأعداد الحقيقية للعقارات
  Future<void> _loadRealCounts() async {
    try {
      // تحميل أعداد جميع التصنيفات الفرعية
      for (final mainCategoryEntry in _subCategories.entries) {
        final mainCategoryId = mainCategoryEntry.key;
        final subCategories = mainCategoryEntry.value;

        // تحويل معرف التصنيف الرئيسي إلى اسم
        final mainCategoryName = _getMainCategoryName(mainCategoryId);

        for (final subCategory in subCategories) {
          final subCategoryTitle = subCategory['title'] as String;
          final count = await _countService.getSubCategoryCount(
            mainCategoryName,
            subCategoryTitle
          );

          final key = '${mainCategoryId}_${subCategory['id']}';
          _realCounts[key] = count;
        }
      }

      // تحديث الواجهة
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الأعداد الحقيقية: $e');
    }
  }

  /// الحصول على اسم التصنيف الرئيسي من المعرف
  String _getMainCategoryName(String categoryId) {
    switch (categoryId) {
      case 'sale':
        return 'عقار للبيع';
      case 'rent':
        return 'عقار للايجار';
      case 'swap':
        return 'عقار للبدل';
      case 'international':
        return 'عقار دولي';
      case 'manage':
        return 'ادارة أملاك الغير';
      default:
        return categoryId;
    }
  }

  /// الحصول على العدد الحقيقي للتصنيف الفرعي
  int _getRealCount(Map<String, dynamic> subCategory) {
    final key = '${_selectedMainCategory}_${subCategory['id']}';
    return _realCounts[key] ?? (subCategory['count'] as int? ?? 0);
  }

  /// تحميل آخر مسودة
  Future<void> _loadLastDraft() async {
    final lastDraft = await _draftService.getLastDraft();
    if (lastDraft != null && mounted) {
      if (lastDraft.containsKey('mainCategory') &&
          lastDraft.containsKey('subCategory')) {
        final mainCategory = lastDraft['mainCategory'] as String;
        final subCategory = lastDraft['subCategory'] as String;

        if (mainCategory.isNotEmpty) {
          // البحث عن فهرس التصنيف الرئيسي
          final mainIndex =
              _mainCategories.indexWhere((cat) => cat['title'] == mainCategory);
          if (mainIndex != -1) {
            setState(() {
              _selectedMainCategory =
                  _mainCategories[mainIndex]['id'] as String;
              _tabController.animateTo(mainIndex);

              if (subCategory.isNotEmpty) {
                _selectedSubCategory = subCategory;
                _showSubCategories = true;
              }
            });
          }
        }
      }
    }
  }

  /// معالجة تغيير التاب
  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        _selectedMainCategory =
            _mainCategories[_tabController.index]['id'] as String;
        _selectedSubCategory = "";
        _showSubCategories = true;
      });
    }
  }

  /// اختيار التصنيف الفرعي والانتقال للخطوة التالية
  Future<void> _selectSubCategory(String subCategoryTitle) async {
    // التحقق من صلاحيات المستخدم قبل المتابعة
    if (!_canCreateAd) {
      _showUpgradeDialog();
      return;
    }

    final mainCategoryTitle = _mainCategories.firstWhere(
      (cat) => cat['id'] == _selectedMainCategory,
      orElse: () => {"title": ""})['title'] as String;

    // حفظ التصنيفات في BLoC
    context.read<ImprovedAdBloc>().add(SetMainCategory(mainCategoryTitle));
    context.read<ImprovedAdBloc>().add(SetSubCategory(subCategoryTitle));

    // حفظ المسودة مع معلومات إضافية
    _draftService.autoSaveDraft({
      'mainCategory': mainCategoryTitle,
      'subCategory': subCategoryTitle,
      'step': 1,
      'title': 'مسودة $mainCategoryTitle - $subCategoryTitle',
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });

    // إنشاء كائن Estate فارغ
    final emptyEstate = Estate.empty();

    // الانتقال إلى صفحة رفع الصور
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => ImprovedMediaUploadPage(estate: emptyEstate)));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      // مؤشر التقدم أسفل الصفحة
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أزرار التنقل
          AdCreationNavigationButtons(
            onNext: () {
              if (_selectedSubCategory.isNotEmpty) {
                _selectSubCategory(_selectedSubCategory);
              } else {
                // عرض رسالة تنبيه إذا لم يتم اختيار تصنيف فرعي
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      "يرجى اختيار تصنيف فرعي للمتابعة",
                      style: GoogleFonts.cairo()),
                    backgroundColor: Colors.red.shade700));
              }
            },
            onBack: () => Navigator.pop(context),
            nextText: "متابعة",
            backText: "العودة",
            isNextDisabled: _selectedSubCategory.isEmpty),

          // مؤشر التقدم
          ImprovedAdCreationProgress(
            currentStep: 1,
            allowNavigation: false),
        ]),
      body: SafeArea(
        child: Column(
          children: [
            // شريط العنوان
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.black87),
                    onPressed: () => Navigator.of(context).pop()),
                  const SizedBox(width: 8),
                  Text(
                    "اختيار تصنيف العقار",
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87)),
                ])),

            // شريط التاب للتصنيفات الرئيسية
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 5,
                    offset: const Offset(0, 2)),
                ]),
              child: TabBar(
                controller: _tabController,
                isScrollable: true,
                labelColor: Theme.of(context).primaryColor,
                unselectedLabelColor: Colors.grey,
                indicatorColor: Theme.of(context).primaryColor,
                tabs: _mainCategories.map((category) {
                  return Tab(
                    icon: Icon(category['icon'] as IconData),
                    text: category['title'] as String);
                }).toList())),

            // وصف التصنيف المختار
            if (_selectedMainCategory.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  _mainCategories.firstWhere(
                    (cat) => cat['id'] == _selectedMainCategory,
                    orElse: () => {"description": ""})['description'] as String,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey.shade600),
                  textAlign: TextAlign.center)),

            // قائمة التصنيفات الفرعية
            if (_showSubCategories && _selectedMainCategory.isNotEmpty)
              Expanded(
                child: GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 1.5,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16),
                  itemCount: _subCategories[_selectedMainCategory]?.length ?? 0,
                  itemBuilder: (context, index) {
                    final subCategory =
                        _subCategories[_selectedMainCategory]![index];
                    final isSelected =
                        _selectedSubCategory == subCategory['title'];

                    return GestureDetector(
                      onTap: () =>
                          _selectSubCategory(subCategory['title'] as String),
                      child: Container(
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Theme.of(context).primaryColor.withOpacity(0.1)
                              : Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelected
                                ? Theme.of(context).primaryColor
                                : Colors.grey.shade300,
                            width: isSelected ? 2 : 1),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 5,
                              offset: const Offset(0, 2)),
                          ]),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              subCategory['icon'] as IconData,
                              color: isSelected
                                  ? Theme.of(context).primaryColor
                                  : Colors.grey.shade600,
                              size: 32),
                            const SizedBox(height: 8),
                            Text(
                              subCategory['title'] as String,
                              style: GoogleFonts.cairo(
                                fontSize: 14,
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                color: isSelected
                                    ? Theme.of(context).primaryColor
                                    : Colors.black87),
                              textAlign: TextAlign.center),
                            Text(
                              "${_getRealCount(subCategory)} عقار",
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: Colors.grey.shade600)),
                          ])));
                  })),
          ])));
  }
}
