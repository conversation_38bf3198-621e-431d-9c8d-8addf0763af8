import 'dart:io';
import 'dart:developer' as developer;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:uuid/uuid.dart';

import '../../domain/models/forum/category_model.dart';
import '../../domain/models/forum/topic_model.dart';
import '../../domain/models/forum/post_model.dart';
import '../../domain/models/forum/user_statistics_model.dart';
import '../../domain/models/forum/notification_model.dart';
import '../../domain/models/forum/reaction_model.dart';
import '../../domain/models/forum/poll_model.dart';
import '../../domain/models/forum/poll_option_model.dart';
import '../../domain/repositories/lobby_repository.dart';

/// تنفيذ مستودع اللوبي
class LobbyRepositoryImpl implements LobbyRepository {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;
  final Uuid _uuid = const Uuid();

  LobbyRepositoryImpl({
    required FirebaseFirestore firestore,
    required FirebaseStorage storage,
  })  : _firestore = firestore,
        _storage = storage;

  /// رفع صور إلى Firebase Storage
  Future<List<String>> _uploadImages(List<File> images, String path) async {
    final List<String> imageUrls = [];

    try {
      for (final image in images) {
        try {
          // التحقق من وجود الملف
          if (!await image.exists()) {
            continue;
          }

          // التحقق من حجم الملف
          final fileSize = await image.length();
          if (fileSize <= 0) {
            continue;
          }

          // إنشاء اسم فريد للملف
          final fileName = '${_uuid.v4()}.jpg';

          // إنشاء مرجع للملف في Firebase Storage
          final ref = _storage.ref().child('$path/$fileName');

          // رفع الملف مع البيانات الوصفية
          final uploadTask = ref.putFile(
            image,
            SettableMetadata(contentType: 'image/jpeg'));

          // انتظار اكتمال الرفع
          final snapshot = await uploadTask.whenComplete(() => null);

          // الحصول على رابط التنزيل
          final url = await snapshot.ref.getDownloadURL();

          // إضافة الرابط إلى القائمة
          imageUrls.add(url);
        } catch (e) {
          developer.log('خطأ في رفع الصورة: $e');
          // الاستمرار مع الصورة التالية
          continue;
        }
      }
    } catch (e) {
      developer.log('خطأ في رفع الصور: $e');
      // إرجاع أي صور تم رفعها بنجاح
    }

    return imageUrls;
  }

  /// حذف صور من Firebase Storage
  Future<void> _deleteImages(List<String> imageUrls) async {
    try {
      for (final url in imageUrls) {
        try {
          // استخراج المسار من URL
          final ref = _storage.refFromURL(url);

          // حذف الملف
          await ref.delete();
        } catch (e) {
          developer.log('خطأ في حذف الصورة: $e');
          // الاستمرار مع الصورة التالية
          continue;
        }
      }
    } catch (e) {
      developer.log('خطأ في حذف الصور: $e');
    }
  }

  // فئات اللوبي

  @override
  Future<List<CategoryModel>> getCategories() async {
    try {
      final snapshot = await _firestore
          .collection('forum_categories')
          .orderBy('order')
          .get();

      return snapshot.docs
          .map((doc) => CategoryModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      developer.log('خطأ في الحصول على فئات اللوبي: $e');
      return [];
    }
  }

  @override
  Future<CategoryModel?> getCategoryById(String categoryId) async {
    try {
      final doc =
          await _firestore.collection('forum_categories').doc(categoryId).get();

      if (!doc.exists) {
        return null;
      }

      return CategoryModel.fromFirestore(doc);
    } catch (e) {
      developer.log('خطأ في الحصول على فئة اللوبي: $e');
      return null;
    }
  }

  @override
  Future<CategoryModel> createCategory(CategoryModel category) async {
    try {
      final docRef = _firestore.collection('forum_categories').doc();
      final newCategory = category.copyWith(
        id: docRef.id,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now());

      await docRef.set(newCategory.toMap());
      return newCategory;
    } catch (e) {
      developer.log('خطأ في إنشاء فئة اللوبي: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateCategory(CategoryModel category) async {
    try {
      await _firestore
          .collection('forum_categories')
          .doc(category.id)
          .update(category.toMap());
    } catch (e) {
      developer.log('خطأ في تحديث فئة اللوبي: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteCategory(String categoryId) async {
    try {
      // حذف جميع المواضيع في الفئة
      final topicsSnapshot = await _firestore
          .collection('forum_topics')
          .where('categoryId', isEqualTo: categoryId)
          .get();

      final batch = _firestore.batch();

      for (final doc in topicsSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // حذف الفئة
      batch.delete(_firestore.collection('forum_categories').doc(categoryId));

      await batch.commit();
    } catch (e) {
      developer.log('خطأ في حذف فئة اللوبي: $e');
      rethrow;
    }
  }

  // مواضيع اللوبي

  @override
  Future<List<TopicModel>> getTopicsByCategory(
    String categoryId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
    String? sortBy,
    bool descending = true,
  }) async {
    try {
      Query query = _firestore
          .collection('forum_topics')
          .where('categoryId', isEqualTo: categoryId);

      // إضافة الترتيب
      if (sortBy != null) {
        query = query.orderBy(sortBy, descending: descending);
      } else {
        query = query.orderBy('createdAt', descending: true);
      }

      // إضافة الحد
      query = query.limit(limit);

      // إضافة نقطة البداية للصفحات
      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      final snapshot = await query.get();
      return snapshot.docs.map((doc) => TopicModel.fromFirestore(doc)).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على مواضيع الفئة: $e');
      return [];
    }
  }

  @override
  Future<List<TopicModel>> getFeaturedTopics({int limit = 5}) async {
    try {
      final query = _firestore
          .collection('forum_topics')
          .where('status', whereIn: [
            TopicStatus.featured.index,
            TopicStatus.pinnedAndFeatured.index
          ])
          .orderBy('createdAt', descending: true)
          .limit(limit);

      final snapshot = await query.get();
      return snapshot.docs.map((doc) => TopicModel.fromFirestore(doc)).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على المواضيع المميزة: $e');
      return [];
    }
  }

  @override
  Future<List<TopicModel>> getPinnedTopics({int limit = 5}) async {
    try {
      final query = _firestore
          .collection('forum_topics')
          .where('status', whereIn: [
            TopicStatus.pinned.index,
            TopicStatus.pinnedAndFeatured.index,
            TopicStatus.closedAndPinned.index
          ])
          .orderBy('createdAt', descending: true)
          .limit(limit);

      final snapshot = await query.get();
      return snapshot.docs.map((doc) => TopicModel.fromFirestore(doc)).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على المواضيع المثبتة: $e');
      return [];
    }
  }

  @override
  Future<List<TopicModel>> getLatestTopics({int limit = 10}) async {
    try {
      final query = _firestore
          .collection('forum_topics')
          .orderBy('createdAt', descending: true)
          .limit(limit);

      final snapshot = await query.get();
      return snapshot.docs.map((doc) => TopicModel.fromFirestore(doc)).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على أحدث المواضيع: $e');
      return [];
    }
  }

  @override
  Future<List<TopicModel>> getMostViewedTopics({int limit = 10}) async {
    try {
      final query = _firestore
          .collection('forum_topics')
          .orderBy('viewsCount', descending: true)
          .limit(limit);

      final snapshot = await query.get();
      return snapshot.docs.map((doc) => TopicModel.fromFirestore(doc)).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على المواضيع الأكثر مشاهدة: $e');
      return [];
    }
  }

  @override
  Future<List<TopicModel>> getMostLikedTopics({int limit = 10}) async {
    try {
      final query = _firestore
          .collection('forum_topics')
          .orderBy('likesCount', descending: true)
          .limit(limit);

      final snapshot = await query.get();
      return snapshot.docs.map((doc) => TopicModel.fromFirestore(doc)).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على المواضيع الأكثر إعجاباً: $e');
      return [];
    }
  }

  @override
  Future<List<TopicModel>> getMostActiveTopics({int limit = 10}) async {
    try {
      final query = _firestore
          .collection('forum_topics')
          .orderBy('repliesCount', descending: true)
          .limit(limit);

      final snapshot = await query.get();
      return snapshot.docs.map((doc) => TopicModel.fromFirestore(doc)).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على المواضيع الأكثر نشاطاً: $e');
      return [];
    }
  }

  @override
  Future<List<TopicModel>> getUserTopics(
    String userId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
    String? sortBy,
    bool descending = true,
  }) async {
    try {
      Query query = _firestore
          .collection('forum_topics')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      final snapshot = await query.get();
      return snapshot.docs.map((doc) => TopicModel.fromFirestore(doc)).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على مواضيع المستخدم: $e');
      return [];
    }
  }

  @override
  Future<TopicModel?> getTopicById(String topicId) async {
    try {
      final doc =
          await _firestore.collection('forum_topics').doc(topicId).get();

      if (!doc.exists) {
        return null;
      }

      return TopicModel.fromFirestore(doc);
    } catch (e) {
      developer.log('خطأ في الحصول على الموضوع: $e');
      return null;
    }
  }

  @override
  Future<TopicModel> createTopic(TopicModel topic, {List<File>? images}) async {
    try {
      // رفع الصور إذا وجدت
      List<String> imageUrls = [];
      if (images != null && images.isNotEmpty) {
        imageUrls = await _uploadImages(images, 'forum/topics/${topic.id}');
      }

      // إنشاء معرف جديد للموضوع
      final docRef = _firestore.collection('forum_topics').doc();

      // إنشاء نسخة جديدة من الموضوع مع المعرف الجديد والصور
      final newTopic = topic.copyWith(
        id: docRef.id,
        images: imageUrls.isNotEmpty ? imageUrls : topic.images,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now());

      // حفظ الموضوع في قاعدة البيانات
      await docRef.set(newTopic.toMap());

      // تحديث عدد المواضيع في الفئة
      await _firestore
          .collection('forum_categories')
          .doc(topic.categoryId)
          .update({
        'topicsCount': FieldValue.increment(1),
        'updatedAt': Timestamp.now(),
        'lastPost': {
          'topicId': newTopic.id,
          'topicTitle': newTopic.title,
          'userId': newTopic.userId,
          'userName': newTopic.userName,
          'userImage': newTopic.userImage,
          'timestamp': Timestamp.now(),
        },
      });

      return newTopic;
    } catch (e) {
      developer.log('خطأ في إنشاء الموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateTopic(TopicModel topic,
      {List<File>? newImages, List<String>? imagesToDelete}) async {
    try {
      // حذف الصور المطلوب حذفها
      if (imagesToDelete != null && imagesToDelete.isNotEmpty) {
        await _deleteImages(imagesToDelete);
      }

      // رفع الصور الجديدة إذا وجدت
      List<String> newImageUrls = [];
      if (newImages != null && newImages.isNotEmpty) {
        newImageUrls =
            await _uploadImages(newImages, 'forum/topics/${topic.id}');
      }

      // دمج الصور الجديدة مع الصور الموجودة
      List<String> updatedImages = [];
      if (topic.images != null) {
        updatedImages = List<String>.from(topic.images!);
      }

      // إزالة الصور المحذوفة
      if (imagesToDelete != null) {
        updatedImages.removeWhere((url) => imagesToDelete.contains(url));
      }

      // إضافة الصور الجديدة
      updatedImages.addAll(newImageUrls);

      // تحديث الموضوع مع الصور المحدثة
      final updatedTopic = topic.copyWith(
        images: updatedImages,
        updatedAt: DateTime.now());

      await _firestore
          .collection('forum_topics')
          .doc(topic.id)
          .update(updatedTopic.toMap());
    } catch (e) {
      developer.log('خطأ في تحديث الموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteTopic(String topicId) async {
    try {
      // الحصول على الموضوع
      final topicDoc =
          await _firestore.collection('forum_topics').doc(topicId).get();
      if (!topicDoc.exists) {
        return;
      }

      final topic = TopicModel.fromFirestore(topicDoc);

      // حذف جميع المشاركات في الموضوع
      final postsSnapshot = await _firestore
          .collection('forum_posts')
          .where('topicId', isEqualTo: topicId)
          .get();

      final batch = _firestore.batch();

      for (final doc in postsSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // حذف الموضوع
      batch.delete(_firestore.collection('forum_topics').doc(topicId));

      await batch.commit();

      // تحديث عدد المواضيع في الفئة
      await _firestore
          .collection('forum_categories')
          .doc(topic.categoryId)
          .update({
        'topicsCount': FieldValue.increment(-1),
        'postsCount': FieldValue.increment(-postsSnapshot.docs.length),
        'updatedAt': Timestamp.now(),
      });

      // حذف الصور المرتبطة بالموضوع
      if (topic.images != null && topic.images!.isNotEmpty) {
        await _deleteImages(topic.images!);
      }
    } catch (e) {
      developer.log('خطأ في حذف الموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<void> incrementTopicViews(String topicId) async {
    try {
      await _firestore.collection('forum_topics').doc(topicId).update({
        'viewsCount': FieldValue.increment(1),
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في زيادة عدد مشاهدات الموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<void> likeTopic(String topicId, String userId) async {
    try {
      final topicRef = _firestore.collection('forum_topics').doc(topicId);
      final topicDoc = await topicRef.get();

      if (!topicDoc.exists) {
        throw Exception('الموضوع غير موجود');
      }

      final topic = TopicModel.fromFirestore(topicDoc);
      final likedBy = List<String>.from(topic.likedBy ?? []);

      if (likedBy.contains(userId)) {
        return; // المستخدم معجب بالفعل
      }

      likedBy.add(userId);

      await topicRef.update({
        'likesCount': FieldValue.increment(1),
        'likedBy': likedBy,
        'updatedAt': Timestamp.now(),
      });

      // تحديث إحصائيات المستخدم
      await updateUserStatistics(userId);

      // إنشاء إشعار للمؤلف إذا كان مختلفاً عن المستخدم الذي أعجب
      if (topic.userId != userId) {
        final userDoc = await _firestore.collection('users').doc(userId).get();
        final userData = userDoc.data();

        if (userData != null) {
          final notification = NotificationModel(
            id: _uuid.v4(),
            type: NotificationType.topicLike,
            recipientId: topic.userId,
            senderId: userId,
            senderName: userData['displayName'] ?? 'مستخدم',
            senderImage: userData['photoURL'],
            title: 'إعجاب بموضوعك',
            body:
                'أعجب ${userData['displayName'] ?? 'مستخدم'} بموضوعك "${topic.title}"',
            itemId: topicId,
            itemType: 'topic',
            itemTitle: topic.title,
            createdAt: DateTime.now());

          await createNotification(notification);
        }
      }
    } catch (e) {
      developer.log('خطأ في الإعجاب بالموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<void> unlikeTopic(String topicId, String userId) async {
    try {
      final topicRef = _firestore.collection('forum_topics').doc(topicId);
      final topicDoc = await topicRef.get();

      if (!topicDoc.exists) {
        throw Exception('الموضوع غير موجود');
      }

      final topic = TopicModel.fromFirestore(topicDoc);
      final likedBy = List<String>.from(topic.likedBy ?? []);

      if (!likedBy.contains(userId)) {
        return; // المستخدم غير معجب بالفعل
      }

      likedBy.remove(userId);

      await topicRef.update({
        'likesCount': FieldValue.increment(-1),
        'likedBy': likedBy,
        'updatedAt': Timestamp.now(),
      });

      // تحديث إحصائيات المستخدم
      await updateUserStatistics(userId);
    } catch (e) {
      developer.log('خطأ في إلغاء الإعجاب بالموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<void> addTopicReaction(
      String topicId, String userId, String reactionType) async {
    try {
      final topicRef = _firestore.collection('forum_topics').doc(topicId);
      final topicDoc = await topicRef.get();

      if (!topicDoc.exists) {
        throw Exception('الموضوع غير موجود');
      }

      final topic = TopicModel.fromFirestore(topicDoc);
      final reactions = Map<String, List<String>>.from(topic.reactions ?? {});

      if (!reactions.containsKey(reactionType)) {
        reactions[reactionType] = [];
      }

      final usersForReaction = List<String>.from(reactions[reactionType]!);

      if (usersForReaction.contains(userId)) {
        return; // المستخدم متفاعل بالفعل بهذا النوع
      }

      usersForReaction.add(userId);
      reactions[reactionType] = usersForReaction;

      await topicRef.update({
        'reactions': reactions,
        'updatedAt': Timestamp.now(),
      });

      // إنشاء إشعار للمؤلف إذا كان مختلفاً عن المستخدم الذي تفاعل
      if (topic.userId != userId) {
        final userDoc = await _firestore.collection('users').doc(userId).get();
        final userData = userDoc.data();

        if (userData != null) {
          final notification = NotificationModel(
            id: _uuid.v4(),
            type: NotificationType.topicReaction,
            recipientId: topic.userId,
            senderId: userId,
            senderName: userData['displayName'] ?? 'مستخدم',
            senderImage: userData['photoURL'],
            title: 'تفاعل مع موضوعك',
            body:
                'تفاعل ${userData['displayName'] ?? 'مستخدم'} مع موضوعك "${topic.title}" بـ $reactionType',
            itemId: topicId,
            itemType: 'topic',
            itemTitle: topic.title,
            data: {'reactionType': reactionType},
            createdAt: DateTime.now());

          await createNotification(notification);
        }
      }

      // تحديث إحصائيات المستخدم
      await updateUserStatistics(userId);
    } catch (e) {
      developer.log('خطأ في إضافة تفاعل للموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<void> removeTopicReaction(
      String topicId, String userId, String reactionType) async {
    try {
      final topicRef = _firestore.collection('forum_topics').doc(topicId);
      final topicDoc = await topicRef.get();

      if (!topicDoc.exists) {
        throw Exception('الموضوع غير موجود');
      }

      final topic = TopicModel.fromFirestore(topicDoc);
      final reactions = Map<String, List<String>>.from(topic.reactions ?? {});

      if (!reactions.containsKey(reactionType)) {
        return; // لا يوجد تفاعلات من هذا النوع
      }

      final usersForReaction = List<String>.from(reactions[reactionType]!);

      if (!usersForReaction.contains(userId)) {
        return; // المستخدم غير متفاعل بهذا النوع
      }

      usersForReaction.remove(userId);

      if (usersForReaction.isEmpty) {
        reactions.remove(reactionType);
      } else {
        reactions[reactionType] = usersForReaction;
      }

      await topicRef.update({
        'reactions': reactions,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في إزالة تفاعل من الموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<void> pinTopic(String topicId) async {
    try {
      final topicRef = _firestore.collection('forum_topics').doc(topicId);
      final topicDoc = await topicRef.get();

      if (!topicDoc.exists) {
        throw Exception('الموضوع غير موجود');
      }

      final topic = TopicModel.fromFirestore(topicDoc);
      TopicStatus newStatus;

      switch (topic.status) {
        case TopicStatus.open:
          newStatus = TopicStatus.pinned;
          break;
        case TopicStatus.featured:
          newStatus = TopicStatus.pinnedAndFeatured;
          break;
        case TopicStatus.closed:
          newStatus = TopicStatus.closedAndPinned;
          break;
        default:
          return; // الموضوع مثبت بالفعل
      }

      await topicRef.update({
        'status': newStatus.index,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في تثبيت الموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<void> unpinTopic(String topicId) async {
    try {
      final topicRef = _firestore.collection('forum_topics').doc(topicId);
      final topicDoc = await topicRef.get();

      if (!topicDoc.exists) {
        throw Exception('الموضوع غير موجود');
      }

      final topic = TopicModel.fromFirestore(topicDoc);
      TopicStatus newStatus;

      switch (topic.status) {
        case TopicStatus.pinned:
          newStatus = TopicStatus.open;
          break;
        case TopicStatus.pinnedAndFeatured:
          newStatus = TopicStatus.featured;
          break;
        case TopicStatus.closedAndPinned:
          newStatus = TopicStatus.closed;
          break;
        default:
          return; // الموضوع غير مثبت
      }

      await topicRef.update({
        'status': newStatus.index,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في إلغاء تثبيت الموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<void> featureTopic(String topicId) async {
    try {
      final topicRef = _firestore.collection('forum_topics').doc(topicId);
      final topicDoc = await topicRef.get();

      if (!topicDoc.exists) {
        throw Exception('الموضوع غير موجود');
      }

      final topic = TopicModel.fromFirestore(topicDoc);
      TopicStatus newStatus;

      switch (topic.status) {
        case TopicStatus.open:
          newStatus = TopicStatus.featured;
          break;
        case TopicStatus.pinned:
          newStatus = TopicStatus.pinnedAndFeatured;
          break;
        default:
          return; // الموضوع مميز بالفعل أو مغلق
      }

      await topicRef.update({
        'status': newStatus.index,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في تمييز الموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<void> unfeatureTopic(String topicId) async {
    try {
      final topicRef = _firestore.collection('forum_topics').doc(topicId);
      final topicDoc = await topicRef.get();

      if (!topicDoc.exists) {
        throw Exception('الموضوع غير موجود');
      }

      final topic = TopicModel.fromFirestore(topicDoc);
      TopicStatus newStatus;

      switch (topic.status) {
        case TopicStatus.featured:
          newStatus = TopicStatus.open;
          break;
        case TopicStatus.pinnedAndFeatured:
          newStatus = TopicStatus.pinned;
          break;
        default:
          return; // الموضوع غير مميز
      }

      await topicRef.update({
        'status': newStatus.index,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في إلغاء تمييز الموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<void> closeTopic(String topicId) async {
    try {
      final topicRef = _firestore.collection('forum_topics').doc(topicId);
      final topicDoc = await topicRef.get();

      if (!topicDoc.exists) {
        throw Exception('الموضوع غير موجود');
      }

      final topic = TopicModel.fromFirestore(topicDoc);
      TopicStatus newStatus;

      switch (topic.status) {
        case TopicStatus.open:
          newStatus = TopicStatus.closed;
          break;
        case TopicStatus.pinned:
          newStatus = TopicStatus.closedAndPinned;
          break;
        default:
          return; // الموضوع مغلق بالفعل أو مميز
      }

      await topicRef.update({
        'status': newStatus.index,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في إغلاق الموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<void> reopenTopic(String topicId) async {
    try {
      final topicRef = _firestore.collection('forum_topics').doc(topicId);
      final topicDoc = await topicRef.get();

      if (!topicDoc.exists) {
        throw Exception('الموضوع غير موجود');
      }

      final topic = TopicModel.fromFirestore(topicDoc);
      TopicStatus newStatus;

      switch (topic.status) {
        case TopicStatus.closed:
          newStatus = TopicStatus.open;
          break;
        case TopicStatus.closedAndPinned:
          newStatus = TopicStatus.pinned;
          break;
        default:
          return; // الموضوع مفتوح بالفعل
      }

      await topicRef.update({
        'status': newStatus.index,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في إعادة فتح الموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<void> markTopicAsSolved(String topicId, String postId) async {
    try {
      await _firestore.collection('forum_topics').doc(topicId).update({
        'isSolved': true,
        'solutionPostId': postId,
        'updatedAt': Timestamp.now(),
      });

      // تعليم المشاركة كأفضل إجابة
      await markPostAsBestAnswer(postId, topicId);
    } catch (e) {
      developer.log('خطأ في تعليم الموضوع كمحلول: $e');
      rethrow;
    }
  }

  @override
  Future<void> unmarkTopicAsSolved(String topicId) async {
    try {
      final topicDoc =
          await _firestore.collection('forum_topics').doc(topicId).get();

      if (!topicDoc.exists) {
        throw Exception('الموضوع غير موجود');
      }

      final topic = TopicModel.fromFirestore(topicDoc);

      // إلغاء تعليم المشاركة كأفضل إجابة
      if (topic.solutionPostId != null) {
        await unmarkPostAsBestAnswer(topic.solutionPostId!, topicId);
      }

      await _firestore.collection('forum_topics').doc(topicId).update({
        'isSolved': false,
        'solutionPostId': null,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في إلغاء تعليم الموضوع كمحلول: $e');
      rethrow;
    }
  }

  // مشاركات اللوبي

  @override
  Future<List<PostModel>> getPostsByTopic(
    String topicId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
    bool includeReplies = true,
  }) async {
    try {
      Query query = _firestore
          .collection('forum_posts')
          .where('topicId', isEqualTo: topicId);

      if (!includeReplies) {
        query = query.where('parentId', isNull: true);
      }

      query = query.orderBy('createdAt', descending: false);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      query = query.limit(limit);

      final snapshot = await query.get();
      return snapshot.docs.map((doc) => PostModel.fromFirestore(doc)).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على مشاركات الموضوع: $e');
      return [];
    }
  }

  @override
  Future<List<PostModel>> getRepliesByPost(
    String postId, {
    int limit = 10,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      Query query = _firestore
          .collection('forum_posts')
          .where('parentId', isEqualTo: postId)
          .orderBy('createdAt', descending: false);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      query = query.limit(limit);

      final snapshot = await query.get();
      return snapshot.docs.map((doc) => PostModel.fromFirestore(doc)).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على ردود المشاركة: $e');
      return [];
    }
  }

  @override
  Future<List<PostModel>> getUserPosts(
    String userId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
    String? sortBy,
    bool descending = true,
  }) async {
    try {
      Query query = _firestore
          .collection('forum_posts')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      query = query.limit(limit);

      final snapshot = await query.get();
      return snapshot.docs.map((doc) => PostModel.fromFirestore(doc)).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على مشاركات المستخدم: $e');
      return [];
    }
  }

  @override
  Future<PostModel?> getPostById(String postId) async {
    try {
      final doc = await _firestore.collection('forum_posts').doc(postId).get();

      if (!doc.exists) {
        return null;
      }

      return PostModel.fromFirestore(doc);
    } catch (e) {
      developer.log('خطأ في الحصول على المشاركة: $e');
      return null;
    }
  }

  @override
  Future<PostModel> createPost(PostModel post, {List<File>? images}) async {
    try {
      // رفع الصور إذا وجدت
      List<String> imageUrls = [];
      if (images != null && images.isNotEmpty) {
        imageUrls = await _uploadImages(images, 'forum/posts/${post.id}');
      }

      // إنشاء معرف جديد للمشاركة
      final docRef = _firestore.collection('forum_posts').doc();

      // إنشاء نسخة جديدة من المشاركة مع المعرف الجديد والصور
      final newPost = post.copyWith(
        id: docRef.id,
        images: imageUrls.isNotEmpty ? imageUrls : post.images,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now());

      // حفظ المشاركة في قاعدة البيانات
      await docRef.set(newPost.toMap());

      // تحديث عدد المشاركات في الموضوع
      await _firestore.collection('forum_topics').doc(post.topicId).update({
        'repliesCount': FieldValue.increment(1),
        'updatedAt': Timestamp.now(),
        'lastReplyUserId': post.userId,
        'lastReplyUserName': post.userName,
        'lastReplyUserImage': post.userImage,
        'lastReplyDate': Timestamp.now(),
      });

      // تحديث عدد المشاركات في الفئة
      await _firestore
          .collection('forum_categories')
          .doc(post.categoryId)
          .update({
        'postsCount': FieldValue.increment(1),
        'updatedAt': Timestamp.now(),
        'lastPost': {
          'topicId': post.topicId,
          'topicTitle': post.topicTitle,
          'userId': post.userId,
          'userName': post.userName,
          'userImage': post.userImage,
          'timestamp': Timestamp.now(),
        },
      });

      // إذا كانت المشاركة رداً على مشاركة أخرى، تحديث عدد الردود في المشاركة الأصلية
      if (post.parentId != null) {
        await _firestore.collection('forum_posts').doc(post.parentId).update({
          'repliesCount': FieldValue.increment(1),
          'updatedAt': Timestamp.now(),
        });
      }

      // إنشاء إشعار للمؤلف الأصلي إذا كانت المشاركة رداً على موضوع
      if (post.parentId == null) {
        final topicDoc =
            await _firestore.collection('forum_topics').doc(post.topicId).get();
        final topic = TopicModel.fromFirestore(topicDoc);

        if (topic.userId != post.userId) {
          final notification = NotificationModel(
            id: _uuid.v4(),
            type: NotificationType.topicReply,
            recipientId: topic.userId,
            senderId: post.userId,
            senderName: post.userName,
            senderImage: post.userImage,
            title: 'رد جديد على موضوعك',
            body: 'قام ${post.userName} بالرد على موضوعك "${topic.title}"',
            itemId: post.topicId,
            itemType: 'topic',
            itemTitle: topic.title,
            createdAt: DateTime.now());

          await createNotification(notification);
        }
      }
      // إنشاء إشعار لمؤلف المشاركة الأصلية إذا كانت المشاركة رداً على مشاركة
      else {
        final parentPostDoc =
            await _firestore.collection('forum_posts').doc(post.parentId).get();
        final parentPost = PostModel.fromFirestore(parentPostDoc);

        if (parentPost.userId != post.userId) {
          final notification = NotificationModel(
            id: _uuid.v4(),
            type: NotificationType.postReply,
            recipientId: parentPost.userId,
            senderId: post.userId,
            senderName: post.userName,
            senderImage: post.userImage,
            title: 'رد جديد على مشاركتك',
            body:
                'قام ${post.userName} بالرد على مشاركتك في موضوع "${parentPost.topicTitle}"',
            itemId: post.id,
            itemType: 'post',
            itemTitle: parentPost.topicTitle,
            createdAt: DateTime.now());

          await createNotification(notification);
        }
      }

      return newPost;
    } catch (e) {
      developer.log('خطأ في إنشاء المشاركة: $e');
      rethrow;
    }
  }

  @override
  Future<void> updatePost(PostModel post,
      {List<File>? newImages, List<String>? imagesToDelete}) async {
    try {
      // حذف الصور المطلوب حذفها
      if (imagesToDelete != null && imagesToDelete.isNotEmpty) {
        await _deleteImages(imagesToDelete);
      }

      // رفع الصور الجديدة إذا وجدت
      List<String> newImageUrls = [];
      if (newImages != null && newImages.isNotEmpty) {
        newImageUrls = await _uploadImages(newImages, 'forum/posts/${post.id}');
      }

      // دمج الصور الجديدة مع الصور الموجودة
      List<String> updatedImages = [];
      if (post.images != null) {
        updatedImages = List<String>.from(post.images!);
      }

      // إزالة الصور المحذوفة
      if (imagesToDelete != null) {
        updatedImages.removeWhere((url) => imagesToDelete.contains(url));
      }

      // إضافة الصور الجديدة
      updatedImages.addAll(newImageUrls);

      // تحديث المشاركة مع الصور المحدثة
      final updatedPost = post.copyWith(
        images: updatedImages,
        updatedAt: DateTime.now());

      await _firestore
          .collection('forum_posts')
          .doc(post.id)
          .update(updatedPost.toMap());
    } catch (e) {
      developer.log('خطأ في تحديث المشاركة: $e');
      rethrow;
    }
  }

  @override
  Future<void> deletePost(String postId) async {
    try {
      // الحصول على المشاركة
      final postDoc =
          await _firestore.collection('forum_posts').doc(postId).get();
      if (!postDoc.exists) {
        return;
      }

      final post = PostModel.fromFirestore(postDoc);

      // حذف جميع الردود على المشاركة
      final repliesSnapshot = await _firestore
          .collection('forum_posts')
          .where('parentId', isEqualTo: postId)
          .get();

      final batch = _firestore.batch();

      for (final doc in repliesSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // حذف المشاركة
      batch.delete(_firestore.collection('forum_posts').doc(postId));

      await batch.commit();

      // تحديث عدد المشاركات في الموضوع
      await _firestore.collection('forum_topics').doc(post.topicId).update({
        'repliesCount':
            FieldValue.increment(-(repliesSnapshot.docs.length + 1)),
        'updatedAt': Timestamp.now(),
      });

      // تحديث عدد المشاركات في الفئة
      await _firestore
          .collection('forum_categories')
          .doc(post.categoryId)
          .update({
        'postsCount': FieldValue.increment(-(repliesSnapshot.docs.length + 1)),
        'updatedAt': Timestamp.now(),
      });

      // إذا كانت المشاركة رداً على مشاركة أخرى، تحديث عدد الردود في المشاركة الأصلية
      if (post.parentId != null) {
        await _firestore.collection('forum_posts').doc(post.parentId).update({
          'repliesCount': FieldValue.increment(-1),
          'updatedAt': Timestamp.now(),
        });
      }

      // حذف الصور المرتبطة بالمشاركة
      if (post.images != null && post.images!.isNotEmpty) {
        await _deleteImages(post.images!);
      }
    } catch (e) {
      developer.log('خطأ في حذف المشاركة: $e');
      rethrow;
    }
  }

  @override
  Future<void> likePost(String postId, String userId) async {
    try {
      final postRef = _firestore.collection('forum_posts').doc(postId);
      final postDoc = await postRef.get();

      if (!postDoc.exists) {
        throw Exception('المشاركة غير موجودة');
      }

      final post = PostModel.fromFirestore(postDoc);
      final likedBy = List<String>.from(post.likedBy ?? []);

      if (likedBy.contains(userId)) {
        return; // المستخدم معجب بالفعل
      }

      likedBy.add(userId);

      await postRef.update({
        'likesCount': FieldValue.increment(1),
        'likedBy': likedBy,
        'updatedAt': Timestamp.now(),
      });

      // تحديث إحصائيات المستخدم
      await updateUserStatistics(userId);

      // إنشاء إشعار للمؤلف إذا كان مختلفاً عن المستخدم الذي أعجب
      if (post.userId != userId) {
        final userDoc = await _firestore.collection('users').doc(userId).get();
        final userData = userDoc.data();

        if (userData != null) {
          final notification = NotificationModel(
            id: _uuid.v4(),
            type: NotificationType.postLike,
            recipientId: post.userId,
            senderId: userId,
            senderName: userData['displayName'] ?? 'مستخدم',
            senderImage: userData['photoURL'],
            title: 'إعجاب بمشاركتك',
            body:
                'أعجب ${userData['displayName'] ?? 'مستخدم'} بمشاركتك في موضوع "${post.topicTitle}"',
            itemId: postId,
            itemType: 'post',
            itemTitle: post.topicTitle,
            createdAt: DateTime.now());

          await createNotification(notification);
        }
      }
    } catch (e) {
      developer.log('خطأ في الإعجاب بالمشاركة: $e');
      rethrow;
    }
  }

  @override
  Future<void> unlikePost(String postId, String userId) async {
    try {
      final postRef = _firestore.collection('forum_posts').doc(postId);
      final postDoc = await postRef.get();

      if (!postDoc.exists) {
        throw Exception('المشاركة غير موجودة');
      }

      final post = PostModel.fromFirestore(postDoc);
      final likedBy = List<String>.from(post.likedBy ?? []);

      if (!likedBy.contains(userId)) {
        return; // المستخدم غير معجب بالفعل
      }

      likedBy.remove(userId);

      await postRef.update({
        'likesCount': FieldValue.increment(-1),
        'likedBy': likedBy,
        'updatedAt': Timestamp.now(),
      });

      // تحديث إحصائيات المستخدم
      await updateUserStatistics(userId);
    } catch (e) {
      developer.log('خطأ في إلغاء الإعجاب بالمشاركة: $e');
      rethrow;
    }
  }

  @override
  Future<void> addPostReaction(
      String postId, String userId, String reactionType) async {
    try {
      final postRef = _firestore.collection('forum_posts').doc(postId);
      final postDoc = await postRef.get();

      if (!postDoc.exists) {
        throw Exception('المشاركة غير موجودة');
      }

      final post = PostModel.fromFirestore(postDoc);
      final reactions = Map<String, List<String>>.from(post.reactions ?? {});

      if (!reactions.containsKey(reactionType)) {
        reactions[reactionType] = [];
      }

      final usersForReaction = List<String>.from(reactions[reactionType]!);

      if (usersForReaction.contains(userId)) {
        return; // المستخدم متفاعل بالفعل بهذا النوع
      }

      usersForReaction.add(userId);
      reactions[reactionType] = usersForReaction;

      await postRef.update({
        'reactions': reactions,
        'updatedAt': Timestamp.now(),
      });

      // إنشاء إشعار للمؤلف إذا كان مختلفاً عن المستخدم الذي تفاعل
      if (post.userId != userId) {
        final userDoc = await _firestore.collection('users').doc(userId).get();
        final userData = userDoc.data();

        if (userData != null) {
          final notification = NotificationModel(
            id: _uuid.v4(),
            type: NotificationType.postReaction,
            recipientId: post.userId,
            senderId: userId,
            senderName: userData['displayName'] ?? 'مستخدم',
            senderImage: userData['photoURL'],
            title: 'تفاعل مع مشاركتك',
            body:
                'تفاعل ${userData['displayName'] ?? 'مستخدم'} مع مشاركتك في موضوع "${post.topicTitle}" بـ $reactionType',
            itemId: postId,
            itemType: 'post',
            itemTitle: post.topicTitle,
            data: {'reactionType': reactionType},
            createdAt: DateTime.now());

          await createNotification(notification);
        }
      }

      // تحديث إحصائيات المستخدم
      await updateUserStatistics(userId);
    } catch (e) {
      developer.log('خطأ في إضافة تفاعل للمشاركة: $e');
      rethrow;
    }
  }

  @override
  Future<void> removePostReaction(
      String postId, String userId, String reactionType) async {
    try {
      final postRef = _firestore.collection('forum_posts').doc(postId);
      final postDoc = await postRef.get();

      if (!postDoc.exists) {
        throw Exception('المشاركة غير موجودة');
      }

      final post = PostModel.fromFirestore(postDoc);
      final reactions = Map<String, List<String>>.from(post.reactions ?? {});

      if (!reactions.containsKey(reactionType)) {
        return; // لا يوجد تفاعلات من هذا النوع
      }

      final usersForReaction = List<String>.from(reactions[reactionType]!);

      if (!usersForReaction.contains(userId)) {
        return; // المستخدم غير متفاعل بهذا النوع
      }

      usersForReaction.remove(userId);

      if (usersForReaction.isEmpty) {
        reactions.remove(reactionType);
      } else {
        reactions[reactionType] = usersForReaction;
      }

      await postRef.update({
        'reactions': reactions,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في إزالة تفاعل من المشاركة: $e');
      rethrow;
    }
  }

  @override
  Future<void> pinPost(String postId) async {
    try {
      await _firestore.collection('forum_posts').doc(postId).update({
        'isPinned': true,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في تثبيت المشاركة: $e');
      rethrow;
    }
  }

  @override
  Future<void> unpinPost(String postId) async {
    try {
      await _firestore.collection('forum_posts').doc(postId).update({
        'isPinned': false,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في إلغاء تثبيت المشاركة: $e');
      rethrow;
    }
  }

  @override
  Future<void> markPostAsBestAnswer(String postId, String topicId) async {
    try {
      // تعليم المشاركة كأفضل إجابة
      await _firestore.collection('forum_posts').doc(postId).update({
        'isBestAnswer': true,
        'updatedAt': Timestamp.now(),
      });

      // تحديث الموضوع
      await _firestore.collection('forum_topics').doc(topicId).update({
        'isSolved': true,
        'solutionPostId': postId,
        'updatedAt': Timestamp.now(),
      });

      // إنشاء إشعار لمؤلف المشاركة
      final postDoc =
          await _firestore.collection('forum_posts').doc(postId).get();
      final post = PostModel.fromFirestore(postDoc);

      final topicDoc =
          await _firestore.collection('forum_topics').doc(topicId).get();
      final topic = TopicModel.fromFirestore(topicDoc);

      if (post.userId != topic.userId) {
        final notification = NotificationModel(
          id: _uuid.v4(),
          type: NotificationType.bestAnswer,
          recipientId: post.userId,
          senderId: topic.userId,
          senderName: topic.userName,
          senderImage: topic.userImage,
          title: 'تم اختيار مشاركتك كأفضل إجابة',
          body: 'تم اختيار مشاركتك كأفضل إجابة في موضوع "${topic.title}"',
          itemId: postId,
          itemType: 'post',
          itemTitle: topic.title,
          createdAt: DateTime.now());

        await createNotification(notification);
      }

      // تحديث إحصائيات المستخدم
      await updateUserStatistics(post.userId);
    } catch (e) {
      developer.log('خطأ في تعليم المشاركة كأفضل إجابة: $e');
      rethrow;
    }
  }

  @override
  Future<void> unmarkPostAsBestAnswer(String postId, String topicId) async {
    try {
      // إلغاء تعليم المشاركة كأفضل إجابة
      await _firestore.collection('forum_posts').doc(postId).update({
        'isBestAnswer': false,
        'updatedAt': Timestamp.now(),
      });

      // تحديث الموضوع
      await _firestore.collection('forum_topics').doc(topicId).update({
        'isSolved': false,
        'solutionPostId': null,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في إلغاء تعليم المشاركة كأفضل إجابة: $e');
      rethrow;
    }
  }

  @override
  Future<void> reportPost(String postId, String userId, String reason) async {
    try {
      final postRef = _firestore.collection('forum_posts').doc(postId);
      final postDoc = await postRef.get();

      if (!postDoc.exists) {
        throw Exception('المشاركة غير موجودة');
      }

      // إنشاء تقرير جديد
      await _firestore.collection('forum_reports').add({
        'postId': postId,
        'userId': userId,
        'reason': reason,
        'status': 'pending', // pending, reviewed, rejected, accepted
        'createdAt': Timestamp.now(),
      });

      // تحديث المشاركة
      await postRef.update({
        'isReported': true,
        'reportReason': reason,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في الإبلاغ عن المشاركة: $e');
      rethrow;
    }
  }

  // البحث

  @override
  Future<List<TopicModel>> searchTopics(
    String query, {
    int limit = 20,
    String? categoryId,
  }) async {
    try {
      Query baseQuery = _firestore.collection('forum_topics');

      // إضافة فلتر الفئة إذا تم تحديدها
      if (categoryId != null) {
        baseQuery = baseQuery.where('categoryId', isEqualTo: categoryId);
      }

      // البحث في العنوان والمحتوى والوسوم
      // ملاحظة: هذا بحث بسيط، للبحث المتقدم يجب استخدام خدمة مثل Algolia أو Elasticsearch
      final titleSnapshot = await baseQuery
          .orderBy('title')
          .startAt([query])
          .endAt(['$query\uf8ff'])
          .limit(limit)
          .get();

      final contentSnapshot = await baseQuery
          .orderBy('content')
          .startAt([query])
          .endAt(['$query\uf8ff'])
          .limit(limit)
          .get();

      // دمج النتائج وإزالة التكرارات
      final Map<String, TopicModel> results = {};

      for (final doc in titleSnapshot.docs) {
        final topic = TopicModel.fromFirestore(doc);
        results[topic.id] =
            topic.copyWith(searchScore: 2.0); // درجة أعلى للتطابق في العنوان
      }

      for (final doc in contentSnapshot.docs) {
        final topic = TopicModel.fromFirestore(doc);
        if (results.containsKey(topic.id)) {
          // زيادة درجة التطابق إذا كان موجوداً بالفعل
          results[topic.id] = results[topic.id]!.copyWith(
            searchScore: (results[topic.id]!.searchScore ?? 0) + 1.0);
        } else {
          results[topic.id] = topic.copyWith(searchScore: 1.0);
        }
      }

      // ترتيب النتائج حسب درجة التطابق
      final sortedResults = results.values.toList()
        ..sort((a, b) => (b.searchScore ?? 0).compareTo(a.searchScore ?? 0));

      return sortedResults.take(limit).toList();
    } catch (e) {
      developer.log('خطأ في البحث عن المواضيع: $e');
      return [];
    }
  }

  @override
  Future<List<PostModel>> searchPosts(
    String query, {
    int limit = 20,
    String? topicId,
  }) async {
    try {
      Query baseQuery = _firestore.collection('forum_posts');

      // إضافة فلتر الموضوع إذا تم تحديده
      if (topicId != null) {
        baseQuery = baseQuery.where('topicId', isEqualTo: topicId);
      }

      // البحث في المحتوى
      final contentSnapshot = await baseQuery
          .orderBy('content')
          .startAt([query])
          .endAt(['$query\uf8ff'])
          .limit(limit)
          .get();

      final posts = contentSnapshot.docs
          .map((doc) => PostModel.fromFirestore(doc))
          .toList();

      // ترتيب النتائج حسب التاريخ
      posts.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return posts;
    } catch (e) {
      developer.log('خطأ في البحث عن المشاركات: $e');
      return [];
    }
  }

  // الإشعارات

  @override
  Future<NotificationModel> createNotification(
      NotificationModel notification) async {
    try {
      final docRef =
          _firestore.collection('forum_notifications').doc(notification.id);
      await docRef.set(notification.toMap());
      return notification;
    } catch (e) {
      developer.log('خطأ في إنشاء الإشعار: $e');
      rethrow;
    }
  }

  @override
  Future<List<NotificationModel>> getUserNotifications(
    String userId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
    bool unreadOnly = false,
  }) async {
    try {
      Query query = _firestore
          .collection('forum_notifications')
          .where('recipientId', isEqualTo: userId);

      if (unreadOnly) {
        query = query.where('isRead', isEqualTo: false);
      }

      query = query.orderBy('createdAt', descending: true);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      query = query.limit(limit);

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => NotificationModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      developer.log('خطأ في الحصول على إشعارات المستخدم: $e');
      return [];
    }
  }

  @override
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await _firestore
          .collection('forum_notifications')
          .doc(notificationId)
          .update({
        'isRead': true,
        'readAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في تعليم الإشعار كمقروء: $e');
      rethrow;
    }
  }

  @override
  Future<void> markAllNotificationsAsRead(String userId) async {
    try {
      final batch = _firestore.batch();

      final snapshot = await _firestore
          .collection('forum_notifications')
          .where('recipientId', isEqualTo: userId)
          .where('isRead', isEqualTo: false)
          .get();

      for (final doc in snapshot.docs) {
        batch.update(doc.reference, {
          'isRead': true,
          'readAt': Timestamp.now(),
        });
      }

      await batch.commit();
    } catch (e) {
      developer.log('خطأ في تعليم جميع الإشعارات كمقروءة: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _firestore
          .collection('forum_notifications')
          .doc(notificationId)
          .delete();
    } catch (e) {
      developer.log('خطأ في حذف الإشعار: $e');
      rethrow;
    }
  }

  @override
  Future<int> getUnreadNotificationsCount(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('forum_notifications')
          .where('recipientId', isEqualTo: userId)
          .where('isRead', isEqualTo: false)
          .count()
          .get();

      return snapshot.count ?? 0;
    } catch (e) {
      developer.log('خطأ في الحصول على عدد الإشعارات غير المقروءة: $e');
      return 0;
    }
  }

  // إحصائيات المستخدم

  @override
  Future<UserStatisticsModel?> getUserStatistics(String userId) async {
    try {
      developer.log('جاري جلب إحصائيات المستخدم: $userId');

      final doc = await _firestore
          .collection('forum_user_statistics')
          .doc(userId)
          .get();

      if (!doc.exists) {
        developer.log('لا توجد إحصائيات للمستخدم: $userId، جاري إنشاء إحصائيات جديدة');
        return await recalculateUserStatistics(userId);
      }

      developer.log('تم جلب إحصائيات المستخدم بنجاح: $userId');
      return UserStatisticsModel.fromFirestore(doc);
    } catch (e) {
      developer.log('خطأ في الحصول على إحصائيات المستخدم: $e');
      return null;
    }
  }

  @override
  Future<void> updateUserStatistics(String userId) async {
    try {
      // التحقق من وجود إحصائيات للمستخدم
      final statsDoc = await _firestore
          .collection('forum_user_statistics')
          .doc(userId)
          .get();

      if (!statsDoc.exists) {
        // إنشاء إحصائيات جديدة للمستخدم
        final userDoc = await _firestore.collection('users').doc(userId).get();
        final userData = userDoc.data();

        if (userData != null) {
          final newStats = UserStatisticsModel(
            userId: userId,
            userName: userData['displayName'] ?? 'مستخدم',
            userImage: userData['photoURL'],
            joinDate: userDoc.exists
                ? (userData['createdAt'] as Timestamp?)?.toDate() ??
                    DateTime.now()
                : DateTime.now(),
            lastActivityDate: DateTime.now(),
            level: 'مبتدئ',
            badges: []);

          await _firestore
              .collection('forum_user_statistics')
              .doc(userId)
              .set(newStats.toMap());
        }
      } else {
        // تحديث تاريخ آخر نشاط
        await _firestore
            .collection('forum_user_statistics')
            .doc(userId)
            .update({
          'lastActivityDate': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        });
      }
    } catch (e) {
      developer.log('خطأ في تحديث إحصائيات المستخدم: $e');
      rethrow;
    }
  }

  @override
  Future<UserStatisticsModel?> recalculateUserStatistics(String userId) async {
    try {


      // الحصول على معلومات المستخدم
      final userDoc = await _firestore.collection('users').doc(userId).get();
      final userData = userDoc.data();

      if (userData == null) {


        // إنشاء إحصائيات افتراضية للمستخدم
        final defaultStats = UserStatisticsModel(
          userId: userId,
          userName: 'مستخدم',
          joinDate: DateTime.now(),
          lastActivityDate: DateTime.now(),
          level: 'مبتدئ');

        await _firestore
            .collection('forum_user_statistics')
            .doc(userId)
            .set(defaultStats.toMap());

        developer.log('تم إنشاء إحصائيات افتراضية للمستخدم: $userId');
        return defaultStats;
      }

      developer.log('تم العثور على بيانات المستخدم: $userId، جاري حساب الإحصائيات');

      // حساب عدد المواضيع
      final topicsSnapshot = await _firestore
          .collection('forum_topics')
          .where('userId', isEqualTo: userId)
          .count()
          .get();

      // حساب عدد المشاركات
      final postsSnapshot = await _firestore
          .collection('forum_posts')
          .where('userId', isEqualTo: userId)
          .count()
          .get();

      // حساب إجمالي مشاهدات المواضيع
      final topicsViewsSnapshot = await _firestore
          .collection('forum_topics')
          .where('userId', isEqualTo: userId)
          .get();

      int totalTopicViews = 0;
      for (final doc in topicsViewsSnapshot.docs) {
        final topic = TopicModel.fromFirestore(doc);
        totalTopicViews += topic.viewsCount;
      }

      // حساب إجمالي إعجابات المواضيع
      final topicsLikesSnapshot = await _firestore
          .collection('forum_topics')
          .where('userId', isEqualTo: userId)
          .get();

      int totalTopicLikes = 0;
      for (final doc in topicsLikesSnapshot.docs) {
        final topic = TopicModel.fromFirestore(doc);
        totalTopicLikes += topic.likesCount;
      }

      // حساب إجمالي إعجابات المشاركات
      final postsLikesSnapshot = await _firestore
          .collection('forum_posts')
          .where('userId', isEqualTo: userId)
          .get();

      int totalPostLikes = 0;
      for (final doc in postsLikesSnapshot.docs) {
        final post = doc.data();
        totalPostLikes += (post['likesCount'] as num? ?? 0).toInt();
      }

      // حساب عدد المواضيع المميزة
      final featuredTopicsSnapshot = await _firestore
          .collection('forum_topics')
          .where('userId', isEqualTo: userId)
          .where('status', whereIn: [
            TopicStatus.featured.index,
            TopicStatus.pinnedAndFeatured.index
          ])
          .count()
          .get();

      // حساب عدد المواضيع المثبتة
      final pinnedTopicsSnapshot = await _firestore
          .collection('forum_topics')
          .where('userId', isEqualTo: userId)
          .where('status', whereIn: [
            TopicStatus.pinned.index,
            TopicStatus.pinnedAndFeatured.index,
            TopicStatus.closedAndPinned.index
          ])
          .count()
          .get();

      // حساب عدد المواضيع المحلولة
      final solvedTopicsSnapshot = await _firestore
          .collection('forum_topics')
          .where('userId', isEqualTo: userId)
          .where('isSolved', isEqualTo: true)
          .count()
          .get();

      // حساب عدد أفضل الإجابات
      final bestAnswersSnapshot = await _firestore
          .collection('forum_posts')
          .where('userId', isEqualTo: userId)
          .where('isBestAnswer', isEqualTo: true)
          .count()
          .get();

      // حساب المتوسطات
      double averagePostsPerTopic = (topicsSnapshot.count ?? 0) > 0
          ? (postsSnapshot.count ?? 0) / (topicsSnapshot.count ?? 1)
          : 0;

      double averageViewsPerTopic = (topicsSnapshot.count ?? 0) > 0
          ? totalTopicViews / (topicsSnapshot.count ?? 1)
          : 0;

      double averageLikesPerTopic = (topicsSnapshot.count ?? 0) > 0
          ? totalTopicLikes / (topicsSnapshot.count ?? 1)
          : 0;

      // حساب النقاط
      int points = 0;
      points += (topicsSnapshot.count ?? 0) * 10; // 10 نقاط لكل موضوع
      points += (postsSnapshot.count ?? 0) * 5; // 5 نقاط لكل مشاركة
      points += totalTopicLikes * 2; // 2 نقطة لكل إعجاب بموضوع
      points += totalPostLikes * 1; // 1 نقطة لكل إعجاب بمشاركة
      points +=
          (featuredTopicsSnapshot.count ?? 0) * 20; // 20 نقطة لكل موضوع مميز
      points +=
          (pinnedTopicsSnapshot.count ?? 0) * 15; // 15 نقطة لكل موضوع مثبت
      points +=
          (solvedTopicsSnapshot.count ?? 0) * 15; // 15 نقطة لكل موضوع محلول
      points += (bestAnswersSnapshot.count ?? 0) * 25; // 25 نقطة لكل أفضل إجابة

      // تحديد المستوى
      String level;
      if (points < 100) {
        level = 'مبتدئ';
      } else if (points < 500) {
        level = 'نشط';
      } else if (points < 1000) {
        level = 'متميز';
      } else if (points < 2000) {
        level = 'محترف';
      } else if (points < 5000) {
        level = 'خبير';
      } else {
        level = 'أسطورة';
      }

      // تحديد الشارات
      List<String> badges = [];

      if ((topicsSnapshot.count ?? 0) >= 10) badges.add('كاتب');
      if ((topicsSnapshot.count ?? 0) >= 50) badges.add('كاتب متميز');
      if ((topicsSnapshot.count ?? 0) >= 100) badges.add('كاتب محترف');

      if ((postsSnapshot.count ?? 0) >= 50) badges.add('مشارك');
      if ((postsSnapshot.count ?? 0) >= 200) badges.add('مشارك نشط');
      if ((postsSnapshot.count ?? 0) >= 500) badges.add('مشارك محترف');

      if (totalTopicLikes >= 100) badges.add('محبوب');
      if (totalTopicLikes >= 500) badges.add('مشهور');

      if ((bestAnswersSnapshot.count ?? 0) >= 5) badges.add('مساعد');
      if ((bestAnswersSnapshot.count ?? 0) >= 20) badges.add('مساعد متميز');
      if ((bestAnswersSnapshot.count ?? 0) >= 50) badges.add('خبير');

      if ((featuredTopicsSnapshot.count ?? 0) >= 5) badges.add('مميز');
      if ((solvedTopicsSnapshot.count ?? 0) >= 10) badges.add('حلال المشاكل');

      // إنشاء أو تحديث إحصائيات المستخدم
      final stats = UserStatisticsModel(
        userId: userId,
        userName: userData['displayName'] ?? 'مستخدم',
        userImage: userData['photoURL'],
        joinDate: userDoc.exists
            ? (userData['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now()
            : DateTime.now(),
        lastActivityDate: DateTime.now(),
        topicsCount: topicsSnapshot.count ?? 0,
        postsCount: postsSnapshot.count ?? 0,
        totalTopicViews: totalTopicViews,
        totalTopicLikes: totalTopicLikes,
        totalPostLikes: totalPostLikes,
        featuredTopicsCount: featuredTopicsSnapshot.count ?? 0,
        pinnedTopicsCount: pinnedTopicsSnapshot.count ?? 0,
        solvedTopicsCount: solvedTopicsSnapshot.count ?? 0,
        bestAnswersCount: bestAnswersSnapshot.count ?? 0,
        averagePostsPerTopic: averagePostsPerTopic,
        averageViewsPerTopic: averageViewsPerTopic,
        averageLikesPerTopic: averageLikesPerTopic,
        points: points,
        level: level,
        badges: badges);

      await _firestore
          .collection('forum_user_statistics')
          .doc(userId)
          .set(stats.toMap());

      developer.log('تم إعادة حساب وحفظ إحصائيات المستخدم بنجاح: $userId');
      return stats;
    } catch (e) {
      developer.log('خطأ في إعادة حساب إحصائيات المستخدم: $e');

      // في حالة الخطأ، نحاول إنشاء إحصائيات افتراضية
      try {
        final defaultStats = UserStatisticsModel(
          userId: userId,
          userName: 'مستخدم',
          joinDate: DateTime.now(),
          lastActivityDate: DateTime.now(),
          level: 'مبتدئ');

        await _firestore
            .collection('forum_user_statistics')
            .doc(userId)
            .set(defaultStats.toMap());

        developer.log('تم إنشاء إحصائيات افتراضية للمستخدم بعد الخطأ: $userId');
        return defaultStats;
      } catch (innerError) {
        developer.log('خطأ في إنشاء إحصائيات افتراضية للمستخدم: $innerError');
        return null;
      }
    }
  }

  // التفاعلات

  @override
  Future<ReactionStats?> getReactionStats(
      String itemId, ReactionItemType itemType) async {
    try {
      final doc = await _firestore
          .collection('forum_reaction_stats')
          .doc('${itemType.index}_$itemId')
          .get();

      if (!doc.exists) {
        return null;
      }

      return ReactionStats.fromFirestore(doc);
    } catch (e) {
      developer.log('خطأ في الحصول على إحصائيات التفاعلات: $e');
      return null;
    }
  }

  // متابعة المواضيع

  @override
  Future<void> followTopic(String topicId, String userId) async {
    try {
      await _firestore
          .collection('forum_topic_followers')
          .doc('${topicId}_$userId')
          .set({
        'topicId': topicId,
        'userId': userId,
        'createdAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في متابعة الموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<void> unfollowTopic(String topicId, String userId) async {
    try {
      await _firestore
          .collection('forum_topic_followers')
          .doc('${topicId}_$userId')
          .delete();
    } catch (e) {
      developer.log('خطأ في إلغاء متابعة الموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<List<TopicModel>> getFollowedTopics(
    String userId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      Query query = _firestore
          .collection('forum_topic_followers')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      query = query.limit(limit);

      final snapshot = await query.get();

      // الحصول على معرفات المواضيع المتابعة
      final topicIds = snapshot.docs
          .map((doc) =>
              (doc.data() as Map<String, dynamic>)['topicId'] as String)
          .toList();

      if (topicIds.isEmpty) {
        return [];
      }

      // الحصول على المواضيع
      final topics = <TopicModel>[];

      // Firestore لا يدعم where-in مع أكثر من 10 قيم، لذلك نقسم الطلب
      for (int i = 0; i < topicIds.length; i += 10) {
        final end = (i + 10 < topicIds.length) ? i + 10 : topicIds.length;
        final batch = topicIds.sublist(i, end);

        final topicsSnapshot = await _firestore
            .collection('forum_topics')
            .where(FieldPath.documentId, whereIn: batch)
            .get();

        topics.addAll(
            topicsSnapshot.docs.map((doc) => TopicModel.fromFirestore(doc)));
      }

      // ترتيب المواضيع حسب تاريخ آخر تحديث
      topics.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

      return topics;
    } catch (e) {
      developer.log('خطأ في الحصول على المواضيع المتابعة: $e');
      return [];
    }
  }

  @override
  Future<bool> isFollowingTopic(String topicId, String userId) async {
    try {
      final doc = await _firestore
          .collection('forum_topic_followers')
          .doc('${topicId}_$userId')
          .get();

      return doc.exists;
    } catch (e) {
      developer.log('خطأ في التحقق من متابعة الموضوع: $e');
      return false;
    }
  }

  // الإشارات المرجعية

  @override
  Future<void> bookmarkTopic(String topicId, String userId) async {
    try {
      // إضافة الإشارة المرجعية
      await _firestore
          .collection('forum_topic_bookmarks')
          .doc('${topicId}_$userId')
          .set({
        'topicId': topicId,
        'userId': userId,
        'createdAt': Timestamp.now(),
      });

      // تحديث عدد الإشارات المرجعية في الموضوع
      await _firestore.collection('forum_topics').doc(topicId).update({
        'bookmarksCount': FieldValue.increment(1),
        'bookmarkedBy': FieldValue.arrayUnion([userId]),
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في إضافة إشارة مرجعية للموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<void> unbookmarkTopic(String topicId, String userId) async {
    try {
      // حذف الإشارة المرجعية
      await _firestore
          .collection('forum_topic_bookmarks')
          .doc('${topicId}_$userId')
          .delete();

      // تحديث عدد الإشارات المرجعية في الموضوع
      await _firestore.collection('forum_topics').doc(topicId).update({
        'bookmarksCount': FieldValue.increment(-1),
        'bookmarkedBy': FieldValue.arrayRemove([userId]),
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      developer.log('خطأ في إزالة إشارة مرجعية من الموضوع: $e');
      rethrow;
    }
  }

  @override
  Future<List<TopicModel>> getBookmarkedTopics(
    String userId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      Query query = _firestore
          .collection('forum_topic_bookmarks')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      query = query.limit(limit);

      final snapshot = await query.get();

      // الحصول على معرفات المواضيع المحفوظة
      final topicIds = snapshot.docs
          .map((doc) =>
              (doc.data() as Map<String, dynamic>)['topicId'] as String)
          .toList();

      if (topicIds.isEmpty) {
        return [];
      }

      // الحصول على المواضيع
      final topics = <TopicModel>[];

      // Firestore لا يدعم where-in مع أكثر من 10 قيم، لذلك نقسم الطلب
      for (int i = 0; i < topicIds.length; i += 10) {
        final end = (i + 10 < topicIds.length) ? i + 10 : topicIds.length;
        final batch = topicIds.sublist(i, end);

        final topicsSnapshot = await _firestore
            .collection('forum_topics')
            .where(FieldPath.documentId, whereIn: batch)
            .get();

        topics.addAll(
            topicsSnapshot.docs.map((doc) => TopicModel.fromFirestore(doc)));
      }

      return topics;
    } catch (e) {
      developer.log('خطأ في الحصول على المواضيع المحفوظة: $e');
      return [];
    }
  }

  @override
  Future<bool> isTopicBookmarked(String topicId, String userId) async {
    try {
      final doc = await _firestore
          .collection('forum_topic_bookmarks')
          .doc('${topicId}_$userId')
          .get();

      return doc.exists;
    } catch (e) {
      developer.log('خطأ في التحقق من حفظ الموضوع: $e');
      return false;
    }
  }

  // مشاركة الموضوع

  @override
  Future<void> shareTopic(String topicId, String userId) async {
    try {
      // تحديث عدد المشاركات في الموضوع
      await _firestore.collection('forum_topics').doc(topicId).update({
        'sharesCount': FieldValue.increment(1),
        'updatedAt': Timestamp.now(),
      });

      // تسجيل عملية المشاركة
      await _firestore.collection('forum_topic_shares').add({
        'topicId': topicId,
        'userId': userId,
        'createdAt': Timestamp.now(),
      });

      // تحديث إحصائيات المستخدم
      await updateUserStatistics(userId);
    } catch (e) {
      developer.log('خطأ في مشاركة الموضوع: $e');
      rethrow;
    }
  }

  // إحصائيات المنتدى

  @override
  Future<Map<String, dynamic>> getForumStatistics() async {
    try {
      developer.log('بدء جلب إحصائيات المنتدى...');

      // التحقق من وجود وثيقة إحصائيات المنتدى
      final statsDoc = await _firestore.collection('forum_statistics').doc('global').get();

      // إذا كانت وثيقة الإحصائيات موجودة، نستخدمها
      if (statsDoc.exists) {
        developer.log('تم العثور على وثيقة إحصائيات المنتدى');
        final data = statsDoc.data() ?? {};
        return data;
      }

      developer.log('وثيقة إحصائيات المنتدى غير موجودة، جاري حساب الإحصائيات...');

      // حساب الإحصائيات من الصفر
      int categoriesCount = 0;
      int topicsCount = 0;
      int postsCount = 0;
      int activeUsersCount = 0;
      List<TopicModel> latestTopics = [];
      List<TopicModel> mostViewedTopics = [];
      List<TopicModel> mostActiveTopics = [];

      // محاولة الحصول على عدد الفئات
      try {
        final categoriesSnapshot = await _firestore.collection('forum_categories').count().get();
        categoriesCount = categoriesSnapshot.count ?? 0;
        developer.log('عدد الفئات: $categoriesCount');
      } catch (e) {
        developer.log('خطأ في الحصول على عدد الفئات: $e');
      }

      // محاولة الحصول على عدد المواضيع
      try {
        final topicsSnapshot = await _firestore.collection('forum_topics').count().get();
        topicsCount = topicsSnapshot.count ?? 0;
        developer.log('عدد المواضيع: $topicsCount');
      } catch (e) {
        developer.log('خطأ في الحصول على عدد المواضيع: $e');
      }

      // محاولة الحصول على عدد المشاركات
      try {
        final postsSnapshot = await _firestore.collection('forum_posts').count().get();
        postsCount = postsSnapshot.count ?? 0;
        developer.log('عدد المشاركات: $postsCount');
      } catch (e) {
        developer.log('خطأ في الحصول على عدد المشاركات: $e');
      }

      // محاولة الحصول على عدد المستخدمين النشطين
      try {
        final activeUsersSnapshot = await _firestore
            .collection('forum_user_statistics')
            .where('lastActivityDate',
                isGreaterThan: Timestamp.fromDate(
                  DateTime.now().subtract(const Duration(days: 30))))
            .count()
            .get();
        activeUsersCount = activeUsersSnapshot.count ?? 0;
        developer.log('عدد المستخدمين النشطين: $activeUsersCount');
      } catch (e) {
        developer.log('خطأ في الحصول على عدد المستخدمين النشطين: $e');
      }

      // محاولة الحصول على آخر المواضيع
      try {
        final latestTopicsSnapshot = await _firestore
            .collection('forum_topics')
            .orderBy('createdAt', descending: true)
            .limit(5)
            .get();

        latestTopics = latestTopicsSnapshot.docs
            .map((doc) => TopicModel.fromFirestore(doc))
            .toList();
        developer.log('عدد آخر المواضيع: ${latestTopics.length}');
      } catch (e) {
        developer.log('خطأ في الحصول على آخر المواضيع: $e');
      }

      // محاولة الحصول على المواضيع الأكثر مشاهدة
      try {
        final mostViewedTopicsSnapshot = await _firestore
            .collection('forum_topics')
            .orderBy('viewsCount', descending: true)
            .limit(5)
            .get();

        mostViewedTopics = mostViewedTopicsSnapshot.docs
            .map((doc) => TopicModel.fromFirestore(doc))
            .toList();
        developer.log('عدد المواضيع الأكثر مشاهدة: ${mostViewedTopics.length}');
      } catch (e) {
        developer.log('خطأ في الحصول على المواضيع الأكثر مشاهدة: $e');
      }

      // محاولة الحصول على المواضيع الأكثر نشاطاً
      try {
        final mostActiveTopicsSnapshot = await _firestore
            .collection('forum_topics')
            .orderBy('repliesCount', descending: true)
            .limit(5)
            .get();

        mostActiveTopics = mostActiveTopicsSnapshot.docs
            .map((doc) => TopicModel.fromFirestore(doc))
            .toList();
        developer.log('عدد المواضيع الأكثر نشاطاً: ${mostActiveTopics.length}');
      } catch (e) {
        developer.log('خطأ في الحصول على المواضيع الأكثر نشاطاً: $e');
      }

      // حساب بيانات النشاط الأسبوعي (بناءً على البيانات الحقيقية)
      Map<String, int> activityData = {};
      try {
        final now = DateTime.now();
        final weekStart = now.subtract(Duration(days: now.weekday - 1));

        for (int i = 0; i < 7; i++) {
          final day = weekStart.add(Duration(days: i));
          final dayName = _getDayName(day.weekday);

          final dayStart = Timestamp.fromDate(DateTime(day.year, day.month, day.day));
          final dayEnd = Timestamp.fromDate(DateTime(day.year, day.month, day.day, 23, 59, 59));

          final dayActivitySnapshot = await _firestore
              .collection('forum_topics')
              .where('createdAt', isGreaterThanOrEqualTo: dayStart)
              .where('createdAt', isLessThanOrEqualTo: dayEnd)
              .count()
              .get();

          activityData[dayName] = dayActivitySnapshot.count ?? 0;
        }
        developer.log('تم حساب بيانات النشاط الأسبوعي: ${activityData.length} أيام');
      } catch (e) {
        developer.log('خطأ في حساب بيانات النشاط الأسبوعي: $e');
        // استخدام بيانات فارغة في حالة الخطأ
        activityData = {
          'الأحد': 0,
          'الإثنين': 0,
          'الثلاثاء': 0,
          'الأربعاء': 0,
          'الخميس': 0,
          'الجمعة': 0,
          'السبت': 0,
        };
      }

      // حساب بيانات الفئات (بناءً على البيانات الحقيقية)
      Map<String, int> categoriesData = {};
      try {
        final categoriesSnapshot = await _firestore.collection('forum_categories').get();

        for (final categoryDoc in categoriesSnapshot.docs) {
          final category = CategoryModel.fromFirestore(categoryDoc);

          final categoryTopicsSnapshot = await _firestore
              .collection('forum_topics')
              .where('categoryId', isEqualTo: category.id)
              .count()
              .get();

          categoriesData[category.name] = categoryTopicsSnapshot.count ?? 0;
        }
        developer.log('تم حساب بيانات الفئات: ${categoriesData.length} فئات');
      } catch (e) {
        developer.log('خطأ في حساب بيانات الفئات: $e');
        // استخدام بيانات فارغة في حالة الخطأ
        categoriesData = {};
      }

      // إنشاء بيانات الإحصائيات
      final statistics = {
        'categoriesCount': categoriesCount,
        'topicsCount': topicsCount,
        'postsCount': postsCount,
        'activeUsersCount': activeUsersCount,
        'latestTopics': latestTopics.map((topic) => topic.toMap()).toList(),
        'mostViewedTopics': mostViewedTopics.map((topic) => topic.toMap()).toList(),
        'mostActiveTopics': mostActiveTopics.map((topic) => topic.toMap()).toList(),
        'updatedAt': Timestamp.now(),
        'activityData': activityData,
        'categoriesData': categoriesData,
      };

      developer.log('إحصائيات المنتدى المحسوبة: فئات=$categoriesCount، مواضيع=$topicsCount، مشاركات=$postsCount، مستخدمين نشطين=$activeUsersCount');

      // حفظ الإحصائيات في Firestore للاستخدام المستقبلي
      try {
        await _firestore.collection('forum_statistics').doc('global').set(statistics);
        developer.log('تم حفظ إحصائيات اللوبي في Firestore');
      } catch (e) {
        developer.log('خطأ في حفظ إحصائيات اللوبي في Firestore: $e');
      }

      return statistics;
    } catch (e) {
      developer.log('خطأ في الحصول على إحصائيات اللوبي: $e');

      // في حالة الخطأ، نرجع بيانات أولية بسيطة
      return {
        'categoriesCount': 0,
        'topicsCount': 0,
        'postsCount': 0,
        'activeUsersCount': 0,
        'latestTopics': [],
        'mostViewedTopics': [],
        'mostActiveTopics': [],
        'updatedAt': Timestamp.now(),
        'activityData': {
          'الأحد': 0,
          'الإثنين': 0,
          'الثلاثاء': 0,
          'الأربعاء': 0,
          'الخميس': 0,
          'الجمعة': 0,
          'السبت': 0,
        },
        'categoriesData': {
          'النقاش العام': 0,
          'بيع العقارات': 0,
          'إيجار العقارات': 0,
          'السيارات': 0,
        },
      };
    }
  }

  /// الحصول على اسم اليوم بالعربية
  String _getDayName(int weekday) {
    switch (weekday) {
      case 1: return 'الإثنين';
      case 2: return 'الثلاثاء';
      case 3: return 'الأربعاء';
      case 4: return 'الخميس';
      case 5: return 'الجمعة';
      case 6: return 'السبت';
      case 7: return 'الأحد';
      default: return '';
    }
  }

  // استطلاعات الرأي

  @override
  Future<String?> createPoll(String topicId, Map<String, dynamic> pollData) async {
    try {
      // إنشاء معرف جديد للاستطلاع
      final docRef = _firestore
          .collection('forum_topics')
          .doc(topicId)
          .collection('polls')
          .doc();

      // إضافة المعرف إلى بيانات الاستطلاع
      pollData['id'] = docRef.id;

      // إضافة تواريخ الإنشاء والتحديث إذا لم تكن موجودة
      if (!pollData.containsKey('createdAt')) {
        pollData['createdAt'] = Timestamp.now();
      }

      if (!pollData.containsKey('updatedAt')) {
        pollData['updatedAt'] = Timestamp.now();
      }

      // حفظ الاستطلاع في قاعدة البيانات
      await docRef.set(pollData);

      // تحديث الموضوع لإضافة معرف الاستطلاع
      final topicRef = _firestore.collection('forum_topics').doc(topicId);
      final topicDoc = await topicRef.get();

      if (topicDoc.exists) {
        final topic = TopicModel.fromFirestore(topicDoc);
        List<PollModel> polls = [];

        if (topic.polls != null) {
          polls = List<PollModel>.from(topic.polls!);
        }

        // إضافة الاستطلاع الجديد إلى قائمة الاستطلاعات
        final newPoll = PollModel.fromMap(pollData);
        polls.add(newPoll);

        // تحديث الموضوع
        await topicRef.update({
          'polls': polls.map((poll) => poll.toMap()).toList(),
          'updatedAt': Timestamp.now(),
        });
      }

      return docRef.id;
    } catch (e) {
      developer.log('خطأ في إنشاء استطلاع الرأي: $e');
      return null;
    }
  }

  @override
  Future<void> updatePoll(String topicId, String pollId, Map<String, dynamic> pollData) async {
    try {
      // تحديث تاريخ التحديث
      pollData['updatedAt'] = Timestamp.now();

      // تحديث الاستطلاع في قاعدة البيانات
      await _firestore
          .collection('forum_topics')
          .doc(topicId)
          .collection('polls')
          .doc(pollId)
          .update(pollData);

      // تحديث الاستطلاع في الموضوع
      final topicRef = _firestore.collection('forum_topics').doc(topicId);
      final topicDoc = await topicRef.get();

      if (topicDoc.exists) {
        final topic = TopicModel.fromFirestore(topicDoc);
        if (topic.polls != null) {
          List<PollModel> polls = List<PollModel>.from(topic.polls!);

          // البحث عن الاستطلاع وتحديثه
          final pollIndex = polls.indexWhere((poll) => poll.id == pollId);
          if (pollIndex != -1) {
            // الحصول على الاستطلاع الحالي
            final currentPoll = polls[pollIndex];

            // دمج البيانات الجديدة مع البيانات الحالية
            final updatedPollData = {...currentPoll.toMap(), ...pollData};

            // تحديث الاستطلاع في القائمة
            polls[pollIndex] = PollModel.fromMap(updatedPollData);

            // تحديث الموضوع
            await topicRef.update({
              'polls': polls.map((poll) => poll.toMap()).toList(),
              'updatedAt': Timestamp.now(),
            });
          }
        }
      }
    } catch (e) {
      developer.log('خطأ في تحديث استطلاع الرأي: $e');
      rethrow;
    }
  }

  @override
  Future<void> deletePoll(String topicId, String pollId) async {
    try {
      // حذف الاستطلاع من قاعدة البيانات
      await _firestore
          .collection('forum_topics')
          .doc(topicId)
          .collection('polls')
          .doc(pollId)
          .delete();

      // حذف الاستطلاع من الموضوع
      final topicRef = _firestore.collection('forum_topics').doc(topicId);
      final topicDoc = await topicRef.get();

      if (topicDoc.exists) {
        final topic = TopicModel.fromFirestore(topicDoc);
        if (topic.polls != null) {
          List<PollModel> polls = List<PollModel>.from(topic.polls!);

          // حذف الاستطلاع من القائمة
          polls.removeWhere((poll) => poll.id == pollId);

          // تحديث الموضوع
          await topicRef.update({
            'polls': polls.map((poll) => poll.toMap()).toList(),
            'updatedAt': Timestamp.now(),
          });
        }
      }
    } catch (e) {
      developer.log('خطأ في حذف استطلاع الرأي: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>?> getPollById(String topicId, String pollId) async {
    try {
      final doc = await _firestore
          .collection('forum_topics')
          .doc(topicId)
          .collection('polls')
          .doc(pollId)
          .get();

      if (!doc.exists) {
        return null;
      }

      return doc.data();
    } catch (e) {
      developer.log('خطأ في الحصول على استطلاع الرأي: $e');
      return null;
    }
  }

  @override
  Future<void> voteOnPoll(String topicId, String pollId, List<String> optionIds, String userId) async {
    try {
      // الحصول على الاستطلاع
      final pollDoc = await _firestore
          .collection('forum_topics')
          .doc(topicId)
          .collection('polls')
          .doc(pollId)
          .get();

      if (!pollDoc.exists) {
        throw Exception('الاستطلاع غير موجود');
      }

      final pollData = pollDoc.data()!;
      final options = List<Map<String, dynamic>>.from(pollData['options'] ?? []);

      // التحقق من وجود الخيارات
      for (final optionId in optionIds) {
        final optionIndex = options.indexWhere((option) => option['id'] == optionId);
        if (optionIndex == -1) {
          throw Exception('الخيار غير موجود');
        }
      }

      // إضافة المستخدم إلى قائمة المصوتين لكل خيار
      for (int i = 0; i < options.length; i++) {
        final option = options[i];
        final optionId = option['id'];
        List<String> votedBy = List<String>.from(option['votedBy'] ?? []);

        // إذا كان الخيار في قائمة الخيارات المحددة
        if (optionIds.contains(optionId)) {
          // إضافة المستخدم إلى قائمة المصوتين إذا لم يكن موجوداً
          if (!votedBy.contains(userId)) {
            votedBy.add(userId);
            option['votedBy'] = votedBy;
            option['votesCount'] = votedBy.length;
          }
        } else {
          // إزالة المستخدم من قائمة المصوتين إذا كان موجوداً
          if (votedBy.contains(userId)) {
            votedBy.remove(userId);
            option['votedBy'] = votedBy;
            option['votesCount'] = votedBy.length;
          }
        }

        options[i] = option;
      }

      // تحديث الاستطلاع
      await _firestore
          .collection('forum_topics')
          .doc(topicId)
          .collection('polls')
          .doc(pollId)
          .update({
        'options': options,
        'updatedAt': Timestamp.now(),
      });

      // تحديث الاستطلاع في الموضوع
      final topicRef = _firestore.collection('forum_topics').doc(topicId);
      final topicDoc = await topicRef.get();

      if (topicDoc.exists) {
        final topic = TopicModel.fromFirestore(topicDoc);
        if (topic.polls != null) {
          List<PollModel> polls = List<PollModel>.from(topic.polls!);

          // البحث عن الاستطلاع وتحديثه
          final pollIndex = polls.indexWhere((poll) => poll.id == pollId);
          if (pollIndex != -1) {
            // تحديث الاستطلاع في القائمة
            final updatedPoll = polls[pollIndex].copyWith(
              options: options.map((option) => PollOptionModel.fromMap(option)).toList(),
              updatedAt: DateTime.now());

            polls[pollIndex] = updatedPoll;

            // تحديث الموضوع
            await topicRef.update({
              'polls': polls.map((poll) => poll.toMap()).toList(),
              'updatedAt': Timestamp.now(),
            });
          }
        }
      }
    } catch (e) {
      developer.log('خطأ في التصويت على استطلاع الرأي: $e');
      rethrow;
    }
  }

  @override
  Future<void> unvoteOnPoll(String topicId, String pollId, String userId) async {
    try {
      // الحصول على الاستطلاع
      final pollDoc = await _firestore
          .collection('forum_topics')
          .doc(topicId)
          .collection('polls')
          .doc(pollId)
          .get();

      if (!pollDoc.exists) {
        throw Exception('الاستطلاع غير موجود');
      }

      final pollData = pollDoc.data()!;
      final options = List<Map<String, dynamic>>.from(pollData['options'] ?? []);

      // إزالة المستخدم من قائمة المصوتين لكل خيار
      for (int i = 0; i < options.length; i++) {
        final option = options[i];
        List<String> votedBy = List<String>.from(option['votedBy'] ?? []);

        // إزالة المستخدم من قائمة المصوتين إذا كان موجوداً
        if (votedBy.contains(userId)) {
          votedBy.remove(userId);
          option['votedBy'] = votedBy;
          option['votesCount'] = votedBy.length;
        }

        options[i] = option;
      }

      // تحديث الاستطلاع
      await _firestore
          .collection('forum_topics')
          .doc(topicId)
          .collection('polls')
          .doc(pollId)
          .update({
        'options': options,
        'updatedAt': Timestamp.now(),
      });

      // تحديث الاستطلاع في الموضوع
      final topicRef = _firestore.collection('forum_topics').doc(topicId);
      final topicDoc = await topicRef.get();

      if (topicDoc.exists) {
        final topic = TopicModel.fromFirestore(topicDoc);
        if (topic.polls != null) {
          List<PollModel> polls = List<PollModel>.from(topic.polls!);

          // البحث عن الاستطلاع وتحديثه
          final pollIndex = polls.indexWhere((poll) => poll.id == pollId);
          if (pollIndex != -1) {
            // تحديث الاستطلاع في القائمة
            final updatedPoll = polls[pollIndex].copyWith(
              options: options.map((option) => PollOptionModel.fromMap(option)).toList(),
              updatedAt: DateTime.now());

            polls[pollIndex] = updatedPoll;

            // تحديث الموضوع
            await topicRef.update({
              'polls': polls.map((poll) => poll.toMap()).toList(),
              'updatedAt': Timestamp.now(),
            });
          }
        }
      }
    } catch (e) {
      developer.log('خطأ في إلغاء التصويت على استطلاع الرأي: $e');
      rethrow;
    }
  }

  @override
  Future<List<PollModel>> getTopicPolls(String topicId) async {
    try {
      final snapshot = await _firestore
          .collection('forum_topics')
          .doc(topicId)
          .collection('polls')
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        return PollModel.fromMap(data);
      }).toList();
    } catch (e) {
      developer.log('خطأ في الحصول على استطلاعات الرأي للموضوع: $e');
      return [];
    }
  }

  // الاستماع للتغييرات

  @override
  Stream<List<CategoryModel>> listenToCategories() {
    return _firestore
        .collection('forum_categories')
        .orderBy('order')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => CategoryModel.fromFirestore(doc))
            .toList());
  }

  @override
  Stream<List<TopicModel>> listenToCategoryTopics(
    String categoryId, {
    int limit = 20,
    String? sortBy,
    bool descending = true,
  }) {
    Query query = _firestore
        .collection('forum_topics')
        .where('categoryId', isEqualTo: categoryId);

    if (sortBy != null) {
      query = query.orderBy(sortBy, descending: descending);
    } else {
      query = query.orderBy('createdAt', descending: true);
    }

    query = query.limit(limit);

    return query.snapshots().map((snapshot) =>
        snapshot.docs.map((doc) => TopicModel.fromFirestore(doc)).toList());
  }

  @override
  Stream<TopicModel?> listenToTopic(String topicId) {
    return _firestore
        .collection('forum_topics')
        .doc(topicId)
        .snapshots()
        .map((doc) => doc.exists ? TopicModel.fromFirestore(doc) : null);
  }

  @override
  Stream<List<PostModel>> listenToTopicPosts(
    String topicId, {
    int limit = 20,
    bool includeReplies = true,
  }) {
    Query query = _firestore
        .collection('forum_posts')
        .where('topicId', isEqualTo: topicId);

    if (!includeReplies) {
      query = query.where('parentId', isNull: true);
    }

    query = query.orderBy('createdAt', descending: false).limit(limit);

    return query.snapshots().map((snapshot) =>
        snapshot.docs.map((doc) => PostModel.fromFirestore(doc)).toList());
  }

  @override
  Stream<PostModel?> listenToPost(String postId) {
    return _firestore
        .collection('forum_posts')
        .doc(postId)
        .snapshots()
        .map((doc) => doc.exists ? PostModel.fromFirestore(doc) : null);
  }

  @override
  Stream<List<NotificationModel>> listenToUserNotifications(
    String userId, {
    int limit = 20,
    bool unreadOnly = false,
  }) {
    Query query = _firestore
        .collection('forum_notifications')
        .where('recipientId', isEqualTo: userId);

    if (unreadOnly) {
      query = query.where('isRead', isEqualTo: false);
    }

    query = query.orderBy('createdAt', descending: true).limit(limit);

    return query.snapshots().map((snapshot) => snapshot.docs
        .map((doc) => NotificationModel.fromFirestore(doc))
        .toList());
  }

  @override
  Stream<int> listenToUnreadNotificationsCount(String userId) {
    return _firestore
        .collection('forum_notifications')
        .where('recipientId', isEqualTo: userId)
        .where('isRead', isEqualTo: false)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  @override
  Stream<UserStatisticsModel?> listenToUserStatistics(String userId) {
    return _firestore
        .collection('forum_user_statistics')
        .doc(userId)
        .snapshots()
        .map((doc) =>
            doc.exists ? UserStatisticsModel.fromFirestore(doc) : null);
  }
}
