import 'package:cloud_firestore/cloud_firestore.dart';

/// حالة استبدال المكافأة
enum RedemptionStatus {
  /// في الانتظار
  pending,
  
  /// تم التأكيد
  confirmed,
  
  /// تم التسليم/التطبيق
  delivered,
  
  /// مرفوض
  rejected,
  
  /// منتهي الصلاحية
  expired,
  
  /// تم الإلغاء
  cancelled,
}

/// نموذج استبدال المكافأة للمستخدم
class UserRewardRedemptionModel {
  /// معرف الاستبدال
  final String id;
  
  /// معرف المستخدم
  final String userId;
  
  /// معرف المكافأة
  final String rewardId;
  
  /// اسم المكافأة (نسخة للحفظ)
  final String rewardName;
  
  /// وصف المكافأة (نسخة للحفظ)
  final String rewardDescription;
  
  /// نوع المكافأة (نسخة للحفظ)
  final int rewardType;
  
  /// عدد النقاط المستخدمة
  final int pointsUsed;
  
  /// قيمة المكافأة
  final double rewardValue;
  
  /// تاريخ الاستبدال
  final DateTime redemptionDate;
  
  /// حالة الاستبدال
  final RedemptionStatus status;
  
  /// رمز الكوبون المُنشأ (إن وجد)
  final String? couponCode;
  
  /// تاريخ انتهاء صلاحية الكوبون
  final DateTime? couponExpiryDate;
  
  /// ملاحظات الاستبدال
  final String? notes;
  
  /// معرف المعاملة (للمكافآت النقدية)
  final String? transactionId;
  
  /// تاريخ التسليم/التطبيق
  final DateTime? deliveryDate;
  
  /// تاريخ آخر تحديث
  final DateTime updatedAt;
  
  /// هل الاستبدال نشط
  bool get isActive {
    return status == RedemptionStatus.confirmed || 
           status == RedemptionStatus.delivered;
  }
  
  /// هل الكوبون صالح
  bool get isCouponValid {
    if (couponCode == null || couponExpiryDate == null) return false;
    return DateTime.now().isBefore(couponExpiryDate!) && isActive;
  }
  
  /// هل الاستبدال مكتمل
  bool get isCompleted {
    return status == RedemptionStatus.delivered;
  }
  
  /// هل الاستبدال في الانتظار
  bool get isPending {
    return status == RedemptionStatus.pending;
  }
  
  /// الحصول على نص حالة الاستبدال
  String get statusText {
    switch (status) {
      case RedemptionStatus.pending:
        return 'في الانتظار';
      case RedemptionStatus.confirmed:
        return 'تم التأكيد';
      case RedemptionStatus.delivered:
        return 'تم التسليم';
      case RedemptionStatus.rejected:
        return 'مرفوض';
      case RedemptionStatus.expired:
        return 'منتهي الصلاحية';
      case RedemptionStatus.cancelled:
        return 'تم الإلغاء';
    }
  }
  
  /// الحصول على أيام انتهاء الكوبون المتبقية
  int? get daysUntilCouponExpiry {
    if (couponExpiryDate == null) return null;
    final difference = couponExpiryDate!.difference(DateTime.now()).inDays;
    return difference > 0 ? difference : 0;
  }
  
  UserRewardRedemptionModel({
    required this.id,
    required this.userId,
    required this.rewardId,
    required this.rewardName,
    required this.rewardDescription,
    required this.rewardType,
    required this.pointsUsed,
    required this.rewardValue,
    required this.redemptionDate,
    required this.status,
    this.couponCode,
    this.couponExpiryDate,
    this.notes,
    this.transactionId,
    this.deliveryDate,
    required this.updatedAt,
  });
  
  /// إنشاء نسخة معدلة من الاستبدال
  UserRewardRedemptionModel copyWith({
    String? id,
    String? userId,
    String? rewardId,
    String? rewardName,
    String? rewardDescription,
    int? rewardType,
    int? pointsUsed,
    double? rewardValue,
    DateTime? redemptionDate,
    RedemptionStatus? status,
    String? couponCode,
    DateTime? couponExpiryDate,
    String? notes,
    String? transactionId,
    DateTime? deliveryDate,
    DateTime? updatedAt,
  }) {
    return UserRewardRedemptionModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      rewardId: rewardId ?? this.rewardId,
      rewardName: rewardName ?? this.rewardName,
      rewardDescription: rewardDescription ?? this.rewardDescription,
      rewardType: rewardType ?? this.rewardType,
      pointsUsed: pointsUsed ?? this.pointsUsed,
      rewardValue: rewardValue ?? this.rewardValue,
      redemptionDate: redemptionDate ?? this.redemptionDate,
      status: status ?? this.status,
      couponCode: couponCode ?? this.couponCode,
      couponExpiryDate: couponExpiryDate ?? this.couponExpiryDate,
      notes: notes ?? this.notes,
      transactionId: transactionId ?? this.transactionId,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
  
  /// تحويل الاستبدال إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'rewardId': rewardId,
      'rewardName': rewardName,
      'rewardDescription': rewardDescription,
      'rewardType': rewardType,
      'pointsUsed': pointsUsed,
      'rewardValue': rewardValue,
      'redemptionDate': redemptionDate.millisecondsSinceEpoch,
      'status': status.index,
      'couponCode': couponCode,
      'couponExpiryDate': couponExpiryDate?.millisecondsSinceEpoch,
      'notes': notes,
      'transactionId': transactionId,
      'deliveryDate': deliveryDate?.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }
  
  /// إنشاء استبدال من خريطة
  factory UserRewardRedemptionModel.fromMap(Map<String, dynamic> map) {
    return UserRewardRedemptionModel(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      rewardId: map['rewardId'] ?? '',
      rewardName: map['rewardName'] ?? '',
      rewardDescription: map['rewardDescription'] ?? '',
      rewardType: map['rewardType'] ?? 0,
      pointsUsed: map['pointsUsed'] ?? 0,
      rewardValue: (map['rewardValue'] ?? 0.0).toDouble(),
      redemptionDate: DateTime.fromMillisecondsSinceEpoch(
        map['redemptionDate'] ?? DateTime.now().millisecondsSinceEpoch,
      ),
      status: RedemptionStatus.values[map['status'] ?? 0],
      couponCode: map['couponCode'],
      couponExpiryDate: map['couponExpiryDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['couponExpiryDate'])
          : null,
      notes: map['notes'],
      transactionId: map['transactionId'],
      deliveryDate: map['deliveryDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['deliveryDate'])
          : null,
      updatedAt: DateTime.fromMillisecondsSinceEpoch(
        map['updatedAt'] ?? DateTime.now().millisecondsSinceEpoch,
      ),
    );
  }
  
  /// تحويل الاستبدال إلى Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'rewardId': rewardId,
      'rewardName': rewardName,
      'rewardDescription': rewardDescription,
      'rewardType': rewardType,
      'pointsUsed': pointsUsed,
      'rewardValue': rewardValue,
      'redemptionDate': Timestamp.fromDate(redemptionDate),
      'status': status.index,
      'couponCode': couponCode,
      'couponExpiryDate': couponExpiryDate != null 
          ? Timestamp.fromDate(couponExpiryDate!) 
          : null,
      'notes': notes,
      'transactionId': transactionId,
      'deliveryDate': deliveryDate != null 
          ? Timestamp.fromDate(deliveryDate!) 
          : null,
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }
  
  /// إنشاء استبدال من Firestore
  factory UserRewardRedemptionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return UserRewardRedemptionModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      rewardId: data['rewardId'] ?? '',
      rewardName: data['rewardName'] ?? '',
      rewardDescription: data['rewardDescription'] ?? '',
      rewardType: data['rewardType'] ?? 0,
      pointsUsed: data['pointsUsed'] ?? 0,
      rewardValue: (data['rewardValue'] ?? 0.0).toDouble(),
      redemptionDate: data['redemptionDate'] != null
          ? (data['redemptionDate'] as Timestamp).toDate()
          : DateTime.now(),
      status: RedemptionStatus.values[data['status'] ?? 0],
      couponCode: data['couponCode'],
      couponExpiryDate: data['couponExpiryDate'] != null
          ? (data['couponExpiryDate'] as Timestamp).toDate()
          : null,
      notes: data['notes'],
      transactionId: data['transactionId'],
      deliveryDate: data['deliveryDate'] != null
          ? (data['deliveryDate'] as Timestamp).toDate()
          : null,
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }
}
