import 'package:cloud_firestore/cloud_firestore.dart';

/// خدمة للتحقق من صلاحية كود الخصم
class DiscountCodeService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// التحقق من صلاحية كود الخصم
  /// [code] كود الخصم
  /// يعيد معلومات الخصم إذا كان الكود صالحًا، وإلا يعيد null
  Future<Map<String, dynamic>?> validateDiscountCode(String code) async {
    try {
      // البحث عن كود الخصم في قاعدة البيانات
      final snapshot = await _firestore
          .collection('discountCodes')
          .where('code', isEqualTo: code)
          .limit(1)
          .get();
      
      if (snapshot.docs.isEmpty) {
        return null;
      }
      
      final discountData = snapshot.docs.first.data();
      
      // التحقق من صلاحية الكود
      final isActive = discountData['isActive'] ?? false;
      if (!isActive) {
        return null;
      }
      
      // التحقق من تاريخ انتهاء الصلاحية
      final expiryDate = discountData['expiryDate'] as Timestamp?;
      if (expiryDate != null) {
        final now = DateTime.now();
        if (now.isAfter(expiryDate.toDate())) {
          return null;
        }
      }
      
      // التحقق من عدد مرات الاستخدام
      final maxUses = discountData['maxUses'] as int?;
      final usedCount = discountData['usedCount'] as int? ?? 0;
      
      if (maxUses != null && usedCount >= maxUses) {
        return null;
      }
      
      // الكود صالح، إرجاع معلومات الخصم
      return {
        'id': snapshot.docs.first.id,
        'code': discountData['code'],
        'discountType': discountData['discountType'] ?? 'percentage', // percentage or fixed
        'discountValue': discountData['discountValue'] ?? 0,
        'maxDiscountAmount': discountData['maxDiscountAmount'],
      };
    } catch (e) {
      return null;
    }
  }

  /// تطبيق الخصم على المبلغ
  /// [amount] المبلغ الأصلي
  /// [discountInfo] معلومات الخصم
  double applyDiscount(double amount, Map<String, dynamic> discountInfo) {
    final discountType = discountInfo['discountType'] as String;
    final discountValue = discountInfo['discountValue'] as num;
    final maxDiscountAmount = discountInfo['maxDiscountAmount'] as num?;
    
    double discountAmount = 0;
    
    if (discountType == 'percentage') {
      // خصم بالنسبة المئوية
      discountAmount = amount * (discountValue / 100);
    } else {
      // خصم بمبلغ ثابت
      discountAmount = discountValue.toDouble();
    }
    
    // التحقق من الحد الأقصى للخصم
    if (maxDiscountAmount != null && discountAmount > maxDiscountAmount) {
      discountAmount = maxDiscountAmount.toDouble();
    }
    
    // التأكد من أن الخصم لا يتجاوز المبلغ الأصلي
    if (discountAmount > amount) {
      discountAmount = amount;
    }
    
    return amount - discountAmount;
  }

  /// تحديث عدد مرات استخدام كود الخصم
  /// [codeId] معرف كود الخصم
  Future<void> incrementCodeUsage(String codeId) async {
    try {
      await _firestore.collection('discountCodes').doc(codeId).update({
        'usedCount': FieldValue.increment(1),
      });
    } catch (e) {
      // تجاهل الخطأ
    }
  }
}
