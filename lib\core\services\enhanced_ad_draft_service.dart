// lib/core/services/enhanced_ad_draft_service.dart
import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// خدمة محسنة لإدارة مسودات الإعلانات
/// تتيح حفظ واسترجاع مسودات الإعلانات بشكل تلقائي
class EnhancedAdDraftService {
  static const String _draftsKey = 'ad_drafts';
  static const String _lastDraftKey = 'last_draft';
  static const String _templatesKey = 'ad_templates';
  static const String _currentDraftKey = 'current_draft';
  static const int _maxDrafts = 10;

  /// مراقب تغييرات المسودات
  final StreamController<List<Map<String, dynamic>>> _draftsStreamController =
      StreamController<List<Map<String, dynamic>>>.broadcast();

  /// تدفق تغييرات المسودات
  Stream<List<Map<String, dynamic>>> get draftsStream => _draftsStreamController.stream;

  /// حفظ مسودة إعلان
  /// يتم حفظ المسودة مع معرف المستخدم الحالي
  Future<bool> saveDraft(Map<String, dynamic> draftData, {String? draftId}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = FirebaseAuth.instance.currentUser?.uid;

      if (userId == null) {
        return false;
      }

      // إضافة معرف المستخدم والوقت إلى بيانات المسودة
      draftData['userId'] = userId;
      draftData['timestamp'] = DateTime.now().millisecondsSinceEpoch;
      draftData['draftId'] = draftId ?? DateTime.now().millisecondsSinceEpoch.toString();

      // إضافة معلومات إضافية للمسودة
      draftData['lastModified'] = DateTime.now().toIso8601String();
      draftData['version'] = '2.0'; // إصدار محسن من المسودات

      // الحصول على المسودات الحالية
      final draftsJson = prefs.getString(_draftsKey) ?? '{}';
      final Map<String, dynamic> drafts = json.decode(draftsJson);

      // إضافة المسودة الجديدة أو تحديث مسودة موجودة
      if (!drafts.containsKey(userId)) {
        drafts[userId] = [];
      }

      final List<dynamic> userDrafts = List.from(drafts[userId]);

      // البحث عن المسودة إذا كان لها معرف
      if (draftId != null) {
        final index = userDrafts.indexWhere((draft) => draft['draftId'] == draftId);
        if (index != -1) {
          // الاحتفاظ بالبيانات السابقة ودمجها مع الجديدة
          final existingDraft = Map<String, dynamic>.from(userDrafts[index]);
          existingDraft.addAll(draftData);
          userDrafts[index] = existingDraft;
        } else {
          userDrafts.add(draftData);
        }
      } else {
        userDrafts.add(draftData);
      }

      // الاحتفاظ بعدد محدد من المسودات فقط
      if (userDrafts.length > _maxDrafts) {
        userDrafts.sort((a, b) => b['timestamp'].compareTo(a['timestamp']));
        userDrafts.removeRange(_maxDrafts, userDrafts.length);
      }

      drafts[userId] = userDrafts;

      // حفظ المسودات
      await prefs.setString(_draftsKey, json.encode(drafts));

      // حفظ آخر مسودة
      await prefs.setString(_lastDraftKey, json.encode(draftData));

      // تعيين المسودة الحالية
      await setCurrentDraft(draftData['draftId']);

      // إشعار المراقبين بتغيير المسودات
      _notifyDraftsChanged();

      return true;
    } catch (e) {
      print('خطأ في حفظ المسودة: $e');
      return false;
    }
  }

  /// الحصول على مسودات المستخدم الحالي
  Future<List<Map<String, dynamic>>> getUserDrafts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = FirebaseAuth.instance.currentUser?.uid;

      if (userId == null) {
        return [];
      }

      final draftsJson = prefs.getString(_draftsKey) ?? '{}';
      final Map<String, dynamic> drafts = json.decode(draftsJson);

      if (!drafts.containsKey(userId)) {
        return [];
      }

      final List<dynamic> userDrafts = List.from(drafts[userId]);

      // ترتيب المسودات حسب الوقت (الأحدث أولاً)
      userDrafts.sort((a, b) => b['timestamp'].compareTo(a['timestamp']));

      return userDrafts.map((draft) => Map<String, dynamic>.from(draft)).toList();
    } catch (e) {
      print('خطأ في الحصول على المسودات: $e');
      return [];
    }
  }

  /// الحصول على آخر مسودة
  Future<Map<String, dynamic>?> getLastDraft() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = FirebaseAuth.instance.currentUser?.uid;

      if (userId == null) {
        return null;
      }

      final lastDraftJson = prefs.getString(_lastDraftKey);
      if (lastDraftJson == null) {
        return null;
      }

      final Map<String, dynamic> lastDraft = json.decode(lastDraftJson);

      // التحقق من أن المسودة تنتمي للمستخدم الحالي
      if (lastDraft['userId'] != userId) {
        return null;
      }

      return lastDraft;
    } catch (e) {
      print('خطأ في الحصول على آخر مسودة: $e');
      return null;
    }
  }

  /// حذف مسودة
  Future<bool> deleteDraft(String draftId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = FirebaseAuth.instance.currentUser?.uid;

      if (userId == null) {
        return false;
      }

      final draftsJson = prefs.getString(_draftsKey) ?? '{}';
      final Map<String, dynamic> drafts = json.decode(draftsJson);

      if (!drafts.containsKey(userId)) {
        return false;
      }

      final List<dynamic> userDrafts = List.from(drafts[userId]);

      // حذف المسودة
      userDrafts.removeWhere((draft) => draft['draftId'] == draftId);

      drafts[userId] = userDrafts;

      // حفظ المسودات
      await prefs.setString(_draftsKey, json.encode(drafts));

      // إذا كانت المسودة الحالية، إعادة تعيين المسودة الحالية
      final currentDraftId = await getCurrentDraftId();
      if (currentDraftId == draftId) {
        await clearCurrentDraft();
      }

      // إشعار المراقبين بتغيير المسودات
      _notifyDraftsChanged();

      return true;
    } catch (e) {
      print('خطأ في حذف المسودة: $e');
      return false;
    }
  }

  /// حفظ قالب إعلان
  Future<bool> saveTemplate(Map<String, dynamic> templateData, String templateName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = FirebaseAuth.instance.currentUser?.uid;

      if (userId == null) {
        return false;
      }

      // إضافة معرف المستخدم والاسم إلى بيانات القالب
      templateData['userId'] = userId;
      templateData['templateName'] = templateName;
      templateData['timestamp'] = DateTime.now().millisecondsSinceEpoch;

      // الحصول على القوالب الحالية
      final templatesJson = prefs.getString(_templatesKey) ?? '{}';
      final Map<String, dynamic> templates = json.decode(templatesJson);

      // إضافة القالب الجديد
      if (!templates.containsKey(userId)) {
        templates[userId] = [];
      }

      final List<dynamic> userTemplates = List.from(templates[userId]);

      // البحث عن القالب إذا كان موجوداً بنفس الاسم
      final index = userTemplates.indexWhere((template) => template['templateName'] == templateName);
      if (index != -1) {
        userTemplates[index] = templateData;
      } else {
        userTemplates.add(templateData);
      }

      templates[userId] = userTemplates;

      // حفظ القوالب
      await prefs.setString(_templatesKey, json.encode(templates));

      return true;
    } catch (e) {
      print('خطأ في حفظ القالب: $e');
      return false;
    }
  }

  /// الحصول على قوالب المستخدم الحالي
  Future<List<Map<String, dynamic>>> getUserTemplates() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = FirebaseAuth.instance.currentUser?.uid;

      if (userId == null) {
        return [];
      }

      final templatesJson = prefs.getString(_templatesKey) ?? '{}';
      final Map<String, dynamic> templates = json.decode(templatesJson);

      if (!templates.containsKey(userId)) {
        return [];
      }

      final List<dynamic> userTemplates = List.from(templates[userId]);

      return userTemplates.map((template) => Map<String, dynamic>.from(template)).toList();
    } catch (e) {
      print('خطأ في الحصول على القوالب: $e');
      return [];
    }
  }

  /// حذف قالب
  Future<bool> deleteTemplate(String templateName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = FirebaseAuth.instance.currentUser?.uid;

      if (userId == null) {
        return false;
      }

      final templatesJson = prefs.getString(_templatesKey) ?? '{}';
      final Map<String, dynamic> templates = json.decode(templatesJson);

      if (!templates.containsKey(userId)) {
        return false;
      }

      final List<dynamic> userTemplates = List.from(templates[userId]);

      // حذف القالب
      userTemplates.removeWhere((template) => template['templateName'] == templateName);

      templates[userId] = userTemplates;

      // حفظ القوالب
      await prefs.setString(_templatesKey, json.encode(templates));

      return true;
    } catch (e) {
      print('خطأ في حذف القالب: $e');
      return false;
    }
  }

  /// تعيين المسودة الحالية
  Future<bool> setCurrentDraft(String draftId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_currentDraftKey, draftId);
      return true;
    } catch (e) {
      print('خطأ في تعيين المسودة الحالية: $e');
      return false;
    }
  }

  /// الحصول على معرف المسودة الحالية
  Future<String?> getCurrentDraftId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_currentDraftKey);
    } catch (e) {
      print('خطأ في الحصول على معرف المسودة الحالية: $e');
      return null;
    }
  }

  /// مسح المسودة الحالية
  Future<bool> clearCurrentDraft() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_currentDraftKey);
      return true;
    } catch (e) {
      print('خطأ في مسح المسودة الحالية: $e');
      return false;
    }
  }

  /// إشعار المراقبين بتغيير المسودات
  void _notifyDraftsChanged() async {
    final drafts = await getUserDrafts();
    _draftsStreamController.add(drafts);
  }

  /// الحصول على مسودة محددة بالمعرف
  Future<Map<String, dynamic>?> getDraftById(String draftId) async {
    try {
      final drafts = await getUserDrafts();
      return drafts.firstWhere(
        (draft) => draft['draftId'] == draftId,
        orElse: () => <String, dynamic>{},
      );
    } catch (e) {
      return null;
    }
  }

  /// حفظ تلقائي للمسودة
  Future<bool> autoSaveDraft(Map<String, dynamic> draftData, {String? draftId}) async {
    // إضافة علامة الحفظ التلقائي
    draftData['isAutoSaved'] = true;
    return await saveDraft(draftData, draftId: draftId);
  }

  /// تحديد الخطوة التالية بناءً على البيانات المحفوظة
  int getNextStep(Map<String, dynamic> draftData) {
    // الخطوة 1: اختيار التصنيف
    if (draftData['mainCategory'] == null || draftData['subCategory'] == null) {
      return 1;
    }

    // الخطوة 2: رفع الصور
    if (draftData['imagePaths'] == null || (draftData['imagePaths'] as List).isEmpty) {
      return 2;
    }

    // الخطوة 3: تفاصيل الإعلان
    if (draftData['title'] == null || draftData['price'] == null) {
      return 3;
    }

    // الخطوة 4: الإعدادات
    if (draftData['planType'] == null) {
      return 4;
    }

    // الخطوة 5: المعاينة والنشر
    return 5;
  }

  /// حساب نسبة اكتمال المسودة
  double getDraftCompletionPercentage(Map<String, dynamic> draftData) {
    int completedSteps = 0;
    const int totalSteps = 5;

    // التحقق من اكتمال كل خطوة
    if (draftData['mainCategory'] != null && draftData['subCategory'] != null) {
      completedSteps++;
    }

    if (draftData['imagePaths'] != null && (draftData['imagePaths'] as List).isNotEmpty) {
      completedSteps++;
    }

    if (draftData['title'] != null && draftData['price'] != null) {
      completedSteps++;
    }

    if (draftData['planType'] != null) {
      completedSteps++;
    }

    if (draftData['isReadyForPublish'] == true) {
      completedSteps++;
    }

    return completedSteps / totalSteps;
  }

  /// تنظيف المسودات القديمة (أكثر من 30 يوم)
  Future<bool> cleanOldDrafts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = FirebaseAuth.instance.currentUser?.uid;

      if (userId == null) {
        return false;
      }

      final draftsJson = prefs.getString(_draftsKey) ?? '{}';
      final Map<String, dynamic> drafts = json.decode(draftsJson);

      if (!drafts.containsKey(userId)) {
        return true;
      }

      final List<dynamic> userDrafts = List.from(drafts[userId]);
      final now = DateTime.now().millisecondsSinceEpoch;
      const thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000;

      // إزالة المسودات القديمة
      userDrafts.removeWhere((draft) {
        final timestamp = draft['timestamp'] as int? ?? 0;
        return (now - timestamp) > thirtyDaysInMs;
      });

      drafts[userId] = userDrafts;
      await prefs.setString(_draftsKey, json.encode(drafts));

      _notifyDraftsChanged();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// التنظيف عند التخلص من الخدمة
  void dispose() {
    _draftsStreamController.close();
  }
}
