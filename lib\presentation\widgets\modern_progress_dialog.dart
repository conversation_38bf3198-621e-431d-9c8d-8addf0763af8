import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// حوار مؤشر التقدم العصري لنشر الإعلان
class ModernProgressDialog extends StatefulWidget {
  final String title;
  final List<String> steps;
  final int currentStep;
  final bool isCompleted;
  final String? errorMessage;

  const ModernProgressDialog({
    super.key,
    required this.title,
    required this.steps,
    required this.currentStep,
    this.isCompleted = false,
    this.errorMessage,
  });

  @override
  State<ModernProgressDialog> createState() => _ModernProgressDialogState();
}

class _ModernProgressDialogState extends State<ModernProgressDialog>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _pulseController;
  late Animation<double> _progressAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.elasticInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  @override
  void didUpdateWidget(ModernProgressDialog oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.currentStep != oldWidget.currentStep) {
      _progressController.animateTo(
        widget.currentStep / widget.steps.length,
      );
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(51), // 0.2 * 255
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // العنوان
            Text(
              widget.title,
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 24),
            
            // مؤشر التقدم الدائري
            if (!widget.isCompleted && widget.errorMessage == null)
              ScaleTransition(
                scale: _pulseAnimation,
                child: SizedBox(
                  width: 80,
                  height: 80,
                  child: Stack(
                    children: [
                      // الخلفية
                      CircularProgressIndicator(
                        value: 1.0,
                        strokeWidth: 6,
                        backgroundColor: Colors.grey.shade200,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.grey.shade200,
                        ),
                      ),
                      // التقدم
                      AnimatedBuilder(
                        animation: _progressAnimation,
                        builder: (context, child) {
                          return CircularProgressIndicator(
                            value: _progressAnimation.value,
                            strokeWidth: 6,
                            backgroundColor: Colors.transparent,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).primaryColor,
                            ),
                          );
                        },
                      ),
                      // النسبة المئوية
                      Center(
                        child: AnimatedBuilder(
                          animation: _progressAnimation,
                          builder: (context, child) {
                            final percentage = (_progressAnimation.value * 100).round();
                            return Text(
                              '$percentage%',
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            
            // أيقونة النجاح
            if (widget.isCompleted)
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 40,
                ),
              ),
            
            // أيقونة الخطأ
            if (widget.errorMessage != null)
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.error,
                  color: Colors.white,
                  size: 40,
                ),
              ),
            
            const SizedBox(height: 24),
            
            // الخطوة الحالية
            if (!widget.isCompleted && widget.errorMessage == null)
              Text(
                widget.currentStep < widget.steps.length
                    ? widget.steps[widget.currentStep]
                    : 'جاري الإنتهاء...',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
            
            // رسالة النجاح
            if (widget.isCompleted)
              Column(
                children: [
                  Text(
                    'تم نشر الإعلان بنجاح! 🎉',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'سيتم توجيهك إلى صفحة عقاراتي',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            
            // رسالة الخطأ
            if (widget.errorMessage != null)
              Column(
                children: [
                  Text(
                    'حدث خطأ أثناء النشر',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.errorMessage!,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            
            const SizedBox(height: 16),
            
            // مؤشر التقدم الخطي
            if (!widget.isCompleted && widget.errorMessage == null)
              Column(
                children: [
                  AnimatedBuilder(
                    animation: _progressAnimation,
                    builder: (context, child) {
                      return LinearProgressIndicator(
                        value: _progressAnimation.value,
                        backgroundColor: Colors.grey.shade200,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).primaryColor,
                        ),
                        minHeight: 6,
                      );
                    },
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'الخطوة ${widget.currentStep + 1} من ${widget.steps.length}',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
