import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// خدمة للتحقق من صلاحيات المستخدم
class UserPermissionService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// التحقق من تسجيل دخول المستخدم
  bool isUserLoggedIn() {
    return _auth.currentUser != null;
  }

  /// التحقق من تأكيد البريد الإلكتروني للمستخدم
  bool isEmailVerified() {
    final user = _auth.currentUser;
    return user != null && user.emailVerified;
  }

  /// التحقق من صلاحية المستخدم لإنشاء إعلان
  /// يتحقق من تسجيل الدخول وتأكيد البريد الإلكتروني وعدم حظر المستخدم
  Future<Map<String, dynamic>> canCreateAd() async {
    if (!isUserLoggedIn()) {
      return {
        'canCreate': false,
        'message': 'يجب تسجيل الدخول أولاً',
      };
    }

    if (!isEmailVerified()) {
      return {
        'canCreate': false,
        'message': 'يجب تأكيد البريد الإلكتروني أولاً',
      };
    }

    try {
      final user = _auth.currentUser!;
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      
      if (!userDoc.exists) {
        return {
          'canCreate': false,
          'message': 'لم يتم العثور على بيانات المستخدم',
        };
      }
      
      final userData = userDoc.data()!;
      
      // التحقق من عدم حظر المستخدم
      if (userData['isBlocked'] == true) {
        return {
          'canCreate': false,
          'message': 'تم حظر حسابك. يرجى التواصل مع الدعم الفني',
        };
      }
      
      // التحقق من عدد الإعلانات المسموح بها
      final adsLimit = userData['adsLimit'] ?? 10;
      final adsCount = await _getUserAdsCount(user.uid);
      
      if (adsCount >= adsLimit) {
        return {
          'canCreate': false,
          'message': 'لقد وصلت إلى الحد الأقصى من الإعلانات المسموح بها ($adsLimit)',
        };
      }
      
      return {
        'canCreate': true,
        'message': '',
      };
    } catch (e) {
      return {
        'canCreate': false,
        'message': 'حدث خطأ أثناء التحقق من صلاحيات المستخدم',
      };
    }
  }

  /// الحصول على عدد إعلانات المستخدم
  Future<int> _getUserAdsCount(String userId) async {
    try {
      final snapshot = await _firestore
          .collection('estates')
          .where('ownerId', isEqualTo: userId)
          .get();
      
      return snapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }
}
