import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/presentation/widgets/loading_widget.dart';
import 'package:kuwait_corners/presentation/widgets/project_background_widget.dart';


/// صفحة إدارة المهام
class TaskManagementPage extends StatefulWidget {
  final String? projectId;

  const TaskManagementPage({super.key, this.projectId});

  @override
  State<TaskManagementPage> createState() => _TaskManagementPageState();
}

class _TaskManagementPageState extends State<TaskManagementPage> with TickerProviderStateMixin {
  late TabController _tabController;
  List<Map<String, dynamic>> _tasks = [];
  bool _isLoading = true;
  String _searchQuery = '';
  final String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadTasks();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadTasks() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      Query query = FirebaseFirestore.instance.collection('project_tasks');

      if (widget.projectId != null) {
        query = query.where('projectId', isEqualTo: widget.projectId);
      } else {
        // إذا لم يتم تحديد مشروع، اجلب المهام من جميع مشاريع الشركة
        final projectsSnapshot = await FirebaseFirestore.instance
            .collection('projects')
            .where('companyId', isEqualTo: currentUser.uid)
            .get();

        final projectIds = projectsSnapshot.docs.map((doc) => doc.id).toList();
        if (projectIds.isNotEmpty) {
          query = query.where('projectId', whereIn: projectIds);
        }
      }

      final snapshot = await query.orderBy('createdAt', descending: true).get();

      setState(() {
        _tasks = snapshot.docs.map((doc) {
          final data = doc.data() as Map<String, dynamic>;
          data['id'] = doc.id;
          return data;
        }).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          widget.projectId != null ? 'مهام المشروع' : 'إدارة المهام',
          style: CairoTextStyles.appBarTitle,
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.add_task),
            onPressed: _addTask,
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            icon: const Icon(Icons.more_vert),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'filter',
                child: Row(
                  children: [
                    Icon(Icons.filter_list, size: 16),
                    SizedBox(width: 8),
                    Text('فلترة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download, size: 16),
                    SizedBox(width: 8),
                    Text('تصدير'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          labelStyle: CairoTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
          tabs: const [
            Tab(text: 'الكل', icon: Icon(Icons.list, size: 20)),
            Tab(text: 'قيد التنفيذ', icon: Icon(Icons.play_arrow, size: 20)),
            Tab(text: 'مكتملة', icon: Icon(Icons.check_circle, size: 20)),
            Tab(text: 'متأخرة', icon: Icon(Icons.warning, size: 20)),
          ],
        ),
      ),
      body: ProjectBackgroundWidget(
        child: Column(
          children: [
            // شريط البحث والفلترة
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  bottom: BorderSide(color: Colors.grey[200]!, width: 1),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      style: CairoTextStyles.bodyMedium,
                      decoration: InputDecoration(
                        hintText: 'البحث عن مهمة...',
                        hintStyle: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[500]),
                        prefixIcon: Icon(Icons.search, color: AppColors.primary),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: AppColors.primary.withValues(alpha: 0.3)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: AppColors.primary, width: 2),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: IconButton(
                      onPressed: _showFilterDialog,
                      icon: Icon(Icons.tune, color: AppColors.primary),
                    ),
                  ),
                ],
              ),
            ),

            // المحتوى
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildTasksList('all'),
                  _buildTasksList('inProgress'),
                  _buildTasksList('completed'),
                  _buildTasksList('overdue'),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addTask,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add_task, color: Colors.white),
      ),
    );
  }

  Widget _buildTasksList(String filter) {
    if (_isLoading) {
      return const Center(child: LoadingWidget());
    }

    final filteredTasks = _getFilteredTasks(filter);

    if (filteredTasks.isEmpty) {
      return _buildEmptyState(filter);
    }

    return RefreshIndicator(
      onRefresh: _loadTasks,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredTasks.length,
        itemBuilder: (context, index) {
          return _buildTaskCard(filteredTasks[index]);
        },
      ),
    );
  }

  Widget _buildTaskCard(Map<String, dynamic> taskData) {
    final title = taskData['title'] ?? 'مهمة غير محددة';
    final description = taskData['description'] ?? '';
    final status = taskData['status'] ?? 'pending';
    final priority = taskData['priority'] ?? 'medium';
    final progress = (taskData['progress'] ?? 0.0).toDouble();
    final assignedToName = taskData['assignedToName'] ?? '';
    final dueDate = taskData['dueDate'] as Timestamp?;
    final createdAt = taskData['createdAt'] as Timestamp?;

    final isOverdue = dueDate != null &&
                     DateTime.now().isAfter(dueDate.toDate()) &&
                     status != 'completed';

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isOverdue
              ? Colors.red.withValues(alpha: 0.3)
              : AppColors.primary.withValues(alpha: 0.1),
          width: 1
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الصف الأول: العنوان والحالة
          Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: CairoTextStyles.cardTitle,
                ),
              ),
              const SizedBox(width: 12),
              _buildTaskStatusChip(status),
            ],
          ),

          if (description.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              description,
              style: CairoTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],

          const SizedBox(height: 12),

          // الصف الثاني: الأولوية والمسؤول
          Row(
            children: [
              _buildTaskPriorityChip(priority),
              const Spacer(),
              if (assignedToName.isNotEmpty) ...[
                Icon(Icons.person, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  assignedToName,
                  style: CairoTextStyles.bodySmall,
                ),
              ],
            ],
          ),

          const SizedBox(height: 12),

          // شريط التقدم
          Row(
            children: [
              Text(
                'التقدم',
                style: CairoTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                '${progress.toStringAsFixed(0)}%',
                style: CairoTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          LinearProgressIndicator(
            value: progress / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              progress >= 75 ? Colors.green :
              progress >= 50 ? AppColors.primary : Colors.orange,
            ),
          ),

          const SizedBox(height: 12),

          // التواريخ والإجراءات
          Row(
            children: [
              if (dueDate != null) ...[
                Icon(
                  Icons.schedule,
                  size: 16,
                  color: isOverdue ? Colors.red : Colors.grey[600]
                ),
                const SizedBox(width: 6),
                Text(
                  'الاستحقاق: ${_formatDate(dueDate.toDate())}',
                  style: CairoTextStyles.bodySmall.copyWith(
                    color: isOverdue ? Colors.red : Colors.grey[600],
                  ),
                ),
              ],
              const Spacer(),
              PopupMenuButton<String>(
                onSelected: (value) => _handleTaskAction(value, taskData['id']),
                icon: Icon(Icons.more_vert, color: Colors.grey[600], size: 20),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'view',
                    child: Row(
                      children: [
                        Icon(Icons.visibility, size: 16),
                        SizedBox(width: 8),
                        Text('عرض'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 16),
                        SizedBox(width: 8),
                        Text('تعديل'),
                      ],
                    ),
                  ),
                  if (status != 'completed')
                    const PopupMenuItem(
                      value: 'complete',
                      child: Row(
                        children: [
                          Icon(Icons.check_circle, size: 16, color: Colors.green),
                          SizedBox(width: 8),
                          Text('إكمال', style: TextStyle(color: Colors.green)),
                        ],
                      ),
                    ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 16, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String filter) {
    String message;
    IconData icon;

    switch (filter) {
      case 'inProgress':
        message = 'لا توجد مهام قيد التنفيذ';
        icon = Icons.play_arrow;
        break;
      case 'completed':
        message = 'لا توجد مهام مكتملة';
        icon = Icons.check_circle;
        break;
      case 'overdue':
        message = 'لا توجد مهام متأخرة';
        icon = Icons.warning;
        break;
      default:
        message = 'لا توجد مهام';
        icon = Icons.task_alt;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: CairoTextStyles.bodyLarge.copyWith(color: Colors.grey[600]),
          ),
          if (filter == 'all') ...[
            const SizedBox(height: 8),
            Text(
              'اضغط على "+" لإضافة مهمة جديدة',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[500]),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTaskStatusChip(String status) {
    Color color;
    String displayText;

    switch (status) {
      case 'pending':
        color = Colors.orange;
        displayText = 'معلقة';
        break;
      case 'inProgress':
        color = Colors.blue;
        displayText = 'قيد التنفيذ';
        break;
      case 'completed':
        color = Colors.green;
        displayText = 'مكتملة';
        break;
      case 'cancelled':
        color = Colors.red;
        displayText = 'ملغية';
        break;
      case 'onHold':
        color = Colors.grey;
        displayText = 'متوقفة';
        break;
      default:
        color = Colors.grey;
        displayText = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color),
      ),
      child: Text(
        displayText,
        style: CairoTextStyles.bodySmall.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildTaskPriorityChip(String priority) {
    Color color;
    IconData icon;
    String displayText;

    switch (priority) {
      case 'urgent':
        color = Colors.purple;
        icon = Icons.priority_high;
        displayText = 'عاجل';
        break;
      case 'high':
        color = Colors.red;
        icon = Icons.keyboard_arrow_up;
        displayText = 'عالي';
        break;
      case 'medium':
        color = Colors.orange;
        icon = Icons.remove;
        displayText = 'متوسط';
        break;
      case 'low':
        color = Colors.green;
        icon = Icons.keyboard_arrow_down;
        displayText = 'منخفض';
        break;
      default:
        color = Colors.grey;
        icon = Icons.remove;
        displayText = priority;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            displayText,
            style: CairoTextStyles.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredTasks(String filter) {
    List<Map<String, dynamic>> filtered = _tasks;

    // تطبيق فلتر البحث
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((task) {
        final title = task['title']?.toString().toLowerCase() ?? '';
        final description = task['description']?.toString().toLowerCase() ?? '';
        final assignedTo = task['assignedToName']?.toString().toLowerCase() ?? '';

        return title.contains(query) ||
               description.contains(query) ||
               assignedTo.contains(query);
      }).toList();
    }

    // تطبيق فلتر الحالة
    switch (filter) {
      case 'inProgress':
        filtered = filtered.where((task) => task['status'] == 'inProgress').toList();
        break;
      case 'completed':
        filtered = filtered.where((task) => task['status'] == 'completed').toList();
        break;
      case 'overdue':
        filtered = filtered.where((task) {
          final dueDate = task['dueDate'] as Timestamp?;
          final status = task['status'] ?? 'pending';
          return dueDate != null &&
                 DateTime.now().isAfter(dueDate.toDate()) &&
                 status != 'completed';
        }).toList();
        break;
    }

    return filtered;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // وظائف الإجراءات
  void _addTask() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'إضافة مهمة جديدة قيد التطوير',
          style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
        ),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'filter':
        _showFilterDialog();
        break;
      case 'export':
        _exportTasks();
        break;
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'فلترة المهام',
          style: CairoTextStyles.titleMedium,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'خيارات الفلترة قيد التطوير',
              style: CairoTextStyles.bodyMedium,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: CairoTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  void _exportTasks() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تصدير المهام قيد التطوير',
          style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
        ),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _handleTaskAction(String action, String taskId) {
    switch (action) {
      case 'view':
        _viewTaskDetails(taskId);
        break;
      case 'edit':
        _editTask(taskId);
        break;
      case 'complete':
        _completeTask(taskId);
        break;
      case 'delete':
        _deleteTask(taskId);
        break;
    }
  }

  void _viewTaskDetails(String taskId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'عرض تفاصيل المهمة قيد التطوير',
          style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
        ),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _editTask(String taskId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تعديل المهمة قيد التطوير',
          style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
        ),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _completeTask(String taskId) async {
    try {
      await FirebaseFirestore.instance
          .collection('project_tasks')
          .doc(taskId)
          .update({
        'status': 'completed',
        'progress': 100.0,
        'completedDate': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إكمال المهمة بنجاح',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
            ),
            backgroundColor: Colors.green,
          ),
        );
        _loadTasks();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ في إكمال المهمة',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _deleteTask(String taskId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد الحذف',
          style: CairoTextStyles.titleMedium,
        ),
        content: Text(
          'هل أنت متأكد من حذف هذه المهمة؟ لا يمكن التراجع عن هذا الإجراء.',
          style: CairoTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'إلغاء',
              style: CairoTextStyles.bodyMedium,
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'حذف',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        await FirebaseFirestore.instance
            .collection('project_tasks')
            .doc(taskId)
            .delete();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم حذف المهمة بنجاح',
                style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
              ),
              backgroundColor: Colors.green,
            ),
          );
          _loadTasks();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'حدث خطأ في حذف المهمة',
                style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
