/// إعدادات وضع التطبيق - للتحكم في الميزات المالية
class AppModeConfig {
  // وضع التطبيق: معلوماتي فقط (بدون ميزات مالية)
  static const bool isInformationalOnly = true;

  // تعطيل جميع الميزات المالية
  static const bool enablePaymentProcessing = false;
  static const bool enableBankingIntegration = false;
  static const bool enableSubscriptions = false;
  static const bool enableCommercialTransactions = false;
  static const bool enableMortgageApplications = false;
  static const bool enableWamdPayments = false;

  // الميزات المسموحة في الوضع المعلوماتي
  static const bool enablePropertyViewing = true;
  static const bool enablePropertySearch = true;
  static const bool enablePropertyFiltering = true;
  static const bool enableContactInfo = true;
  static const bool enablePropertyImages = true;
  static const bool enablePropertyDetails = true;
  static const bool enableUserProfiles = true;
  static const bool enableFavorites = true;
  static const bool enablePropertyRequests = true;

  // رسائل للمستخدمين عند محاولة الوصول للميزات المعطلة
  static const String paymentDisabledMessage =
      'هذه الميزة غير متاحة حالياً. يرجى التواصل مع المعلن مباشرة.';

  static const String subscriptionDisabledMessage =
      'خدمة الاشتراكات غير متاحة حالياً. التطبيق مجاني بالكامل.';

  static const String bankingDisabledMessage =
      'للحصول على خدمات التمويل، يرجى التواصل مع البنوك مباشرة.';

  /// التحقق من إمكانية الوصول لميزة معينة
  static bool canAccessFeature(String featureName) {
    if (isInformationalOnly) {
      switch (featureName) {
        case 'payment':
        case 'banking':
        case 'subscription':
        case 'commercial_transaction':
        case 'mortgage':
        case 'wamd_payment':
          return false;
        default:
          return true;
      }
    }
    return true;
  }

  /// التحقق من إمكانية عرض الميزات المالية
  static bool shouldShowFinancialFeatures() {
    return !isInformationalOnly;
  }

  /// التحقق من إمكانية عرض أزرار الدفع
  static bool shouldShowPaymentButtons() {
    return !isInformationalOnly;
  }

  /// التحقق من إمكانية عرض أزرار الترقية
  static bool shouldShowUpgradeButtons() {
    return !isInformationalOnly;
  }

  /// التحقق من إمكانية عرض أزرار الاشتراك
  static bool shouldShowSubscriptionButtons() {
    return !isInformationalOnly;
  }

  /// التحقق من إمكانية عرض أزرار النسخ المدفوع
  static bool shouldShowCopyButtons() {
    return !isInformationalOnly;
  }

  /// الحصول على رسالة التعطيل للميزة
  static String getDisabledMessage(String featureName) {
    switch (featureName) {
      case 'payment':
      case 'wamd_payment':
        return paymentDisabledMessage;
      case 'subscription':
        return subscriptionDisabledMessage;
      case 'banking':
      case 'mortgage':
        return bankingDisabledMessage;
      default:
        return 'هذه الميزة غير متاحة حالياً.';
    }
  }

  /// معلومات التطبيق في الوضع المعلوماتي
  static const String appDescription =
      'تطبيق KREA - دليل العقارات في الكويت. تصفح العقارات واطلع على التفاصيل وتواصل مع المعلنين مباشرة.';

  static const String appDisclaimer =
      'هذا التطبيق للعرض والمعلومات فقط. جميع المعاملات المالية تتم خارج التطبيق مع المعلنين مباشرة.';
}
