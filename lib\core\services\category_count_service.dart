import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// خدمة حساب عدد العقارات في كل تصنيف
class CategoryCountService {
  static final CategoryCountService _instance = CategoryCountService._internal();
  factory CategoryCountService() => _instance;
  CategoryCountService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // كاش للأعداد لتحسين الأداء
  final Map<String, int> _countCache = {};
  DateTime? _lastCacheUpdate;
  static const Duration _cacheValidDuration = Duration(minutes: 5);

  /// الحصول على عدد العقارات في تصنيف فرعي محدد
  Future<int> getSubCategoryCount(String mainCategory, String subCategory) async {
    try {
      final cacheKey = '${mainCategory}_$subCategory';
      
      // التحقق من الكاش
      if (_isCacheValid() && _countCache.containsKey(cacheKey)) {
        return _countCache[cacheKey]!;
      }

      // استعلام قاعدة البيانات
      final query = _firestore
          .collection('estates')
          .where('mainCategory', isEqualTo: mainCategory)
          .where('subCategory', isEqualTo: subCategory)
          .where('isActive', isEqualTo: true);

      final snapshot = await query.count().get();
      final count = snapshot.count ?? 0;

      // حفظ في الكاش
      _countCache[cacheKey] = count;
      _lastCacheUpdate = DateTime.now();

      return count;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد العقارات: $e');
      return 0;
    }
  }

  /// الحصول على عدد العقارات في تصنيف رئيسي
  Future<int> getMainCategoryCount(String mainCategory) async {
    try {
      final cacheKey = 'main_$mainCategory';
      
      // التحقق من الكاش
      if (_isCacheValid() && _countCache.containsKey(cacheKey)) {
        return _countCache[cacheKey]!;
      }

      // استعلام قاعدة البيانات
      final query = _firestore
          .collection('estates')
          .where('mainCategory', isEqualTo: mainCategory)
          .where('isActive', isEqualTo: true);

      final snapshot = await query.count().get();
      final count = snapshot.count ?? 0;

      // حفظ في الكاش
      _countCache[cacheKey] = count;
      _lastCacheUpdate = DateTime.now();

      return count;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد العقارات: $e');
      return 0;
    }
  }

  /// الحصول على أعداد جميع التصنيفات الفرعية لتصنيف رئيسي
  Future<Map<String, int>> getAllSubCategoryCounts(String mainCategory, List<String> subCategories) async {
    try {
      final Map<String, int> counts = {};
      
      // معالجة متوازية للحصول على الأعداد
      final futures = subCategories.map((subCategory) async {
        final count = await getSubCategoryCount(mainCategory, subCategory);
        return MapEntry(subCategory, count);
      });

      final results = await Future.wait(futures);
      
      for (final entry in results) {
        counts[entry.key] = entry.value;
      }

      return counts;
    } catch (e) {
      debugPrint('خطأ في الحصول على أعداد التصنيفات الفرعية: $e');
      return {};
    }
  }

  /// تحديث الكاش بالقوة
  Future<void> refreshCache() async {
    _countCache.clear();
    _lastCacheUpdate = null;
  }

  /// التحقق من صحة الكاش
  bool _isCacheValid() {
    if (_lastCacheUpdate == null) return false;
    return DateTime.now().difference(_lastCacheUpdate!) < _cacheValidDuration;
  }

  /// الحصول على إجمالي عدد العقارات النشطة
  Future<int> getTotalActiveEstatesCount() async {
    try {
      const cacheKey = 'total_active';
      
      // التحقق من الكاش
      if (_isCacheValid() && _countCache.containsKey(cacheKey)) {
        return _countCache[cacheKey]!;
      }

      // استعلام قاعدة البيانات
      final query = _firestore
          .collection('estates')
          .where('isActive', isEqualTo: true);

      final snapshot = await query.count().get();
      final count = snapshot.count ?? 0;

      // حفظ في الكاش
      _countCache[cacheKey] = count;
      _lastCacheUpdate = DateTime.now();

      return count;
    } catch (e) {
      debugPrint('خطأ في الحصول على إجمالي عدد العقارات: $e');
      return 0;
    }
  }

  /// الحصول على أعداد العقارات حسب المحافظة
  Future<Map<String, int>> getEstateCountsByGovernorate() async {
    try {
      final Map<String, int> counts = {};
      
      // قائمة المحافظات
      final governorates = [
        'محافظة العاصمة',
        'محافظة حولي', 
        'محافظة مبارك الكبير',
        'محافظة الأحمدي',
        'محافظة الفروانية',
        'محافظة الجهراء'
      ];

      // معالجة متوازية
      final futures = governorates.map((governorate) async {
        final query = _firestore
            .collection('estates')
            .where('governorate', isEqualTo: governorate)
            .where('isActive', isEqualTo: true);
            
        final snapshot = await query.count().get();
        return MapEntry(governorate, snapshot.count ?? 0);
      });

      final results = await Future.wait(futures);
      
      for (final entry in results) {
        counts[entry.key] = entry.value;
      }

      return counts;
    } catch (e) {
      debugPrint('خطأ في الحصول على أعداد العقارات حسب المحافظة: $e');
      return {};
    }
  }
}
