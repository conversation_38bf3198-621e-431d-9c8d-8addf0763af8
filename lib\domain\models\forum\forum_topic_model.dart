// ARCHIVO ADAPTADOR - PARA COMPATIBILIDAD
// Este archivo es un adaptador para mantener compatibilidad con código existente
// Utiliza TopicModel internamente

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'topic_model.dart';

/// Adaptador para mantener compatibilidad con código existente
/// Utiliza TopicModel internamente
class ForumTopic extends Equatable {
  final TopicModel _model;

  /// Obtener el modelo interno
  TopicModel get model => _model;

  /// معرف الموضوع
  String get id => _model.id;

  /// معرف الفئة
  String get categoryId => _model.categoryId;

  /// اسم الفئة
  String get categoryName => _model.categoryName;

  /// عنوان الموضوع
  String get title => _model.title;

  /// محتوى الموضوع
  String get content => _model.content;

  /// معرف المستخدم
  String get userId => _model.userId;

  /// اسم المستخدم
  String get userName => _model.userName;

  /// صورة المستخدم
  String? get userImage => _model.userImage;

  /// حالة الموضوع
  TopicStatus get status => _model.status;

  /// نوع الموضوع
  TopicType get type => _model.type;

  /// أولوية الموضوع
  TopicPriority get priority => _model.priority;

  /// عدد المشاهدات
  int get viewsCount => _model.viewsCount;

  /// عدد الردود
  int get repliesCount => _model.repliesCount;

  /// عدد الإعجابات
  int get likesCount => _model.likesCount;

  /// عدد المشاركات
  int get sharesCount => _model.sharesCount;

  /// عدد الإشارات المرجعية
  int get bookmarksCount => _model.bookmarksCount;

  /// قائمة المستخدمين الذين أعجبوا بالموضوع
  List<String>? get likedBy => _model.likedBy;

  /// قائمة المستخدمين الذين أضافوا الموضوع للإشارات المرجعية
  List<String>? get bookmarkedBy => _model.bookmarkedBy;

  /// قائمة المستخدمين الذين يتابعون الموضوع
  List<String>? get followedBy => _model.followedBy;

  /// تاريخ إنشاء الموضوع
  DateTime get createdAt => _model.createdAt;

  /// تاريخ آخر تحديث للموضوع
  DateTime get updatedAt => _model.updatedAt;

  /// تاريخ انتهاء الموضوع (للاستطلاعات والإعلانات)
  DateTime? get expiryDate => _model.expiryDate;

  /// معرف آخر مستخدم رد على الموضوع
  String? get lastReplyUserId => _model.lastReplyUserId;

  /// اسم آخر مستخدم رد على الموضوع
  String? get lastReplyUserName => _model.lastReplyUserName;

  /// صورة آخر مستخدم رد على الموضوع
  String? get lastReplyUserImage => _model.lastReplyUserImage;

  /// تاريخ آخر رد على الموضوع
  DateTime? get lastReplyDate => _model.lastReplyDate;

  /// الوسوم المرتبطة بالموضوع
  List<String>? get tags => _model.tags;

  /// الصور المرفقة بالموضوع
  List<String>? get images => _model.images;

  /// المرفقات الأخرى
  List<Map<String, dynamic>>? get attachments => _model.attachments;

  /// خيارات الاستطلاع (للمواضيع من نوع استطلاع)
  List<Map<String, dynamic>>? get pollOptions => _model.pollOptions;

  /// عدد الأصوات في الاستطلاع
  int get pollVotesCount => _model.pollVotesCount;

  /// قائمة المستخدمين الذين صوتوا في الاستطلاع
  List<String>? get pollVotedBy => _model.pollVotedBy;

  /// ما إذا كان الموضوع محلولاً (للمواضيع من نوع سؤال)
  bool get isSolved => _model.isSolved;

  /// معرف المشاركة التي تمثل الحل
  String? get solutionPostId => _model.solutionPostId;

  /// ما إذا كان الموضوع متعلقاً بعقار
  bool get isRelatedToEstate => _model.isRelatedToEstate;

  /// معرف العقار المرتبط
  String? get relatedEstateId => _model.relatedEstateId;

  /// عنوان العقار المرتبط
  String? get relatedEstateTitle => _model.relatedEstateTitle;

  /// صورة العقار المرتبط
  String? get relatedEstateImage => _model.relatedEstateImage;

  /// موقع الموضوع (للمواضيع المتعلقة بمواقع محددة)
  Map<String, dynamic>? get location => _model.location;

  /// ما إذا كان الموضوع يحتوي على محتوى للبالغين
  bool get isAdultContent => _model.isAdultContent;

  /// ما إذا كان الموضوع مختاراً من قبل فريق الإدارة
  bool get isStaffPicked => _model.isStaffPicked;

  /// درجة التطابق في نتائج البحث
  double? get searchScore => _model.searchScore;

  /// تفاعلات المستخدمين مع الموضوع
  Map<String, List<String>>? get reactions => _model.reactions;

  /// Constructor
  const ForumTopic(this._model);

  /// Factory constructor from TopicModel
  factory ForumTopic.fromModel(TopicModel model) {
    return ForumTopic(model);
  }

  /// Factory constructor from Map
  factory ForumTopic.fromMap(Map<String, dynamic> map) {
    return ForumTopic(TopicModel.fromMap(map));
  }

  /// Factory constructor from Firestore
  factory ForumTopic.fromFirestore(DocumentSnapshot doc) {
    return ForumTopic(TopicModel.fromFirestore(doc));
  }

  @override
  List<Object?> get props => [_model];
}
