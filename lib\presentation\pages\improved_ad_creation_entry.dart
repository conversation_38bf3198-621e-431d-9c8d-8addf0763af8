// lib/presentation/pages/improved_ad_creation_entry.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/config/app_mode_config.dart';
import '../../core/services/ad_limit_notification_service.dart';
import '../../core/services/enhanced_ad_draft_service.dart';
import '../../core/services/enhanced_subscription_service.dart';
import '../../domain/entities/estate.dart';
import '../bloc/improved_ad_bloc.dart';
import '../widgets/enhanced_draft_card.dart';

import 'improved_category_selection_page.dart';
import 'improved_media_upload_page.dart';
import 'improved_ad_details_page.dart';
import 'improved_ad_settings_page.dart';
import 'improved_ad_preview_page.dart';
import 'in_flow_upgrade_page.dart';

/// صفحة بدء إنشاء الإعلان المحسنة
/// تعرض خيارات بدء إنشاء إعلان جديد أو استئناف مسودة سابقة
class ImprovedAdCreationEntry extends StatefulWidget {
  const ImprovedAdCreationEntry({super.key});

  @override
  State<ImprovedAdCreationEntry> createState() =>
      _ImprovedAdCreationEntryState();
}

class _ImprovedAdCreationEntryState extends State<ImprovedAdCreationEntry>
    with SingleTickerProviderStateMixin {
  // خدمة المسودات المحسنة
  final _draftService = EnhancedAdDraftService();

  // خدمة الاشتراكات
  final _subscriptionService = EnhancedSubscriptionService();

  // خدمة إشعارات حد الإعلانات
  final _notificationService = AdLimitNotificationService();

  // متغيرات الحالة
  bool _isLoading = true;
  bool _isCheckingPermissions = false;
  bool _canCreateAd = true;
  int _remainingAds = 0;
  List<Map<String, dynamic>> _drafts = [];
  List<Map<String, dynamic>> _templates = [];

  // متغيرات الرسوم المتحركة
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500));

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn));

    _animationController.forward();

    // تحميل المسودات والقوالب
    _loadData();

    // التحقق من صلاحيات المستخدم
    _checkPermissions();

    // التحقق من حد الإعلانات وإرسال إشعار إذا لزم الأمر
    _notificationService.checkAdLimit();
  }

  /// التحقق من صلاحيات المستخدم - معطل في الوضع المعلوماتي
  Future<void> _checkPermissions() async {
    setState(() {
      _isCheckingPermissions = true;
    });

    // في الوضع المعلوماتي، السماح بإنشاء الإعلانات بدون قيود
    if (AppModeConfig.isInformationalOnly) {
      setState(() {
        _canCreateAd = true;
        _remainingAds = 999; // عدد غير محدود
        _isCheckingPermissions = false;
      });
      return;
    }

    try {
      // التحقق مما إذا كان المستخدم يمكنه إنشاء إعلان
      final canCreate = await _subscriptionService.canCreateAd(null);

      // الحصول على عدد الإعلانات المتبقية
      final remaining = await _subscriptionService.getRemainingAds(null);

      setState(() {
        _canCreateAd = canCreate;
        _remainingAds = remaining;
        _isCheckingPermissions = false;
      });
    } catch (e) {
      setState(() {
        _isCheckingPermissions = false;
        _canCreateAd = true; // السماح بالإنشاء في حالة الخطأ
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// تحميل المسودات والقوالب
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    // تحميل المسودات
    final drafts = await _draftService.getUserDrafts();

    // تحميل القوالب
    final templates = await _draftService.getUserTemplates();

    setState(() {
      _drafts = drafts;
      _templates = templates;
      _isLoading = false;
    });
  }

  /// بدء إنشاء إعلان جديد
  void _createNewAd() {
    // في الوضع المعلوماتي، السماح بإنشاء الإعلانات مباشرة
    if (AppModeConfig.isInformationalOnly) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => const ImprovedCategorySelectionPage()));
      return;
    }

    // التحقق من صلاحيات المستخدم قبل المتابعة
    if (!_canCreateAd) {
      _showUpgradeDialog();
      return;
    }

    // إذا كان عدد الإعلانات المتبقية قليل، عرض تنبيه
    if (_remainingAds <= 3 && _remainingAds > 0) {
      _showLowAdsWarning();
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => const ImprovedCategorySelectionPage()));
  }

  /// عرض حوار الترقية
  void _showUpgradeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          "ترقية الباقة مطلوبة",
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold)),
        content: Text(
          "لقد وصلت إلى الحد الأقصى من الإعلانات المسموح بها في باقتك الحالية. يرجى ترقية باقتك للاستمرار.",
          style: GoogleFonts.cairo()),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: Text(
              "إلغاء",
              style: GoogleFonts.cairo())),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // الانتقال إلى صفحة الترقية
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => InFlowUpgradePage(
                    onUpgradeComplete: () {
                      // إعادة التحقق من الصلاحيات بعد الترقية
                      _checkPermissions();
                    })));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor),
            child: Text(
              "ترقية الآن",
              style: GoogleFonts.cairo(
                color: Colors.white))),
        ]));
  }

  /// عرض تنبيه انخفاض عدد الإعلانات
  void _showLowAdsWarning() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          "تنبيه: تبقى لديك $_remainingAds إعلانات فقط. يمكنك ترقية باقتك للحصول على المزيد.",
          style: GoogleFonts.cairo()),
        backgroundColor: Colors.amber.shade700,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: "ترقية",
          textColor: Colors.white,
          onPressed: () {
            // الانتقال إلى صفحة الترقية
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => InFlowUpgradePage(
                  onUpgradeComplete: () {
                    // إعادة التحقق من الصلاحيات بعد الترقية
                    _checkPermissions();
                  })));
          })));
  }

  /// استئناف مسودة
  void _resumeDraft(Map<String, dynamic> draft) {
    // تحديد الخطوة التالية بناءً على البيانات المحفوظة
    final nextStep = _draftService.getNextStep(draft);

    // إنشاء كائن Estate من بيانات المسودة إذا لزم الأمر
    Estate? estate;
    if (nextStep > 2) {
      estate = _createEstateFromDraft(draft);
    }

    // الانتقال إلى الصفحة المناسبة
    Widget targetPage;
    switch (nextStep) {
      case 1:
        targetPage = const ImprovedCategorySelectionPage();
        break;
      case 2:
        targetPage = ImprovedMediaUploadPage(estate: estate!);
        break;
      case 3:
        targetPage = ImprovedAdDetailsPage(estate: estate!);
        break;
      case 4:
        targetPage = ImprovedAdSettingsPage(estate: estate!);
        break;
      case 5:
        targetPage = ImprovedAdPreviewPage(estate: estate!);
        break;
      default:
        targetPage = const ImprovedCategorySelectionPage();
    }

    // تحديث BLoC بالبيانات المحفوظة
    _loadDraftDataToBloc(draft);

    Navigator.push(
      context,
      MaterialPageRoute(builder: (_) => targetPage),
    );
  }

  /// إنشاء كائن Estate من بيانات المسودة
  Estate _createEstateFromDraft(Map<String, dynamic> draft) {
    // تجميع الموقع من المحافظة والمدينة والقطعة
    final governorate = draft['governorate'] as String? ?? '';
    final city = draft['city'] as String? ?? '';
    final piece = draft['piece'] as String? ?? '';
    final location = [governorate, city, piece].where((s) => s.isNotEmpty).join(', ');

    return Estate(
      id: draft['id'] as String? ?? '',
      title: draft['title'] as String? ?? '',
      description: draft['description'] as String? ?? '',
      price: (draft['price'] as num?)?.toDouble() ?? 0.0,
      location: location.isNotEmpty ? location : (draft['location'] as String? ?? ''),
      photoUrls: List<String>.from(draft['photoUrls'] as List? ?? []),
      isFeatured: draft['isFeatured'] as bool? ?? false,
      planType: draft['planType'] as String? ?? 'basic',
      startDate: DateTime.now(),
      endDate: DateTime.now().add(const Duration(days: 30)),
      createdAt: DateTime.now(),
      mainCategory: draft['mainCategory'] as String?,
      subCategory: draft['subCategory'] as String?,
      area: (draft['area'] as num?)?.toDouble(),
      numberOfRooms: draft['numberOfRooms'] as int?,
      numberOfBathrooms: draft['numberOfBathrooms'] as int?,
      floorNumber: draft['floorNumber'] as int?,
      buildingAge: draft['buildingAge'] as int?,
      numberOfFloors: draft['numberOfFloors'] as int?,
      propertyType: draft['propertyType'] as String?,
      usageType: draft['usageType'] as String?,
      hasCentralAC: draft['hasCentralAC'] as bool? ?? false,
      hasSecurity: draft['hasSecurity'] as bool?,
      allowPets: draft['allowPets'] as bool?,
      hasElevator: draft['hasElevator'] as bool?,
      hasSwimmingPool: draft['hasSwimmingPool'] as bool?,
      hasMaidRoom: draft['hasMaidRoom'] as bool? ?? false,
      hasGarage: draft['hasGarage'] as bool? ?? false,
      hasBalcony: draft['hasBalcony'] as bool?,
      isFullyFurnished: draft['isFullyFurnished'] as bool?,
      rebound: draft['rebound'] as String?,
      internalLocation: draft['internalLocation'] as String?,
      salon: draft['salon'] as String?,
      autoRepublish: draft['autoRepublish'] as bool? ?? false,
      kuwaitCornersPin: draft['kuwaitCornersPin'] as bool? ?? false,
      movingAd: draft['movingAd'] as bool? ?? false,
      vipBadge: draft['vipBadge'] as bool? ?? false,
      pinnedOnHome: draft['pinnedOnHome'] as bool? ?? false,
      discountCode: draft['discountCode'] as String?,
    );
  }

  /// تحميل بيانات المسودة إلى BLoC
  void _loadDraftDataToBloc(Map<String, dynamic> draft) {
    final bloc = context.read<ImprovedAdBloc>();

    // تحميل التصنيفات
    if (draft['mainCategory'] != null) {
      bloc.add(SetMainCategory(draft['mainCategory']));
    }
    if (draft['subCategory'] != null) {
      bloc.add(SetSubCategory(draft['subCategory']));
    }

    // تحميل الصور
    if (draft['imagePaths'] != null) {
      bloc.add(AddImages(List<String>.from(draft['imagePaths'])));
    }

    // تحميل التفاصيل الأساسية
    if (draft['title'] != null && draft['price'] != null) {
      bloc.add(SetBasicDetails(
        title: draft['title'],
        price: (draft['price'] as num).toDouble(),
        governorate: draft['governorate'] ?? '',
        city: draft['city'] ?? '',
        piece: draft['piece'] ?? '',
        description: draft['description'] ?? '',
      ));
    }

    // تحميل نوع الاستغلال
    if (draft['usageType'] != null) {
      bloc.add(SetUsageType(draft['usageType']));
    }

    // تحميل الميزات الإضافية
    bloc.add(SetExtraFeatures(
      autoRepublish: draft['autoRepublish'] ?? false,
      kuwaitCornersPin: draft['kuwaitCornersPin'] ?? false,
      movingAd: draft['movingAd'] ?? false,
      vipBadge: draft['vipBadge'] ?? false,
      pinnedOnHome: draft['pinnedOnHome'] ?? false,
      discountCode: draft['discountCode'],
      hasGarage: draft['hasGarage'] ?? false,
      hasCentralAC: draft['hasCentralAC'] ?? false,
      hasMaidRoom: draft['hasMaidRoom'] ?? false,
      isFullyFurnished: draft['isFullyFurnished'] ?? false,
      hasSecurity: draft['hasSecurity'] ?? false,
      allowPets: draft['allowPets'] ?? false,
      hasElevator: draft['hasElevator'] ?? false,
      hasSwimmingPool: draft['hasSwimmingPool'] ?? false,
      hasBalcony: draft['hasBalcony'] ?? false,
    ));
  }

  /// استخدام قالب
  void _useTemplate(Map<String, dynamic> template) {
    // الانتقال إلى صفحة اختيار التصنيف
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => const ImprovedCategorySelectionPage()));
  }

  /// حذف مسودة
  Future<void> _deleteDraft(String draftId) async {
    await _draftService.deleteDraft(draftId);
    _loadData();
  }

  /// حذف قالب
  Future<void> _deleteTemplate(String templateName) async {
    await _draftService.deleteTemplate(templateName);
    _loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          "إنشاء إعلان",
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87)),
        iconTheme: const IconThemeData(color: Colors.black87)),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : FadeTransition(
              opacity: _fadeAnimation,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // بطاقة إنشاء إعلان جديد
                    _buildCreateNewAdCard(),
                    const SizedBox(height: 24),

                    // قسم المسودات
                    if (_drafts.isNotEmpty) ...[
                      Row(
                        children: [
                          Icon(
                            Icons.drafts,
                            color: Theme.of(context).primaryColor,
                            size: 24,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            "المسودات المحفوظة",
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                          const Spacer(),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${_drafts.length}',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      ..._drafts.map((draft) => EnhancedDraftCard(
                        draft: draft,
                        onTap: () => _resumeDraft(draft),
                        onDelete: () => _deleteDraft(draft['draftId']),
                        draftService: _draftService,
                      )),
                      const SizedBox(height: 24),
                    ],

                    // قسم القوالب
                    if (_templates.isNotEmpty) ...[
                      Text(
                        "القوالب المحفوظة",
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor)),
                      const SizedBox(height: 16),
                      ..._templates
                          .map((template) => _buildTemplateCard(template)),
                    ],
                  ]))));
  }

  /// بناء بطاقة إنشاء إعلان جديد
  Widget _buildCreateNewAdCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: _createNewAd,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      shape: BoxShape.circle),
                    child: Icon(
                      Icons.add_circle,
                      color: Theme.of(context).primaryColor,
                      size: 32)),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "إنشاء إعلان جديد",
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold)),
                        Text(
                          "ابدأ بإنشاء إعلان عقاري جديد",
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.grey.shade600)),
                      ])),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey.shade400,
                    size: 16),
                ]),
              const SizedBox(height: 16),
              Text(
                "قم بإنشاء إعلان عقاري جديد بخطوات بسيطة وسريعة. يمكنك إضافة الصور والتفاصيل والميزات الإضافية لجذب المزيد من المشترين المحتملين.",
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: Colors.grey.shade600)),
            ]))));
  }



  /// بناء بطاقة قالب
  Widget _buildTemplateCard(Map<String, dynamic> template) {
    // استخراج البيانات
    final templateName = template['templateName'] as String? ?? 'قالب بدون اسم';
    final mainCategory = template['mainCategory'] as String? ?? '';
    final subCategory = template['subCategory'] as String? ?? '';

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _useTemplate(template),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.purple.shade100,
                  shape: BoxShape.circle),
                child: Icon(
                  Icons.content_copy,
                  color: Colors.purple.shade800,
                  size: 20)),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      templateName,
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold)),
                    if (mainCategory.isNotEmpty || subCategory.isNotEmpty)
                      Text(
                        "$mainCategory - $subCategory",
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.grey.shade600)),
                  ])),
              IconButton(
                icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                onPressed: () => _deleteTemplate(templateName)),
            ]))));
  }
}
