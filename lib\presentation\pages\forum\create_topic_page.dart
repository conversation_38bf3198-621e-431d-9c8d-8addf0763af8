import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/category_model.dart';
import '../../../domain/models/forum/topic_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/forum_provider.dart';

/// صفحة إنشاء موضوع جديد
class CreateTopicPage extends StatefulWidget {
  /// مسار الصفحة
  static const String routeName = '/forum/create-topic';

  /// معرف الموضوع للتعديل (اختياري)
  final String? topicId;

  const CreateTopicPage({super.key, this.topicId});

  @override
  State<CreateTopicPage> createState() => _CreateTopicPageState();
}

class _CreateTopicPageState extends State<CreateTopicPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _tagsController = TextEditingController();

  String? _selectedCategoryId;
  final List<File> _selectedImages = [];
  List<String> _existingImages = [];
  final List<String> _imagesToDelete = [];

  bool _isEditing = false;
  TopicModel? _existingTopic;

  @override
  void initState() {
    super.initState();

    _isEditing = widget.topicId != null;

    // جلب البيانات عند تحميل الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);

    // جلب الفئات إذا لم تكن محملة
    if (forumProvider.categories.isEmpty || forumProvider.categoriesState == LoadingState.error) {
      await forumProvider.fetchCategories();
    }

    // إذا لم تكن هناك فئات، قم بإنشاء الفئات الافتراضية
    if (forumProvider.categories.isEmpty && forumProvider.categoriesState == LoadingState.empty) {
      await _createDefaultCategories();
      // إعادة جلب الفئات بعد الإنشاء
      await forumProvider.fetchCategories();
    }

    // إذا كان تعديل موضوع موجود
    if (_isEditing && widget.topicId != null) {
      try {
        // جلب تفاصيل الموضوع
        await forumProvider.fetchTopic(widget.topicId!);

        final topic = forumProvider.currentTopic;
        if (topic != null) {
          _existingTopic = topic;

          // تعبئة البيانات
          _titleController.text = topic.title;
          _contentController.text = topic.content;
          _selectedCategoryId = topic.categoryId;

          if (topic.tags != null && topic.tags!.isNotEmpty) {
            _tagsController.text = topic.tags!.join(', ');
          }

          if (topic.images != null && topic.images!.isNotEmpty) {
            _existingImages = List.from(topic.images!);
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('حدث خطأ أثناء تحميل بيانات الموضوع')));
        }
      }
    }
  }

  /// إنشاء الفئات الافتراضية
  Future<void> _createDefaultCategories() async {
    try {
      final firestore = FirebaseFirestore.instance;

      final defaultCategories = [
        {
          'id': 'general_discussion',
          'name': 'النقاش العام',
          'description': 'مناقشات عامة حول مختلف المواضيع',
          'icon': 'chat',
          'color': '#2196F3',
          'order': 1,
          'topicsCount': 0,
          'postsCount': 0,
          'isFeatured': false,
          'isHidden': false,
          'type': 0, // CategoryType.general
          'requiresApproval': false,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'real_estate_sales',
          'name': 'بيع العقارات',
          'description': 'مناقشات حول بيع العقارات والشقق والفلل',
          'icon': 'home_work',
          'color': '#FF9800',
          'order': 2,
          'topicsCount': 0,
          'postsCount': 0,
          'isFeatured': false,
          'isHidden': false,
          'type': 1, // CategoryType.realEstate
          'requiresApproval': false,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'real_estate_rental',
          'name': 'إيجار العقارات',
          'description': 'مناقشات حول إيجار العقارات والشقق والمكاتب',
          'icon': 'key',
          'color': '#607D8B',
          'order': 3,
          'topicsCount': 0,
          'postsCount': 0,
          'isFeatured': false,
          'isHidden': false,
          'type': 2, // CategoryType.property
          'requiresApproval': false,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'cars_discussion',
          'name': 'السيارات',
          'description': 'مناقشات حول السيارات والمركبات',
          'icon': 'directions_car',
          'color': '#E91E63',
          'order': 4,
          'topicsCount': 0,
          'postsCount': 0,
          'isFeatured': false,
          'isHidden': false,
          'type': 3, // CategoryType.cars
          'requiresApproval': false,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'jobs_opportunities',
          'name': 'الوظائف والفرص',
          'description': 'فرص العمل والوظائف المتاحة',
          'icon': 'work',
          'color': '#9C27B0',
          'order': 5,
          'topicsCount': 0,
          'postsCount': 0,
          'isFeatured': false,
          'isHidden': false,
          'type': 4, // CategoryType.jobs
          'requiresApproval': false,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'services_offers',
          'name': 'الخدمات والعروض',
          'description': 'خدمات متنوعة وعروض مختلفة',
          'icon': 'room_service',
          'color': '#FF5722',
          'order': 6,
          'topicsCount': 0,
          'postsCount': 0,
          'isFeatured': false,
          'isHidden': false,
          'type': 5, // CategoryType.services
          'requiresApproval': false,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'qa_section',
          'name': 'أسئلة وأجوبة',
          'description': 'اطرح أسئلتك واحصل على إجابات من المجتمع',
          'icon': 'help_outline',
          'color': '#4CAF50',
          'order': 7,
          'topicsCount': 0,
          'postsCount': 0,
          'isFeatured': false,
          'isHidden': false,
          'type': 6, // CategoryType.qa
          'requiresApproval': false,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'discussions_general',
          'name': 'نقاشات عامة',
          'description': 'نقاشات متنوعة حول مواضيع مختلفة',
          'icon': 'forum',
          'color': '#795548',
          'order': 8,
          'topicsCount': 0,
          'postsCount': 0,
          'isFeatured': false,
          'isHidden': false,
          'type': 7, // CategoryType.discussions
          'requiresApproval': false,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
        {
          'id': 'news_updates',
          'name': 'الأخبار والتحديثات',
          'description': 'آخر الأخبار والتحديثات المهمة',
          'icon': 'newspaper',
          'color': '#607D8B',
          'order': 9,
          'topicsCount': 0,
          'postsCount': 0,
          'isFeatured': false,
          'isHidden': false,
          'type': 8, // CategoryType.news
          'requiresApproval': false,
          'createdAt': Timestamp.now(),
          'updatedAt': Timestamp.now(),
        },
      ];

      for (final categoryData in defaultCategories) {
        await firestore
            .collection('forum_categories')
            .doc(categoryData['id'] as String)
            .set(categoryData, SetOptions(merge: true));
      }

      debugPrint('تم إنشاء الفئات الافتراضية');
    } catch (e) {
      debugPrint('خطأ في إنشاء الفئات الافتراضية: $e');
    }
  }

  /// إظهار مؤشر التحميل
  void _showLoadingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              padding: EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                  ),
                  SizedBox(height: 16),
                  Text(
                    _isEditing ? 'جاري تحديث الموضوع...' : 'جاري نشر الموضوع...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// إظهار رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        duration: Duration(seconds: 3),
      ),
    );
  }

  /// إظهار رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white),
            SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        duration: Duration(seconds: 3),
      ),
    );
  }

  /// تفريغ الحقول بعد النجاح
  void _clearForm() {
    _titleController.clear();
    _contentController.clear();
    _tagsController.clear();
    _selectedImages.clear();
    _existingImages.clear();
    _imagesToDelete.clear();
    _selectedCategoryId = null;

    setState(() {
      // تحديث الواجهة
    });
  }

  /// اختيار صورة من المعرض
  Future<void> _pickImage() async {
    final ImagePicker picker = ImagePicker();

    try {
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);

      if (image != null) {
        setState(() {
          _selectedImages.add(File(image.path));
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء اختيار الصورة')));
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _takePhoto() async {
    final ImagePicker picker = ImagePicker();

    try {
      final XFile? image = await picker.pickImage(source: ImageSource.camera);

      if (image != null) {
        setState(() {
          _selectedImages.add(File(image.path));
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء التقاط الصورة')));
    }
  }

  /// حذف صورة مختارة
  void _removeSelectedImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  /// حذف صورة موجودة
  void _removeExistingImage(int index) {
    setState(() {
      final imageUrl = _existingImages[index];
      _imagesToDelete.add(imageUrl);
      _existingImages.removeAt(index);
    });
  }

  /// حفظ الموضوع
  Future<void> _saveTopic() async {
    if (!_formKey.currentState!.validate()) return;

    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn) {
      _showErrorSnackBar('يجب تسجيل الدخول لإنشاء موضوع');
      return;
    }

    if (_selectedCategoryId == null) {
      _showErrorSnackBar('يرجى اختيار فئة للموضوع');
      return;
    }

    // إظهار مؤشر التحميل
    _showLoadingDialog();

    try {
      // تحويل الوسوم إلى قائمة
      List<String>? tags;
      if (_tagsController.text.isNotEmpty) {
        tags = _tagsController.text
            .split(',')
            .map((tag) => tag.trim())
            .where((tag) => tag.isNotEmpty)
            .toList();
      }

      // الحصول على اسم الفئة
      final category = forumProvider.categories.firstWhere(
        (category) => category.id == _selectedCategoryId,
        orElse: () => CategoryModel(
          id: _selectedCategoryId!,
          name: 'فئة',
          description: '',
          order: 0,
          color: '#000000',
          icon: 'category',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now()));

      if (_isEditing && _existingTopic != null) {
        // تحديث موضوع موجود
        final updatedTopic = TopicModel(
          id: _existingTopic!.id,
          categoryId: _selectedCategoryId!,
          categoryName: category.name,
          userId: _existingTopic!.userId,
          userName: _existingTopic!.userName,
          userImage: _existingTopic!.userImage,
          title: _titleController.text.trim(),
          content: _contentController.text.trim(),
          tags: tags,
          images: _existingImages,
          viewsCount: _existingTopic!.viewsCount,
          likesCount: _existingTopic!.likesCount,
          repliesCount: _existingTopic!.repliesCount,
          sharesCount: _existingTopic!.sharesCount,
          bookmarksCount: _existingTopic!.bookmarksCount,
          status: _existingTopic!.status,
          isSolved: _existingTopic!.isSolved,
          createdAt: _existingTopic!.createdAt,
          updatedAt: DateTime.now());

        final success = await forumProvider.updateTopic(
          updatedTopic,
          newImages: _selectedImages,
          imagesToDelete: _imagesToDelete);

        // إخفاء مؤشر التحميل
        if (mounted) {
          Navigator.pop(context);
        }

        if (success && mounted) {
          _showSuccessSnackBar('تم تحديث الموضوع بنجاح');

          // العودة للصفحة السابقة بعد تأخير قصير
          Future.delayed(Duration(milliseconds: 500), () {
            if (mounted) {
              Navigator.pop(context, true); // إرجاع true للإشارة للنجاح
            }
          });
        } else {
          _showErrorSnackBar('حدث خطأ أثناء تحديث الموضوع');
        }
      } else {
        // إنشاء موضوع جديد
        final newTopic = TopicModel(
          id: '',
          categoryId: _selectedCategoryId!,
          categoryName: category.name,
          userId: authProvider.user!.uid,
          userName: authProvider.user!.displayName ?? 'مستخدم',
          userImage: authProvider.user!.photoURL,
          title: _titleController.text.trim(),
          content: _contentController.text.trim(),
          tags: tags,
          status: TopicStatus.open,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now());

        final createdTopic = await forumProvider.createTopic(
          newTopic,
          images: _selectedImages);

        // إخفاء مؤشر التحميل
        if (mounted) {
          Navigator.pop(context);
        }

        if (createdTopic != null && mounted) {
          _showSuccessSnackBar('تم إنشاء الموضوع بنجاح');

          // تفريغ الحقول
          _clearForm();

          // تحديث قائمة المواضيع والإحصائيات في ForumProvider
          await forumProvider.fetchTopics();
          await forumProvider.fetchForumStatistics();

          // العودة للصفحة السابقة بعد تأخير قصير
          Future.delayed(Duration(milliseconds: 1000), () {
            if (mounted) {
              Navigator.pop(context, true); // إرجاع true للإشارة للنجاح
            }
          });
        } else {
          _showErrorSnackBar('حدث خطأ أثناء إنشاء الموضوع');
        }
      }
    } catch (e) {
      // إخفاء مؤشر التحميل في حالة الخطأ
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }
      if (mounted) {
        _showErrorSnackBar('حدث خطأ: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل موضوع' : 'موضوع جديد')),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildCategoryDropdown(),
              SizedBox(height: 16),
              _buildTitleField(),
              SizedBox(height: 16),
              _buildContentField(),
              SizedBox(height: 16),
              _buildTagsField(),
              SizedBox(height: 16),
              _buildImagesSection(),
              SizedBox(height: 32),
              _buildSubmitButton(),
            ]))));
  }

  /// بناء قائمة الفئات
  Widget _buildCategoryDropdown() {
    return Consumer<ForumProvider>(
      builder: (context, forumProvider, child) {
        if (forumProvider.categoriesState == LoadingState.loading) {
          return Container(
            height: 60,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 8),
                  Text('جاري تحميل الفئات...'),
                ],
              ),
            ),
          );
        }

        if (forumProvider.categoriesState == LoadingState.error) {
          return Container(
            height: 60,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.red),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error, color: Colors.red),
                  SizedBox(width: 8),
                  Text('خطأ في تحميل الفئات'),
                  SizedBox(width: 8),
                  TextButton(
                    onPressed: () => forumProvider.fetchCategories(),
                    child: Text('إعادة المحاولة'),
                  ),
                ],
              ),
            ),
          );
        }

        final categories = forumProvider.categories;

        if (categories.isEmpty) {
          return Container(
            height: 60,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.orange),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Text('لا توجد فئات متاحة'),
            ),
          );
        }

        return DropdownButtonFormField<String>(
          decoration: InputDecoration(
            labelText: 'الفئة',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.category)),
          value: _selectedCategoryId,
          hint: Text('اختر فئة'),
          items: categories.map((category) {
            return DropdownMenuItem<String>(
              value: category.id,
              child: Text(category.name));
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedCategoryId = value;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'يرجى اختيار فئة';
            }
            return null;
          });
      });
  }

  /// بناء حقل العنوان
  Widget _buildTitleField() {
    return TextFormField(
      controller: _titleController,
      decoration: InputDecoration(
        labelText: 'العنوان',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.title)),
      maxLength: 100,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال عنوان للموضوع';
        }
        if (value.trim().length < 5) {
          return 'العنوان قصير جداً';
        }
        return null;
      });
  }

  /// بناء حقل المحتوى
  Widget _buildContentField() {
    return TextFormField(
      controller: _contentController,
      decoration: InputDecoration(
        labelText: 'المحتوى',
        border: OutlineInputBorder(),
        alignLabelWithHint: true),
      maxLines: 10,
      maxLength: 2000,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال محتوى للموضوع';
        }
        if (value.trim().length < 10) {
          return 'المحتوى قصير جداً';
        }
        return null;
      });
  }

  /// بناء حقل الوسوم
  Widget _buildTagsField() {
    return TextFormField(
      controller: _tagsController,
      decoration: InputDecoration(
        labelText: 'الوسوم (مفصولة بفواصل)',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.tag),
        hintText: 'مثال: عقارات, استثمار, شقق'));
  }

  /// بناء قسم الصور
  Widget _buildImagesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الصور',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        SizedBox(height: 8),
        Row(
          children: [
            ElevatedButton.icon(
              icon: Icon(Icons.photo_library),
              label: Text('اختيار من المعرض'),
              onPressed: _pickImage,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary)),
            SizedBox(width: 8),
            ElevatedButton.icon(
              icon: Icon(Icons.camera_alt),
              label: Text('التقاط صورة'),
              onPressed: _takePhoto,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary)),
          ]),
        SizedBox(height: 16),
        if (_existingImages.isNotEmpty) ...[
          Text(
            'الصور الحالية',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold)),
          SizedBox(height: 8),
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _existingImages.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          _existingImages[index],
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover)),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: GestureDetector(
                          onTap: () => _removeExistingImage(index),
                          child: Container(
                            padding: EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle),
                            child: Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16)))),
                    ]));
              })),
          SizedBox(height: 16),
        ],
        if (_selectedImages.isNotEmpty) ...[
          Text(
            'الصور المختارة',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold)),
          SizedBox(height: 8),
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedImages.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          _selectedImages[index],
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover)),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: GestureDetector(
                          onTap: () => _removeSelectedImage(index),
                          child: Container(
                            padding: EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle),
                            child: Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16)))),
                    ]));
              })),
        ],
      ]);
  }

  /// بناء زر الإرسال
  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _saveTopic,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8))),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _isEditing ? Icons.update : Icons.publish,
              color: Colors.white,
            ),
            SizedBox(width: 8),
            Text(
              _isEditing ? 'تحديث الموضوع' : 'نشر الموضوع',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
