import 'package:flutter/material.dart';
import 'package:kuwait_corners/presentation/pages/home_page.dart';
import 'package:kuwait_corners/presentation/pages/improved_ad_creation_entry.dart';
import 'package:kuwait_corners/presentation/pages/improved_media_upload_page.dart';
import 'package:kuwait_corners/presentation/pages/login_page.dart';
import 'package:kuwait_corners/presentation/pages/loyalty_program_page.dart';
import 'package:kuwait_corners/presentation/pages/notification_settings_page.dart';
import 'package:kuwait_corners/presentation/pages/notifications_page.dart';
import 'package:kuwait_corners/presentation/pages/profile_page.dart';
import 'package:kuwait_corners/presentation/pages/referral_program_page.dart';
import 'package:kuwait_corners/presentation/pages/splash_page.dart';
import 'package:kuwait_corners/presentation/pages/user_verification_page.dart';
import 'package:kuwait_corners/presentation/screens/analytics/analytics_home_screen.dart';
import 'package:kuwait_corners/presentation/screens/analytics/market_analysis_screen.dart';
import 'package:kuwait_corners/presentation/screens/analytics/market_prediction_screen.dart';
import 'package:kuwait_corners/presentation/screens/analytics/price_estimation_screen.dart';
import 'package:kuwait_corners/presentation/screens/recommendations_screen.dart';

import 'package:kuwait_corners/presentation/pages/forum/topic_details_page.dart';
import 'package:kuwait_corners/presentation/pages/forum/create_topic_page.dart';
import 'package:kuwait_corners/presentation/pages/forum/user_statistics_page.dart';
import 'package:kuwait_corners/presentation/pages/forum/saved_topics_page.dart';
import 'package:kuwait_corners/presentation/pages/forum/notifications_page.dart'
    as forum_notifications;
import 'package:kuwait_corners/presentation/pages/forum/user_topics_page.dart';
import 'package:kuwait_corners/presentation/pages/forum/modern_forum_home_page.dart';
import 'package:kuwait_corners/presentation/pages/forum/modern_topic_details_page.dart';
import 'package:kuwait_corners/presentation/pages/forum/categories_page.dart';
import 'package:kuwait_corners/presentation/pages/forum/forum_statistics_page.dart';
import 'package:kuwait_corners/presentation/pages/bank_transfer_payment_page.dart';

import 'package:kuwait_corners/presentation/pages/messaging/conversations_page.dart';



import 'package:kuwait_corners/presentation/pages/property_request/property_requests_page.dart';
import 'package:kuwait_corners/presentation/pages/property_request/property_request_details_page.dart';
import 'package:kuwait_corners/presentation/pages/property_request/create_property_request_page.dart';
import 'package:kuwait_corners/presentation/pages/property_request/edit_property_request_page.dart';
import 'package:kuwait_corners/presentation/pages/property_request/create_property_offer_page.dart';
import 'package:kuwait_corners/presentation/pages/property_request/property_request_notifications_page.dart';
import 'package:kuwait_corners/presentation/pages/property_request/property_request_statistics_page.dart';
import 'package:kuwait_corners/presentation/pages/property_request/my_property_requests_page.dart';
import 'package:kuwait_corners/core/middleware/route_guard.dart';
import 'package:kuwait_corners/domain/models/property_request/property_request_model.dart';

// الصفحات الجديدة
import 'package:kuwait_corners/presentation/pages/advanced_search_page.dart';
import 'package:kuwait_corners/presentation/pages/favorites_page.dart';
import 'package:kuwait_corners/presentation/pages/property_comparison_page.dart';
import 'package:kuwait_corners/presentation/pages/property_owner_dashboard.dart';
import 'package:kuwait_corners/presentation/pages/investment_portfolio_page.dart';
import 'package:kuwait_corners/presentation/pages/premium_ads_dashboard.dart';
import 'package:kuwait_corners/presentation/pages/estate_copy_page.dart';
import 'package:kuwait_corners/presentation/pages/paid_ad_payment_page.dart';
import 'package:kuwait_corners/presentation/pages/copy_activation_payment_page.dart';
import 'package:kuwait_corners/presentation/pages/wamd_payment_page.dart';
import 'package:kuwait_corners/presentation/pages/copied_estates_management_page.dart';
import 'package:kuwait_corners/presentation/pages/messaging/conversation_details_page.dart';
import 'package:kuwait_corners/presentation/pages/user_properties_page.dart';
import 'package:kuwait_corners/domain/entities/estate.dart';

// الصفحات الجديدة للـ drawer
import 'package:kuwait_corners/presentation/pages/clients_management_page.dart';
import 'package:kuwait_corners/presentation/pages/projects_management_page.dart';
import 'package:kuwait_corners/presentation/pages/team_management_page.dart';
import 'package:kuwait_corners/presentation/pages/reports_page.dart';
import 'package:kuwait_corners/presentation/pages/copy_property_page.dart';
import 'package:kuwait_corners/presentation/pages/copied_properties_page.dart';
import 'package:kuwait_corners/presentation/pages/copy_analytics_page.dart';
import 'package:kuwait_corners/presentation/pages/all_investments_page.dart';
import 'package:kuwait_corners/presentation/pages/settings_page.dart';
import 'package:kuwait_corners/presentation/pages/add_project_page.dart';
import 'package:kuwait_corners/presentation/pages/add_client_page.dart';
import 'package:kuwait_corners/presentation/pages/client_details_page.dart';
import 'package:kuwait_corners/presentation/pages/edit_client_page.dart';
import 'package:kuwait_corners/presentation/pages/add_team_member_page.dart';
import 'package:kuwait_corners/presentation/pages/member_details_page.dart';
import 'package:kuwait_corners/presentation/pages/edit_member_page.dart';
import 'package:kuwait_corners/presentation/pages/project_details_page.dart';
import 'package:kuwait_corners/presentation/pages/edit_project_page.dart';

// الصفحات الجديدة للجولات الافتراضية والتقييمات
import 'package:kuwait_corners/presentation/pages/virtual_tours_management_page.dart';
import 'package:kuwait_corners/presentation/widgets/virtual_tour_viewer.dart';

/// مسارات التطبيق
class AppRoutes {
  /// مسار الصفحة الرئيسية
  static const String home = '/home';

  /// مسار صفحة البداية
  static const String splash = '/splash';

  /// مسار صفحة تسجيل الدخول
  static const String login = '/login';

  /// مسار صفحة التحقق من المستخدم
  static const String userVerification = '/user-verification';

  /// مسار صفحة برنامج الولاء
  static const String loyaltyProgram = '/loyalty-program';

  /// مسار صفحة برنامج الإحالة
  static const String referralProgram = '/referral-program';

  /// مسار صفحة الإشعارات
  static const String notifications = '/notifications';

  /// مسار صفحة إعدادات الإشعارات
  static const String notificationSettings = '/notification-settings';

  /// مسار صفحة الملف الشخصي
  static const String profile = '/profile';

  /// مسار صفحة التحليلات الرئيسية
  static const String analyticsHome = '/analytics-home';

  /// مسار صفحة تحليلات السوق
  static const String marketAnalysis = '/market-analysis';

  /// مسار صفحة تنبؤات السوق
  static const String marketPrediction = '/market-prediction';

  /// مسار صفحة تقدير الأسعار
  static const String priceEstimation = '/price-estimation';

  /// مسار صفحة التوصيات الذكية
  static const String recommendations = '/recommendations';

  /// مسار صفحة المنتدى
  static const String forum = '/forum';

  /// مسار صفحة المنتدى الحديثة
  static const String modernForum = '/modern-forum';

  /// مسار صفحة تفاصيل الموضوع
  static const String forumTopic = '/forum/topic';

  /// مسار صفحة تفاصيل الموضوع الحديثة
  static const String modernForumTopic = '/modern-forum/topic';

  /// مسار صفحة أقسام اللوبي
  static const String forumCategories = '/modern-forum/categories';

  /// مسار صفحة إنشاء موضوع جديد
  static const String forumCreateTopic = '/forum/create-topic';

  /// مسار صفحة إحصائيات المستخدم
  static const String forumUserStatistics = '/forum/user-statistics';

  /// مسار صفحة المواضيع المحفوظة
  static const String forumSavedTopics = '/forum/saved-topics';

  /// مسار صفحة إشعارات اللوبي
  static const String forumNotifications = '/forum/notifications';

  /// مسار صفحة مواضيع المستخدم
  static const String forumUserTopics = '/forum/user-topics';

  /// مسار صفحة إحصائيات اللوبي
  static const String forumStatistics = '/forum/statistics';





  /// مسار صفحة المحادثات
  static const String conversations = '/conversations';

  /// مسار صفحة إنشاء الإعلان المحسنة
  static const String improvedAdCreation = '/improved-ad-creation';

  /// مسار صفحة الدفع بالتحويل البنكي
  static const String bankTransfer = '/bank-transfer';

  /// مسار صفحة طلبات العقارات
  static const String propertyRequests = '/property-requests';

  /// مسار صفحة تفاصيل طلب عقار
  static const String propertyRequestDetails = '/property-request-details';

  /// مسار صفحة إنشاء طلب عقار
  static const String createPropertyRequest = '/create-property-request';

  /// مسار صفحة تعديل طلب عقار
  static const String editPropertyRequest = '/edit-property-request';

  /// مسار صفحة إنشاء عرض لطلب عقار
  static const String createPropertyOffer = '/create-property-offer';

  /// مسار صفحة إشعارات طلبات العقارات
  static const String propertyRequestNotifications = '/property-request-notifications';

  /// مسار صفحة إحصائيات طلبات العقارات
  static const String propertyRequestStatistics = '/property-request-statistics';

  /// مسار صفحة طلبات العقارات الخاصة بالمستخدم
  static const String myPropertyRequests = '/my-property-requests';

  // المسارات الجديدة
  /// مسار صفحة البحث المتقدم
  static const String advancedSearch = '/advanced-search';

  /// مسار صفحة المفضلة
  static const String favorites = '/favorites';

  /// مسار صفحة مقارنة العقارات
  static const String propertyComparison = '/property-comparison';

  /// مسار لوحة تحكم مالك العقار
  static const String propertyOwnerDashboard = '/property-owner-dashboard';

  /// مسار محفظة الاستثمارات
  static const String investmentPortfolio = '/investment-portfolio';

  /// مسار لوحة تحكم الإعلانات المدفوعة
  static const String premiumAdsDashboard = '/premium-ads-dashboard';

  /// مسار صفحة نسخ العقار
  static const String copyEstate = '/copy-estate';

  /// مسار صفحة دفع الإعلان المدفوع
  static const String payment = '/payment';

  /// مسار صفحة دفع ومض
  static const String wamdPayment = '/wamd-payment';

  /// مسار صفحة إدارة العقارات المنسوخة
  static const String copiedEstatesManagement = '/copied-estates-management';

  /// مسار صفحة تفاصيل المحادثة
  static const String conversationDetails = '/conversation-details';

  /// مسار صفحة عقاراتي
  static const String myProperties = '/my-properties';

  // المسارات المفقودة للـ drawer


  /// مسار صفحة إدارة العملاء
  static const String clients = '/clients';

  /// مسار صفحة إدارة المشاريع
  static const String projects = '/projects';

  /// مسار صفحة فريق العمل
  static const String team = '/team';

  /// مسار صفحة التقارير
  static const String reports = '/reports';

  /// مسار صفحة نسخ العقارات
  static const String copyProperty = '/copy-property';

  /// مسار صفحة العقارات المنسوخة
  static const String copiedProperties = '/copied-properties';

  /// مسار صفحة تحليلات النسخ
  static const String copyAnalytics = '/copy-analytics';

  /// مسار صفحة الإعدادات
  static const String settings = '/settings';



  // المسارات المفقودة
  /// مسار صفحة إضافة مشروع
  static const String addProject = '/add-project';

  /// مسار صفحة إضافة عميل
  static const String addClient = '/add-client';

  /// مسار صفحة تفاصيل العميل
  static const String clientDetails = '/client-details';

  /// مسار صفحة تعديل العميل
  static const String editClient = '/edit-client';

  /// مسار صفحة الجدول الزمني للمشروع
  static const String projectTimeline = '/project-timeline';

  /// مسار صفحة تحليلات المشروع
  static const String projectAnalytics = '/project-analytics';

  /// مسار صفحة تحليلات العملاء
  static const String clientAnalytics = '/client-analytics';

  /// مسار صفحة تحليلات الفريق
  static const String teamAnalytics = '/team-analytics';

  /// مسار صفحة إضافة عضو فريق
  static const String addTeamMember = '/add-team-member';

  /// مسار صفحة تفاصيل العضو
  static const String memberDetails = '/member-details';

  /// مسار صفحة تعديل العضو
  static const String editMember = '/edit-member';

  /// مسار صفحة إضافة عقار
  static const String addProperty = '/add-property';

  /// مسار صفحة عرض الخريطة
  static const String mapView = '/map-view';

  /// مسار صفحة تفاصيل المشروع
  static const String projectDetails = '/project-details';

  /// مسار صفحة تعديل المشروع
  static const String editProject = '/edit-project';

  /// مسار صفحة فريق المشروع
  static const String projectTeam = '/project-team';

  /// مسار صفحة مهام المشروع
  static const String projectTasks = '/project-tasks';

  // مسارات الجولات الافتراضية والتقييمات
  /// مسار صفحة إدارة الجولات الافتراضية
  static const String virtualToursManagement = '/virtual-tours-management';

  /// مسار صفحة التقييمات
  static const String estateRatings = '/estate-ratings';

  /// مسار عارض الجولة الافتراضية
  static const String virtualTourViewer = '/virtual-tour-viewer';

  /// مسار صفحة جميع الاستثمارات العقارية
  static const String allInvestments = '/all-investments';

  /// الحصول على مسارات التطبيق
  static Map<String, WidgetBuilder> getRoutes() {
    return {
      splash: (context) => const SplashPage(),
      home: (context) => const HomePage(),
      login: (context) => const LoginPage(),
      userVerification: (context) => const UserVerificationPage(),
      loyaltyProgram: (context) => const LoyaltyProgramPage(),
      referralProgram: (context) => const ReferralProgramPage(),
      notifications: (context) => const NotificationsPage(),
      notificationSettings: (context) => const NotificationSettingsPage(),
      profile: (context) => const ProfilePage(),
      analyticsHome: (context) => const AnalyticsHomeScreen(),
      marketAnalysis: (context) => const MarketAnalysisScreen(),
      marketPrediction: (context) => const MarketPredictionScreen(),
      priceEstimation: (context) => const PriceEstimationScreen(),
      recommendations: (context) => const RecommendationsScreen(),
      forum: (context) => const ModernForumHomePage(),
      modernForum: (context) => const ModernForumHomePage(),
      forumTopic: (context) {
        final topicId = ModalRoute.of(context)?.settings.arguments as String?;
        return TopicDetailsPage(topicId: topicId ?? '');
      },
      modernForumTopic: (context) {
        final topicId = ModalRoute.of(context)?.settings.arguments as String?;
        return ModernTopicDetailsPage(topicId: topicId ?? '');
      },
      forumCategories: (context) => const CategoriesPage(),
      forumCreateTopic: (context) {
        final topicId = ModalRoute.of(context)?.settings.arguments as String?;
        return CreateTopicPage(topicId: topicId);
      },
      forumUserStatistics: (context) {
        final userId = ModalRoute.of(context)?.settings.arguments as String?;
        return UserStatisticsPage(userId: userId);
      },
      forumSavedTopics: (context) => const SavedTopicsPage(),
      forumNotifications: (context) =>
          const forum_notifications.NotificationsPage(),
      forumUserTopics: (context) {
        final userId = ModalRoute.of(context)?.settings.arguments as String?;
        return UserTopicsPage(userId: userId);
      },
      forumStatistics: (context) => const ForumStatisticsPage(),

      conversations: (context) => const ConversationsPage(),
      bankTransfer: (context) {
        final args = ModalRoute.of(context)?.settings.arguments;
        if (args != null && args is Map<String, dynamic>) {
          final estate = args['estate'];
          final totalAmount = args['totalAmount'] as double? ?? 0.0;
          final selectedFeatures = args['selectedFeatures'] as List<String>? ?? [];
          final isUpdate = args['isUpdate'] as bool? ?? false;

          return BankTransferPaymentPage(
            estate: estate,
            totalAmount: totalAmount,
            selectedFeatures: selectedFeatures,
            isUpdate: isUpdate);
        }
        // هذه الحالة لن تحدث عادة لأننا نمرر دائمًا العقار
        return const Scaffold(
          body: Center(child: Text('خطأ: لم يتم تمرير بيانات العقار')));
      },
      improvedAdCreation: (context) {
        // استخراج المعلومات من الـ arguments إذا كانت موجودة
        final args = ModalRoute.of(context)?.settings.arguments;
        if (args != null && args is Map<String, dynamic>) {
          final isEditing = args['isEditing'] as bool? ?? false;
          final estate = args['estate'];

          if (isEditing && estate != null) {
            // إذا كان في وضع التحرير، انتقل مباشرة إلى صفحة تحميل الوسائط مع تمرير العقار
            return ImprovedMediaUploadPage(estate: estate, isEditing: true);
          }
        }

        // إذا لم يكن في وضع التحرير، عرض صفحة البداية العادية
        return const ImprovedAdCreationEntry();
      },

      // مسارات طلبات العقارات (محمية)
      propertyRequests: (context) => ProtectedRoute(
        guard: RouteGuard.canAccessPropertyRequests,
        child: const PropertyRequestsPage()),
      propertyRequestDetails: (context) {
        final requestId = ModalRoute.of(context)?.settings.arguments as String?;
        return PropertyRequestDetailsPage(requestId: requestId ?? '');
      },
      createPropertyRequest: (context) => ProtectedRoute(
        guard: RouteGuard.canCreatePropertyRequest,
        child: const CreatePropertyRequestPage()),
      editPropertyRequest: (context) {
        final request = ModalRoute.of(context)?.settings.arguments;
        if (request != null && request is PropertyRequestModel) {
          return EditPropertyRequestPage(request: request);
        }
        // هذه الحالة لن تحدث عادة لأننا نمرر دائمًا الطلب
        return const Scaffold(
          body: Center(child: Text('خطأ: لم يتم تمرير بيانات الطلب')));
      },
      createPropertyOffer: (context) {
        final args = ModalRoute.of(context)?.settings.arguments;
        if (args != null && args is Map<String, dynamic>) {
          final requestId = args['requestId'] as String;
          final requestTitle = args['requestTitle'] as String;
          return CreatePropertyOfferPage(
            requestId: requestId,
            requestTitle: requestTitle);
        }
        // هذه الحالة لن تحدث عادة لأننا نمرر دائمًا بيانات الطلب
        return const Scaffold(
          body: Center(child: Text('خطأ: لم يتم تمرير بيانات الطلب')));
      },

      // صفحة إشعارات طلبات العقارات
      propertyRequestNotifications: (context) => const PropertyRequestNotificationsPage(),

      // صفحة إحصائيات طلبات العقارات
      propertyRequestStatistics: (context) => const PropertyRequestStatisticsPage(),

      // صفحة طلبات العقارات الخاصة بالمستخدم (محمية)
      myPropertyRequests: (context) => ProtectedRoute(
        guard: RouteGuard.canAccessMyPropertyRequests,
        child: const MyPropertyRequestsPage()),

      // الصفحات الجديدة
      advancedSearch: (context) => const AdvancedSearchPage(),
      favorites: (context) => const FavoritesPage(),
      propertyComparison: (context) {
        final properties = ModalRoute.of(context)?.settings.arguments as List<Estate>?;
        return PropertyComparisonPage(
          properties: properties ?? []);
      },
      propertyOwnerDashboard: (context) => const PropertyOwnerDashboard(),
      investmentPortfolio: (context) => const InvestmentPortfolioPage(),
      premiumAdsDashboard: (context) => const PremiumAdsDashboard(),

      // مسارات نسخ العقارات والإعلانات المدفوعة
      copyEstate: (context) {
        final estate = ModalRoute.of(context)?.settings.arguments as Estate?;
        if (estate != null) {
          return EstateCopyPage(originalEstate: estate);
        }
        return const Scaffold(
          body: Center(child: Text('خطأ: لم يتم تمرير بيانات العقار')));
      },
      payment: (context) {
        final args = ModalRoute.of(context)?.settings.arguments;

        // التحقق من نوع البيانات المرسلة
        if (args != null && args is Map<String, dynamic>) {
          // التحقق من نوع الدفع
          final paymentType = args['paymentType'] as String?;

          if (paymentType == 'copy_activation') {
            // دفع تفعيل نسخة عقار
            final estateId = args['estateId'] as String?;
            final amount = args['amount'] as double? ?? 50.0;
            final description = args['description'] as String? ?? 'تفعيل إعلان منسوخ';

            if (estateId != null) {
              return CopyActivationPaymentPage(
                estateId: estateId,
                amount: amount,
                description: description,
              );
            }
          } else {
            // دفع إعلان مدفوع عادي
            final paymentRequest = args['paymentRequest'];
            final copiedEstate = args['copiedEstate'];

            if (paymentRequest != null && copiedEstate != null) {
              return PaidAdPaymentPage(
                paymentRequest: paymentRequest,
                copiedEstate: copiedEstate);
            }
          }
        }

        return const Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red),
                SizedBox(height: 16),
                Text(
                  'خطأ: لم يتم تمرير بيانات الدفع',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                Text(
                  'يرجى المحاولة مرة أخرى',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ),
        );
      },
      copiedEstatesManagement: (context) => const CopiedEstatesManagementPage(),

      // صفحة دفع ومض
      wamdPayment: (context) {
        final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
        if (args != null) {
          return WamdPaymentPage(
            estate: args['estate'] as Estate,
            selectedFeatures: args['selectedFeatures'] as List<String>,
            totalAmount: args['totalAmount'] as double,
            isUpdate: args['isUpdate'] as bool,
          );
        }
        return const Scaffold(
          body: Center(child: Text('خطأ: لم يتم تمرير بيانات الدفع')),
        );
      },

      // مسارات المراسلة والتعليقات
      conversationDetails: (context) {
        final conversationId = ModalRoute.of(context)?.settings.arguments as String?;
        if (conversationId != null) {
          return ConversationDetailsPage(conversationId: conversationId);
        }
        return const Scaffold(
          body: Center(child: Text('خطأ: لم يتم تمرير معرف المحادثة')));
      },

      // مسار صفحة عقاراتي
      myProperties: (context) {
        final userType = ModalRoute.of(context)?.settings.arguments as String? ?? 'owner';
        return UserPropertiesPage(userType: userType);
      },

      // المسارات الجديدة للـ drawer
      clients: (context) => const ClientsManagementPage(),
      projects: (context) => const ProjectsManagementPage(),
      team: (context) => const TeamManagementPage(),
      reports: (context) => const ReportsPage(),
      copyProperty: (context) => const CopyPropertyPage(),
      copiedProperties: (context) => const CopiedPropertiesPage(),
      copyAnalytics: (context) => const CopyAnalyticsPage(),
      allInvestments: (context) => const AllInvestmentsPage(),
      settings: (context) => const SettingsPage(),



      // المسارات المفقودة
      addProject: (context) => const AddProjectPage(),
      addClient: (context) => const AddClientPage(),
      clientDetails: (context) {
        final clientId = ModalRoute.of(context)?.settings.arguments as String?;
        return ClientDetailsPage(clientId: clientId ?? '');
      },
      editClient: (context) {
        final clientId = ModalRoute.of(context)?.settings.arguments as String?;
        return EditClientPage(clientId: clientId ?? '');
      },
      projectTimeline: (context) {
        final projectId = ModalRoute.of(context)?.settings.arguments as String?;
        return Scaffold(
          appBar: AppBar(
            title: const Text('الجدول الزمني للمشروع')),
          body: const Center(
            child: Text(
              'صفحة الجدول الزمني للمشروع قيد التطوير',
              style: TextStyle( fontSize: 18))));
      },
      projectAnalytics: (context) {
        final projectId = ModalRoute.of(context)?.settings.arguments as String?;
        return Scaffold(
          appBar: AppBar(
            title: const Text('تحليلات المشروع')),
          body: const Center(
            child: Text(
              'صفحة تحليلات المشروع قيد التطوير',
              style: TextStyle( fontSize: 18))));
      },
      clientAnalytics: (context) => Scaffold(
        appBar: AppBar(
          title: const Text('تحليلات العملاء')),
        body: const Center(
          child: Text(
            'صفحة تحليلات العملاء قيد التطوير',
            style: TextStyle( fontSize: 18)))),
      teamAnalytics: (context) => Scaffold(
        appBar: AppBar(
          title: const Text('تحليلات الفريق')),
        body: const Center(
          child: Text(
            'صفحة تحليلات الفريق قيد التطوير',
            style: TextStyle( fontSize: 18)))),
      addTeamMember: (context) {
        final projectId = ModalRoute.of(context)?.settings.arguments as String?;
        return AddTeamMemberPage(projectId: projectId ?? '');
      },
      memberDetails: (context) {
        final memberId = ModalRoute.of(context)?.settings.arguments as String?;
        return MemberDetailsPage(memberId: memberId ?? '');
      },
      editMember: (context) {
        final memberId = ModalRoute.of(context)?.settings.arguments as String?;
        return EditMemberPage(memberId: memberId ?? '');
      },

      // المسارات المفقودة
      addProperty: (context) => const ImprovedAdCreationEntry(),
      mapView: (context) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('عرض الخريطة'),
            backgroundColor: Colors.green,
            foregroundColor: Colors.white),
          body: const Center(
            child: Text(
              'صفحة عرض الخريطة قيد التطوير',
              style: TextStyle( fontSize: 18))));
      },

      // مسارات المشاريع الجديدة
      projectDetails: (context) {
        final projectId = ModalRoute.of(context)?.settings.arguments as String?;
        return ProjectDetailsPage(projectId: projectId ?? '');
      },
      editProject: (context) {
        final projectId = ModalRoute.of(context)?.settings.arguments as String?;
        return EditProjectPage(projectId: projectId ?? '');
      },
      projectTeam: (context) {
        final projectId = ModalRoute.of(context)?.settings.arguments as String?;
        return Scaffold(
          appBar: AppBar(
            title: const Text('فريق المشروع'),
            backgroundColor: Colors.green,
            foregroundColor: Colors.white),
          body: const Center(
            child: Text(
              'صفحة فريق المشروع قيد التطوير',
              style: TextStyle( fontSize: 18))));
      },
      projectTasks: (context) {
        final projectId = ModalRoute.of(context)?.settings.arguments as String?;
        return Scaffold(
          appBar: AppBar(
            title: const Text('مهام المشروع'),
            backgroundColor: Colors.green,
            foregroundColor: Colors.white),
          body: const Center(
            child: Text(
              'صفحة مهام المشروع قيد التطوير',
              style: TextStyle( fontSize: 18))));
      },

      // مسارات الجولات الافتراضية والتقييمات
      virtualToursManagement: (context) {
        final estateId = ModalRoute.of(context)?.settings.arguments as String?;
        return VirtualToursManagementPage(estateId: estateId);
      },

      virtualTourViewer: (context) {
        final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
        final estateId = args?['estateId'] as String? ?? '';
        return Scaffold(
          appBar: AppBar(
            title: const Text('الجولة الافتراضية'),
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
          body: VirtualTourViewer(
            estateId: estateId,
            height: double.infinity,
          ),
        );
      },
    };
  }
}
