// lib/core/services/enhanced_logout_service.dart
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_sign_in/google_sign_in.dart';

import '../theme/app_colors.dart';
import '../constants/auth_messages.dart';
import '../routes/app_routes.dart';


/// خدمة تسجيل الخروج المحسنة مع إدارة شاملة للجلسات والبيانات
class EnhancedLogoutService {
  static final EnhancedLogoutService _instance = EnhancedLogoutService._internal();
  factory EnhancedLogoutService() => _instance;
  EnhancedLogoutService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();

  /// تسجيل الخروج الشامل مع تنظيف جميع البيانات
  Future<bool> performComprehensiveLogout({
    required BuildContext context,
    bool showConfirmationDialog = true,
    bool clearCache = true,
  }) async {
    try {
      // عرض حوار التأكيد إذا كان مطلوباً
      if (showConfirmationDialog) {
        final shouldLogout = await _showLogoutConfirmationDialog(context);
        if (shouldLogout != true) {
          return false;
        }
      }

      // عرض مؤشر التحميل
      if (context.mounted) {
        _showLoadingDialog(context);
      }



      // تنفيذ عملية تسجيل الخروج
      await _executeLogout(clearCache: clearCache);

      // إغلاق مؤشر التحميل
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // عرض رسالة النجاح
      if (context.mounted) {
        _showSuccessMessage(context);
      }

      // التوجه لصفحة تسجيل الدخول
      if (context.mounted) {
        await _navigateToLogin(context);
      }

      return true;
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // عرض رسالة الخطأ
      if (context.mounted) {
        _showErrorMessage(context, e.toString());
      }

      return false;
    }
  }

  /// عرض حوار تأكيد تسجيل الخروج
  Future<bool?> _showLogoutConfirmationDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: AppColors.orangeGradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.logout_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'تسجيل الخروج',
                style: GoogleFonts.cairo(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'هل أنت متأكد من رغبتك في تسجيل الخروج؟',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.orange.shade200,
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.orange.shade600,
                          size: 18,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'سيتم تنفيذ الإجراءات التالية:',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    _buildLogoutActionItem('إلغاء تفعيل خاصية "تذكرني"'),
                    _buildLogoutActionItem('مسح البيانات المحفوظة محلياً'),
                    _buildLogoutActionItem('تنظيف التخزين المؤقت'),
                    _buildLogoutActionItem('إنهاء جميع الجلسات النشطة'),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
              child: Text(
                'إلغاء',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryOrange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'تسجيل الخروج',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// بناء عنصر إجراء تسجيل الخروج
  Widget _buildLogoutActionItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Icon(
            Icons.check_circle_outline,
            color: Colors.orange.shade600,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: GoogleFonts.cairo(
                fontSize: 13,
                color: Colors.orange.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// عرض مؤشر التحميل
  void _showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 20),
              Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      gradient: AppColors.orangeGradient,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(
                    width: 40,
                    height: 40,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      strokeWidth: 3,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Text(
                'جاري تسجيل الخروج...',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'يرجى الانتظار قليلاً',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }



  /// تنفيذ عملية تسجيل الخروج الفعلية
  Future<void> _executeLogout({bool clearCache = true}) async {
    // تسجيل الخروج من Google Sign-In
    try {
      await _googleSignIn.signOut();
    } catch (e) {
      debugPrint('Error signing out from Google: $e');
    }

    // تسجيل الخروج من Firebase
    await _auth.signOut();

    // مسح البيانات المحلية
    await _clearLocalData();

    // تنظيف التخزين المؤقت إذا كان مطلوباً
    if (clearCache) {
      await _clearAppCache();
    }
  }

  /// مسح البيانات المحلية
  Future<void> _clearLocalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // مسح بيانات المصادقة
      await prefs.setBool('rememberMe', false);
      await prefs.remove('savedUserType');
      await prefs.remove('lastLoginTime');
      await prefs.remove('userPreferences');
      
      // مسح بيانات الجلسة
      await prefs.remove('sessionToken');
      await prefs.remove('refreshToken');
      
      // مسح بيانات التطبيق المؤقتة
      await prefs.remove('tempUserData');
      await prefs.remove('draftData');
      
    } catch (e) {
      debugPrint('Error clearing local data: $e');
    }
  }

  /// تنظيف التخزين المؤقت
  Future<void> _clearAppCache() async {
    try {
      // تنظيف التخزين المؤقت الأساسي
      // يمكن إضافة المزيد من عمليات التنظيف هنا حسب الحاجة
      debugPrint('Cache cleared successfully');
    } catch (e) {
      debugPrint('Error clearing app cache: $e');
    }
  }

  /// عرض رسالة النجاح
  void _showSuccessMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Text(
              AuthMessages.logoutSuccess,
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(16),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// عرض رسالة الخطأ
  void _showErrorMessage(BuildContext context, String error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'حدث خطأ أثناء تسجيل الخروج: $error',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(16),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// التوجه لصفحة تسجيل الدخول
  Future<void> _navigateToLogin(BuildContext context) async {
    // انتظار قصير لضمان اكتمال العمليات
    await Future.delayed(const Duration(milliseconds: 500));

    if (context.mounted) {
      Navigator.pushNamedAndRemoveUntil(
        context,
        AppRoutes.login,
        (route) => false,
      );
    }
  }

  /// تسجيل خروج سريع بدون حوار تأكيد (للاستخدام في حالات خاصة)
  Future<bool> performQuickLogout(BuildContext context) async {
    return await performComprehensiveLogout(
      context: context,
      showConfirmationDialog: false,
      clearCache: true,
    );
  }

  /// تسجيل خروج صامت (بدون رسائل أو حوارات)
  Future<void> performSilentLogout({BuildContext? context}) async {
    try {
      await _executeLogout(clearCache: true);

      // التوجه لصفحة تسجيل الدخول إذا تم توفير السياق
      if (context != null && context.mounted) {
        await _navigateToLogin(context);
      }
    } catch (e) {
      debugPrint('Error during silent logout: $e');
    }
  }
}
