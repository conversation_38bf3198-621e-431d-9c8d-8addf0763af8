import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/domain/entities/estate.dart';

/// صفحة تفاصيل العقار المنسوخ مع عرض جميع الصور
class CopiedEstateDetailsPage extends StatefulWidget {
  final Estate estate;
  final Map<String, dynamic> copiedData;

  const CopiedEstateDetailsPage({
    super.key,
    required this.estate,
    required this.copiedData,
  });

  @override
  State<CopiedEstateDetailsPage> createState() => _CopiedEstateDetailsPageState();
}

class _CopiedEstateDetailsPageState extends State<CopiedEstateDetailsPage>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  late AnimationController _animationController;
  late AnimationController _fabAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  late Animation<double> _fabAnimation;

  int _currentImageIndex = 0;
  List<String> _imageUrls = [];
  bool _showAppBarTitle = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadImages();
    _initAnimations();
    _setupScrollListener();
  }

  void _loadImages() {
    final images = widget.copiedData['images'] as List<dynamic>? ?? [];
    _imageUrls = images.map((e) => e.toString()).toList();
  }

  void _initAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _fabAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.elasticOut),
    );

    _animationController.forward();
    Future.delayed(const Duration(milliseconds: 500), () {
      _fabAnimationController.forward();
    });
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      final showTitle = _scrollController.offset > 200;
      if (showTitle != _showAppBarTitle) {
        setState(() {
          _showAppBarTitle = showTitle;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          _buildSliverAppBar(),
          SliverToBoxAdapter(
            child: AnimatedBuilder(
              animation: _fadeAnimation,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, _slideAnimation.value),
                  child: Opacity(
                    opacity: _fadeAnimation.value,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildPropertyHeader(),
                        const SizedBox(height: 16),
                        _buildQuickStats(),
                        const SizedBox(height: 20),
                        _buildPropertyDetails(),
                        const SizedBox(height: 20),
                        _buildAmenities(),
                        const SizedBox(height: 20),
                        _buildContactInfo(),
                        const SizedBox(height: 20),
                        _buildCopyInfo(),
                        const SizedBox(height: 100), // مساحة للأزرار السفلية
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButtons(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 350,
      pinned: true,
      backgroundColor: AppColors.primary,
      iconTheme: const IconThemeData(color: Colors.white),
      title: AnimatedOpacity(
        opacity: _showAppBarTitle ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 300),
        child: Text(
          widget.estate.title,
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
      actions: [
        IconButton(
          onPressed: _shareProperty,
          icon: const Icon(Icons.share, color: Colors.white),
          tooltip: 'مشاركة',
        ),
        IconButton(
          onPressed: _toggleFavorite,
          icon: const Icon(Icons.favorite_border, color: Colors.white),
          tooltip: 'إضافة للمفضلة',
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: _imageUrls.isNotEmpty
            ? Stack(
                children: [
                  PageView.builder(
                    controller: _pageController,
                    itemCount: _imageUrls.length,
                    onPageChanged: (index) {
                      setState(() {
                        _currentImageIndex = index;
                      });
                    },
                    itemBuilder: (context, index) {
                      return CachedNetworkImage(
                        imageUrl: _imageUrls[index],
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: Colors.grey[200],
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Colors.grey[200],
                          child: const Icon(Icons.error, size: 50),
                        ),
                      );
                    },
                  ),
                  // مؤشر الصور
                  if (_imageUrls.length > 1)
                    Positioned(
                      bottom: 20,
                      left: 0,
                      right: 0,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: _imageUrls.asMap().entries.map((entry) {
                          return Container(
                            width: 8,
                            height: 8,
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: _currentImageIndex == entry.key
                                  ? Colors.white
                                  : Colors.white.withValues(alpha: 0.5),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  // عداد الصور
                  if (_imageUrls.length > 1)
                    Positioned(
                      top: 60,
                      right: 16,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '${_currentImageIndex + 1} / ${_imageUrls.length}',
                          style: GoogleFonts.cairo(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                ],
              )
            : Container(
                color: Colors.grey[200],
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.home_outlined, size: 80, color: Colors.grey[400]),
                    const SizedBox(height: 16),
                    Text(
                      'لا توجد صور',
                      style: GoogleFonts.cairo(
                        color: Colors.grey[600],
                        fontSize: 18,
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  /// بناء header العقار مع المعلومات الأساسية
  Widget _buildPropertyHeader() {
    final isPaid = widget.copiedData['isPaid'] ?? false;
    final isActive = widget.copiedData['isActive'] ?? false;

    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان مع الحالة
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.estate.title,
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                    height: 1.3,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getStatusColor(isPaid, isActive),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getStatusIcon(isPaid, isActive),
                      size: 16,
                      color: Colors.white,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _getStatusText(isPaid, isActive),
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الموقع
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.location_on,
                  size: 20,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  widget.estate.location,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // السعر
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primary.withValues(alpha: 0.1),
                  AppColors.primary.withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.primary.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.payments,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'السعر الشهري',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${widget.estate.price.toStringAsFixed(0)} د.ك',
                      style: GoogleFonts.cairo(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats() {
    final views = widget.copiedData['views'] ?? 0;
    final inquiries = widget.copiedData['inquiries'] ?? 0;
    final likes = widget.copiedData['likes'] ?? 0;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              Icons.visibility_outlined,
              '$views',
              'مشاهدة',
              Colors.blue,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              Icons.message_outlined,
              '$inquiries',
              'استفسار',
              Colors.orange,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              Icons.favorite_outline,
              '$likes',
              'إعجاب',
              Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم المرافق والخدمات
  Widget _buildAmenities() {
    final amenities = [
      {'key': 'furnished', 'label': 'مفروش', 'icon': Icons.chair},
      {'key': 'parking', 'label': 'موقف سيارة', 'icon': Icons.local_parking},
      {'key': 'elevator', 'label': 'مصعد', 'icon': Icons.elevator},
      {'key': 'balcony', 'label': 'شرفة', 'icon': Icons.balcony},
      {'key': 'garden', 'label': 'حديقة', 'icon': Icons.grass},
      {'key': 'swimmingPool', 'label': 'مسبح', 'icon': Icons.pool},
      {'key': 'gym', 'label': 'صالة رياضية', 'icon': Icons.fitness_center},
      {'key': 'security', 'label': 'أمن', 'icon': Icons.security},
      {'key': 'airConditioning', 'label': 'تكييف', 'icon': Icons.ac_unit},
      {'key': 'heating', 'label': 'تدفئة', 'icon': Icons.whatshot},
      {'key': 'internet', 'label': 'إنترنت', 'icon': Icons.wifi},
      {'key': 'satelliteDish', 'label': 'ستلايت', 'icon': Icons.satellite_alt},
    ];

    final availableAmenities = amenities.where((amenity) {
      return widget.copiedData[amenity['key']] == true;
    }).toList();

    if (availableAmenities.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.home_repair_service,
                  color: AppColors.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'المرافق والخدمات',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: availableAmenities.map((amenity) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: AppColors.primary.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      amenity['icon'] as IconData,
                      size: 16,
                      color: AppColors.primary,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      amenity['label'] as String,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// بناء معلومات الاتصال
  Widget _buildContactInfo() {
    final contactPhone = widget.copiedData['contactPhone'] as String? ?? '';
    final contactWhatsApp = widget.copiedData['contactWhatsApp'] as String? ?? '';
    final contactEmail = widget.copiedData['contactEmail'] as String? ?? '';

    if (contactPhone.isEmpty && contactWhatsApp.isEmpty && contactEmail.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.contact_phone,
                  color: Colors.green,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'معلومات الاتصال',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (contactPhone.isNotEmpty)
            _buildContactItem(
              Icons.phone,
              'الهاتف',
              contactPhone,
              Colors.blue,
              () => _makePhoneCall(contactPhone),
            ),
          if (contactWhatsApp.isNotEmpty)
            _buildContactItem(
              Icons.chat,
              'واتساب',
              contactWhatsApp,
              Colors.green,
              () => _openWhatsApp(contactWhatsApp),
            ),
          if (contactEmail.isNotEmpty)
            _buildContactItem(
              Icons.email,
              'البريد الإلكتروني',
              contactEmail,
              Colors.orange,
              () => _sendEmail(contactEmail),
            ),
        ],
      ),
    );
  }

  Widget _buildPropertyDetails() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تفاصيل العقار',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          if (widget.estate.subCategory != null)
            _buildDetailRow('النوع', widget.estate.subCategory!),
          if (widget.estate.area != null)
            _buildDetailRow('المساحة', '${widget.estate.area} م²'),
          if (widget.estate.rooms != null)
            _buildDetailRow('عدد الغرف', '${widget.estate.rooms}'),
          if (widget.estate.bathrooms != null)
            _buildDetailRow('عدد الحمامات', '${widget.estate.bathrooms}'),
          if (widget.estate.buildingAge != null)
            _buildDetailRow('عمر البناء', '${widget.estate.buildingAge} سنة'),
          if (widget.estate.description.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'الوصف',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.estate.description,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: AppColors.textSecondary,
                height: 1.5,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCopyInfo() {
    final copiedAt = widget.copiedData['copiedAt'];
    final originalEstateId = widget.copiedData['originalEstateId'];
    final views = widget.copiedData['views'] ?? 0;
    final inquiries = widget.copiedData['inquiries'] ?? 0;

    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.content_copy, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'معلومات النسخة',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  Icons.visibility_outlined,
                  '$views',
                  'مشاهدة',
                  AppColors.primary,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  Icons.message_outlined,
                  '$inquiries',
                  'استفسار',
                  Colors.orange,
                ),
              ),
            ],
          ),
          if (copiedAt != null) ...[
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: AppColors.textSecondary),
                const SizedBox(width: 8),
                Text(
                  'تم النسخ: ${_formatDate(copiedAt.toDate())}',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ],
          if (originalEstateId != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.link, size: 16, color: AppColors.textSecondary),
                const SizedBox(width: 8),
                Text(
                  'معرف العقار الأصلي: $originalEstateId',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatCard(IconData icon, String value, String label, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Color _getStatusColor(bool isPaid, bool isActive) {
    if (isPaid && isActive) return Colors.green;
    if (isPaid) return Colors.orange;
    return Colors.red;
  }

  IconData _getStatusIcon(bool isPaid, bool isActive) {
    if (isPaid && isActive) return Icons.check_circle;
    if (isPaid) return Icons.schedule;
    return Icons.pending;
  }

  String _getStatusText(bool isPaid, bool isActive) {
    if (isPaid && isActive) return 'نشط';
    if (isPaid) return 'مدفوع';
    return 'معلق';
  }

  /// بناء عنصر الاتصال
  Widget _buildContactItem(IconData icon, String label, String value, Color color, VoidCallback onTap) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: color.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: Colors.white, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      value,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 16, color: color),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء الأزرار العائمة
  Widget _buildFloatingActionButtons() {
    return AnimatedBuilder(
      animation: _fabAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _fabAnimation.value,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Expanded(
                  child: FloatingActionButton.extended(
                    onPressed: _contactOwner,
                    backgroundColor: Colors.green,
                    icon: const Icon(Icons.phone, color: Colors.white),
                    label: Text(
                      'اتصال',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    heroTag: "contact",
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: FloatingActionButton.extended(
                    onPressed: _sendMessage,
                    backgroundColor: AppColors.primary,
                    icon: const Icon(Icons.message, color: Colors.white),
                    label: Text(
                      'رسالة',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    heroTag: "message",
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // دوال الاتصال والتفاعل
  void _shareProperty() {
    // TODO: تنفيذ مشاركة العقار
  }

  void _toggleFavorite() {
    // TODO: تنفيذ إضافة/إزالة من المفضلة
  }

  void _makePhoneCall(String phoneNumber) {
    // TODO: تنفيذ الاتصال الهاتفي
  }

  void _openWhatsApp(String phoneNumber) {
    // TODO: تنفيذ فتح واتساب
  }

  void _sendEmail(String email) {
    // TODO: تنفيذ إرسال بريد إلكتروني
  }

  void _contactOwner() {
    final contactPhone = widget.copiedData['contactPhone'] as String? ?? '';
    if (contactPhone.isNotEmpty) {
      _makePhoneCall(contactPhone);
    }
  }

  void _sendMessage() {
    final contactWhatsApp = widget.copiedData['contactWhatsApp'] as String? ?? '';
    if (contactWhatsApp.isNotEmpty) {
      _openWhatsApp(contactWhatsApp);
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    _fabAnimationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
