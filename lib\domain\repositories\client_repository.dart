import 'dart:io';
import '../entities/client.dart';
import '../entities/search_criteria.dart';

/// واجهة مستودع العملاء
abstract class ClientRepository {
  /// إنشاء عميل جديد
  Future<String> createClient(Client client);
  
  /// تحديث عميل موجود
  Future<void> updateClient(Client client);
  
  /// حذف عميل
  Future<void> deleteClient(String clientId);
  
  /// الحصول على عميل بواسطة المعرف
  Future<Client?> getClientById(String clientId);
  
  /// الحصول على عملاء المستخدم
  Future<List<Client>> getUserClients(String userId);
  
  /// الحصول على عملاء المستخدم بالتحميل المتدرج
  /// [userId] معرف المستخدم
  /// [limit] عدد العملاء في كل صفحة
  /// [lastClientId] معرف آخر عميل تم تحميله (للصفحات التالية)
  /// [status] حالة العملاء المطلوبة
  /// [query] نص البحث
  /// يعيد Map تحتوي على:
  /// - 'clients': قائمة العملاء
  /// - 'lastClientId': معرف آخر عميل (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من العملاء
  Future<Map<String, dynamic>> getUserClientsPaginated({
    required String userId,
    int limit = 20,
    String? lastClientId,
    ClientStatus? status,
    String? query,
  });
  
  /// البحث عن عملاء
  Future<List<Client>> searchClients({
    required String userId,
    String? query,
    ClientStatus? status,
    String? type,
  });
  
  /// تحديث حالة العميل
  Future<void> updateClientStatus(String clientId, ClientStatus status);
  
  /// تحديث تاريخ آخر تواصل مع العميل
  Future<void> updateClientLastContactDate(String clientId);
  
  /// إضافة عقار مفضل للعميل
  Future<void> addClientFavoriteEstate(String clientId, String estateId);
  
  /// إزالة عقار مفضل من العميل
  Future<void> removeClientFavoriteEstate(String clientId, String estateId);
  
  /// إضافة عقار تمت زيارته للعميل
  Future<void> addClientVisitedEstate(String clientId, String estateId);
  
  /// إضافة موعد للعميل
  Future<void> addClientAppointment(String clientId, String appointmentId);
  
  /// إضافة عرض للعميل
  Future<void> addClientOffer(String clientId, String offerId);
  
  /// إضافة عقار مباع/مؤجر للعميل
  Future<void> addClientSoldRentedEstate(String clientId, String estateId);
  
  /// تحديث معايير البحث للعميل
  Future<void> updateClientSearchCriteria(String clientId, SearchCriteria criteria);
  
  /// تحديث ميزانية العميل
  Future<void> updateClientBudget(String clientId, double budget);
  
  /// تحميل صورة العميل
  Future<String> uploadClientImage(String clientId, File image);
  
  /// إضافة ملاحظة للعميل
  Future<void> addClientNote(String clientId, String note);
  
  /// استيراد عملاء من ملف
  Future<int> importClientsFromFile(String userId, File file);
  
  /// تصدير عملاء إلى ملف
  Future<String> exportClientsToFile(String userId);
  
  /// الحصول على إحصائيات العملاء
  Future<Map<String, dynamic>> getClientsStatistics(String userId);
}
