rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    
    // قواعد عامة - منع الوصول غير المصرح به
    match /{allPaths=**} {
      allow read, write: if false;
    }
    
    // قواعد صور المستخدمين
    match /users/{userId}/profile/{imageId} {
      // القراءة: متاحة للجميع المسجلين
      allow read: if request.auth != null;
      
      // الكتابة: فقط لصاحب الملف الشخصي أو المشرفين
      allow write: if request.auth != null 
        && (request.auth.uid == userId || isAdmin())
        && isValidImage()
        && resource.size < 5 * 1024 * 1024; // حد أقصى 5 ميجابايت
      
      // الحذف: فقط لصاحب الملف الشخصي أو المشرفين
      allow delete: if request.auth != null 
        && (request.auth.uid == userId || isAdmin());
    }
    
    // قواعد صور العقارات
    match /estates/{estateId}/images/{imageId} {
      // القراءة: متاحة للجميع المسجلين
      allow read: if request.auth != null;
      
      // الكتابة: فقط لصاحب العقار أو المشرفين
      allow write: if request.auth != null 
        && (isEstateOwner(estateId) || isAdmin())
        && isValidImage()
        && resource.size < 10 * 1024 * 1024; // حد أقصى 10 ميجابايت
      
      // الحذف: فقط لصاحب العقار أو المشرفين
      allow delete: if request.auth != null 
        && (isEstateOwner(estateId) || isAdmin());
    }
    
    // قواعد مستندات المشاريع
    match /projects/{projectId}/documents/{documentId} {
      // القراءة: فقط لأعضاء المشروع أو المشرفين
      allow read: if request.auth != null 
        && (isProjectMember(projectId) || isAdmin());
      
      // الكتابة: فقط لأعضاء المشروع أو المشرفين
      allow write: if request.auth != null 
        && (isProjectMember(projectId) || isAdmin())
        && isValidDocument()
        && resource.size < 50 * 1024 * 1024; // حد أقصى 50 ميجابايت
      
      // الحذف: فقط لمدير المشروع أو المشرفين
      allow delete: if request.auth != null 
        && (isProjectManager(projectId) || isAdmin());
    }
    
    // قواعد ملفات المحادثات
    match /conversations/{conversationId}/files/{fileId} {
      // القراءة: فقط لأطراف المحادثة
      allow read: if request.auth != null 
        && isConversationParticipant(conversationId);
      
      // الكتابة: فقط لأطراف المحادثة
      allow write: if request.auth != null 
        && isConversationParticipant(conversationId)
        && isValidFile()
        && resource.size < 25 * 1024 * 1024; // حد أقصى 25 ميجابايت
      
      // الحذف: فقط لمرسل الملف أو المشرفين
      allow delete: if request.auth != null 
        && (isFileSender(conversationId, fileId) || isAdmin());
    }
    
    // قواعد النسخ الاحتياطية
    match /backups/{backupId}/{fileName} {
      // القراءة: فقط للمشرفين
      allow read: if request.auth != null && isAdmin();
      
      // الكتابة: فقط للمشرفين
      allow write: if request.auth != null && isAdmin();
      
      // الحذف: فقط للمشرفين
      allow delete: if request.auth != null && isAdmin();
    }
    
    // قواعد ملفات التقارير
    match /reports/{reportId}/{fileName} {
      // القراءة: فقط للمشرفين ومديري الشركات
      allow read: if request.auth != null 
        && (isAdmin() || isCompanyManager());
      
      // الكتابة: فقط للمشرفين
      allow write: if request.auth != null && isAdmin();
      
      // الحذف: فقط للمشرفين
      allow delete: if request.auth != null && isAdmin();
    }
    
    // الدوال المساعدة
    function isAdmin() {
      return request.auth != null 
        && firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role in ['admin', 'moderator'];
    }
    
    function isEstateOwner(estateId) {
      return request.auth != null 
        && firestore.get(/databases/(default)/documents/estates/$(estateId)).data.userId == request.auth.uid;
    }
    
    function isProjectMember(projectId) {
      return request.auth != null 
        && request.auth.uid in firestore.get(/databases/(default)/documents/projects/$(projectId)).data.memberIds;
    }
    
    function isProjectManager(projectId) {
      return request.auth != null 
        && firestore.get(/databases/(default)/documents/projects/$(projectId)).data.managerId == request.auth.uid;
    }
    
    function isConversationParticipant(conversationId) {
      return request.auth != null 
        && request.auth.uid in firestore.get(/databases/(default)/documents/conversations/$(conversationId)).data.participantIds;
    }
    
    function isFileSender(conversationId, fileId) {
      return request.auth != null 
        && firestore.get(/databases/(default)/documents/conversations/$(conversationId)/files/$(fileId)).data.senderId == request.auth.uid;
    }
    
    function isCompanyManager() {
      let userData = firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data;
      return userData.type == 4; // نوع شركة عقارية
    }
    
    function isValidImage() {
      return request.resource.contentType.matches('image/.*') 
        && request.resource.contentType in ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    }
    
    function isValidDocument() {
      return request.resource.contentType in [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain'
      ];
    }
    
    function isValidFile() {
      return isValidImage() || isValidDocument() || request.resource.contentType.matches('video/.*');
    }
  }
}
