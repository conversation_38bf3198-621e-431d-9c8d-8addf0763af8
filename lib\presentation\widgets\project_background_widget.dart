import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'dart:math' as math;

/// Widget للخلفية مع الأشكال الهندسية المعبرة عن المشاريع
class ProjectBackgroundWidget extends StatelessWidget {
  final Widget child;
  final double opacity;

  const ProjectBackgroundWidget({
    super.key,
    required this.child,
    this.opacity = 0.05,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // الخلفية البيضاء
        Container(
          color: Colors.white,
        ),
        
        // الأشكال الهندسية
        Positioned.fill(
          child: CustomPaint(
            painter: ProjectShapesPainter(
              color: AppColors.primary.withValues(alpha: opacity),
            ),
          ),
        ),
        
        // المحتوى
        child,
      ],
    );
  }
}

/// رسام الأشكال الهندسية للمشاريع
class ProjectShapesPainter extends CustomPainter {
  final Color color;

  ProjectShapesPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final strokePaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    // إنشاء أشكال متنوعة معبرة عن المشاريع
    _drawProjectShapes(canvas, size, paint, strokePaint);
  }

  void _drawProjectShapes(Canvas canvas, Size size, Paint fillPaint, Paint strokePaint) {
    final random = math.Random(42); // seed ثابت للحصول على نفس النمط
    
    // رسم أشكال مختلفة
    for (int i = 0; i < 25; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final shapeType = random.nextInt(8);
      final scale = 0.3 + random.nextDouble() * 0.7; // حجم متغير
      
      canvas.save();
      canvas.translate(x, y);
      canvas.scale(scale);
      
      switch (shapeType) {
        case 0:
          _drawHouse(canvas, fillPaint, strokePaint);
          break;
        case 1:
          _drawBuilding(canvas, fillPaint, strokePaint);
          break;
        case 2:
          _drawGear(canvas, strokePaint);
          break;
        case 3:
          _drawBlueprint(canvas, strokePaint);
          break;
        case 4:
          _drawHammer(canvas, fillPaint);
          break;
        case 5:
          _drawRuler(canvas, strokePaint);
          break;
        case 6:
          _drawCrane(canvas, strokePaint);
          break;
        case 7:
          _drawArchitecture(canvas, strokePaint);
          break;
      }
      
      canvas.restore();
    }
  }

  // رسم منزل
  void _drawHouse(Canvas canvas, Paint fillPaint, Paint strokePaint) {
    final path = Path();
    // قاعدة المنزل
    path.addRect(const Rect.fromLTWH(-10, -5, 20, 15));
    // سقف المنزل
    path.moveTo(-12, -5);
    path.lineTo(0, -15);
    path.lineTo(12, -5);
    path.close();
    
    canvas.drawPath(path, strokePaint);
  }

  // رسم مبنى
  void _drawBuilding(Canvas canvas, Paint fillPaint, Paint strokePaint) {
    // المبنى الرئيسي
    canvas.drawRect(const Rect.fromLTWH(-8, -15, 16, 25), strokePaint);
    // النوافذ
    for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 2; j++) {
        canvas.drawRect(
          Rect.fromLTWH(-6 + j * 8, -12 + i * 6, 3, 3),
          fillPaint,
        );
      }
    }
  }

  // رسم ترس
  void _drawGear(Canvas canvas, Paint strokePaint) {
    final center = const Offset(0, 0);
    final radius = 8.0;
    
    // الدائرة الخارجية
    canvas.drawCircle(center, radius, strokePaint);
    // الدائرة الداخلية
    canvas.drawCircle(center, radius * 0.4, strokePaint);
    
    // أسنان الترس
    for (int i = 0; i < 8; i++) {
      final angle = (i * math.pi * 2) / 8;
      final x1 = math.cos(angle) * radius;
      final y1 = math.sin(angle) * radius;
      final x2 = math.cos(angle) * (radius + 3);
      final y2 = math.sin(angle) * (radius + 3);
      
      canvas.drawLine(Offset(x1, y1), Offset(x2, y2), strokePaint);
    }
  }

  // رسم مخطط
  void _drawBlueprint(Canvas canvas, Paint strokePaint) {
    // إطار المخطط
    canvas.drawRect(const Rect.fromLTWH(-10, -8, 20, 16), strokePaint);
    // خطوط المخطط
    canvas.drawLine(const Offset(-8, -6), const Offset(8, -6), strokePaint);
    canvas.drawLine(const Offset(-8, 0), const Offset(8, 0), strokePaint);
    canvas.drawLine(const Offset(-8, 6), const Offset(8, 6), strokePaint);
    canvas.drawLine(const Offset(-6, -6), const Offset(-6, 6), strokePaint);
    canvas.drawLine(const Offset(0, -6), const Offset(0, 6), strokePaint);
    canvas.drawLine(const Offset(6, -6), const Offset(6, 6), strokePaint);
  }

  // رسم مطرقة
  void _drawHammer(Canvas canvas, Paint fillPaint) {
    // رأس المطرقة
    canvas.drawRect(const Rect.fromLTWH(-8, -3, 10, 6), fillPaint);
    // مقبض المطرقة
    canvas.drawRect(const Rect.fromLTWH(2, -1, 12, 2), fillPaint);
  }

  // رسم مسطرة
  void _drawRuler(Canvas canvas, Paint strokePaint) {
    // جسم المسطرة
    canvas.drawRect(const Rect.fromLTWH(-12, -2, 24, 4), strokePaint);
    // علامات القياس
    for (int i = 0; i < 5; i++) {
      final x = -10 + i * 5.0;
      canvas.drawLine(Offset(x, -2), Offset(x, 2), strokePaint);
    }
  }

  // رسم رافعة
  void _drawCrane(Canvas canvas, Paint strokePaint) {
    // قاعدة الرافعة
    canvas.drawRect(const Rect.fromLTWH(-3, 5, 6, 8), strokePaint);
    // عمود الرافعة
    canvas.drawLine(const Offset(0, 5), const Offset(0, -10), strokePaint);
    // ذراع الرافعة
    canvas.drawLine(const Offset(0, -10), const Offset(15, -8), strokePaint);
    // الخطاف
    canvas.drawLine(const Offset(12, -8), const Offset(12, -3), strokePaint);
  }

  // رسم عمارة
  void _drawArchitecture(Canvas canvas, Paint strokePaint) {
    // أعمدة
    for (int i = 0; i < 3; i++) {
      final x = -8 + i * 8.0;
      canvas.drawLine(Offset(x, -10), Offset(x, 8), strokePaint);
    }
    // القاعدة
    canvas.drawLine(const Offset(-10, 8), const Offset(10, 8), strokePaint);
    // السقف
    canvas.drawLine(const Offset(-10, -10), const Offset(10, -10), strokePaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
