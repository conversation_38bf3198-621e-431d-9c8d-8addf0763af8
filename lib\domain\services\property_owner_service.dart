import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../entities/estate.dart';

class PropertyOwnerService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// الحصول على إحصائيات لوحة التحكم
  Future<Map<String, dynamic>> getDashboardStatistics() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return {};

      // جلب العقارات الخاصة بالمستخدم
      final propertiesSnapshot = await _firestore
          .collection('estates')
          .where('userId', isEqualTo: user.uid)
          .get();

      final properties = propertiesSnapshot.docs;

      // حساب إجمالي المشاهدات
      int totalViews = 0;
      int monthlyViews = 0;
      int totalInquiries = 0;
      int scheduledViewings = 0;

      for (final doc in properties) {
        final data = doc.data();
        totalViews += (data['viewsCount'] as int?) ?? 0;
        totalInquiries += (data['inquiriesCount'] as int?) ?? 0;

        // حساب المشاهدات الشهرية (آخر 30 يوم)
        final viewsHistory = data['viewsHistory'] as List<dynamic>? ?? [];
        final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));

        for (final view in viewsHistory) {
          final viewDate = (view['timestamp'] as Timestamp).toDate();
          if (viewDate.isAfter(thirtyDaysAgo)) {
            monthlyViews++;
          }
        }
      }

      // جلب المعاينات المجدولة
      final viewingsSnapshot = await _firestore
          .collection('propertyViewings')
          .where('ownerId', isEqualTo: user.uid)
          .where('status', isEqualTo: 'scheduled')
          .get();

      scheduledViewings = viewingsSnapshot.docs.length;

      return {
        'totalProperties': properties.length,
        'totalViews': totalViews,
        'monthlyViews': monthlyViews,
        'totalInquiries': totalInquiries,
        'scheduledViewings': scheduledViewings,
        'averageViewsPerProperty': properties.isNotEmpty ? totalViews / properties.length : 0,
      };
    } catch (e) {
      print('Error getting dashboard statistics: $e');
      return {};
    }
  }

  /// الحصول على عقارات المستخدم
  Future<List<Estate>> getMyProperties() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final snapshot = await _firestore
          .collection('estates')
          .where('userId', isEqualTo: user.uid)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => _mapToEstate(doc.data(), doc.id))
          .toList();
    } catch (e) {
      print('Error getting my properties: $e');
      return [];
    }
  }

  /// الحصول على الاستفسارات
  Future<List<Map<String, dynamic>>> getInquiries() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final snapshot = await _firestore
          .collection('propertyInquiries')
          .where('ownerId', isEqualTo: user.uid)
          .orderBy('createdAt', descending: true)
          .limit(50)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('Error getting inquiries: $e');
      return [];
    }
  }

  /// الحصول على المعاينات المجدولة
  Future<List<Map<String, dynamic>>> getScheduledViewings() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final snapshot = await _firestore
          .collection('propertyViewings')
          .where('ownerId', isEqualTo: user.uid)
          .where('status', isEqualTo: 'scheduled')
          .orderBy('scheduledDate', descending: false)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      print('Error getting scheduled viewings: $e');
      return [];
    }
  }

  /// حذف عقار
  Future<bool> deleteProperty(String propertyId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // التحقق من ملكية العقار
      final propertyDoc = await _firestore
          .collection('estates')
          .doc(propertyId)
          .get();

      if (!propertyDoc.exists) {
        throw Exception('العقار غير موجود');
      }

      final propertyData = propertyDoc.data()!;
      if (propertyData['userId'] != user.uid) {
        throw Exception('ليس لديك صلاحية لحذف هذا العقار');
      }

      // حذف العقار
      await _firestore.collection('estates').doc(propertyId).delete();

      // حذف البيانات المرتبطة
      await _deleteRelatedData(propertyId);

      return true;
    } catch (e) {
      print('Error deleting property: $e');
      rethrow;
    }
  }

  /// حذف البيانات المرتبطة بالعقار
  Future<void> _deleteRelatedData(String propertyId) async {
    try {
      final batch = _firestore.batch();

      // حذف الاستفسارات
      final inquiriesSnapshot = await _firestore
          .collection('propertyInquiries')
          .where('propertyId', isEqualTo: propertyId)
          .get();

      for (final doc in inquiriesSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // حذف المعاينات
      final viewingsSnapshot = await _firestore
          .collection('propertyViewings')
          .where('propertyId', isEqualTo: propertyId)
          .get();

      for (final doc in viewingsSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // حذف من المفضلة
      final favoritesSnapshot = await _firestore
          .collectionGroup('favorites')
          .where('propertyId', isEqualTo: propertyId)
          .get();

      for (final doc in favoritesSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // حذف من المقارنة
      final comparisonSnapshot = await _firestore
          .collectionGroup('comparison')
          .where('propertyId', isEqualTo: propertyId)
          .get();

      for (final doc in comparisonSnapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
    } catch (e) {
      print('Error deleting related data: $e');
    }
  }

  /// تحديث عقار
  Future<bool> updateProperty(String propertyId, Map<String, dynamic> updates) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // التحقق من ملكية العقار
      final propertyDoc = await _firestore
          .collection('estates')
          .doc(propertyId)
          .get();

      if (!propertyDoc.exists) {
        throw Exception('العقار غير موجود');
      }

      final propertyData = propertyDoc.data()!;
      if (propertyData['userId'] != user.uid) {
        throw Exception('ليس لديك صلاحية لتعديل هذا العقار');
      }

      // إضافة تاريخ التحديث
      updates['updatedAt'] = FieldValue.serverTimestamp();

      // تحديث العقار
      await _firestore.collection('estates').doc(propertyId).update(updates);

      return true;
    } catch (e) {
      print('Error updating property: $e');
      rethrow;
    }
  }

  /// إضافة مشاهدة للعقار
  Future<void> addPropertyView(String propertyId, String? viewerId) async {
    try {
      final propertyRef = _firestore.collection('estates').doc(propertyId);

      await _firestore.runTransaction((transaction) async {
        final propertyDoc = await transaction.get(propertyRef);

        if (!propertyDoc.exists) return;

        final currentViews = propertyDoc.data()?['viewsCount'] ?? 0;
        final viewsHistory = List<Map<String, dynamic>>.from(
          propertyDoc.data()?['viewsHistory'] ?? []
        );

        // إضافة المشاهدة الجديدة
        viewsHistory.add({
          'viewerId': viewerId,
          'timestamp': Timestamp.now(),
          'ipAddress': null, // يمكن إضافة IP إذا لزم الأمر
        });

        // الاحتفاظ بآخر 1000 مشاهدة فقط
        if (viewsHistory.length > 1000) {
          viewsHistory.removeRange(0, viewsHistory.length - 1000);
        }

        transaction.update(propertyRef, {
          'viewsCount': currentViews + 1,
          'viewsHistory': viewsHistory,
          'lastViewedAt': FieldValue.serverTimestamp(),
        });
      });
    } catch (e) {
      print('Error adding property view: $e');
    }
  }

  /// إضافة استفسار
  Future<bool> addInquiry({
    required String propertyId,
    required String message,
    required String inquirerName,
    required String inquirerPhone,
    String? inquirerEmail,
  }) async {
    try {
      final user = _auth.currentUser;

      // الحصول على معلومات العقار
      final propertyDoc = await _firestore
          .collection('estates')
          .doc(propertyId)
          .get();

      if (!propertyDoc.exists) {
        throw Exception('العقار غير موجود');
      }

      final propertyData = propertyDoc.data()!;
      final ownerId = propertyData['userId'];

      // إضافة الاستفسار
      await _firestore.collection('propertyInquiries').add({
        'propertyId': propertyId,
        'ownerId': ownerId,
        'inquirerId': user?.uid,
        'inquirerName': inquirerName,
        'inquirerPhone': inquirerPhone,
        'inquirerEmail': inquirerEmail,
        'message': message,
        'status': 'pending',
        'createdAt': FieldValue.serverTimestamp(),
        'propertyTitle': propertyData['title'],
        'propertyPrice': propertyData['price'],
      });

      // تحديث عداد الاستفسارات في العقار
      await _firestore.collection('estates').doc(propertyId).update({
        'inquiriesCount': FieldValue.increment(1),
      });

      return true;
    } catch (e) {
      // Error adding inquiry
      rethrow;
    }
  }

  /// جدولة معاينة
  Future<bool> scheduleViewing({
    required String propertyId,
    required DateTime scheduledDate,
    required String viewerName,
    required String viewerPhone,
    String? viewerEmail,
    String? notes,
  }) async {
    try {
      final user = _auth.currentUser;

      // الحصول على معلومات العقار
      final propertyDoc = await _firestore
          .collection('estates')
          .doc(propertyId)
          .get();

      if (!propertyDoc.exists) {
        throw Exception('العقار غير موجود');
      }

      final propertyData = propertyDoc.data()!;
      final ownerId = propertyData['userId'];

      // إضافة المعاينة
      await _firestore.collection('propertyViewings').add({
        'propertyId': propertyId,
        'ownerId': ownerId,
        'viewerId': user?.uid,
        'viewerName': viewerName,
        'viewerPhone': viewerPhone,
        'viewerEmail': viewerEmail,
        'scheduledDate': Timestamp.fromDate(scheduledDate),
        'status': 'scheduled',
        'notes': notes,
        'createdAt': FieldValue.serverTimestamp(),
        'propertyTitle': propertyData['title'],
        'propertyLocation': propertyData['location'],
      });

      return true;
    } catch (e) {
      print('Error scheduling viewing: $e');
      rethrow;
    }
  }

  /// الحصول على إحصائيات مفصلة لعقار
  Future<Map<String, dynamic>> getPropertyStatistics(String propertyId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return {};

      // التحقق من ملكية العقار
      final propertyDoc = await _firestore
          .collection('estates')
          .doc(propertyId)
          .get();

      if (!propertyDoc.exists) {
        throw Exception('العقار غير موجود');
      }

      final propertyData = propertyDoc.data()!;
      if (propertyData['userId'] != user.uid) {
        throw Exception('ليس لديك صلاحية لعرض إحصائيات هذا العقار');
      }

      // جلب الإحصائيات
      final viewsHistory = List<Map<String, dynamic>>.from(
        propertyData['viewsHistory'] ?? []
      );

      final inquiriesSnapshot = await _firestore
          .collection('propertyInquiries')
          .where('propertyId', isEqualTo: propertyId)
          .get();

      final viewingsSnapshot = await _firestore
          .collection('propertyViewings')
          .where('propertyId', isEqualTo: propertyId)
          .get();

      // تحليل المشاهدات حسب التاريخ
      final Map<String, int> dailyViews = {};
      for (final view in viewsHistory) {
        final timestamp = view['timestamp'] as Timestamp?;
        if (timestamp != null) {
          final date = timestamp.toDate();
          final dateKey = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
          dailyViews[dateKey] = (dailyViews[dateKey] ?? 0) + 1;
        }
      }

      return {
        'totalViews': propertyData['viewsCount'] ?? 0,
        'totalInquiries': inquiriesSnapshot.docs.length,
        'totalViewings': viewingsSnapshot.docs.length,
        'dailyViews': dailyViews,
        'favoritesCount': propertyData['favoritesCount'] ?? 0,
        'createdAt': propertyData['createdAt'],
        'lastViewedAt': propertyData['lastViewedAt'],
      };
    } catch (e) {
      print('Error getting property statistics: $e');
      rethrow;
    }
  }

  /// ترويج عقار
  Future<bool> promoteProperty(String propertyId, String promotionType) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // التحقق من ملكية العقار
      final propertyDoc = await _firestore
          .collection('estates')
          .doc(propertyId)
          .get();

      if (!propertyDoc.exists) {
        throw Exception('العقار غير موجود');
      }

      final propertyData = propertyDoc.data()!;
      if (propertyData['userId'] != user.uid) {
        throw Exception('ليس لديك صلاحية لترويج هذا العقار');
      }

      // تحديد مدة الترويج حسب النوع
      DateTime promotionEndDate;
      switch (promotionType) {
        case 'basic':
          promotionEndDate = DateTime.now().add(const Duration(days: 7));
          break;
        case 'premium':
          promotionEndDate = DateTime.now().add(const Duration(days: 15));
          break;
        case 'gold':
          promotionEndDate = DateTime.now().add(const Duration(days: 30));
          break;
        default:
          throw Exception('نوع ترويج غير صحيح');
      }

      // تحديث العقار
      await _firestore.collection('estates').doc(propertyId).update({
        'isPromoted': true,
        'promotionType': promotionType,
        'promotionStartDate': FieldValue.serverTimestamp(),
        'promotionEndDate': Timestamp.fromDate(promotionEndDate),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // إضافة سجل الترويج
      await _firestore.collection('promotions').add({
        'propertyId': propertyId,
        'ownerId': user.uid,
        'promotionType': promotionType,
        'startDate': FieldValue.serverTimestamp(),
        'endDate': Timestamp.fromDate(promotionEndDate),
        'status': 'active',
        'createdAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error promoting property: $e');
      rethrow;
    }
  }

  /// الحصول على تقرير شهري
  Future<Map<String, dynamic>> getMonthlyReport() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return {};

      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0);

      // جلب العقارات
      final propertiesSnapshot = await _firestore
          .collection('estates')
          .where('userId', isEqualTo: user.uid)
          .get();

      int totalViews = 0;
      int totalInquiries = 0;
      int newProperties = 0;

      for (final doc in propertiesSnapshot.docs) {
        final data = doc.data();

        // حساب العقارات الجديدة هذا الشهر
        final createdAt = (data['createdAt'] as Timestamp?)?.toDate();
        if (createdAt != null &&
            createdAt.isAfter(startOfMonth) &&
            createdAt.isBefore(endOfMonth)) {
          newProperties++;
        }

        // حساب المشاهدات والاستفسارات
        totalViews += (data['viewsCount'] as int?) ?? 0;
        totalInquiries += (data['inquiriesCount'] as int?) ?? 0;
      }

      return {
        'month': now.month,
        'year': now.year,
        'totalProperties': propertiesSnapshot.docs.length,
        'newProperties': newProperties,
        'totalViews': totalViews,
        'totalInquiries': totalInquiries,
        'averageViewsPerProperty': propertiesSnapshot.docs.isNotEmpty
            ? totalViews / propertiesSnapshot.docs.length
            : 0,
      };
    } catch (e) {
      print('Error getting monthly report: $e');
      return {};
    }
  }

  /// تحويل البيانات من Firestore إلى Estate object
  Estate _mapToEstate(Map<String, dynamic> data, String id) {
    return Estate(
      id: id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      price: (data['price'] ?? 0).toDouble(),
      location: data['location'] ?? '',
      photoUrls: List<String>.from(data['photoUrls'] ?? []),
      isFeatured: data['isFeatured'] ?? false,
      planType: data['planType'] ?? 'free',
      startDate: data['startDate'] != null
          ? DateTime.parse(data['startDate'])
          : null,
      endDate: data['endDate'] != null
          ? DateTime.parse(data['endDate'])
          : null,
      createdAt: data['createdAt'] != null
          ? DateTime.parse(data['createdAt'])
          : DateTime.now(),
      mainCategory: data['mainCategory'],
      subCategory: data['subCategory'],
      postedByUserType: data['postedByUserType'],
      hidePhone: data['hidePhone'] ?? false,
      extraPhones: List<String>.from(data['extraPhones'] ?? []),
      shareLocation: data['shareLocation'] ?? false,
      lat: data['lat']?.toDouble(),
      lng: data['lng']?.toDouble(),
      hasCentralAC: data['hasCentralAC'] ?? false,
      hasSecurity: data['hasSecurity'],
      allowPets: data['allowPets'],
      hasElevator: data['hasElevator'],
      hasSwimmingPool: data['hasSwimmingPool'],
      hasMaidRoom: data['hasMaidRoom'] ?? false,
      hasGarage: data['hasGarage'] ?? false,
      hasBalcony: data['hasBalcony'],
      isFullyFurnished: data['isFullyFurnished'],
      rebound: data['rebound'],
      numberOfRooms: data['numberOfRooms'],
      internalLocation: data['internalLocation'],
      salon: data['salon'],
      area: data['area']?.toDouble(),
      floorNumber: data['floorNumber'],
      numberOfBathrooms: data['numberOfBathrooms'],
      buildingAge: data['buildingAge'],
      numberOfFloors: data['numberOfFloors'],
      propertyType: data['propertyType'],
      autoRepublish: data['autoRepublish'] ?? false,
      kuwaitCornersPin: data['kuwaitCornersPin'] ?? false,
      movingAd: data['movingAd'] ?? false,
      vipBadge: data['vipBadge'] ?? false,
      pinnedOnHome: data['pinnedOnHome'] ?? false,
      discountCode: data['discountCode'],
      advertiserImage: data['advertiserImage'],
      advertiserName: data['advertiserName'],
      advertiserEmail: data['advertiserEmail'],
      advertiserRegistrationDate: data['advertiserRegistrationDate'] != null
          ? DateTime.parse(data['advertiserRegistrationDate'])
          : null,
      advertiserAdsCount: data['advertiserAdsCount'],
      ownerId: data['ownerId'],
      originalEstateId: data['originalEstateId'],
      isOriginal: data['isOriginal'] ?? true,
      copiedBy: List<String>.from(data['copiedBy'] ?? []),
      isPaymentVerified: data['isPaymentVerified'] ?? false,
      viewsCount: data['viewsCount'],
      inquiriesCount: data['inquiriesCount'],
      favoritesCount: data['favoritesCount'],
      latitude: data['latitude']?.toDouble(),
      longitude: data['longitude']?.toDouble(),
      rooms: data['rooms'],
      bathrooms: data['bathrooms'],
      floors: data['floors'],
      purpose: data['purpose'],
      hasGarden: data['hasGarden'],
      hasPool: data['hasPool'],
      hasDriverRoom: data['hasDriverRoom'],
      hasPrivateEntrance: data['hasPrivateEntrance'],
      hasEquippedKitchen: data['hasEquippedKitchen'],
      isAvailable: data['isAvailable'] ?? true);
  }
}
