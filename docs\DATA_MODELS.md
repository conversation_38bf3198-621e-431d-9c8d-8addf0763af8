# نماذج البيانات - تطبيق Krea

## 📋 نظرة عامة

هذا المرجع يوثق جميع نماذج البيانات المستخدمة في تطبيق Krea مع تفاصيل الحقول والعلاقات.

---

## 👤 نماذج المستخدمين

### User Model
```dart
class User {
  final String id;
  final String email;
  final String? displayName;
  final String? phoneNumber;
  final String? photoURL;
  final UserType type;
  final VerificationStatus verificationStatus;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final Map<String, dynamic>? preferences;
  final bool isActive;
  final bool isEmailVerified;
  final bool isPhoneVerified;
}
```

**الحقول:**
- `id`: معرف المستخدم الفريد
- `email`: البريد الإلكتروني
- `displayName`: الاسم المعروض
- `phoneNumber`: رقم الهاتف
- `photoURL`: رابط صورة الملف الشخصي
- `type`: نوع المستخدم (seeker, agent, owner, company)
- `verificationStatus`: حالة التحقق
- `createdAt`: تاريخ إنشاء الحساب
- `lastLoginAt`: آخر تسجيل دخول
- `preferences`: تفضيلات المستخدم
- `isActive`: حالة النشاط
- `isEmailVerified`: تحقق البريد الإلكتروني
- `isPhoneVerified`: تحقق رقم الهاتف

### UserType Enum
```dart
enum UserType {
  seeker,    // باحث عن عقار
  agent,     // مستثمر/وكيل عقاري
  owner,     // مالك عقار
  company,   // شركة عقارية
}
```

### VerificationStatus Enum
```dart
enum VerificationStatus {
  pending,    // قيد المراجعة
  verified,   // محقق
  rejected,   // مرفوض
  none,       // غير مطلوب
}
```

---

## 🏠 نماذج العقارات

### Estate Model
```dart
class Estate {
  final String id;
  final String title;
  final String description;
  final double price;
  final String currency;
  final EstateType type;
  final String mainCategory;
  final String? subCategory;
  final Location location;
  final List<String> images;
  final Map<String, dynamic> features;
  final String ownerId;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isActive;
  final bool isFeatured;
  final bool isPinned;
  final int viewCount;
  final int favoriteCount;
  final double? rating;
  final List<String> tags;
}
```

**الحقول:**
- `id`: معرف العقار الفريد
- `title`: عنوان العقار
- `description`: وصف مفصل
- `price`: السعر
- `currency`: العملة (KWD)
- `type`: نوع العقار
- `mainCategory`: التصنيف الرئيسي
- `subCategory`: التصنيف الفرعي
- `location`: الموقع الجغرافي
- `images`: قائمة روابط الصور
- `features`: المميزات والخصائص
- `ownerId`: معرف المالك
- `createdAt`: تاريخ الإنشاء
- `updatedAt`: تاريخ آخر تحديث
- `isActive`: حالة النشاط
- `isFeatured`: عقار مميز
- `isPinned`: عقار مثبت
- `viewCount`: عدد المشاهدات
- `favoriteCount`: عدد الإعجابات
- `rating`: التقييم
- `tags`: الكلمات المفتاحية

### EstateType Enum
```dart
enum EstateType {
  apartment,     // شقة
  house,         // منزل
  villa,         // فيلا
  land,          // أرض
  office,        // مكتب
  shop,          // محل تجاري
  warehouse,     // مخزن
}
```

### Location Model
```dart
class Location {
  final String governorate;
  final String area;
  final String? district;
  final String? street;
  final String? buildingNumber;
  final double? latitude;
  final double? longitude;
  final String? mapUrl;
}
```

---

## 💬 نماذج المنتدى

### ForumTopic Model
```dart
class ForumTopic {
  final String id;
  final String title;
  final String content;
  final String authorId;
  final String categoryId;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isPinned;
  final bool isFeatured;
  final bool isLocked;
  final int viewCount;
  final int replyCount;
  final int likeCount;
  final List<String> tags;
  final List<String> attachments;
  final String? lastReplyId;
  final DateTime? lastReplyAt;
}
```

### ForumReply Model
```dart
class ForumReply {
  final String id;
  final String topicId;
  final String content;
  final String authorId;
  final String? parentReplyId;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final int likeCount;
  final List<String> attachments;
  final bool isDeleted;
}
```

### ForumCategory Model
```dart
class ForumCategory {
  final String id;
  final String name;
  final String description;
  final String icon;
  final String color;
  final int order;
  final bool isActive;
  final int topicCount;
  final CategoryType type;
}
```

---

## 📱 نماذج الإشعارات

### NotificationModel
```dart
class NotificationModel {
  final String id;
  final String title;
  final String body;
  final NotificationType type;
  final String userId;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final bool isRead;
  final String? imageUrl;
  final String? actionUrl;
}
```

### NotificationType Enum
```dart
enum NotificationType {
  estate,           // إشعار عقار
  forum,            // إشعار منتدى
  message,          // رسالة
  system,           // إشعار نظام
  payment,          // إشعار دفع
  achievement,      // إنجاز
  reminder,         // تذكير
}
```

---

## 🎯 نماذج طلبات العقارات

### PropertyRequest Model
```dart
class PropertyRequest {
  final String id;
  final String title;
  final String description;
  final String requesterId;
  final EstateType propertyType;
  final String preferredLocation;
  final double? minPrice;
  final double? maxPrice;
  final Map<String, dynamic> requirements;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final RequestStatus status;
  final List<PropertyRequestResponse> responses;
  final int responseCount;
}
```

### PropertyRequestResponse Model
```dart
class PropertyRequestResponse {
  final String id;
  final String requestId;
  final String responderId;
  final String message;
  final String? estateId;
  final DateTime createdAt;
  final ResponseStatus status;
}
```

### RequestStatus Enum
```dart
enum RequestStatus {
  active,      // نشط
  fulfilled,   // مكتمل
  expired,     // منتهي الصلاحية
  cancelled,   // ملغي
}
```

---

## 🏆 نماذج الولاء والمكافآت

### LoyaltyPoints Model
```dart
class LoyaltyPoints {
  final String userId;
  final int totalPoints;
  final int availablePoints;
  final int usedPoints;
  final List<PointTransaction> transactions;
  final DateTime lastUpdated;
}
```

### PointTransaction Model
```dart
class PointTransaction {
  final String id;
  final String userId;
  final int points;
  final TransactionType type;
  final String reason;
  final String? referenceId;
  final DateTime timestamp;
}
```

### Reward Model
```dart
class Reward {
  final String id;
  final String name;
  final String description;
  final int pointsCost;
  final RewardType type;
  final String? imageUrl;
  final bool isActive;
  final DateTime? expiresAt;
  final int maxRedemptions;
  final int currentRedemptions;
}
```

---

## 💳 نماذج الدفع

### PaymentTransaction Model
```dart
class PaymentTransaction {
  final String id;
  final String userId;
  final double amount;
  final String currency;
  final PaymentMethod method;
  final PaymentStatus status;
  final String description;
  final String? referenceId;
  final DateTime createdAt;
  final DateTime? completedAt;
  final Map<String, dynamic>? metadata;
}
```

### PaymentMethod Enum
```dart
enum PaymentMethod {
  creditCard,    // بطاقة ائتمانية
  debitCard,     // بطاقة مدى
  bankTransfer,  // تحويل بنكي
  wamda,         // خدمة ومض
}
```

### PaymentStatus Enum
```dart
enum PaymentStatus {
  pending,    // قيد المعالجة
  completed,  // مكتمل
  failed,     // فاشل
  cancelled,  // ملغي
  refunded,   // مسترد
}
```

---

## 📊 نماذج المشاريع والإدارة

### Project Model
```dart
class Project {
  final String id;
  final String name;
  final String description;
  final String companyId;
  final ProjectStatus status;
  final DateTime startDate;
  final DateTime? endDate;
  final double? budget;
  final List<String> teamMembers;
  final List<String> estates;
  final List<ProjectTask> tasks;
  final Map<String, dynamic> metadata;
}
```

### ProjectTask Model
```dart
class ProjectTask {
  final String id;
  final String projectId;
  final String title;
  final String? description;
  final String? assigneeId;
  final TaskStatus status;
  final TaskPriority priority;
  final DateTime? dueDate;
  final DateTime createdAt;
  final DateTime? completedAt;
}
```

### Client Model
```dart
class Client {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final ClientType type;
  final String? companyId;
  final List<ClientInteraction> interactions;
  final Map<String, dynamic> preferences;
  final DateTime createdAt;
  final DateTime? lastContactAt;
}
```

---

## 📈 نماذج التحليلات

### AnalyticsData Model
```dart
class AnalyticsData {
  final String id;
  final String entityType;
  final String entityId;
  final String eventType;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final String? userId;
  final String? sessionId;
}
```

### MarketAnalysis Model
```dart
class MarketAnalysis {
  final String id;
  final String region;
  final EstateType propertyType;
  final double averagePrice;
  final double priceChange;
  final int totalListings;
  final int soldProperties;
  final double demandIndex;
  final DateTime analysisDate;
  final Map<String, dynamic> trends;
}
```

---

## 🔐 نماذج الأمان

### SecurityLog Model
```dart
class SecurityLog {
  final String id;
  final String userId;
  final SecurityEventType eventType;
  final String description;
  final String ipAddress;
  final String userAgent;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;
}
```

### VerificationRequest Model
```dart
class VerificationRequest {
  final String id;
  final String userId;
  final UserType userType;
  final VerificationStatus status;
  final List<String> documentUrls;
  final DateTime submissionDate;
  final DateTime? reviewDate;
  final String? reviewNotes;
  final Map<String, dynamic>? additionalInfo;
}
```

---

## 📁 نماذج الملفات

### FileMetadata Model
```dart
class FileMetadata {
  final String id;
  final String fileName;
  final String originalName;
  final String mimeType;
  final int size;
  final String url;
  final String uploaderId;
  final DateTime uploadedAt;
  final FileType type;
  final Map<String, dynamic>? metadata;
}
```

### FileType Enum
```dart
enum FileType {
  image,      // صورة
  document,   // مستند
  video,      // فيديو
  audio,      // صوت
  other,      // أخرى
}
```

---

## 🔄 نماذج التحديثات

### AppUpdate Model
```dart
class AppUpdate {
  final String version;
  final String buildNumber;
  final String title;
  final String description;
  final List<String> features;
  final List<String> bugFixes;
  final bool isRequired;
  final DateTime releaseDate;
  final String downloadUrl;
}
```

---

## 📱 نماذج الجهاز

### DeviceInfo Model
```dart
class DeviceInfo {
  final String deviceId;
  final String platform;
  final String osVersion;
  final String appVersion;
  final String model;
  final String manufacturer;
  final bool isPhysicalDevice;
  final Map<String, dynamic>? additionalInfo;
}
```

---

## 🌐 نماذج الشبكة

### ApiResponse Model
```dart
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final String? errorCode;
  final int statusCode;
  final DateTime timestamp;
}
```

### NetworkError Model
```dart
class NetworkError {
  final String code;
  final String message;
  final int? statusCode;
  final String? details;
  final DateTime timestamp;
}
```

---

## 🔧 معايير تصميم النماذج

### مبادئ التصميم
تم تصميم جميع نماذج البيانات بواسطة فريق **Codnet Moroccan** وفقاً للمبادئ التالية:

- **Type Safety**: استخدام الأنواع الآمنة في Dart
- **Immutability**: النماذج غير قابلة للتغيير لضمان الأمان
- **Serialization**: دعم كامل للتحويل من وإلى JSON
- **Validation**: التحقق من صحة البيانات عند الإنشاء
- **Documentation**: توثيق شامل لكل حقل ونموذج

### التوافق مع قواعد البيانات
- **Firestore**: تحويل تلقائي من وإلى Firestore
- **JSON**: دعم كامل لـ JSON serialization
- **Type Conversion**: تحويل آمن بين الأنواع المختلفة
- **Null Safety**: دعم Dart null safety

### إرشادات الاستخدام
- استخدم المصانع (factories) لإنشاء النماذج
- تحقق من صحة البيانات قبل الحفظ
- استخدم copyWith للتحديثات الجزئية
- اتبع نمط Repository للوصول للبيانات

---

*تم تصميم هذه النماذج بواسطة فريق Codnet Moroccan - جميع النماذج تدعم التحويل من وإلى JSON وFirestore للتكامل مع قاعدة البيانات*
