import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/core/services/settings_service.dart';
import 'package:kuwait_corners/core/models/app_settings.dart';

class PerformanceSettingsPage extends StatefulWidget {
  const PerformanceSettingsPage({super.key});

  @override
  State<PerformanceSettingsPage> createState() => _PerformanceSettingsPageState();
}

class _PerformanceSettingsPageState extends State<PerformanceSettingsPage> {
  final SettingsService _settingsService = SettingsService();
  late PerformanceSettings _settings;
  bool _isLoading = true;
  String _cacheSize = '0 MB';

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _loadCacheSize();
  }

  Future<void> _loadSettings() async {
    await _settingsService.initialize();
    setState(() {
      _settings = _settingsService.currentSettings.performanceSettings;
      _isLoading = false;
    });
  }

  Future<void> _loadCacheSize() async {
    final size = await _settingsService.getCacheSize();
    setState(() {
      _cacheSize = size;
    });
  }

  Future<void> _updateSetting(String key, dynamic value) async {
    setState(() {
      switch (key) {
        case 'autoSync':
          _settings = _settings.copyWith(autoSync: value as bool);
          break;
        case 'offlineMode':
          _settings = _settings.copyWith(offlineMode: value as bool);
          break;
        case 'dataUsageMode':
          _settings = _settings.copyWith(dataUsageMode: value as String);
          break;
        case 'imageQuality':
          _settings = _settings.copyWith(imageQuality: value as String);
          break;
        case 'cacheEnabled':
          _settings = _settings.copyWith(cacheEnabled: value as bool);
          break;
        case 'analyticsEnabled':
          _settings = _settings.copyWith(analyticsEnabled: value as bool);
          break;
        case 'crashReporting':
          _settings = _settings.copyWith(crashReporting: value as bool);
          break;
        case 'performanceMode':
          _settings = _settings.copyWith(performanceMode: value as bool);
          break;
        case 'autoBackup':
          _settings = _settings.copyWith(autoBackup: value as bool);
          break;
      }
    });

    await _settingsService.updatePerformanceSettings(
      autoSync: _settings.autoSync,
      offlineMode: _settings.offlineMode,
      dataUsageMode: _settings.dataUsageMode,
      imageQuality: _settings.imageQuality,
      cacheEnabled: _settings.cacheEnabled,
      analyticsEnabled: _settings.analyticsEnabled,
      crashReporting: _settings.crashReporting,
      performanceMode: _settings.performanceMode,
      autoBackup: _settings.autoBackup,
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        title,
        style: CairoTextStyles.headlineSmall.copyWith(color: AppColors.primary),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required String key,
    required IconData icon,
    Color? iconColor,
  }) {
    return SwitchListTile(
      title: Text(title, style: CairoTextStyles.titleMedium),
      subtitle: Text(
        subtitle,
        style: CairoTextStyles.bodySmall.copyWith(color: Colors.grey.shade600),
      ),
      value: value,
      onChanged: (newValue) => _updateSetting(key, newValue),
      secondary: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: (iconColor ?? AppColors.primary).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: iconColor ?? AppColors.primary),
      ),
    );
  }

  Widget _buildListTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    Color? iconColor,
    Widget? trailing,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: (iconColor ?? AppColors.primary).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: iconColor ?? AppColors.primary),
      ),
      title: Text(title, style: CairoTextStyles.titleMedium),
      subtitle: Text(
        subtitle,
        style: CairoTextStyles.bodySmall.copyWith(color: Colors.grey.shade600),
      ),
      trailing: trailing ?? const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  void _showDataUsageModeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('وضع استخدام البيانات', style: CairoTextStyles.titleLarge),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('منخفض'),
              subtitle: const Text('توفير البيانات'),
              value: 'low',
              groupValue: _settings.dataUsageMode,
              onChanged: (value) {
                if (value != null) {
                  _updateSetting('dataUsageMode', value);
                  Navigator.pop(context);
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('متوسط'),
              subtitle: const Text('متوازن'),
              value: 'medium',
              groupValue: _settings.dataUsageMode,
              onChanged: (value) {
                if (value != null) {
                  _updateSetting('dataUsageMode', value);
                  Navigator.pop(context);
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('عالي'),
              subtitle: const Text('أفضل جودة'),
              value: 'high',
              groupValue: _settings.dataUsageMode,
              onChanged: (value) {
                if (value != null) {
                  _updateSetting('dataUsageMode', value);
                  Navigator.pop(context);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showImageQualityDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('جودة الصور', style: CairoTextStyles.titleLarge),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('منخفضة'),
              subtitle: const Text('تحميل أسرع'),
              value: 'low',
              groupValue: _settings.imageQuality,
              onChanged: (value) {
                if (value != null) {
                  _updateSetting('imageQuality', value);
                  Navigator.pop(context);
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('متوسطة'),
              subtitle: const Text('متوازنة'),
              value: 'medium',
              groupValue: _settings.imageQuality,
              onChanged: (value) {
                if (value != null) {
                  _updateSetting('imageQuality', value);
                  Navigator.pop(context);
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('عالية'),
              subtitle: const Text('أفضل جودة'),
              value: 'high',
              groupValue: _settings.imageQuality,
              onChanged: (value) {
                if (value != null) {
                  _updateSetting('imageQuality', value);
                  Navigator.pop(context);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _clearCache() async {
    // عرض تأكيد قبل المسح
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد المسح', style: CairoTextStyles.titleLarge),
        content: const Text('هل أنت متأكد من مسح التخزين المؤقت؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('مسح'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    if (!mounted) return;

    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      await _settingsService.clearCache();
      await _loadCacheSize();

      if (mounted) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم مسح التخزين المؤقت بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في مسح التخزين المؤقت: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('الأداء والتخزين', style: CairoTextStyles.appBarTitle),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              children: [
                _buildSectionTitle('المزامنة والاتصال'),
                _buildSwitchTile(
                  title: 'المزامنة التلقائية',
                  subtitle: 'مزامنة البيانات تلقائياً مع الخادم',
                  value: _settings.autoSync,
                  key: 'autoSync',
                  icon: Icons.sync,
                  iconColor: AppColors.info,
                ),
                _buildSwitchTile(
                  title: 'الوضع غير المتصل',
                  subtitle: 'استخدام التطبيق بدون اتصال بالإنترنت',
                  value: _settings.offlineMode,
                  key: 'offlineMode',
                  icon: Icons.cloud_off,
                  iconColor: AppColors.warning,
                ),

                const Divider(),

                _buildSectionTitle('استخدام البيانات'),
                _buildListTile(
                  title: 'وضع استخدام البيانات',
                  subtitle: _settings.dataUsageMode == 'low'
                      ? 'منخفض'
                      : _settings.dataUsageMode == 'medium'
                          ? 'متوسط'
                          : 'عالي',
                  icon: Icons.data_usage,
                  iconColor: AppColors.secondary,
                  onTap: _showDataUsageModeDialog,
                ),
                _buildListTile(
                  title: 'جودة الصور',
                  subtitle: _settings.imageQuality == 'low'
                      ? 'منخفضة'
                      : _settings.imageQuality == 'medium'
                          ? 'متوسطة'
                          : 'عالية',
                  icon: Icons.image,
                  iconColor: AppColors.success,
                  onTap: _showImageQualityDialog,
                ),

                const Divider(),

                _buildSectionTitle('التخزين المؤقت'),
                _buildSwitchTile(
                  title: 'تفعيل التخزين المؤقت',
                  subtitle: 'حفظ البيانات محلياً لتسريع التطبيق',
                  value: _settings.cacheEnabled,
                  key: 'cacheEnabled',
                  icon: Icons.storage,
                  iconColor: AppColors.primary,
                ),
                _buildListTile(
                  title: 'مسح التخزين المؤقت',
                  subtitle: 'الحجم الحالي: $_cacheSize',
                  icon: Icons.delete_sweep,
                  iconColor: AppColors.error,
                  onTap: _clearCache,
                ),

                const Divider(),

                _buildSectionTitle('الأداء والتحليلات'),
                _buildSwitchTile(
                  title: 'وضع الأداء العالي',
                  subtitle: 'تحسين الأداء مع استهلاك أكثر للبطارية',
                  value: _settings.performanceMode,
                  key: 'performanceMode',
                  icon: Icons.speed,
                  iconColor: AppColors.warning,
                ),
                _buildSwitchTile(
                  title: 'تفعيل التحليلات',
                  subtitle: 'مساعدة في تحسين التطبيق',
                  value: _settings.analyticsEnabled,
                  key: 'analyticsEnabled',
                  icon: Icons.analytics,
                  iconColor: AppColors.info,
                ),
                _buildSwitchTile(
                  title: 'تقارير الأخطاء',
                  subtitle: 'إرسال تقارير الأخطاء للمطورين',
                  value: _settings.crashReporting,
                  key: 'crashReporting',
                  icon: Icons.bug_report,
                  iconColor: AppColors.error,
                ),

                const Divider(),

                _buildSectionTitle('النسخ الاحتياطي'),
                _buildSwitchTile(
                  title: 'النسخ الاحتياطي التلقائي',
                  subtitle: 'نسخ احتياطي تلقائي للبيانات المهمة',
                  value: _settings.autoBackup,
                  key: 'autoBackup',
                  icon: Icons.backup,
                  iconColor: AppColors.success,
                ),

                const SizedBox(height: 20),
              ],
            ),
    );
  }
}
