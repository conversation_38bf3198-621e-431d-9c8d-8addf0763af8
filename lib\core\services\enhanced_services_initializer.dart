import 'package:flutter/foundation.dart';
import 'enhanced_cache_service.dart';
import 'enhanced_state_management_service.dart';
import 'enhanced_error_handler.dart';
import 'enhanced_database_service.dart';

/// خدمة تهيئة جميع الخدمات المحسنة
class EnhancedServicesInitializer {
  static final EnhancedServicesInitializer _instance = 
      EnhancedServicesInitializer._internal();
  
  factory EnhancedServicesInitializer() => _instance;
  
  EnhancedServicesInitializer._internal();

  bool _isInitialized = false;
  
  // الخدمات المحسنة
  late final EnhancedCacheService _cacheService;
  late final EnhancedStateManagementService _stateService;
  late final EnhancedErrorHandler _errorHandler;
  late final EnhancedDatabaseService _databaseService;

  /// تهيئة جميع الخدمات المحسنة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('🚀 بدء تهيئة الخدمات المحسنة...');
      }

      // تهيئة خدمة التخزين المؤقت
      _cacheService = EnhancedCacheService();
      await _cacheService.initialize();
      if (kDebugMode) {
        print('✅ تم تهيئة خدمة التخزين المؤقت');
      }

      // تهيئة خدمة إدارة الحالة
      _stateService = EnhancedStateManagementService();
      await _stateService.initialize();
      if (kDebugMode) {
        print('✅ تم تهيئة خدمة إدارة الحالة');
      }

      // تهيئة معالج الأخطاء
      _errorHandler = EnhancedErrorHandler();
      if (kDebugMode) {
        print('✅ تم تهيئة معالج الأخطاء');
      }

      // تهيئة خدمة قاعدة البيانات
      _databaseService = EnhancedDatabaseService();
      if (kDebugMode) {
        print('✅ تم تهيئة خدمة قاعدة البيانات');
      }

      // تشغيل مهام التنظيف الدورية
      _startPeriodicCleanup();

      _isInitialized = true;
      
      if (kDebugMode) {
        print('🎉 تم تهيئة جميع الخدمات المحسنة بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة الخدمات المحسنة: $e');
      }
      rethrow;
    }
  }

  /// بدء مهام التنظيف الدورية
  void _startPeriodicCleanup() {
    // تنظيف التخزين المؤقت كل 30 دقيقة
    Stream.periodic(const Duration(minutes: 30)).listen((_) {
      _performCleanup();
    });

    // تنظيف فوري بعد 5 دقائق من التهيئة
    Future.delayed(const Duration(minutes: 5), () {
      _performCleanup();
    });
  }

  /// تنفيذ عمليات التنظيف
  Future<void> _performCleanup() async {
    try {
      if (kDebugMode) {
        print('🧹 بدء عمليات التنظيف الدورية...');
      }

      // تنظيف التخزين المؤقت المنتهي الصلاحية
      await _cacheService.clearExpiredCache();
      
      // تنظيف استعلامات قاعدة البيانات المنتهية الصلاحية
      _databaseService.clearExpiredQueryCache();
      
      // مسح محاولات إعادة المحاولة القديمة
      _errorHandler.clearAllRetryAttempts();

      if (kDebugMode) {
        print('✅ تم إنجاز عمليات التنظيف بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ خطأ في عمليات التنظيف: $e');
      }
    }
  }

  /// الحصول على خدمة التخزين المؤقت
  EnhancedCacheService get cacheService {
    _ensureInitialized();
    return _cacheService;
  }

  /// الحصول على خدمة إدارة الحالة
  EnhancedStateManagementService get stateService {
    _ensureInitialized();
    return _stateService;
  }

  /// الحصول على معالج الأخطاء
  EnhancedErrorHandler get errorHandler {
    _ensureInitialized();
    return _errorHandler;
  }

  /// الحصول على خدمة قاعدة البيانات
  EnhancedDatabaseService get databaseService {
    _ensureInitialized();
    return _databaseService;
  }

  /// التأكد من تهيئة الخدمات
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('الخدمات المحسنة غير مهيئة. يرجى استدعاء initialize() أولاً.');
    }
  }

  /// الحصول على إحصائيات جميع الخدمات
  Future<Map<String, dynamic>> getServicesStatistics() async {
    _ensureInitialized();
    
    return {
      'cache': await _cacheService.getCacheStatistics(),
      'database': _databaseService.getQueryCacheStats(),
      'errors': _errorHandler.getErrorStats(),
      'state': {
        'isLoading': _stateService.isLoading(),
        'activeErrors': _stateService.getAllActiveErrors().length,
      },
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// إعادة تعيين جميع الخدمات
  Future<void> reset() async {
    try {
      if (kDebugMode) {
        print('🔄 إعادة تعيين جميع الخدمات...');
      }

      if (_isInitialized) {
        // مسح جميع البيانات المخزنة
        await _cacheService.clearAllCache();
        _databaseService.clearAllQueryCache();
        _errorHandler.clearAllRetryAttempts();
        _stateService.clearAllErrors();
      }

      _isInitialized = false;
      
      if (kDebugMode) {
        print('✅ تم إعادة تعيين جميع الخدمات');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إعادة تعيين الخدمات: $e');
      }
    }
  }

  /// تنظيف الموارد
  void dispose() {
    if (_isInitialized) {
      _stateService.dispose();
      _cacheService.dispose();
      _isInitialized = false;
    }
  }

  /// فحص صحة الخدمات
  Future<Map<String, bool>> healthCheck() async {
    final results = <String, bool>{};
    
    try {
      // فحص خدمة التخزين المؤقت
      results['cache'] = _isInitialized;
      
      // فحص خدمة إدارة الحالة
      results['state'] = _isInitialized;
      
      // فحص معالج الأخطاء
      results['errorHandler'] = _isInitialized;
      
      // فحص خدمة قاعدة البيانات
      results['database'] = _isInitialized;
      
      // فحص الاتصال بالإنترنت
      results['connectivity'] = await _stateService.isConnected;
      
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في فحص صحة الخدمات: $e');
      }
      results['error'] = false;
    }
    
    return results;
  }

  /// تشغيل وضع التطوير مع إعدادات خاصة
  void enableDevelopmentMode() {
    if (kDebugMode) {
      print('🔧 تفعيل وضع التطوير للخدمات المحسنة');
      
      // إعدادات خاصة بوضع التطوير
      // يمكن إضافة المزيد من الإعدادات هنا
    }
  }

  /// تشغيل وضع الإنتاج مع إعدادات محسنة
  void enableProductionMode() {
    if (kDebugMode) {
      print('🚀 تفعيل وضع الإنتاج للخدمات المحسنة');
      
      // إعدادات خاصة بوضع الإنتاج
      // تقليل مستوى التسجيل، تحسين الأداء، إلخ
    }
  }
}

/// امتدادات مساعدة للوصول السريع للخدمات
extension EnhancedServicesExtension on EnhancedServicesInitializer {
  /// الوصول السريع لخدمة التخزين المؤقت
  static EnhancedCacheService get cache => 
      EnhancedServicesInitializer().cacheService;
  
  /// الوصول السريع لخدمة إدارة الحالة
  static EnhancedStateManagementService get state => 
      EnhancedServicesInitializer().stateService;
  
  /// الوصول السريع لمعالج الأخطاء
  static EnhancedErrorHandler get errorHandler => 
      EnhancedServicesInitializer().errorHandler;
  
  /// الوصول السريع لخدمة قاعدة البيانات
  static EnhancedDatabaseService get database => 
      EnhancedServicesInitializer().databaseService;
}
