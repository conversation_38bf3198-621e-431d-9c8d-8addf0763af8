import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../core/config/app_mode_config.dart';
import '../../core/theme/app_colors.dart';
import '../../core/services/wamd_payment_service.dart';
import '../../core/services/notification_service.dart';
import '../../domain/entities/estate.dart';
import '../../domain/models/notification_model.dart' as domain;
import '../bloc/improved_ad_bloc.dart';
import 'improved_ad_creation_success_page.dart';

/// صفحة الدفع عبر خدمة ومض الكويتية
class WamdPaymentPage extends StatefulWidget {
  final Estate estate;
  final List<String> selectedFeatures;
  final double totalAmount;
  final bool isUpdate;

  const WamdPaymentPage({
    super.key,
    required this.estate,
    required this.selectedFeatures,
    required this.totalAmount,
    required this.isUpdate,
  });

  @override
  State<WamdPaymentPage> createState() => _WamdPaymentPageState();
}

class _WamdPaymentPageState extends State<WamdPaymentPage>
    with TickerProviderStateMixin {
  bool _isCompleted = false;
  bool _isProcessing = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // خدمة الدفع عبر ومض
  late WamdPaymentService _wamdService;
  String? _paymentId;
  Map<String, dynamic>? _paymentRequest;

  @override
  void initState() {
    super.initState();
    _wamdService = WamdPaymentService();
    _initializePayment();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  /// تهيئة طلب الدفع
  Future<void> _initializePayment() async {
    try {
      _paymentRequest = await _wamdService.createPaymentRequest(
        estateId: widget.estate.id,
        amount: widget.totalAmount,
        currency: 'KWD',
        description: 'دفع إعلان عقاري - ${widget.estate.title}',
        metadata: {
          'features': widget.selectedFeatures,
          'isUpdate': widget.isUpdate,
        },
      );
      _paymentId = _paymentRequest?['paymentId'];
      setState(() {});
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل في تهيئة الدفع: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// الحصول على رقم هاتف ومض
  String get _wamdPhoneNumber {
    return _paymentRequest?['wamdPhoneNumber'] ?? WamdPaymentService.wamdPhoneNumber;
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // في الوضع المعلوماتي، إخفاء الصفحة تماماً
    if (AppModeConfig.isInformationalOnly) {
      // العودة للصفحة السابقة فوراً
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.pop(context);
      });
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: _buildAppBar(),
      body: SafeArea(
        child: _isCompleted ? _buildSuccessView() : _buildPaymentView(),
      ),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        "الدفع عبر ومض",
        style: GoogleFonts.cairo(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      centerTitle: true,
      backgroundColor: AppColors.primary,
      elevation: 0,
      iconTheme: const IconThemeData(color: Colors.white),
    );
  }

  /// بناء واجهة الدفع
  Widget _buildPaymentView() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildPaymentSummaryCard(),
                  const SizedBox(height: 20),
                  _buildWamdInfoCard(),
                  const SizedBox(height: 20),
                  _buildPhoneNumberCard(),
                  const SizedBox(height: 20),
                  _buildInstructionsCard(),
                  const SizedBox(height: 20),
                  _buildActionButtons(),
                  const SizedBox(height: 32),
                  _buildConfirmationButton(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// بطاقة ملخص الدفع
  Widget _buildPaymentSummaryCard() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.receipt_long,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                "ملخص الدفع",
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "المبلغ الإجمالي:",
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
                Text(
                  "${widget.totalAmount.toStringAsFixed(3)} د.ك",
                  style: GoogleFonts.cairo(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بطاقة معلومات ومض
  Widget _buildWamdInfoCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.payment,
                  color: AppColors.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                "خدمة ومض للدفع الفوري",
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            "ومض هي خدمة الدفع الفوري المعتمدة من البنك المركزي الكويتي، تتيح لك إرسال الأموال باستخدام رقم الهاتف فقط.",
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.grey[600],
              height: 1.5,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.green.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.verified,
                  color: Colors.green[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    "خدمة آمنة ومعتمدة من البنك المركزي الكويتي",
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.green[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بطاقة رقم الهاتف
  Widget _buildPhoneNumberCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.phone,
                  color: AppColors.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                "رقم الهاتف للتحويل",
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.primary.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "رقم الهاتف:",
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _wamdPhoneNumber,
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: _copyPhoneNumber,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.copy,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Text(
            "انسخ رقم الهاتف واستخدمه في تطبيق ومض لإرسال المبلغ المطلوب",
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey[600],
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  /// بطاقة التعليمات
  Widget _buildInstructionsCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.info_outline,
                  color: Colors.orange,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                "خطوات الدفع",
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInstructionStep(
            "1",
            "افتح تطبيق ومض أو تطبيق البنك الخاص بك",
          ),
          _buildInstructionStep(
            "2",
            "اختر خدمة التحويل السريع (ومض)",
          ),
          _buildInstructionStep(
            "3",
            "أدخل رقم الهاتف: $_wamdPhoneNumber",
          ),
          _buildInstructionStep(
            "4",
            "أدخل المبلغ: ${widget.totalAmount.toStringAsFixed(3)} د.ك",
          ),
          _buildInstructionStep(
            "5",
            "أكمل عملية التحويل واضغط على 'تم الدفع' أدناه",
          ),
        ],
      ),
    );
  }

  /// خطوة في التعليمات
  Widget _buildInstructionStep(String number, String instruction) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                number,
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              instruction,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// أزرار الإجراءات
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _openWamdApp,
            icon: const Icon(Icons.open_in_new, size: 20),
            label: Text(
              "فتح تطبيق ومض",
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _copyPhoneNumber,
            icon: const Icon(Icons.copy, size: 20),
            label: Text(
              "نسخ الرقم",
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// زر تأكيد الدفع
  Widget _buildConfirmationButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isProcessing ? null : _confirmPayment,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: _isProcessing
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    "جاري المعالجة...",
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              )
            : Text(
                "تم الدفع - إنشاء الإعلان",
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  /// واجهة النجاح
  Widget _buildSuccessView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.circular(50),
              ),
              child: const Icon(
                Icons.check,
                color: Colors.white,
                size: 60,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              "تم إنشاء الإعلان بنجاح!",
              style: GoogleFonts.cairo(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              "سيتم عرض إعلانك بعد التحقق من الدفع",
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  "العودة للرئيسية",
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// نسخ رقم الهاتف
  void _copyPhoneNumber() {
    Clipboard.setData(ClipboardData(text: _wamdPhoneNumber));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          "تم نسخ رقم الهاتف: $_wamdPhoneNumber",
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// فتح تطبيق ومض
  Future<void> _openWamdApp() async {
    try {
      // محاولة فتح تطبيق ومض مباشرة
      const wamdAppUrl = 'wamd://';
      if (await canLaunchUrl(Uri.parse(wamdAppUrl))) {
        await launchUrl(Uri.parse(wamdAppUrl));
      } else {
        // إذا لم يكن التطبيق مثبتاً، توجيه لمتجر التطبيقات
        await _openAppStore();
      }
    } catch (e) {
      // في حالة الفشل، توجيه لمتجر التطبيقات
      await _openAppStore();
    }
  }

  /// فتح متجر التطبيقات لتحميل ومض
  Future<void> _openAppStore() async {
    try {
      // رابط تطبيق ومض في متجر التطبيقات (يجب تحديثه بالرابط الصحيح)
      const appStoreUrl = 'https://apps.apple.com/app/wamd';
      const playStoreUrl = 'https://play.google.com/store/apps/details?id=com.wamd';

      // تحديد النظام وفتح المتجر المناسب
      if (Theme.of(context).platform == TargetPlatform.iOS) {
        await launchUrl(Uri.parse(appStoreUrl));
      } else {
        await launchUrl(Uri.parse(playStoreUrl));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              "لا يمكن فتح متجر التطبيقات",
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// تأكيد الدفع
  Future<void> _confirmPayment() async {
    if (_paymentId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لم يتم إنشاء طلب الدفع بعد'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      // تأكيد الدفع عبر خدمة ومض
      final success = await _wamdService.confirmPayment(
        paymentId: _paymentId!,
        transactionReference: 'user_confirmed_${DateTime.now().millisecondsSinceEpoch}',
        additionalData: {
          'confirmationTime': DateTime.now().toIso8601String(),
          'userAgent': 'mobile_app',
        },
      );

      if (success) {
        if (!widget.isUpdate && mounted) {
          // إنشاء إعلان جديد
          context.read<ImprovedAdBloc>().add(SubmitAd());
        }

        // عرض إشعار فوري
        if (mounted) {
          NotificationService.showInAppNotification(
            context,
            title: 'تم تأكيد الدفع بنجاح!',
            body: 'سيتم مراجعة دفعتك خلال 24-48 ساعة',
            type: domain.NotificationType.system,
            duration: const Duration(seconds: 4),
          );
        }

        setState(() {
          _isCompleted = true;
          _isProcessing = false;
        });

        // الانتقال لصفحة النجاح بعد 3 ثوان
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => ImprovedAdCreationSuccessPage(
                  adId: widget.estate.id.isNotEmpty ? widget.estate.id : "wamd_${DateTime.now().millisecondsSinceEpoch}",
                  isNewAd: !widget.isUpdate,
                  isPaymentVerified: false, // يحتاج مراجعة يدوية
                ),
              ),
            );
          }
        });
      } else {
        throw Exception('فشل في تأكيد الدفع');
      }
    } catch (e) {
      setState(() {
        _isProcessing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              "حدث خطأ أثناء تأكيد الدفع: $e",
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
