// lib/presentation/pages/improved_media_upload_page.dart
import 'dart:io';

import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';


import '../../core/services/enhanced_ad_draft_service.dart';
import '../../core/services/image_processing_service.dart';
import '../../domain/entities/estate.dart';
import '../bloc/improved_ad_bloc.dart';

import '../widgets/improved_ad_creation_progress.dart';
import '../widgets/ad_creation_navigation_buttons.dart';

import 'improved_ad_details_page.dart';

/// صفحة رفع الصور والوسائط المحسنة
class ImprovedMediaUploadPage extends StatefulWidget {
  final Estate estate;
  final bool isEditing;

  const ImprovedMediaUploadPage(
      {super.key, required this.estate, this.isEditing = false});

  @override
  State<ImprovedMediaUploadPage> createState() =>
      _ImprovedMediaUploadPageState();
}

class _ImprovedMediaUploadPageState extends State<ImprovedMediaUploadPage> {
  // خدمات ومتغيرات
  final ImagePicker _picker = ImagePicker();
  final ImageProcessingService _imageService = ImageProcessingService();
  final EnhancedAdDraftService _draftService = EnhancedAdDraftService();

  // متغيرات الحالة
  List<File> _pickedFiles = [];
  bool _isProcessing = false;
  String? _errorMessage;
  bool _showTips = true;



  // ثوابت
  final int _maxImages = 10;
  final double _maxSizeMB = 5.0;

  @override
  void initState() {
    super.initState();
    _loadDraft();
  }



  /// تحميل المسودة
  Future<void> _loadDraft() async {
    final lastDraft = await _draftService.getLastDraft();
    if (lastDraft != null && mounted) {
      if (lastDraft.containsKey('imagePaths')) {
        final List<dynamic> paths = lastDraft['imagePaths'] as List<dynamic>;
        if (paths.isNotEmpty) {
          setState(() {
            _pickedFiles = paths.map((path) => File(path.toString())).toList();
          });
        }
      }
    }
  }

  /// اختيار صور من المعرض
  Future<void> _pickImages() async {
    try {
      setState(() {
        _isProcessing = true;
        _errorMessage = null;
      });

      final files = await _picker.pickMultiImage();
      if (files.isNotEmpty) {
        final availableSlots = _maxImages - _pickedFiles.length;
        if (availableSlots > 0) {
          // تحويل XFile إلى File
          final newFiles =
              files.take(availableSlots).map((f) => File(f.path)).toList();

          // معالجة الصور
          await _processImages(newFiles);
        } else {
          setState(() {
            _errorMessage =
                "لا يمكن إضافة المزيد من الصور. الحد الأقصى هو $_maxImages صور.";
          });
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = "حدث خطأ أثناء اختيار الصور. يرجى المحاولة مرة أخرى.";
      });
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _takePhoto() async {
    try {
      if (mounted) {
        setState(() {
          _isProcessing = true;
          _errorMessage = null;
        });
      }

      final file = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
        preferredCameraDevice: CameraDevice.rear,
      );

      if (file != null && mounted) {
        if (_pickedFiles.length < _maxImages) {
          // تحويل XFile إلى File
          final newFile = File(file.path);

          // التحقق من وجود الملف
          if (await newFile.exists()) {
            // معالجة الصورة
            await _processImages([newFile]);

            // إظهار رسالة نجاح
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'تم التقاط الصورة بنجاح',
                    style: GoogleFonts.cairo()),
                  backgroundColor: Colors.green,
                  duration: const Duration(seconds: 2),
                ));
            }
          } else {
            throw Exception('فشل في حفظ الصورة');
          }
        } else {
          setState(() {
            _errorMessage =
                "لا يمكن إضافة المزيد من الصور. الحد الأقصى هو $_maxImages صور.";
          });
        }
      }
    } catch (e) {
      debugPrint('Error taking photo: $e');
      if (mounted) {
        setState(() {
          _errorMessage = "حدث خطأ أثناء التقاط الصورة";
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء التقاط الصورة. تأكد من إعطاء الأذونات المطلوبة.',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// معالجة الصور (التحقق من الصحة والضغط)
  Future<void> _processImages(List<File> files) async {
    for (final file in files) {
      // التحقق من حجم الصورة
      final isValidSize =
          await _imageService.isImageSizeValid(file, _maxSizeMB);
      if (!isValidSize) {
        setState(() {
          _isProcessing = false;
          _errorMessage =
              "حجم الصورة كبير جدًا. يجب أن لا يتجاوز $_maxSizeMB ميجابايت.";
        });
        return;
      }

      // التحقق من أبعاد الصورة
      final isValidDimensions =
          await _imageService.isImageDimensionsValid(file);
      if (!isValidDimensions) {
        setState(() {
          _isProcessing = false;
          _errorMessage =
              "أبعاد الصورة صغيرة جدًا. يجب أن لا تقل عن 300×300 بكسل.";
        });
        return;
      }

      // التحقق من نوع الصورة
      final isValidType = _imageService.isImageTypeValid(file);
      if (!isValidType) {
        setState(() {
          _isProcessing = false;
          _errorMessage =
              "نوع الصورة غير مدعوم. الأنواع المدعومة: JPG, JPEG, PNG.";
        });
        return;
      }
    }

    // ضغط الصور
    final compressedFiles =
        await _imageService.compressImages(imageFiles: files);

    setState(() {
      _pickedFiles.addAll(compressedFiles);
    });

    // حفظ المسودة
    _saveDraft();
  }

  /// تحرير صورة
  Future<void> _editImage(int index) async {
    try {
      // التحقق من صحة الفهرس
      if (index < 0 || index >= _pickedFiles.length) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فهرس الصورة غير صحيح', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ));
        }
        return;
      }

      // إظهار مؤشر التحميل
      if (mounted) {
        setState(() {
          _isProcessing = true;
          _errorMessage = null;
        });
      }

      final file = _pickedFiles[index];

      // حفظ الألوان قبل العملية غير المتزامنة
      final primaryColor = Theme.of(context).primaryColor;

      // التحقق من وجود الملف
      if (!await file.exists()) {
        if (mounted) {
          setState(() {
            _isProcessing = false;
            _errorMessage = "الملف غير موجود";
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('الملف غير موجود', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ));
        }
        return;
      }

      // استخدام ImageCropper مع معالجة أفضل للأخطاء
      CroppedFile? croppedFile;

      try {
        croppedFile = await ImageCropper().cropImage(
          sourcePath: file.path,
          compressFormat: ImageCompressFormat.jpg,
          compressQuality: 85,
          maxWidth: 1920,
          maxHeight: 1920,
          uiSettings: [
            AndroidUiSettings(
              toolbarTitle: 'تحرير الصورة',
              toolbarColor: primaryColor,
              toolbarWidgetColor: Colors.white,
              statusBarColor: primaryColor,
              backgroundColor: Colors.white,
              activeControlsWidgetColor: primaryColor,
              lockAspectRatio: false,
              hideBottomControls: false,
              initAspectRatio: CropAspectRatioPreset.original,
              showCropGrid: true,
              cropFrameStrokeWidth: 2,
              cropGridStrokeWidth: 1,
              aspectRatioPresets: [
                CropAspectRatioPreset.original,
                CropAspectRatioPreset.square,
                CropAspectRatioPreset.ratio3x2,
                CropAspectRatioPreset.ratio4x3,
                CropAspectRatioPreset.ratio16x9
              ],
            ),
            IOSUiSettings(
              title: 'تحرير الصورة',
              doneButtonTitle: 'تم',
              cancelButtonTitle: 'إلغاء',
            ),
          ]);
      } catch (cropError) {
        debugPrint('Error in ImageCropper: $cropError');
        if (mounted) {
          setState(() {
            _isProcessing = false;
            _errorMessage = "فشل في فتح محرر الصور";
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في فتح محرر الصور', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ));
        }
        return;
      }

      if (croppedFile != null && mounted) {
        setState(() {
          _pickedFiles[index] = File(croppedFile!.path);
          _isProcessing = false;
        });

        // حفظ المسودة
        await _saveDraft();

        // إظهار رسالة نجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم تحرير الصورة بنجاح',
                style: GoogleFonts.cairo()),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8)),
            ));
        }
      } else if (mounted) {
        // المستخدم ألغى العملية
        setState(() {
          _isProcessing = false;
        });
      }
    } catch (e) {
      debugPrint('Error editing image: $e');
      if (mounted) {
        setState(() {
          _isProcessing = false;
          _errorMessage = "حدث خطأ أثناء تحرير الصورة";
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء تحرير الصورة',
              style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8)),
          ));
      }
    }
  }

  /// حذف صورة
  void _deleteImage(int index) {
    setState(() {
      _pickedFiles.removeAt(index);
    });

    // حفظ المسودة
    _saveDraft();
  }

  // تم إزالة دالة _onReorder لأننا لا نستخدم ReorderableGridView

  /// حفظ المسودة
  Future<void> _saveDraft() async {
    final paths = _pickedFiles.map((f) => f.path).toList();
    await _draftService.saveDraft({
      'imagePaths': paths,
      'step': 2,
    });
  }





  /// الانتقال إلى الخطوة التالية
  void _goToNextStep() {
    // حفظ الصور في BLoC
    final paths = _pickedFiles.map((f) => f.path).toList();
    context.read<ImprovedAdBloc>().add(AddImages(paths));

    // حفظ المسودة مع الصور
    _saveDraftWithImages(paths);

    // الانتقال إلى صفحة التفاصيل
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => ImprovedAdDetailsPage(
          estate: widget.estate,
          isEditing: widget.isEditing)));
  }

  /// حفظ المسودة مع الصور
  void _saveDraftWithImages(List<String> imagePaths) {
    final adState = context.read<ImprovedAdBloc>().state;

    final mainCategory = adState.mainCategory;
    final subCategory = adState.subCategory;

    _draftService.autoSaveDraft({
      'mainCategory': mainCategory,
      'subCategory': subCategory,
      'imagePaths': imagePaths,
      'step': 2,
      'title': 'مسودة $mainCategory - $subCategory',
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  @override
  Widget build(BuildContext context) {
    final canProceed = _pickedFiles.isNotEmpty;
    final primaryColor = Theme.of(context).primaryColor;

    return Scaffold(
      backgroundColor: Colors.white,
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أزرار التنقل
          AdCreationNavigationButtons(
            onNext: canProceed ? () => _goToNextStep() : () {},
            onBack: () => Navigator.pop(context),
            nextText: "متابعة",
            backText: "العودة",
            isNextDisabled: !canProceed,
          ),

          // مؤشر التقدم
          ImprovedAdCreationProgress(
            currentStep: 2,
            allowNavigation: false,
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // شريط العنوان
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.black87),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    "إضافة صور العقار",
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),

            // المحتوى القابل للتمرير
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عنوان القسم
                    Text(
                      "صور العقار",
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: primaryColor,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // نصائح لتحسين جودة الصور
                    if (_showTips)
                      Card(
                        color: Colors.blue.shade50,
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.lightbulb, color: Colors.amber.shade700),
                                  const SizedBox(width: 8),
                                  Text(
                                    "نصائح لصور أفضل",
                                    style: GoogleFonts.cairo(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.blue.shade700,
                                    ),
                                  ),
                                  const Spacer(),
                                  IconButton(
                                    onPressed: () {
                                      setState(() {
                                        _showTips = false;
                                      });
                                    },
                                    icon: Icon(Icons.close, color: Colors.blue.shade700),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                "• التقط صوراً بإضاءة جيدة وواضحة\n"
                                "• أضف صوراً لجميع غرف العقار\n"
                                "• تأكد من نظافة وترتيب المكان قبل التصوير\n"
                                "• استخدم الوضع الأفقي للحصول على صور أوسع",
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: Colors.blue.shade900,
                                  height: 1.3,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                    const SizedBox(height: 16),

                    // أزرار إضافة الصور
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isProcessing ? null : _pickImages,
                            icon: const Icon(Icons.photo_library),
                            label: Text(
                              "اختيار من المعرض",
                              style: GoogleFonts.cairo(fontSize: 14),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: primaryColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isProcessing ? null : _takePhoto,
                            icon: const Icon(Icons.camera_alt),
                            label: Text(
                              "التقاط صورة",
                              style: GoogleFonts.cairo(fontSize: 14),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // زر النصائح
                    if (!_showTips)
                      TextButton.icon(
                        onPressed: () {
                          setState(() {
                            _showTips = true;
                          });
                        },
                        icon: Icon(Icons.tips_and_updates, color: Colors.blue.shade600),
                        label: Text(
                          "عرض نصائح التصوير",
                          style: GoogleFonts.cairo(color: Colors.blue.shade600),
                        ),
                      ),

                    const SizedBox(height: 16),

                    // عرض رسالة الخطأ إذا وجدت
                    if (_errorMessage != null)
                      Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.error_outline, color: Colors.red.shade600),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  color: Colors.red.shade800,
                                ),
                              ),
                            ),
                            TextButton(
                              onPressed: () {
                                setState(() {
                                  _errorMessage = null;
                                });
                              },
                              child: Text(
                                "إغلاق",
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: Colors.red.shade600,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                    // عرض مؤشر التحميل أثناء معالجة الصور
                    if (_isProcessing)
                      Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 10,
                              spreadRadius: 1,
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              "جاري معالجة الصور...",
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey.shade800,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              "يتم التحقق من الحجم والأبعاد وضغط الصور",
                              style: GoogleFonts.cairo(
                                fontSize: 14,
                                color: Colors.grey.shade600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),

                    // عنوان قسم الصور
                    Row(
                      children: [
                        Text(
                          "الصور المختارة",
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          "(${_pickedFiles.length}/$_maxImages)",
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const Spacer(),
                        if (_pickedFiles.isNotEmpty)
                          TextButton.icon(
                            onPressed: () {
                              setState(() {
                                _pickedFiles.clear();
                              });
                              _saveDraft();
                            },
                            icon: const Icon(Icons.delete_sweep, size: 16),
                            label: Text(
                              "حذف الكل",
                              style: GoogleFonts.cairo(fontSize: 12),
                            ),
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.red,
                            ),
                          ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // عرض الصور المختارة
                    _pickedFiles.isEmpty
                        ? _buildEmptyState()
                        : _buildImageGrid(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة فارغة عندما لا توجد صور
  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 20),
      child: Card(
        margin: const EdgeInsets.all(0),
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.add_photo_alternate,
                  size: 48,
                  color: Colors.grey.shade400,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                "لم يتم اختيار أي صورة بعد",
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade700,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                "يجب إضافة صورة واحدة على الأقل لإكمال إعلانك",
                textAlign: TextAlign.center,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                "انقر على الأزرار أعلاه لإضافة الصور",
                textAlign: TextAlign.center,
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء شبكة الصور
  Widget _buildImageGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: _pickedFiles.length,
      itemBuilder: (context, index) {
        return _buildImageCard(index);
      },
    );
  }

  /// بناء بطاقة الصورة
  Widget _buildImageCard(int index) {
    final isMainImage = index == 0;

    return Card(
      margin: const EdgeInsets.all(0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Stack(
          fit: StackFit.expand,
          children: [
            // الصورة
            Image.file(
              _pickedFiles[index],
              fit: BoxFit.cover,
            ),

            // تدرج للأيقونات
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.5),
                  ],
                  stops: const [0.6, 1.0],
                ),
              ),
            ),

            // شارة الصورة الرئيسية
            if (isMainImage)
              Positioned(
                top: 4,
                right: 4,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    "رئيسية",
                    style: GoogleFonts.cairo(
                      fontSize: 8,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),

            // أزرار التحرير والحذف
            Positioned(
              bottom: 4,
              left: 4,
              right: 4,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.8),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      onPressed: () => _editImage(index),
                      icon: const Icon(Icons.edit, color: Colors.white, size: 14),
                      padding: const EdgeInsets.all(2),
                      constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.8),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      onPressed: () => _deleteImage(index),
                      icon: const Icon(Icons.delete, color: Colors.white, size: 14),
                      padding: const EdgeInsets.all(2),
                      constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

}
