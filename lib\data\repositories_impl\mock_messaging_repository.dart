import '../../domain/entities/conversation.dart';
import '../../domain/entities/message.dart';
import '../../domain/repositories/messaging_repository.dart';

/// Implementación simulada del repositorio de mensajería
class MockMessagingRepository implements MessagingRepository {
  @override
  Future<String> createConversation(Conversation conversation) async {
    // Simulación de creación de conversación
    return 'mock-conversation-id-${DateTime.now().millisecondsSinceEpoch}';
  }
  
  @override
  Future<Conversation?> getConversationById(String conversationId) async {
    // Simulación de obtención de conversación
    return null;
  }
  
  @override
  Future<Conversation?> getConversationBetweenUsers(String userId1, String userId2) async {
    // Simulación de obtención de conversación entre usuarios
    return null;
  }
  
  @override
  Future<Conversation?> getConversationForEstate(String estateId, String userId) async {
    // Simulación de obtención de conversación para propiedad
    return null;
  }
  
  @override
  Future<Conversation?> getConversationForOffer(String offerId) async {
    // Simulación de obtención de conversación para oferta
    return null;
  }
  
  @override
  Future<List<Conversation>> getUserConversations(String userId) async {
    // Simulación de obtención de conversaciones del usuario
    return [];
  }
  
  @override
  Future<Map<String, dynamic>> getUserConversationsPaginated({
    required String userId,
    int limit = 20,
    String? lastConversationId,
    ConversationStatus? status,
  }) async {
    // Simulación de obtención paginada de conversaciones
    return {
      'conversations': <Conversation>[],
      'lastConversationId': null,
      'hasMore': false,
    };
  }
  
  @override
  Future<void> updateConversation(Conversation conversation) async {
    // Simulación de actualización de conversación
  }
  
  @override
  Future<void> updateConversationStatus(String conversationId, ConversationStatus status) async {
    // Simulación de actualización de estado de conversación
  }
  
  @override
  Future<void> togglePinConversation(String conversationId, String userId, bool isPinned) async {
    // Simulación de fijar/desfijar conversación
  }
  
  @override
  Future<void> toggleMuteConversation(String conversationId, String userId, bool isMuted) async {
    // Simulación de silenciar/desilenciar conversación
  }
  
  @override
  Future<void> setConversationNotificationSettings(String conversationId, String userId, Map<String, bool> settings) async {
    // Simulación de configuración de notificaciones
  }
  
  @override
  Future<void> deleteConversation(String conversationId) async {
    // Simulación de eliminación de conversación
  }
  
  @override
  Future<void> archiveConversation(String conversationId) async {
    // Simulación de archivo de conversación
  }
  
  @override
  Future<void> addParticipantToConversation(String conversationId, String userId, String userName, String? userImage) async {
    // Simulación de adición de participante
  }
  
  @override
  Future<void> removeParticipantFromConversation(String conversationId, String userId) async {
    // Simulación de eliminación de participante
  }
  
  @override
  Future<void> updateParticipantInConversation(String conversationId, String userId, String? userName, String? userImage) async {
    // Simulación de actualización de participante
  }
  
  @override
  Future<void> setParticipantTyping(String conversationId, String userId, bool isTyping) async {
    // Simulación de estado de escritura
  }
  
  @override
  Future<String> sendMessage(Message message) async {
    // Simulación de envío de mensaje
    return 'mock-message-id-${DateTime.now().millisecondsSinceEpoch}';
  }
  
  @override
  Future<Message?> getMessageById(String messageId) async {
    // Simulación de obtención de mensaje
    return null;
  }
  
  @override
  Future<List<Message>> getConversationMessages(String conversationId) async {
    // Simulación de obtención de mensajes
    return [];
  }
  
  @override
  Future<Map<String, dynamic>> getConversationMessagesPaginated({
    required String conversationId,
    int limit = 20,
    String? lastMessageId,
  }) async {
    // Simulación de obtención paginada de mensajes
    return {
      'messages': <Message>[],
      'lastMessageId': null,
      'hasMore': false,
    };
  }
  
  @override
  Future<void> markMessagesAsRead(String conversationId, String userId) async {
    // Simulación de marcar mensajes como leídos
  }
  
  @override
  Future<void> markMessageAsRead(String messageId, String userId) async {
    // Simulación de marcar mensaje como leído
  }
  
  @override
  Future<void> updateMessageStatus(String messageId, MessageStatus status) async {
    // Simulación de actualización de estado de mensaje
  }
  
  @override
  Future<void> editMessage(String messageId, String newContent) async {
    // Simulación de edición de mensaje
  }
  
  @override
  Future<void> deleteMessage(String messageId) async {
    // Simulación de eliminación de mensaje
  }
  
  @override
  Future<void> togglePinMessage(String messageId, bool isPinned) async {
    // Simulación de fijar/desfijar mensaje
  }
  
  @override
  Future<void> toggleStarMessage(String messageId, String userId, bool isStarred) async {
    // Simulación de marcar/desmarcar mensaje con estrella
  }
  
  @override
  Future<void> archiveMessage(String messageId) async {
    // Simulación de archivo de mensaje
  }
  
  @override
  Future<void> reportMessage(String messageId, String userId, String reason) async {
    // Simulación de reporte de mensaje
  }
  
  @override
  Future<void> addReactionToMessage(String messageId, String userId, String reaction) async {
    // Simulación de adición de reacción
  }
  
  @override
  Future<void> removeReactionFromMessage(String messageId, String userId, String reaction) async {
    // Simulación de eliminación de reacción
  }
  
  @override
  Future<List<Message>> getUserStarredMessages(String userId) async {
    // Simulación de obtención de mensajes destacados
    return [];
  }
  
  @override
  Future<Map<String, dynamic>> getUserStarredMessagesPaginated({
    required String userId,
    int limit = 20,
    String? lastMessageId,
  }) async {
    // Simulación de obtención paginada de mensajes destacados
    return {
      'messages': <Message>[],
      'lastMessageId': null,
      'hasMore': false,
    };
  }
  
  @override
  Future<List<Message>> searchUserMessages(String userId, String query) async {
    // Simulación de búsqueda de mensajes
    return [];
  }
  
  @override
  Future<List<Conversation>> searchUserConversations(String userId, String query) async {
    // Simulación de búsqueda de conversaciones
    return [];
  }
  
  @override
  Stream<List<Conversation>> listenToUserConversations(String userId) {
    // Simulación de escucha de conversaciones
    return Stream.value([]);
  }
  
  @override
  Stream<List<Message>> listenToConversationMessages(String conversationId) {
    // Simulación de escucha de mensajes
    return Stream.value([]);
  }
  
  @override
  Stream<List<String>> listenToTypingParticipants(String conversationId) {
    // Simulación de escucha de participantes escribiendo
    return Stream.value([]);
  }
  
  @override
  Stream<Message?> listenToMessage(String messageId) {
    // Simulación de escucha de mensaje
    return Stream.value(null);
  }
  
  @override
  Stream<Conversation?> listenToConversation(String conversationId) {
    // Simulación de escucha de conversación
    return Stream.value(null);
  }
  
  @override
  Future<int> getUnreadConversationsCount(String userId) async {
    // Simulación de conteo de conversaciones no leídas
    return 0;
  }
  
  @override
  Stream<int> listenToUnreadConversationsCount(String userId) {
    // Simulación de escucha de conteo de conversaciones no leídas
    return Stream.value(0);
  }
  
  @override
  Future<Map<String, dynamic>> getUserMessagingStatistics(String userId) async {
    // Simulación de estadísticas de mensajería
    return {
      'totalConversations': 0,
      'totalMessages': 0,
      'unreadMessages': 0,
    };
  }
}
