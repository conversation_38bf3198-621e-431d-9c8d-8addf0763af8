/// بيانات المحافظات والمناطق والأحياء في دولة الكويت
/// مصدر البيانات: الموقع الرسمي لوزارة الاتصالات الكويتية (moc.gov.kw) وويكيبيديا
/// تاريخ التحديث: يناير 2025
library;

class KuwaitLocations {
  /// قائمة المحافظات الست في دولة الكويت (بالترتيب الرسمي)
  static const List<String> governorates = [
    'محافظة العاصمة',
    'محافظة حولي',
    'محافظة مبارك الكبير',
    'محافظة الأحمدي',
    'محافظة الفروانية',
    'محافظة الجهراء',
  ];

  /// خريطة شاملة للمحافظات ومناطقها (مرتبة حسب البيانات الرسمية)
  static const Map<String, List<String>> governorateAreas = {
    'محافظة العاصمة': [
      // المناطق الرئيسية
      'مدينة الكويت',
      'دسمان',
      'الشرق',
      'المرقاب',
      'الصوابر',
      'القبلة',
      'الصالحية',
      'بنيد القار',
      'الدوحة',
      'ميناء الدوحة',
      'الدسمة',
      'الدعية',
      'الفيحاء',
      'جبلة',
      'كيفان',
      'الخالدية',
      'المنصورية',
      'النزهة',
      'القادسية',
      'قرطبة',
      'الروضة',
      'الشامية',
      'الشويخ',
      'الشويخ الصناعية',
      'ميناء الشويخ',
      'الصليبيخات',
      'السرة',
      'العديلية',
      'غرناطة',
      'النهضة',
      'شمال غرب الصليبيخات',
      'اليرموك',
      'القيروان',
      'مدينة جابر الأحمد',
      'الري',
      // الجزر
      'جزيرة فيلكا',
      'جزيرة أوها',
      'جزيرة ميشان',
      'جزيرة أم النمل',
      'حدائق السور',
    ],

    'محافظة الأحمدي': [
      // المناطق الرئيسية (حسب البيانات الرسمية)
      'الأحمدي',
      'أبو حليفة',
      'ميناء عبد الله',
      'علي صباح السالم',
      'العقيلة',
      'بر الأحمدي',
      'بنيدر',
      'الظهر',
      'الفحيحيل',
      'فهد الأحمد',
      'هدية',
      'جابر العلي',
      'الجليعة',
      'الخيران',
      'المهبولة',
      'المنقف',
      'المقوع',
      'الوفرة السكنية',
      'النويصيب',
      'الرقة',
      'صباح الأحمد',
      'مدينة صباح الأحمد البحرية',
      'الصباحية',
      'جنوب الصباحية',
      'الشعيبة الصناعية',
      'الوفرة',
      'الزور',
      'الفنطاس',
      'الشدادية الصناعية',
    ],

    'محافظة الفروانية': [
      // المناطق الرئيسية (حسب البيانات الرسمية)
      'الفروانية',
      'عبد الله المبارك',
      'منطقة المطار',
      'الأندلس',
      'العارضية',
      'العارضية الحرفية',
      'إشبيلية',
      'الضجيج',
      'الفردوس',
      'جليب الشيوخ',
      'خيطان',
      'العمرية',
      'الرابية',
      'الري',
      'الرقعي',
      'الرحاب',
      'صباح الناصر',
      'جامعة صباح السالم',
      'غرب عبد الله المبارك',
      'جنوب عبد الله المبارك',
      'الصليبية الصناعية',
    ],

    'محافظة الجهراء': [
      // المناطق الرئيسية (حسب البيانات الرسمية)
      'الجهراء',
      'العبدلي',
      'المطلاع',
      'كاظمة',
      'بحرة',
      'كبد',
      'الشقايا',
      'النهضة',
      'أمغرة الصناعية',
      'بر الجهراء',
      'الجهراء الصناعية الحرفية',
      'النعيم',
      'النسيم',
      'العيون',
      'القصر',
      'جابر الأحمد',
      'سعد العبد الله',
      'السالمي',
      'الصبية',
      'الصليبية',
      'المنطقة الزراعية الصليبية',
      'الصليبية السكنية',
      'تيماء',
      'الواحة',
      'جزيرة بوبيان',
      'جزيرة وربة',
    ],

    'محافظة حولي': [
      // المناطق الرئيسية (حسب البيانات الرسمية)
      'حولي',
      'السالمية',
      'الجابرية',
      'الرميثية',
      'سلوى',
      'بيان',
      'مشرف',
      'الشعب',
      'الشهداء',
      'حطين',
      'سلام',
      'الزهراء',
      'مبارك العبدالله',
      'الصديق',
      'البدع',
      'أنجفة',
      'منطقة الوزارات',
    ],

    'محافظة مبارك الكبير': [
      // المناطق الرئيسية (حسب البيانات الرسمية)
      'مبارك الكبير',
      'صباح السالم',
      'العدان',
      'القرين',
      'القصور',
      'الفنيطيس',
      'المسيلة',
      'أبو الحصانية',
      'أبو فطيرة',
      'المسايل',
      'وسطي',
      'صبحان الصناعية',
      'غرب أبو فطيرة الحرفية',
    ],
  };

  /// الجزر التابعة لمحافظة العاصمة
  static const List<String> capitalIslands = [
    'جزيرة فيلكا',
    'جزيرة كبر',
    'جزيرة عوهة',
    'جزيرة أم المرادم',
    'جزيرة مسكان',
    'جزيرة قاروه',
    'جزيرة أم النمل',
    'جزيرة الشويخ',
  ];

  /// الجزر التابعة لمحافظة الجهراء
  static const List<String> jahraIslands = [
    'جزيرة بوبيان',
    'جزيرة وربة',
  ];

  /// جميع الجزر في الكويت
  static const List<String> allIslands = [
    ...capitalIslands,
    ...jahraIslands,
  ];

  /// الحصول على جميع المناطق في الكويت
  static List<String> getAllAreas() {
    List<String> allAreas = [];
    governorateAreas.forEach((governorate, areas) {
      allAreas.addAll(areas);
    });
    return allAreas;
  }

  /// الحصول على المناطق حسب المحافظة
  static List<String> getAreasByGovernorate(String governorate) {
    return governorateAreas[governorate] ?? [];
  }

  /// البحث عن منطقة في المحافظات
  static String? findGovernorateByArea(String area) {
    for (String governorate in governorateAreas.keys) {
      if (governorateAreas[governorate]!.contains(area)) {
        return governorate;
      }
    }
    return null;
  }

  /// فلترة المناطق حسب النص المدخل
  static List<String> searchAreas(String query) {
    if (query.isEmpty) return getAllAreas();
    
    return getAllAreas()
        .where((area) => area.contains(query))
        .toList();
  }

  /// فلترة المحافظات حسب النص المدخل
  static List<String> searchGovernorates(String query) {
    if (query.isEmpty) return governorates;
    
    return governorates
        .where((governorate) => governorate.contains(query))
        .toList();
  }

  /// التحقق من صحة المنطقة
  static bool isValidArea(String area) {
    return getAllAreas().contains(area);
  }

  /// التحقق من صحة المحافظة
  static bool isValidGovernorate(String governorate) {
    return governorates.contains(governorate);
  }

  /// الحصول على عدد المناطق في كل محافظة
  static Map<String, int> getAreasCountByGovernorate() {
    Map<String, int> counts = {};
    governorateAreas.forEach((governorate, areas) {
      counts[governorate] = areas.length;
    });
    return counts;
  }

  /// معلومات إضافية عن المحافظات
  static const Map<String, Map<String, dynamic>> governorateInfo = {
    'محافظة العاصمة': {
      'تاريخ التأسيس': 1962,
      'الوصف': 'مقر الحكم والحكومة ومجلس الأمة',
      'المميزات': 'المركز السياسي والإداري',
    },
    'محافظة الأحمدي': {
      'تاريخ التأسيس': 1962,
      'الوصف': 'أكثر محافظة ذو كثافة سكانية للكويتيين',
      'المميزات': 'المركز النفطي والصناعي',
    },
    'محافظة الفروانية': {
      'تاريخ التأسيس': 1988,
      'الوصف': 'أكبر محافظة من حيث عدد السكان بشكل عام',
      'المميزات': 'الأكثر كثافة سكانية',
    },
    'محافظة الجهراء': {
      'تاريخ التأسيس': 1979,
      'الوصف': 'أكبر المحافظات مساحة',
      'المميزات': 'تضم أكبر المشاريع التنموية الجديدة',
    },
    'محافظة حولي': {
      'تاريخ التأسيس': 1962,
      'الوصف': 'أصغر المحافظات مساحة وأكثرها كثافة سكانية',
      'المميزات': 'المركز التجاري والسكني',
    },
    'محافظة مبارك الكبير': {
      'تاريخ التأسيس': 1999,
      'الوصف': 'أحدث محافظات الكويت تأسيساً',
      'المميزات': 'المناطق السكنية الحديثة',
    },
  };

  /// الحصول على معلومات المحافظة
  static Map<String, dynamic>? getGovernorateInfo(String governorate) {
    return governorateInfo[governorate];
  }

  /// الأحياء والقطع السكنية التفصيلية لبعض المناطق الرئيسية
  static const Map<String, List<String>> areaDistricts = {
    'السالمية': [
      'السالمية البحرية',
      'السالمية الجديدة',
      'السالمية القديمة',
      'مجمع السالمية',
      'شارع سالم المبارك',
      'شارع الخليج العربي',
    ],
    'حولي': [
      'حولي القديمة',
      'حولي الجديدة',
      'شارع تونس',
      'شارع بيروت',
      'منطقة الأسواق',
    ],
    'الجابرية': [
      'الجابرية قطعة 1',
      'الجابرية قطعة 2',
      'الجابرية قطعة 3',
      'الجابرية قطعة 4',
      'الجابرية قطعة 5',
      'الجابرية قطعة 6',
      'الجابرية قطعة 7',
      'الجابرية قطعة 8',
      'الجابرية قطعة 9',
      'الجابرية قطعة 10',
      'الجابرية قطعة 11',
      'الجابرية قطعة 12',
    ],
    'الفروانية': [
      'الفروانية قطعة 1',
      'الفروانية قطعة 2',
      'الفروانية قطعة 3',
      'الفروانية قطعة 4',
      'الفروانية الصناعية',
    ],
    'الأحمدي': [
      'الأحمدي السكنية',
      'الأحمدي الصناعية',
      'الأحمدي النفطية',
      'مجمع الأحمدي',
    ],
    'الجهراء': [
      'الجهراء القديمة',
      'الجهراء الجديدة',
      'الجهراء الصناعية',
      'مدينة الجهراء',
    ],
    'الفحيحيل': [
      'الفحيحيل قطعة 1',
      'الفحيحيل قطعة 2',
      'الفحيحيل قطعة 3',
      'الفحيحيل قطعة 4',
      'الفحيحيل قطعة 5',
      'الفحيحيل قطعة 6',
    ],
    'خيطان': [
      'خيطان الجنوبية',
      'خيطان الشمالية',
      'خيطان الصناعية',
    ],
    'الفردوس': [
      'الفردوس قطعة 1',
      'الفردوس قطعة 2',
      'الفردوس قطعة 3',
      'الفردوس قطعة 4',
    ],
    'الرقة': [
      'الرقة السكنية',
      'الرقة الصناعية',
      'الرقة التجارية',
    ],
  };

  /// الحصول على الأحياء الفرعية للمنطقة
  static List<String> getDistrictsByArea(String area) {
    return areaDistricts[area] ?? [];
  }

  /// البحث في الأحياء الفرعية
  static List<String> searchDistricts(String query) {
    List<String> allDistricts = [];
    areaDistricts.forEach((area, districts) {
      allDistricts.addAll(districts);
    });

    if (query.isEmpty) return allDistricts;

    return allDistricts
        .where((district) => district.contains(query))
        .toList();
  }

  /// المناطق التجارية والصناعية المهمة
  static const Map<String, List<String>> specialAreas = {
    'المناطق التجارية': [
      'الشرق (سوق الذهب)',
      'المباركية',
      'سوق الحريم',
      'الأفنيوز',
      'مارينا مول',
      'الفحيحيل (سوق السمك)',
      'سوق الجمعة',
    ],
    'المناطق الصناعية': [
      'الشويخ الصناعية',
      'صبحان الصناعية',
      'الأحمدي الصناعية',
      'الفروانية الصناعية',
      'أمغرة الصناعية',
    ],
    'المناطق النفطية': [
      'الأحمدي النفطية',
      'الوفرة النفطية',
      'المقوع النفطية',
      'الشعيبة النفطية',
    ],
    'المناطق السياحية': [
      'الكويت التاريخية',
      'جزيرة فيلكا',
      'الخيران السياحية',
      'مدينة صباح الأحمد البحرية',
      'الفنطاس البحرية',
    ],
  };

  /// الحصول على المناطق الخاصة حسب النوع
  static List<String> getSpecialAreas(String type) {
    return specialAreas[type] ?? [];
  }

  /// جميع أنواع المناطق الخاصة
  static List<String> getSpecialAreaTypes() {
    return specialAreas.keys.toList();
  }
}
