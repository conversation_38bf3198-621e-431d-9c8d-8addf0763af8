import 'package:flutter/material.dart';
import 'package:kuwait_corners/core/config/app_mode_config.dart';
import 'package:kuwait_corners/core/models/subscription_model.dart';
import 'package:kuwait_corners/core/services/subscription_service.dart';
import 'package:kuwait_corners/presentation/pages/subscription_page.dart';
import 'package:kuwait_corners/presentation/widgets/custom_button.dart';
import 'package:kuwait_corners/presentation/widgets/loading_indicator.dart';

/// ويدجت للتحقق من إمكانية إنشاء إعلان جديد بناءً على الاشتراك
class SubscriptionCheckWidget extends StatefulWidget {
  /// الدالة التي سيتم استدعاؤها عند نجاح التحقق
  final VoidCallback onSuccess;

  const SubscriptionCheckWidget({
    super.key,
    required this.onSuccess,
  });

  @override
  State<SubscriptionCheckWidget> createState() =>
      _SubscriptionCheckWidgetState();
}

class _SubscriptionCheckWidgetState extends State<SubscriptionCheckWidget> {
  final SubscriptionService _subscriptionService = SubscriptionService();
  bool _isLoading = true;
  bool _canCreateAd = false;
  SubscriptionModel? _currentSubscription;

  @override
  void initState() {
    super.initState();
    _checkSubscription();
  }

  /// التحقق من الاشتراك
  Future<void> _checkSubscription() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final canCreate = await _subscriptionService.canCreateNewAd();
      final subscription = await _subscriptionService.getCurrentSubscription();

      setState(() {
        _canCreateAd = canCreate;
        _currentSubscription = subscription;
        _isLoading = false;
      });

      if (canCreate) {
        widget.onSuccess();
      }
    } catch (e) {
      setState(() {
        _canCreateAd = false;
        _isLoading = false;
      });
    }
  }

  /// تحديث عدد الإعلانات المتبقية
  Future<void> _decrementRemainingAds() async {
    await _subscriptionService.decrementRemainingAds();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    if (_canCreateAd) {
      // إذا كان يمكن إنشاء إعلان، قم بتحديث عدد الإعلانات المتبقية واستدعاء onSuccess
      _decrementRemainingAds();
      return const SizedBox();
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.warning_amber_rounded,
            size: 64,
            color: Colors.amber),
          const SizedBox(height: 16),
          const Text(
            'لا يمكنك إنشاء إعلان جديد',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold),
            textAlign: TextAlign.center),
          const SizedBox(height: 8),
          Text(
            _currentSubscription == null
                ? 'يرجى الاشتراك للتمكن من إنشاء إعلانات'
                : _currentSubscription!.remainingAds <= 0
                    ? 'لقد استنفدت عدد الإعلانات المسموح بها في اشتراكك الحالي'
                    : 'اشتراكك الحالي منتهي الصلاحية',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey),
            textAlign: TextAlign.center),
          const SizedBox(height: 24),
          // إخفاء زر الاشتراك في الوضع المعلوماتي
        if (AppModeConfig.shouldShowSubscriptionButtons())
          CustomButton(
              text: _currentSubscription == null ||
                      _currentSubscription!.type == SubscriptionType.free
                  ? 'اشترك الآن'
                  : 'ترقية الاشتراك',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => const SubscriptionPage())).then((_) => _checkSubscription());
              },
              width: 200),
        ]));
  }
}
