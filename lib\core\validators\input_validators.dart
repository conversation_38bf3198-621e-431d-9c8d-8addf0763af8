/// مجموعة من الدوال للتحقق من صحة المدخلات
class InputValidators {
  /// التحقق من صحة العنوان
  /// يجب أن يكون العنوان غير فارغ وأن يتراوح طوله بين 5 و 100 حرف
  static String? validateTitle(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال عنوان الإعلان';
    }
    if (value.trim().length < 5) {
      return 'يجب أن يكون العنوان 5 أحرف على الأقل';
    }
    if (value.trim().length > 100) {
      return 'يجب أن لا يتجاوز العنوان 100 حرف';
    }
    return null;
  }

  /// التحقق من صحة الوصف
  /// يجب أن يكون الوصف غير فارغ وأن يتراوح طوله بين 20 و 1000 حرف
  static String? validateDescription(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال وصف الإعلان';
    }
    if (value.trim().length < 20) {
      return 'يجب أن يكون الوصف 20 حرف على الأقل';
    }
    if (value.trim().length > 1000) {
      return 'يجب أن لا يتجاوز الوصف 1000 حرف';
    }
    return null;
  }

  /// التحقق من صحة السعر
  /// يجب أن يكون السعر رقمًا موجبًا
  static String? validatePrice(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال سعر العقار';
    }
    
    final price = double.tryParse(value.trim());
    if (price == null) {
      return 'يرجى إدخال سعر صحيح';
    }
    
    if (price <= 0) {
      return 'يجب أن يكون السعر أكبر من صفر';
    }
    
    return null;
  }

  /// التحقق من صحة الموقع
  /// يجب أن يكون الموقع غير فارغ
  static String? validateLocation(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال موقع العقار';
    }
    return null;
  }

  /// التحقق من صحة عدد الغرف
  /// يجب أن يكون عدد الغرف رقمًا صحيحًا موجبًا
  static String? validateRooms(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // اختياري
    }
    
    final rooms = int.tryParse(value.trim());
    if (rooms == null) {
      return 'يرجى إدخال عدد صحيح';
    }
    
    if (rooms < 0) {
      return 'يجب أن يكون عدد الغرف صفر أو أكثر';
    }
    
    return null;
  }

  /// التحقق من صحة المساحة
  /// يجب أن تكون المساحة رقمًا موجبًا
  static String? validateArea(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // اختياري
    }
    
    final area = double.tryParse(value.trim());
    if (area == null) {
      return 'يرجى إدخال مساحة صحيحة';
    }
    
    if (area <= 0) {
      return 'يجب أن تكون المساحة أكبر من صفر';
    }
    
    return null;
  }

  /// التحقق من صحة رقم الهاتف
  /// يجب أن يكون رقم الهاتف 8 أرقام على الأقل
  static String? validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // اختياري
    }
    
    // إزالة الرموز غير الرقمية
    final cleanPhone = value.trim().replaceAll(RegExp(r'[^0-9]'), '');
    
    if (cleanPhone.length < 8) {
      return 'يجب أن يكون رقم الهاتف 8 أرقام على الأقل';
    }
    
    return null;
  }

  /// التحقق من صحة الإحداثيات
  /// يجب أن تكون الإحداثيات أرقامًا صحيحة
  static String? validateCoordinates(String? lat, String? lng) {
    if ((lat == null || lat.trim().isEmpty) && (lng == null || lng.trim().isEmpty)) {
      return null; // اختياري
    }
    
    if ((lat == null || lat.trim().isEmpty) || (lng == null || lng.trim().isEmpty)) {
      return 'يرجى إدخال كلا الإحداثيتين';
    }
    
    final latitude = double.tryParse(lat.trim());
    final longitude = double.tryParse(lng.trim());
    
    if (latitude == null || longitude == null) {
      return 'يرجى إدخال إحداثيات صحيحة';
    }
    
    if (latitude < -90 || latitude > 90) {
      return 'يجب أن تكون خط العرض بين -90 و 90';
    }
    
    if (longitude < -180 || longitude > 180) {
      return 'يجب أن يكون خط الطول بين -180 و 180';
    }
    
    return null;
  }

  /// التحقق من صحة كود الخصم
  /// يجب أن يكون كود الخصم مكونًا من أحرف وأرقام فقط
  static String? validateDiscountCode(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // اختياري
    }
    
    if (!RegExp(r'^[a-zA-Z0-9]+$').hasMatch(value.trim())) {
      return 'يجب أن يتكون كود الخصم من أحرف وأرقام فقط';
    }
    
    return null;
  }

  /// التحقق من صحة الصور
  /// يجب أن يكون هناك صورة واحدة على الأقل
  static String? validateImages(List<dynamic>? images) {
    if (images == null || images.isEmpty) {
      return 'يرجى إضافة صورة واحدة على الأقل';
    }
    
    return null;
  }
}
