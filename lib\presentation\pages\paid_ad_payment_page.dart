import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../core/config/app_mode_config.dart';
import '../../domain/entities/estate.dart';
import '../../domain/services/paid_ads_service.dart';

/// صفحة دفع الإعلانات المدفوعة
class PaidAdPaymentPage extends StatefulWidget {
  final Map<String, dynamic> paymentRequest;
  final Estate? copiedEstate;

  const PaidAdPaymentPage({
    super.key,
    required this.paymentRequest,
    this.copiedEstate,
  });

  @override
  State<PaidAdPaymentPage> createState() => _PaidAdPaymentPageState();
}

class _PaidAdPaymentPageState extends State<PaidAdPaymentPage> {
  final PaidAdsService _paidAdsService = PaidAdsService();
  bool _isProcessing = false;

  @override
  Widget build(BuildContext context) {
    // في الوضع المعلوماتي، عرض رسالة تعطيل الدفع
    if (AppModeConfig.isInformationalOnly) {
      return Scaffold(
        appBar: AppBar(
          title: Text(
            'دفع الإعلان المدفوع',
            style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          ),
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.info_outline,
                  size: 64,
                  color: Colors.blue.shade600,
                ),
                const SizedBox(height: 24),
                Text(
                  'الدفع غير متاح',
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade800,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  AppModeConfig.getDisabledMessage('payment'),
                  textAlign: TextAlign.center,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: Colors.grey.shade700,
                  ),
                ),
                const SizedBox(height: 32),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade600,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 12,
                    ),
                  ),
                  child: Text(
                    'العودة',
                    style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    final bankDetails = widget.paymentRequest['bankDetails'] as Map<String, dynamic>;
    final price = widget.paymentRequest['price'] as double;
    final currency = widget.paymentRequest['currency'] as String;

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text(
          'دفع الإعلان المدفوع',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            color: Colors.white)),
        backgroundColor: Colors.green,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white)),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPaymentSummary(price, currency),
            const SizedBox(height: 24),
            _buildBankTransferInstructions(bankDetails),
            const SizedBox(height: 24),
            _buildBankDetails(bankDetails),
            const SizedBox(height: 24),
            _buildContactInfo(bankDetails),
            const SizedBox(height: 24),
            _buildImportantNotes(),
            const SizedBox(height: 32),
            _buildConfirmationButton(),
          ])));
  }

  /// بناء ملخص الدفع
  Widget _buildPaymentSummary(double price, String currency) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [Colors.green, Colors.green.shade700],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight)),
        child: Column(
          children: [
            Icon(
              Icons.payment,
              size: 48,
              color: Colors.white),
            const SizedBox(height: 12),
            Text(
              'المبلغ المطلوب',
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: Colors.white70)),
            const SizedBox(height: 8),
            Text(
              '${price.toStringAsFixed(1)} $currency',
              style: GoogleFonts.cairo(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white)),
            if (widget.copiedEstate != null) ...[
              const SizedBox(height: 12),
              Text(
                'للعقار: ${widget.copiedEstate!.title}',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.white70),
                textAlign: TextAlign.center),
            ],
          ])));
  }

  /// بناء تعليمات التحويل البنكي
  Widget _buildBankTransferInstructions(Map<String, dynamic> bankDetails) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue, size: 24),
                const SizedBox(width: 8),
                Text(
                  'تعليمات الدفع',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue)),
              ]),
            const SizedBox(height: 16),
            ...((bankDetails['instructions'] as List<String>).asMap().entries.map((entry) {
              final index = entry.key + 1;
              final instruction = entry.value;
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: Colors.blue,
                        borderRadius: BorderRadius.circular(12)),
                      child: Center(
                        child: Text(
                          '$index',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.white)))),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        instruction,
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          height: 1.5))),
                  ]));
            }).toList()),
          ])));
  }

  /// بناء تفاصيل البنك
  Widget _buildBankDetails(Map<String, dynamic> bankDetails) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.account_balance, color: Colors.green, size: 24),
                const SizedBox(width: 8),
                Text(
                  'تفاصيل الحساب البنكي',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green)),
              ]),
            const SizedBox(height: 16),
            
            _buildBankDetailRow('اسم البنك:', bankDetails['bankName']),
            _buildBankDetailRow('رقم الحساب:', bankDetails['accountNumber'], copyable: true),
            _buildBankDetailRow('اسم الحساب:', bankDetails['accountName']),
            _buildBankDetailRow('رمز SWIFT:', bankDetails['swiftCode'], copyable: true),
          ])));
  }

  /// بناء صف تفاصيل البنك
  Widget _buildBankDetailRow(String label, String value, {bool copyable = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700]))),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    value,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      fontWeight: FontWeight.w500))),
                if (copyable)
                  IconButton(
                    icon: Icon(Icons.copy, size: 18, color: Colors.blue),
                    onPressed: () => _copyToClipboard(value),
                    padding: EdgeInsets.zero,
                    constraints: BoxConstraints()),
              ])),
        ]));
  }

  /// بناء معلومات الاتصال
  Widget _buildContactInfo(Map<String, dynamic> bankDetails) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.contact_phone, color: Colors.orange, size: 24),
                const SizedBox(width: 8),
                Text(
                  'للاستفسار والتأكيد',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange)),
              ]),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Text(
                    bankDetails['contactPhone'],
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green))),
                Row(
                  children: [
                    IconButton(
                      icon: Icon(Icons.copy, color: Colors.blue),
                      onPressed: () => _copyToClipboard(bankDetails['contactPhone'])),
                    IconButton(
                      icon: Icon(Icons.phone, color: Colors.green),
                      onPressed: () => _makePhoneCall(bankDetails['contactPhone'])),
                    IconButton(
                      icon: Icon(Icons.message, color: Colors.green),
                      onPressed: () => _openWhatsApp(bankDetails['contactPhone'])),
                  ]),
              ]),
          ])));
  }

  /// بناء الملاحظات المهمة
  Widget _buildImportantNotes() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning_amber, color: Colors.red, size: 24),
                const SizedBox(width: 8),
                Text(
                  'ملاحظات مهمة',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red)),
              ]),
            const SizedBox(height: 16),
            _buildNoteItem('احتفظ بإيصال التحويل البنكي'),
            _buildNoteItem('أرسل صورة الإيصال عبر واتساب للتأكيد'),
            _buildNoteItem('سيتم تفعيل إعلانك خلال 24 ساعة من التأكد من التحويل'),
            _buildNoteItem('في حالة عدم التأكيد خلال 48 ساعة، سيتم إلغاء الطلب'),
          ])));
  }

  /// بناء عنصر ملاحظة
  Widget _buildNoteItem(String note) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(Icons.circle, size: 6, color: Colors.red),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              note,
              style: GoogleFonts.cairo(
                fontSize: 14,
                height: 1.5))),
        ]));
  }

  /// بناء زر التأكيد
  Widget _buildConfirmationButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isProcessing ? null : _confirmPayment,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12))),
        child: _isProcessing
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white)))
            : Text(
                'تم التحويل - تأكيد الدفع',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white))));
  }

  /// نسخ النص للحافظة
  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم نسخ النص: $text'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2)));
  }

  /// إجراء مكالمة هاتفية
  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    }
  }

  /// فتح واتساب
  Future<void> _openWhatsApp(String phoneNumber) async {
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    final Uri whatsappUri = Uri.parse('https://wa.me/$cleanNumber');
    if (await canLaunchUrl(whatsappUri)) {
      await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
    }
  }

  /// تأكيد الدفع
  Future<void> _confirmPayment() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      // تأكيد الدفع (في الواقع سيتم التأكيد يدوياً من قبل الإدارة)
      await Future.delayed(const Duration(seconds: 2)); // محاكاة المعالجة

      // عرض رسالة نجاح
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          title: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 28),
              const SizedBox(width: 8),
              Text(
                'تم الإرسال بنجاح',
                style: GoogleFonts.cairo(fontWeight: FontWeight.bold)),
            ]),
          content: Text(
            'تم إرسال طلب التأكيد بنجاح. سيتم تفعيل إعلانك خلال 24 ساعة من التأكد من التحويل.',
            style: GoogleFonts.cairo()),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(); // إغلاق الحوار
                Navigator.of(context).pushNamedAndRemoveUntil(
                  '/home',
                  (route) => false); // العودة للصفحة الرئيسية
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
              child: Text(
                'موافق',
                style: GoogleFonts.cairo(color: Colors.white))),
          ]));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تأكيد الدفع: $e'),
          backgroundColor: Colors.red));
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }
}
