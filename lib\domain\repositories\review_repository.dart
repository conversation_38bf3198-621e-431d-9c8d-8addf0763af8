import 'dart:io';
import '../entities/review.dart';
import '../entities/comment.dart';

/// واجهة مستودع التقييمات والتعليقات
abstract class ReviewRepository {
  /// إنشاء تقييم جديد
  Future<String> createReview(Review review, {List<File>? images, List<File>? videos});
  
  /// تحديث تقييم موجود
  Future<void> updateReview(Review review, {List<File>? newImages, List<File>? newVideos});
  
  /// حذف تقييم
  Future<void> deleteReview(String reviewId);
  
  /// الحصول على تقييم بواسطة المعرف
  Future<Review?> getReviewById(String reviewId);
  
  /// الحصول على تقييمات عنصر
  Future<List<Review>> getItemReviews(String itemId, String itemType);
  
  /// الحصول على تقييمات عنصر بالتحميل المتدرج
  /// [itemId] معرف العنصر
  /// [itemType] نوع العنصر
  /// [limit] عدد التقييمات في كل صفحة
  /// [lastReviewId] معرف آخر تقييم تم تحميله (للصفحات التالية)
  /// يعيد Map تحتوي على:
  /// - 'reviews': قائمة التقييمات
  /// - 'lastReviewId': معرف آخر تقييم (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من التقييمات
  Future<Map<String, dynamic>> getItemReviewsPaginated({
    required String itemId,
    required String itemType,
    int limit = 10,
    String? lastReviewId,
  });
  
  /// الحصول على تقييمات المستخدم
  Future<List<Review>> getUserReviews(String userId);
  
  /// إضافة إعجاب لتقييم
  Future<void> likeReview(String reviewId, String userId);
  
  /// إزالة إعجاب من تقييم
  Future<void> unlikeReview(String reviewId, String userId);
  
  /// إضافة رد على تقييم
  Future<void> replyToReview(String reviewId, String userId, String userName, String content);
  
  /// إزالة رد من تقييم
  Future<void> removeReviewReply(String reviewId);
  
  /// الإبلاغ عن تقييم
  Future<void> reportReview(String reviewId, String reason);
  
  /// التحقق من تقييم
  Future<void> verifyReview(String reviewId);
  
  /// الموافقة على تقييم
  Future<void> approveReview(String reviewId);
  
  /// رفض تقييم
  Future<void> rejectReview(String reviewId);
  
  /// إنشاء تعليق جديد
  Future<String> createComment(Comment comment, {List<File>? images});
  
  /// تحديث تعليق موجود
  Future<void> updateComment(Comment comment, {List<File>? newImages});
  
  /// حذف تعليق
  Future<void> deleteComment(String commentId);
  
  /// الحصول على تعليق بواسطة المعرف
  Future<Comment?> getCommentById(String commentId);
  
  /// الحصول على تعليقات عنصر
  Future<List<Comment>> getItemComments(String itemId, String itemType);
  
  /// الحصول على تعليقات عنصر بالتحميل المتدرج
  /// [itemId] معرف العنصر
  /// [itemType] نوع العنصر
  /// [limit] عدد التعليقات في كل صفحة
  /// [lastCommentId] معرف آخر تعليق تم تحميله (للصفحات التالية)
  /// [parentId] معرف التعليق الأصلي (للردود على تعليق)
  /// يعيد Map تحتوي على:
  /// - 'comments': قائمة التعليقات
  /// - 'lastCommentId': معرف آخر تعليق (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من التعليقات
  Future<Map<String, dynamic>> getItemCommentsPaginated({
    required String itemId,
    required String itemType,
    int limit = 20,
    String? lastCommentId,
    String? parentId,
  });
  
  /// الحصول على تعليقات المستخدم
  Future<List<Comment>> getUserComments(String userId);
  
  /// إضافة إعجاب لتعليق
  Future<void> likeComment(String commentId, String userId);
  
  /// إزالة إعجاب من تعليق
  Future<void> unlikeComment(String commentId, String userId);
  
  /// الإبلاغ عن تعليق
  Future<void> reportComment(String commentId, String reason);
  
  /// الموافقة على تعليق
  Future<void> approveComment(String commentId);
  
  /// رفض تعليق
  Future<void> rejectComment(String commentId);
  
  /// الحصول على متوسط تقييم عنصر
  Future<double> getItemAverageRating(String itemId, String itemType);
  
  /// الحصول على إحصائيات تقييم عنصر
  Future<Map<int, int>> getItemRatingStatistics(String itemId, String itemType);
  
  /// الحصول على عدد التقييمات لعنصر
  Future<int> getItemReviewsCount(String itemId, String itemType);
  
  /// الحصول على عدد التعليقات لعنصر
  Future<int> getItemCommentsCount(String itemId, String itemType);
}
