
/// خدمة التحقق من العناوين والرموز البريدية الكويتية
class KuwaitAddressValidationService {
  // قاموس المحافظات والمناطق الكويتية مع رموزها البريدية الرسمية
  // المصدر: الموقع الرسمي لوزارة الاتصالات الكويتية (moc.gov.kw) - 2025
  static const Map<String, Map<String, List<String>>> _kuwaitAreas = {
    'محافظة العاصمة': {
      'مدينة الكويت': ['13001', '13002', '13003', '13004', '13005'],
      'دسمان': ['15451', '15452', '15453'],
      'الشرق': ['15001', '15002', '15003', '15004', '15005'],
      'المرقاب': ['13451', '13452', '13453'],
      'الصوابر': ['13501', '13502', '13503'],
      'القبلة': ['13551', '13552', '13553'],
      'الصالحية': ['13601', '13602', '13603'],
      'بنيد القار': ['13651', '13652', '13653'],
      'الدوحة': ['13701', '13702', '13703'],
      'ميناء الدوحة': ['13751', '13752', '13753'],
      'الدسمة': ['13801', '13802', '13803'],
      'الدعية': ['32001', '32002', '32003', '32004', '32005'],
      'الفيحاء': ['13851', '13852', '13853'],
      'جبلة': ['13901', '13902', '13903'],
      'كيفان': ['13951', '13952', '13953'],
      'الخالدية': ['14001', '14002', '14003'],
      'المنصورية': ['14051', '14052', '14053'],
      'النزهة': ['14101', '14102', '14103'],
      'القادسية': ['14151', '14152', '14153'],
      'قرطبة': ['14201', '14202', '14203'],
      'الروضة': ['14251', '14252', '14253'],
      'الشامية': ['14301', '14302', '14303'],
      'الشويخ': ['14351', '14352', '14353'],
      'الشويخ الصناعية': ['14401', '14402', '14403'],
      'ميناء الشويخ': ['14451', '14452', '14453'],
      'الصليبيخات': ['14501', '14502', '14503'],
      'السرة': ['45001', '45002', '45003', '45004'],
      'العديلية': ['14551', '14552', '14553'],
      'غرناطة': ['14601', '14602', '14603'],
      'النهضة': ['14651', '14652', '14653'],
      'شمال غرب الصليبيخات': ['14701', '14702', '14703'],
      'اليرموك': ['14751', '14752', '14753'],
      'القيروان': ['14801', '14802', '14803'],
      'مدينة جابر الأحمد': ['14851', '14852', '14853'],
      'الري': ['14901', '14902', '14903'],
      'جزيرة فيلكا': ['14951', '14952', '14953'],
      'جزيرة أوها': ['14961', '14962', '14963'],
      'جزيرة ميشان': ['14971', '14972', '14973'],
      'جزيرة أم النمل': ['14981', '14982', '14983'],
      'حدائق السور': ['14991', '14992', '14993'],
    },
    'محافظة حولي': {
      'حولي': ['32001', '32002', '32003', '32004', '32005', '32006', '32007', '32008', '32009', '32010'],
      'السالمية': ['22001', '22002', '22003', '22004', '22005', '22006', '22007', '22008', '22009', '22010'],
      'الجابرية': ['46301', '46302', '46303', '46304', '46305', '46306', '46307', '46308', '46309', '46310', '46311', '46312'],
      'الرميثية': ['43801', '43802', '43803', '43804'],
      'سلوى': ['43201', '43202', '43203', '43204', '43205', '43206', '43207', '43208', '43209', '43210'],
      'بيان': ['43651', '43652', '43653', '43654'],
      'مشرف': ['43701', '43702', '43703', '43704'],
      'الشعب': ['32101', '32102', '32103'],
      'الشهداء': ['32501', '32502', '32503'],
      'حطين': ['32451', '32452', '32453', '32454'],
      'سلام': ['43001', '43002', '43003', '43004'],
      'الزهراء': ['32051', '32052', '32053'],
      'مبارك العبدالله': ['32251', '32252', '32253'],
      'الصديق': ['32301', '32302', '32303'],
      'البدع': ['32351', '32352', '32353'],
      'أنجفة': ['32401', '32402', '32403'],
      'منطقة الوزارات': ['32551', '32552', '32553'],
    },
    'محافظة الفروانية': {
      'الفروانية': ['80001', '80002', '80003', '80004', '80005', '80006', '80007', '80008', '80009', '80010', '80011', '80012', '80013', '80014', '80015'],
      'عبد الله المبارك': ['92500', '92501', '92502'],
      'منطقة المطار': ['80051', '80052', '80053'],
      'الأندلس': ['92000', '92001', '92002'],
      'العارضية': ['92400', '92401', '92402'],
      'العارضية الحرفية': ['92551', '92552'],
      'إشبيلية': ['92100', '92101', '92102'],
      'الضجيج': ['92300', '92301', '92302'],
      'الفردوس': ['92201', '92202', '92203', '92204', '92205', '92206', '92207', '92208', '92209'],
      'جليب الشيوخ': ['85600', '85601', '85602'],
      'خيطان': ['83001', '83002', '83003'],
      'العمرية': ['85001', '85002', '85003', '85004', '85005'],
      'الرابية': ['85201', '85202', '85203', '85204', '85205'],
      'الري': ['80101', '80102', '80103'],
      'الرقعي': ['92700', '92701', '92702'],
      'الرحاب': ['86000', '86001', '86002', '86003', '86004', '86005', '86006', '86007', '86008', '86009', '86010', '86011', '86012'],
      'صباح الناصر': ['80151', '80152', '80153'],
      'جامعة صباح السالم': ['80201', '80202', '80203'],
      'غرب عبد الله المبارك': ['80251', '80252', '80253'],
      'جنوب عبد الله المبارك': ['80301', '80302', '80303'],
      'الصليبية الصناعية': ['80351', '80352', '80353'],
    },
    'محافظة الأحمدي': {
      'الأحمدي': ['61001', '61002', '61003', '61004', '61005'],
      'أبو حليفة': ['54001', '54002', '54003', '54004', '54005', '54006', '54007', '54008', '54009', '54010'],
      'ميناء عبد الله': ['61051', '61052', '61053'],
      'علي صباح السالم': ['62451', '62452', '62453'],
      'العقيلة': ['61101', '61102', '61103'],
      'بر الأحمدي': ['61151', '61152', '61153'],
      'بنيدر': ['61201', '61202', '61203'],
      'الظهر': ['61251', '61252', '61253'],
      'الفحيحيل': ['64001', '64002', '64003', '64004', '64005', '64006', '64007', '64008', '64009', '64010'],
      'فهد الأحمد': ['61301', '61302', '61303'],
      'هدية': ['64801', '64802', '64803'],
      'جابر العلي': ['61351', '61352', '61353'],
      'الجليعة': ['61401', '61402', '61403'],
      'الخيران': ['65001', '65002', '65003'],
      'المهبولة': ['62001', '62002', '62003'],
      'المنقف': ['53700', '53701', '53702', '53703', '53704', '53705'],
      'المقوع': ['61451', '61452', '61453'],
      'الوفرة السكنية': ['65451', '65452', '65453'],
      'النويصيب': ['61501', '61502', '61503'],
      'الرقة': ['64501', '64502', '64503'],
      'صباح الأحمد': ['61551', '61552', '61553'],
      'مدينة صباح الأحمد البحرية': ['61601', '61602', '61603'],
      'الصباحية': ['63001', '63002', '63003'],
      'جنوب الصباحية': ['63051', '63052', '63053'],
      'الشعيبة الصناعية': ['61651', '61652', '61653'],
      'الوفرة': ['61701', '61702', '61703'],
      'الزور': ['65501', '65502', '65503'],
      'الفنطاس': ['64451', '64452', '64453', '64454'],
      'الشدادية الصناعية': ['61751', '61752', '61753'],
    },
    'محافظة الجهراء': {
      'الجهراء': ['71001', '71002', '71003', '71004', '71005', '71006', '71007', '71008', '71009', '71010'],
      'العبدلي': ['71051', '71052', '71053'],
      'المطلاع': ['71101', '71102', '71103'],
      'كاظمة': ['71701', '71702', '71703'],
      'بحرة': ['71151', '71152', '71153'],
      'كبد': ['71201', '71202', '71203'],
      'الشقايا': ['71251', '71252', '71253'],
      'النهضة': ['71301', '71302', '71303'],
      'أمغرة الصناعية': ['71851', '71852', '71853'],
      'بر الجهراء': ['71351', '71352', '71353'],
      'الجهراء الصناعية الحرفية': ['71401', '71402', '71403'],
      'النعيم': ['71551', '71552', '71553'],
      'النسيم': ['71801', '71802', '71803'],
      'العيون': ['71601', '71602', '71603'],
      'القصر': ['71451', '71452', '71453'],
      'جابر الأحمد': ['71501', '71502', '71503'],
      'سعد العبد الله': ['71551', '71552', '71553'],
      'السالمي': ['71601', '71602', '71603'],
      'الصبية': ['72001', '72002', '72003'],
      'الصليبية': ['71751', '71752', '71753'],
      'المنطقة الزراعية الصليبية': ['71801', '71802', '71803'],
      'الصليبية السكنية': ['71851', '71852', '71853'],
      'تيماء': ['71901', '71902', '71903'],
      'الواحة': ['71651', '71652', '71653'],
      'جزيرة بوبيان': ['71951', '71952', '71953'],
      'جزيرة وربة': ['72001', '72002', '72003'],
    },
    'محافظة مبارك الكبير': {
      'مبارك الكبير': ['51001', '51002', '51003'],
      'صباح السالم': ['51051', '51052', '51053'],
      'العدان': ['51101', '51102', '51103'],
      'القرين': ['51151', '51152', '51153'],
      'القصور': ['51201', '51202', '51203'],
      'الفنيطيس': ['51251', '51252', '51253'],
      'المسيلة': ['51301', '51302', '51303'],
      'أبو الحصانية': ['51351', '51352', '51353'],
      'أبو فطيرة': ['51401', '51402', '51403'],
      'المسايل': ['51451', '51452', '51453'],
      'وسطي': ['51501', '51502', '51503'],
      'صبحان الصناعية': ['51551', '51552', '51553'],
      'غرب أبو فطيرة الحرفية': ['51601', '51602', '51603'],
    },
  };

  // قائمة الكلمات المفتاحية للعناوين الكويتية المحدثة - البيانات الرسمية 2025
  static const List<String> _kuwaitKeywords = [
    // المحافظات
    'محافظة العاصمة', 'محافظة حولي', 'محافظة الأحمدي', 'محافظة الجهراء', 'محافظة الفروانية', 'محافظة مبارك الكبير',
    'العاصمة', 'حولي', 'الأحمدي', 'الجهراء', 'الفروانية', 'مبارك الكبير',

    // مناطق محافظة العاصمة
    'مدينة الكويت', 'دسمان', 'الشرق', 'المرقاب', 'الصوابر', 'القبلة', 'الصالحية', 'بنيد القار',
    'الدوحة', 'ميناء الدوحة', 'الدسمة', 'الدعية', 'الفيحاء', 'جبلة', 'كيفان', 'الخالدية',
    'المنصورية', 'النزهة', 'القادسية', 'قرطبة', 'الروضة', 'الشامية', 'الشويخ', 'الشويخ الصناعية',
    'ميناء الشويخ', 'الصليبيخات', 'السرة', 'العديلية', 'غرناطة', 'النهضة', 'شمال غرب الصليبيخات',
    'اليرموك', 'القيروان', 'مدينة جابر الأحمد', 'الري', 'جزيرة فيلكا', 'حدائق السور',

    // مناطق محافظة حولي
    'السالمية', 'الجابرية', 'الرميثية', 'سلوى', 'بيان', 'مشرف', 'الشعب', 'الشهداء',
    'حطين', 'سلام', 'الزهراء', 'مبارك العبدالله', 'الصديق', 'البدع', 'أنجفة', 'منطقة الوزارات',

    // مناطق محافظة الفروانية
    'عبد الله المبارك', 'منطقة المطار', 'الأندلس', 'العارضية', 'العارضية الحرفية', 'إشبيلية',
    'الضجيج', 'الفردوس', 'جليب الشيوخ', 'خيطان', 'العمرية', 'الرابية', 'الرقعي', 'الرحاب',
    'صباح الناصر', 'جامعة صباح السالم', 'غرب عبد الله المبارك', 'جنوب عبد الله المبارك',
    'الصليبية الصناعية',

    // مناطق محافظة الأحمدي
    'أبو حليفة', 'ميناء عبد الله', 'علي صباح السالم', 'العقيلة', 'بر الأحمدي', 'بنيدر',
    'الظهر', 'الفحيحيل', 'فهد الأحمد', 'هدية', 'جابر العلي', 'الجليعة', 'الخيران', 'المهبولة',
    'المنقف', 'المقوع', 'الوفرة السكنية', 'النويصيب', 'الرقة', 'صباح الأحمد',
    'مدينة صباح الأحمد البحرية', 'الصباحية', 'جنوب الصباحية', 'الشعيبة الصناعية',
    'الوفرة', 'الزور', 'الفنطاس', 'الشدادية الصناعية',

    // مناطق محافظة الجهراء
    'العبدلي', 'المطلاع', 'كاظمة', 'بحرة', 'كبد', 'الشقايا', 'النهضة', 'أمغرة الصناعية',
    'بر الجهراء', 'الجهراء الصناعية الحرفية', 'النعيم', 'النسيم', 'العيون', 'القصر',
    'جابر الأحمد', 'سعد العبد الله', 'السالمي', 'الصبية', 'الصليبية', 'المنطقة الزراعية الصليبية',
    'الصليبية السكنية', 'تيماء', 'الواحة', 'جزيرة بوبيان', 'جزيرة وربة',

    // مناطق محافظة مبارك الكبير
    'صباح السالم', 'العدان', 'القرين', 'القصور', 'الفنيطيس', 'المسيلة', 'أبو الحصانية',
    'أبو فطيرة', 'المسايل', 'وسطي', 'صبحان الصناعية', 'غرب أبو فطيرة الحرفية',

    // كلمات عامة للعناوين
    'شارع', 'قطعة', 'منزل', 'بيت', 'عمارة', 'مجمع', 'برج', 'شقة', 'دور', 'الدور',
    'مربع', 'بلوك', 'جادة', 'طريق', 'زقاق', 'حي', 'منطقة', 'مدينة', 'جزيرة'
  ];

  /// التحقق من صحة العنوان الكويتي
  static bool isValidKuwaitAddress(String address) {
    if (address.trim().isEmpty) return false;

    final addressLower = address.toLowerCase().trim();

    // التحقق من وجود كلمات مفتاحية كويتية
    bool hasKuwaitKeyword = _kuwaitKeywords.any((keyword) =>
        addressLower.contains(keyword.toLowerCase()));

    // التحقق من طول العنوان (يجب أن يكون مفصلاً)
    bool hasMinimumLength = address.trim().length >= 10;

    // التحقق من وجود أرقام (رقم المنزل أو القطعة)
    bool hasNumbers = RegExp(r'\d+').hasMatch(address);

    return hasKuwaitKeyword && hasMinimumLength && hasNumbers;
  }

  /// التحقق من صحة الرمز البريدي الكويتي
  static bool isValidKuwaitPostalCode(String postalCode) {
    if (postalCode.trim().isEmpty) return false;

    // التحقق من تنسيق الرمز البريدي (5 أرقام)
    final regex = RegExp(r'^\d{5}$');
    if (!regex.hasMatch(postalCode.trim())) return false;

    // التحقق من وجود الرمز في قاعدة البيانات
    return _getAllPostalCodes().contains(postalCode.trim());
  }

  /// التحقق من تطابق العنوان مع الرمز البريدي
  static bool isAddressMatchingPostalCode(String address, String postalCode) {
    if (!isValidKuwaitAddress(address) || !isValidKuwaitPostalCode(postalCode)) {
      return false;
    }

    final addressLower = address.toLowerCase().trim();

    // البحث عن المنطقة المطابقة للرمز البريدي
    for (final governorate in _kuwaitAreas.entries) {
      for (final area in governorate.value.entries) {
        if (area.value.contains(postalCode.trim())) {
          // التحقق من وجود اسم المنطقة في العنوان
          if (addressLower.contains(area.key.toLowerCase()) ||
              addressLower.contains(governorate.key.toLowerCase())) {
            return true;
          }
        }
      }
    }

    return false;
  }

  /// الحصول على جميع الرموز البريدية
  static List<String> _getAllPostalCodes() {
    List<String> allCodes = [];
    for (final governorate in _kuwaitAreas.values) {
      for (final area in governorate.values) {
        allCodes.addAll(area);
      }
    }
    return allCodes;
  }

  /// الحصول على المناطق المطابقة للرمز البريدي
  static List<String> getAreasForPostalCode(String postalCode) {
    List<String> matchingAreas = [];

    for (final governorate in _kuwaitAreas.entries) {
      for (final area in governorate.value.entries) {
        if (area.value.contains(postalCode.trim())) {
          matchingAreas.add('${area.key} - ${governorate.key}');
        }
      }
    }

    return matchingAreas;
  }

  /// الحصول على الرموز البريدية للمنطقة
  static List<String> getPostalCodesForArea(String areaName) {
    List<String> matchingCodes = [];

    for (final governorate in _kuwaitAreas.values) {
      for (final area in governorate.entries) {
        if (area.key.toLowerCase().contains(areaName.toLowerCase())) {
          matchingCodes.addAll(area.value);
        }
      }
    }

    return matchingCodes;
  }

  /// التحقق من صحة البريد الإلكتروني مع التركيز على النطاقات الكويتية
  static bool isValidKuwaitEmail(String email) {
    if (email.trim().isEmpty) return false;

    // التحقق من التنسيق الأساسي للبريد الإلكتروني
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(email.trim())) return false;

    final emailLower = email.toLowerCase().trim();

    // قائمة النطاقات الكويتية المفضلة
    final kuwaitDomains = [
      '.kw', 'gov.kw', 'edu.kw', 'com.kw', 'org.kw', 'net.kw',
      'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
      'icloud.com', 'live.com', 'msn.com'
    ];

    // التحقق من النطاق
    bool hasValidDomain = kuwaitDomains.any((domain) =>
        emailLower.endsWith(domain));

    return hasValidDomain;
  }

  /// اقتراح تصحيحات للعنوان
  static List<String> suggestAddressCorrections(String address) {
    List<String> suggestions = [];

    if (address.trim().length < 10) {
      suggestions.add('يرجى إضافة تفاصيل أكثر للعنوان (اسم المنطقة، رقم المنزل، اسم الشارع)');
    }

    if (!RegExp(r'\d+').hasMatch(address)) {
      suggestions.add('يرجى إضافة رقم المنزل أو القطعة');
    }

    bool hasKuwaitKeyword = _kuwaitKeywords.any((keyword) =>
        address.toLowerCase().contains(keyword.toLowerCase()));

    if (!hasKuwaitKeyword) {
      suggestions.add('يرجى التأكد من إضافة اسم المنطقة الكويتية');
      suggestions.add('مثال: السالمية، قطعة 1، شارع الخليج العربي، منزل 15');
    }

    return suggestions;
  }

  /// اقتراح رموز بريدية بناءً على العنوان
  static List<String> suggestPostalCodes(String address) {
    List<String> suggestions = [];
    final addressLower = address.toLowerCase().trim();

    for (final governorate in _kuwaitAreas.entries) {
      for (final area in governorate.value.entries) {
        if (addressLower.contains(area.key.toLowerCase()) ||
            addressLower.contains(governorate.key.toLowerCase())) {
          suggestions.addAll(area.value);
        }
      }
    }

    return suggestions.take(5).toList(); // أول 5 اقتراحات
  }
}
