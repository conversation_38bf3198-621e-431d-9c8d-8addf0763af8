"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.processScheduledNotifications = void 0;
const functions = require("firebase-functions");
const admin = require("firebase-admin");
/**
 * معالجة الإشعارات المجدولة للأرشفة
 */
exports.processScheduledNotifications = functions.https.onCall(async (_data, context) => {
    // التحقق من المصادقة
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'المستخدم غير مصادق');
    }
    try {
        console.log('🔄 بدء معالجة الإشعارات المجدولة يدوياً...');
        const db = admin.firestore();
        const now = admin.firestore.Timestamp.now();
        // البحث عن الإشعارات المجدولة التي حان وقتها
        const query = await db.collection('scheduledNotifications')
            .where('processed', '==', false)
            .where('scheduledTime', '<=', now.toDate())
            .limit(10)
            .get();
        if (query.empty) {
            return { success: true, message: 'لا توجد إشعارات مجدولة للمعالجة', processed: 0 };
        }
        let processedCount = 0;
        const batch = db.batch();
        for (const doc of query.docs) {
            const notificationData = doc.data();
            try {
                if (notificationData.type === 'estate_archive') {
                    await processEstateArchive(notificationData);
                    processedCount++;
                }
                // تحديد الإشعار كمعالج
                batch.update(doc.ref, {
                    processed: true,
                    processedAt: now.toDate()
                });
            }
            catch (error) {
                console.error(`❌ خطأ في معالجة الإشعار ${doc.id}:`, error);
                // تحديد الإشعار كفاشل
                batch.update(doc.ref, {
                    processed: true,
                    processedAt: now.toDate(),
                    error: String(error)
                });
            }
        }
        await batch.commit();
        return {
            success: true,
            message: `تم معالجة ${processedCount} إشعار بنجاح`,
            processed: processedCount
        };
    }
    catch (error) {
        console.error('❌ خطأ في معالجة الإشعارات:', error);
        throw new functions.https.HttpsError('internal', 'خطأ في معالجة الإشعارات');
    }
});
/**
 * معالجة أرشفة العقار وإرسال الإشعار
 */
async function processEstateArchive(notificationData) {
    const db = admin.firestore();
    const messaging = admin.messaging();
    console.log(`🏠 أرشفة العقار: ${notificationData.estateId}`);
    // أرشفة العقار
    await db.collection('estates').doc(notificationData.estateId).update({
        isArchived: true,
        archivedAt: admin.firestore.Timestamp.now(),
        archivedReason: 'تم الأرشفة تلقائياً بعد 24 ساعة من النشر',
        status: 'archived'
    });
    // الحصول على بيانات المستخدم
    const userDoc = await db.collection('users').doc(notificationData.userId).get();
    const userData = userDoc.data();
    // إنشاء رسالة الإشعار
    const notificationTitle = 'تم أرشفة إعلانك 📦';
    const notificationBody = `تم أرشفة إعلان "${notificationData.estateTitle}" تلقائياً بعد 24 ساعة من النشر.

لتفعيل الإعلان مرة أخرى، تواصل مع إدارة التطبيق:

📱 واتساب: +965 9929 8821
📧 إيميل: <EMAIL>

شكراً لاستخدامك تطبيق Kuwait Corners 🏠`;
    // إرسال إشعار push إذا كان المستخدم لديه رمز FCM
    if (userData && userData.fcmToken) {
        try {
            const message = {
                token: userData.fcmToken,
                notification: {
                    title: notificationTitle,
                    body: 'تم أرشفة إعلانك. اضغط للمزيد من التفاصيل.'
                },
                data: {
                    type: 'estate_archived',
                    estateId: notificationData.estateId,
                    estateTitle: notificationData.estateTitle,
                    whatsapp: '+965 9929 8821',
                    email: '<EMAIL>',
                    fullMessage: notificationBody
                },
                android: {
                    notification: {
                        icon: 'ic_notification',
                        color: '#2E7D32',
                        sound: 'default',
                        channelId: 'estate_notifications',
                        priority: 'high'
                    }
                },
                apns: {
                    payload: {
                        aps: {
                            sound: 'default',
                            badge: 1,
                            alert: {
                                title: notificationTitle,
                                body: 'تم أرشفة إعلانك. اضغط للمزيد من التفاصيل.'
                            }
                        }
                    }
                }
            };
            await messaging.send(message);
            console.log(`✅ تم إرسال إشعار push للمستخدم: ${notificationData.userId}`);
        }
        catch (pushError) {
            console.error('❌ خطأ في إرسال إشعار push:', pushError);
        }
    }
    // حفظ الإشعار في قاعدة البيانات
    await db.collection('users')
        .doc(notificationData.userId)
        .collection('notifications')
        .add({
        title: notificationTitle,
        body: notificationBody,
        type: 'estate_archived',
        data: {
            estateId: notificationData.estateId,
            estateTitle: notificationData.estateTitle,
            contactInfo: {
                whatsapp: '+965 9929 8821',
                email: '<EMAIL>'
            }
        },
        timestamp: admin.firestore.Timestamp.now(),
        isRead: false,
        priority: 'high'
    });
    console.log(`✅ تم حفظ الإشعار للمستخدم: ${notificationData.userId}`);
    // إضافة سجل في مجموعة logs للمراقبة
    await db.collection('systemLogs').add({
        type: 'estate_archived',
        estateId: notificationData.estateId,
        userId: notificationData.userId,
        estateTitle: notificationData.estateTitle,
        timestamp: admin.firestore.Timestamp.now(),
        details: 'تم أرشفة العقار تلقائياً بعد 24 ساعة وإرسال إشعار للمستخدم'
    });
}
//# sourceMappingURL=archiveNotifications.js.map