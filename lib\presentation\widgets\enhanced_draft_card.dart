// lib/presentation/widgets/enhanced_draft_card.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

import '../../core/services/enhanced_ad_draft_service.dart';

/// بطاقة مسودة محسنة بتصميم عصري وجميل
class EnhancedDraftCard extends StatelessWidget {
  final Map<String, dynamic> draft;
  final VoidCallback onTap;
  final VoidCallback onDelete;
  final EnhancedAdDraftService draftService;

  const EnhancedDraftCard({
    super.key,
    required this.draft,
    required this.onTap,
    required this.onDelete,
    required this.draftService,
  });

  @override
  Widget build(BuildContext context) {
    // استخراج البيانات
    final draftId = draft['draftId'] as String? ?? '';
    final title = draft['title'] as String? ?? 'مسودة بدون عنوان';
    final timestamp = draft['timestamp'] as int? ?? 0;
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final isAutoSaved = draft['isAutoSaved'] as bool? ?? false;
    
    // تنسيق التاريخ
    final formattedDate = DateFormat('dd/MM/yyyy HH:mm', 'ar').format(date);
    final timeAgo = _getTimeAgo(date);
    
    // حساب نسبة الاكتمال
    final completionPercentage = draftService.getDraftCompletionPercentage(draft);
    final completionPercent = (completionPercentage * 100).toInt();
    
    // تحديد الخطوة التالية
    final nextStep = draftService.getNextStep(draft);
    final stepName = _getStepName(nextStep);
    
    // استخراج معلومات إضافية
    final mainCategory = draft['mainCategory'] as String? ?? '';
    final subCategory = draft['subCategory'] as String? ?? '';
    final price = draft['price'] as double?;
    final location = draft['governorate'] as String? ?? '';
    final imageCount = (draft['imagePaths'] as List?)?.length ?? 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.grey.shade50,
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الصف العلوي - العنوان والحذف
                Row(
                  children: [
                    // أيقونة المسودة
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Theme.of(context).primaryColor.withOpacity(0.1),
                            Theme.of(context).primaryColor.withOpacity(0.05),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        isAutoSaved ? Icons.auto_awesome : Icons.edit_document,
                        color: Theme.of(context).primaryColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    
                    // العنوان والمعلومات
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.access_time,
                                size: 14,
                                color: Colors.grey.shade600,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                timeAgo,
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                              if (isAutoSaved) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.shade100,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    'حفظ تلقائي',
                                    style: GoogleFonts.cairo(
                                      fontSize: 10,
                                      color: Colors.blue.shade700,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                    ),
                    
                    // زر الحذف
                    IconButton(
                      icon: Icon(
                        Icons.delete_outline,
                        color: Colors.red.shade400,
                        size: 22,
                      ),
                      onPressed: onDelete,
                      tooltip: 'حذف المسودة',
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // معلومات المسودة
                if (mainCategory.isNotEmpty || subCategory.isNotEmpty) ...[
                  Row(
                    children: [
                      Icon(
                        Icons.category_outlined,
                        size: 16,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          '$mainCategory${subCategory.isNotEmpty ? ' - $subCategory' : ''}',
                          style: GoogleFonts.cairo(
                            fontSize: 13,
                            color: Colors.grey.shade700,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                ],
                
                // السعر والموقع
                Row(
                  children: [
                    if (price != null) ...[
                      Icon(
                        Icons.attach_money,
                        size: 16,
                        color: Colors.green.shade600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${price.toStringAsFixed(0)} د.ك',
                        style: GoogleFonts.cairo(
                          fontSize: 13,
                          color: Colors.green.shade700,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (location.isNotEmpty) ...[
                        const SizedBox(width: 16),
                        Icon(
                          Icons.location_on_outlined,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          location,
                          style: GoogleFonts.cairo(
                            fontSize: 13,
                            color: Colors.grey.shade700,
                          ),
                        ),
                      ],
                    ] else if (location.isNotEmpty) ...[
                      Icon(
                        Icons.location_on_outlined,
                        size: 16,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        location,
                        style: GoogleFonts.cairo(
                          fontSize: 13,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                    
                    const Spacer(),
                    
                    // عدد الصور
                    if (imageCount > 0) ...[
                      Icon(
                        Icons.photo_library_outlined,
                        size: 16,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '$imageCount صور',
                        style: GoogleFonts.cairo(
                          fontSize: 13,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // شريط التقدم والخطوة التالية
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'اكتمل بنسبة $completionPercent%',
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                'التالي: $stepName',
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: Theme.of(context).primaryColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(6),
                            child: LinearProgressIndicator(
                              value: completionPercentage,
                              backgroundColor: Colors.grey.shade200,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Theme.of(context).primaryColor,
                              ),
                              minHeight: 8,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// الحصول على اسم الخطوة
  String _getStepName(int step) {
    switch (step) {
      case 1:
        return 'اختيار التصنيف';
      case 2:
        return 'رفع الصور';
      case 3:
        return 'تفاصيل الإعلان';
      case 4:
        return 'الإعدادات';
      case 5:
        return 'المعاينة والنشر';
      default:
        return 'غير محدد';
    }
  }

  /// حساب الوقت المنقضي
  String _getTimeAgo(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }
}
