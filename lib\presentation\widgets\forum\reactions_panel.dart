import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../core/theme/app_colors.dart';
import '../../../domain/models/forum/reaction_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/forum_provider.dart';

/// نوع الكائن الذي يمكن التفاعل معه
enum ReactableType {
  /// موضوع
  topic,

  /// مشاركة
  post,
}

/// لوحة التفاعلات
class ReactionsPanel extends StatefulWidget {
  /// معرف الكائن
  final String objectId;

  /// نوع الكائن
  final ReactableType type;

  /// التفاعلات الحالية
  final List<ReactionModel> reactions;

  /// ما إذا كان مصغر
  final bool isCompact;

  /// دالة يتم استدعاؤها عند تغيير التفاعلات
  final Function(List<ReactionModel>)? onReactionsChanged;

  const ReactionsPanel({
    super.key,
    required this.objectId,
    required this.type,
    required this.reactions,
    this.isCompact = false,
    this.onReactionsChanged,
  });

  @override
  State<ReactionsPanel> createState() => _ReactionsPanelState();
}

class _ReactionsPanelState extends State<ReactionsPanel> {
  bool _isReactionPanelVisible = false;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final currentUserId = Provider.of<AuthProvider>(context).user?.uid;
    final hasReacted = currentUserId != null &&
        widget.reactions.any((r) => r.userId == currentUserId);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            // زر إضافة تفاعل
            InkWell(
              onTap: () {
                setState(() {
                  _isReactionPanelVisible = !_isReactionPanelVisible;
                });
              },
              borderRadius: BorderRadius.circular(20),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6),
                decoration: BoxDecoration(
                  color: _isReactionPanelVisible
                      ? AppColors.primary.withOpacity(0.1)
                      : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: _isReactionPanelVisible
                        ? AppColors.primary
                        : Colors.grey.shade300,
                    width: 1)),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.add_reaction_outlined,
                      size: 18,
                      color: _isReactionPanelVisible
                          ? AppColors.primary
                          : Colors.grey.shade700),
                    const SizedBox(width: 4),
                    Text(
                      'تفاعل',
                      style: TextStyle(
                        fontSize: 12,
                        color: _isReactionPanelVisible
                            ? AppColors.primary
                            : Colors.grey.shade700)),
                  ]))),
            const SizedBox(width: 8),
            // عرض التفاعلات الحالية
            if (widget.reactions.isNotEmpty) ...[
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: _buildReactionChips()))),
            ],
          ]),
        // لوحة التفاعلات
        if (_isReactionPanelVisible) ...[
          const SizedBox(height: 8),
          _buildReactionsPanel(),
        ],
      ]);
  }

  /// بناء رقائق التفاعلات
  List<Widget> _buildReactionChips() {
    // تجميع التفاعلات حسب النوع
    final Map<String, int> reactionCounts = {};
    final Map<String, bool> userReacted = {};
    final currentUserId = Provider.of<AuthProvider>(context).user?.uid;

    for (final reaction in widget.reactions) {
      reactionCounts[reaction.type] = (reactionCounts[reaction.type] ?? 0) + 1;
      if (reaction.userId == currentUserId) {
        userReacted[reaction.type] = true;
      }
    }

    return reactionCounts.entries.map((entry) {
      final isUserReacted = userReacted[entry.key] ?? false;
      return Padding(
        padding: const EdgeInsets.only(right: 4),
        child: InkWell(
          onTap: () => _handleReactionTap(entry.key),
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 4),
            decoration: BoxDecoration(
              color: isUserReacted
                  ? AppColors.primary.withOpacity(0.1)
                  : Colors.grey.shade100,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isUserReacted
                    ? AppColors.primary
                    : Colors.grey.shade300,
                width: 1)),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getReactionEmoji(entry.key),
                  style: const TextStyle(fontSize: 14)),
                const SizedBox(width: 4),
                Text(
                  entry.value.toString(),
                  style: TextStyle(
                    fontSize: 12,
                    color: isUserReacted
                        ? AppColors.primary
                        : Colors.grey.shade700,
                    fontWeight:
                        isUserReacted ? FontWeight.bold : FontWeight.normal)),
              ]))));
    }).toList();
  }

  /// بناء لوحة التفاعلات
  Widget _buildReactionsPanel() {
    final reactions = [
      'like',
      'love',
      'haha',
      'wow',
      'sad',
      'angry',
    ];

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2)),
        ]),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: reactions.map((type) {
          return InkWell(
            onTap: () => _handleReactionTap(type),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _getReactionEmoji(type),
                    style: const TextStyle(fontSize: 24)),
                  const SizedBox(height: 4),
                  Text(
                    _getReactionName(type),
                    style: const TextStyle(fontSize: 10)),
                ])));
        }).toList()));
  }

  /// معالجة النقر على تفاعل
  Future<void> _handleReactionTap(String type) async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    final currentUserId = Provider.of<AuthProvider>(context, listen: false).user?.uid;
    if (currentUserId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب تسجيل الدخول للتفاعل')));
      setState(() {
        _isLoading = false;
      });
      return;
    }

    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    final existingReaction = widget.reactions.firstWhere(
      (r) => r.userId == currentUserId && r.type == type,
      orElse: () => ReactionModel(
        id: '',
        userId: '',
        type: '',
        createdAt: DateTime.now()));

    List<ReactionModel> updatedReactions = List.from(widget.reactions);

    if (existingReaction.id.isNotEmpty) {
      // إزالة التفاعل
      try {
        bool success = false;
        if (widget.type == ReactableType.topic) {
          success = await forumProvider.removeTopicReaction(
            widget.objectId,
            existingReaction.id);
        } else {
          success = await forumProvider.removePostReaction(
            widget.objectId,
            existingReaction.id);
        }

        if (success) {
          updatedReactions.removeWhere((r) => r.id == existingReaction.id);
          if (widget.onReactionsChanged != null) {
            widget.onReactionsChanged!(updatedReactions);
          }
        }
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}')));
      }
    } else {
      // إضافة تفاعل جديد
      try {
        final reaction = ReactionModel(
          id: '',
          userId: currentUserId,
          type: type,
          createdAt: DateTime.now());

        String? reactionId;
        if (widget.type == ReactableType.topic) {
          reactionId = await forumProvider.addTopicReaction(
            widget.objectId,
            reaction);
        } else {
          reactionId = await forumProvider.addPostReaction(
            widget.objectId,
            reaction);
        }

        final newReaction = ReactionModel(
          id: reactionId,
          userId: currentUserId,
          type: type,
          createdAt: DateTime.now());
        updatedReactions.add(newReaction);
        if (widget.onReactionsChanged != null) {
          widget.onReactionsChanged!(updatedReactions);
        }
            } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}')));
      }
    }

    setState(() {
      _isLoading = false;
      _isReactionPanelVisible = false;
    });
  }

  /// الحصول على اسم التفاعل
  String _getReactionName(String type) {
    switch (type) {
      case 'like':
        return 'إعجاب';
      case 'love':
        return 'حب';
      case 'haha':
        return 'ضحك';
      case 'wow':
        return 'دهشة';
      case 'sad':
        return 'حزن';
      case 'angry':
        return 'غضب';
      default:
        return 'تفاعل';
    }
  }

  /// الحصول على رمز التفاعل
  String _getReactionEmoji(String type) {
    switch (type) {
      case 'like':
        return '👍';
      case 'love':
        return '❤️';
      case 'haha':
        return '😂';
      case 'wow':
        return '😮';
      case 'sad':
        return '😢';
      case 'angry':
        return '😡';
      default:
        return '👍';
    }
  }
}
