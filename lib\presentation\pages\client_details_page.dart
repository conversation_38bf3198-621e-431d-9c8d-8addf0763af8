import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart';

/// صفحة تفاصيل العميل
class ClientDetailsPage extends StatefulWidget {
  final String clientId;

  const ClientDetailsPage({super.key, required this.clientId});

  @override
  State<ClientDetailsPage> createState() => _ClientDetailsPageState();
}

class _ClientDetailsPageState extends State<ClientDetailsPage> {
  Map<String, dynamic>? _clientData;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadClientData();
  }

  Future<void> _loadClientData() async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('clients')
          .doc(widget.clientId)
          .get();

      if (doc.exists) {
        setState(() {
          _clientData = doc.data();
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'تفاصيل العميل',
          style: CairoTextStyles.appBarTitle),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () async {
              final result = await Navigator.pushNamed(
                context,
                '/edit-client',
                arguments: widget.clientId);
              if (result == true) {
                _loadClientData(); // إعادة تحميل البيانات بعد التعديل
              }
            }),
          PopupMenuButton<String>(
            onSelected: (value) => _handleAction(value),
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'call',
                child: Row(
                  children: [
                    const Icon(Icons.phone),
                    const SizedBox(width: 8),
                    Text('اتصال', style: CairoTextStyles.bodyMedium),
                  ])),
              PopupMenuItem(
                value: 'whatsapp',
                child: Row(
                  children: [
                    const Icon(Icons.message),
                    const SizedBox(width: 8),
                    Text('واتساب', style: CairoTextStyles.bodyMedium),
                  ])),
              PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    const Icon(Icons.delete, color: Colors.red),
                    const SizedBox(width: 8),
                    Text('حذف', style: CairoTextStyles.bodyMedium.copyWith(color: Colors.red)),
                  ])),
            ]),
        ]),
      body: Stack(
        children: [
          // خلفية بأشكال هندسية
          _buildBackgroundShapes(),

          // المحتوى الرئيسي
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _clientData == null
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
                          const SizedBox(height: 16),
                          Text(
                            'لم يتم العثور على بيانات العميل',
                            style: CairoTextStyles.bodyLarge.copyWith(color: Colors.grey[600])),
                        ]))
                  : SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildClientCard(),
                          const SizedBox(height: 24),
                          _buildContactInfo(),
                          const SizedBox(height: 24),
                          _buildPreferencesInfo(),
                          const SizedBox(height: 24),
                          _buildActivityInfo(),
                          const SizedBox(height: 24),
                          _buildNotesSection(),
                        ])),
        ]));
  }

  Widget _buildBackgroundShapes() {
    return Positioned.fill(
      child: Stack(
        children: [
          // أيقونة عميل - محسنة
          Positioned(
            top: 100,
            right: -30,
            child: Opacity(
              opacity: 0.025,
              child: Icon(
                Icons.person_outline,
                size: 100,
                color: AppColors.primary))),

          // أيقونة تفاصيل - أصغر
          Positioned(
            top: 300,
            left: -20,
            child: Opacity(
              opacity: 0.03,
              child: Icon(
                Icons.info_outline,
                size: 75,
                color: AppColors.primary))),

          // أيقونة اتصال - موضعة أفضل
          Positioned(
            bottom: 220,
            right: -15,
            child: Opacity(
              opacity: 0.025,
              child: Icon(
                Icons.contact_phone_outlined,
                size: 65,
                color: AppColors.primary))),

          // أيقونة ملاحظات - أصغر
          Positioned(
            bottom: 380,
            left: -12,
            child: Opacity(
              opacity: 0.03,
              child: Icon(
                Icons.note_alt_outlined,
                size: 55,
                color: AppColors.primary))),

          // أيقونة إعدادات - جديدة
          Positioned(
            top: 500,
            right: -10,
            child: Opacity(
              opacity: 0.02,
              child: Icon(
                Icons.settings_outlined,
                size: 50,
                color: AppColors.primary))),

          // دوائر هندسية - أصغر وأكثر توزيعاً
          Positioned(
            top: 150,
            left: 35,
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.withValues(alpha: 0.015)))),

          Positioned(
            top: 250,
            right: 50,
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.withValues(alpha: 0.02)))),

          Positioned(
            bottom: 150,
            right: 70,
            child: Container(
              width: 25,
              height: 25,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.withValues(alpha: 0.018)))),

          Positioned(
            bottom: 300,
            left: 60,
            child: Container(
              width: 18,
              height: 18,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.withValues(alpha: 0.022)))),

          // مربعات مدورة - أصغر
          Positioned(
            top: 420,
            right: 25,
            child: Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: AppColors.primary.withValues(alpha: 0.015)))),

          Positioned(
            top: 200,
            left: 20,
            child: Container(
              width: 22,
              height: 22,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                color: AppColors.primary.withValues(alpha: 0.018)))),

          Positioned(
            bottom: 180,
            left: 30,
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(7),
                color: AppColors.primary.withValues(alpha: 0.02)))),
        ]));
  }

  Widget _buildClientCard() {
    final name = _clientData!['name'] ?? 'غير محدد';
    final type = _clientData!['type'] ?? 'غير محدد';
    final status = _clientData!['status'] ?? 'غير محدد';
    final isVip = _clientData!['isVip'] ?? false;
    final createdAt = _clientData!['createdAt'] as Timestamp?;

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: isVip
            ? Border.all(color: Colors.amber.withValues(alpha: 0.3), width: 1)
            : Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1),
      ),
      child: Column(
        children: [
          // صورة العميل والمعلومات الأساسية
          Row(
            children: [
              Stack(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppColors.primary.withValues(alpha: 0.2),
                        width: 2)),
                    child: ClipOval(
                      child: _clientData!['imageUrl'] != null && _clientData!['imageUrl'].toString().isNotEmpty
                          ? Image.network(
                              _clientData!['imageUrl'],
                              width: 80,
                              height: 80,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Center(
                                  child: Text(
                                    name.isNotEmpty ? name[0].toUpperCase() : '؟',
                                    style: CairoTextStyles.headlineLarge.copyWith(
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.bold)));
                              },
                              loadingBuilder: (context, child, loadingProgress) {
                                if (loadingProgress == null) return child;
                                return Center(
                                  child: SizedBox(
                                    width: 30,
                                    height: 30,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 3,
                                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary))));
                              })
                          : Center(
                              child: Text(
                                name.isNotEmpty ? name[0].toUpperCase() : '؟',
                                style: CairoTextStyles.headlineLarge.copyWith(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.bold))))),
                  if (isVip)
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.amber,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2)),
                        child: const Icon(
                          Icons.star,
                          color: Colors.white,
                          size: 16))),
                ]),
              const SizedBox(width: 24),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            name,
                            style: CairoTextStyles.headlineMedium.copyWith(
                              fontWeight: FontWeight.bold))),
                        if (isVip)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.amber.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(color: Colors.amber.withValues(alpha: 0.3))),
                            child: Text(
                              'VIP',
                              style: CairoTextStyles.labelSmall.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.amber[700]))),
                      ]),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        _buildSimpleChip(type, AppColors.primary),
                        const SizedBox(width: 8),
                        _buildSimpleChip(status, _getStatusColor(status)),
                      ]),
                    if (createdAt != null) ...[
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Icon(Icons.schedule, size: 16, color: Colors.grey[500]),
                          const SizedBox(width: 6),
                          Text(
                            'عضو منذ ${_formatDate(createdAt.toDate())}',
                            style: CairoTextStyles.bodySmall.copyWith(
                              color: Colors.grey[600])),
                        ]),
                    ],
                  ])),
            ]),
        ]));
  }

  Widget _buildSimpleChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.2))),
      child: Text(
        label,
        style: CairoTextStyles.labelSmall.copyWith(
          color: color,
          fontWeight: FontWeight.w500)));
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'نشط':
        return Colors.green;
      case 'جديد':
        return Colors.blue;
      case 'محتمل':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  Widget _buildContactInfo() {
    final phone = _clientData!['phone'] ?? '';
    final email = _clientData!['email'] ?? '';

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(Icons.contact_phone, color: AppColors.primary, size: 20)),
              const SizedBox(width: 12),
              Text(
                'معلومات التواصل',
                style: CairoTextStyles.headlineSmall),
            ]),
          const SizedBox(height: 20),
          if (phone.isNotEmpty) ...[
            _buildContactItem(
              Icons.phone,
              'رقم الهاتف',
              phone,
              () => _makePhoneCall(phone)),
            const SizedBox(height: 16),
          ],
          if (email.isNotEmpty) ...[
            _buildContactItem(
              Icons.email,
              'البريد الإلكتروني',
              email,
              () => _sendEmail(email)),
          ],
          if (phone.isEmpty && email.isEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12)),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.grey[500], size: 20),
                  const SizedBox(width: 12),
                  Text(
                    'لا توجد معلومات تواصل متاحة',
                    style: CairoTextStyles.bodyMedium.copyWith(
                      color: Colors.grey[600])),
                ])),
        ]));
  }

  Widget _buildContactItem(IconData icon, String label, String value, VoidCallback onTap) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1))),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(icon, color: AppColors.primary, size: 18)),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(label, style: CairoTextStyles.labelMedium.copyWith(
                      color: Colors.grey[600])),
                    const SizedBox(height: 4),
                    Text(value, style: CairoTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w500)),
                  ])),
              Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
            ]))));
  }

  String _formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }

  void _handleAction(String action) {
    switch (action) {
      case 'call':
        final phone = _clientData!['phone'] ?? '';
        if (phone.isNotEmpty) {
          _makePhoneCall(phone);
        }
        break;
      case 'whatsapp':
        final phone = _clientData!['phone'] ?? '';
        if (phone.isNotEmpty) {
          _openWhatsApp(phone);
        }
        break;
      case 'delete':
        _showDeleteConfirmation();
        break;
    }
  }

  void _makePhoneCall(String phone) async {
    final uri = Uri.parse('tel:$phone');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _sendEmail(String email) async {
    final uri = Uri.parse('mailto:$email');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _openWhatsApp(String phone) async {
    final uri = Uri.parse('https://wa.me/$phone');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد الحذف', style: CairoTextStyles.headlineSmall),
        content: Text(
          'هل أنت متأكد من حذف هذا العميل؟ لا يمكن التراجع عن هذا الإجراء.',
          style: CairoTextStyles.bodyMedium),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: CairoTextStyles.button.copyWith(color: Colors.grey))),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteClient();
            },
            child: Text('حذف', style: CairoTextStyles.button.copyWith(color: Colors.red))),
        ]));
  }

  void _deleteClient() async {
    try {
      await FirebaseFirestore.instance
          .collection('clients')
          .doc(widget.clientId)
          .delete();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف العميل بنجاح', style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white)),
            backgroundColor: Colors.green));
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في حذف العميل', style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white)),
            backgroundColor: Colors.red));
      }
    }
  }

  Widget _buildPreferencesInfo() {
    final interest = _clientData!['interest'] ?? 'غير محدد';
    final budget = _clientData!['budget'] ?? 0.0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(Icons.interests, color: AppColors.primary, size: 20)),
              const SizedBox(width: 12),
              Text(
                'التفضيلات والاهتمامات',
                style: CairoTextStyles.headlineSmall),
            ]),
          const SizedBox(height: 20),
          _buildInfoRow('الاهتمام الرئيسي', interest, Icons.favorite),
          const SizedBox(height: 16),
          _buildInfoRow(
            'الميزانية المتوقعة',
            budget > 0 ? '${budget.toStringAsFixed(0)} د.ك' : 'غير محدد',
            Icons.attach_money),
        ]));
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 18, color: AppColors.primary),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label, style: CairoTextStyles.labelMedium.copyWith(
                  color: Colors.grey[600])),
                const SizedBox(height: 2),
                Text(value, style: CairoTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500)),
              ])),
        ]));
  }

  Widget _buildActivityInfo() {
    final totalDeals = _clientData!['totalDeals'] ?? 0;
    final totalValue = _clientData!['totalValue'] ?? 0.0;
    final lastContact = _clientData!['lastContact'] as Timestamp?;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(Icons.analytics, color: AppColors.primary, size: 20)),
              const SizedBox(width: 12),
              Text(
                'نشاط العميل',
                style: CairoTextStyles.headlineSmall),
            ]),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الصفقات',
                  totalDeals.toString(),
                  Icons.handshake,
                  Colors.blue)),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'القيمة الإجمالية',
                  '${totalValue.toStringAsFixed(0)} د.ك',
                  Icons.monetization_on,
                  Colors.green)),
            ]),
          if (lastContact != null) ...[
            const SizedBox(height: 20),
            _buildInfoRow(
              'آخر تواصل',
              _formatDate(lastContact.toDate()),
              Icons.schedule),
          ],
        ]));
  }

  Widget _buildNotesSection() {
    final notes = _clientData!['notes'] ?? '';

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1), width: 1)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8)),
                child: Icon(Icons.note, color: AppColors.primary, size: 20)),
              const SizedBox(width: 12),
              Text(
                'ملاحظات',
                style: CairoTextStyles.headlineSmall),
            ]),
          const SizedBox(height: 20),
          notes.isNotEmpty
              ? Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12)),
                  child: Text(
                    notes,
                    style: CairoTextStyles.bodyMedium))
              : Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12)),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.grey[500], size: 20),
                      const SizedBox(width: 12),
                      Text(
                        'لا توجد ملاحظات',
                        style: CairoTextStyles.bodyMedium.copyWith(
                          color: Colors.grey[600])),
                    ])),
        ]));
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1)),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12)),
            child: Icon(icon, color: color, size: 24)),
          const SizedBox(height: 16),
          Text(
            value,
            style: CairoTextStyles.headlineMedium.copyWith(
              color: color,
              fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          Text(
            title,
            style: CairoTextStyles.labelMedium.copyWith(
              color: Colors.grey[600]),
            textAlign: TextAlign.center),
        ]));
  }
}
