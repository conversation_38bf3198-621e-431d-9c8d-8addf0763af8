// lib/presentation/widgets/property_request/favorite_property_request_card.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/services/property_request_favorites_service.dart';
import '../../../core/theme/app_colors.dart';
import '../../../domain/models/favorites/favorite_property_request_model.dart';
import '../../pages/property_request/property_request_details_page.dart';

/// بطاقة طلب عقار مفضل
class FavoritePropertyRequestCard extends StatefulWidget {
  final FavoritePropertyRequestModel favorite;
  final VoidCallback? onRemove;

  const FavoritePropertyRequestCard({
    super.key,
    required this.favorite,
    this.onRemove,
  });

  @override
  State<FavoritePropertyRequestCard> createState() => _FavoritePropertyRequestCardState();
}

class _FavoritePropertyRequestCardState extends State<FavoritePropertyRequestCard> {
  final PropertyRequestFavoritesService _favoritesService = PropertyRequestFavoritesService();
  bool _isRemoving = false;

  /// إزالة من المفضلة
  Future<void> _removeFromFavorites() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إزالة من المفضلة',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'هل تريد إزالة هذا الطلب من المفضلة؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('إزالة', style: GoogleFonts.cairo(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _isRemoving = true);

      try {
        await _favoritesService.removeFromFavorites(widget.favorite.requestId);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إزالة الطلب من المفضلة'),
              backgroundColor: Colors.green,
            ),
          );
          widget.onRemove?.call();
        }
      } catch (e) {
        setState(() => _isRemoving = false);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في إزالة الطلب: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// عرض تفاصيل الطلب
  void _viewDetails() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PropertyRequestDetailsPage(
          requestId: widget.favorite.requestId,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _viewDetails,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس البطاقة
                Row(
                  children: [
                    // أيقونة نوع العقار
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        _getPropertyTypeIcon(),
                        color: AppColors.primary,
                        size: 20,
                      ),
                    ),
                    
                    const SizedBox(width: 12),
                    
                    // معلومات أساسية
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.favorite.requestTitle,
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.favorite.propertyType,
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color: AppColors.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // أولوية المفضلة
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Color(int.parse(widget.favorite.priorityColor.substring(1), radix: 16) + 0xFF000000)
                            .withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        widget.favorite.priorityText,
                        style: GoogleFonts.cairo(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: Color(int.parse(widget.favorite.priorityColor.substring(1), radix: 16) + 0xFF000000),
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // الوصف
                if (widget.favorite.requestDescription.isNotEmpty)
                  Text(
                    widget.favorite.requestDescription,
                    style: GoogleFonts.cairo(
                      fontSize: 13,
                      color: AppColors.textSecondary,
                      height: 1.4,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                
                const SizedBox(height: 12),
                
                // معلومات إضافية
                Row(
                  children: [
                    // المناطق
                    Expanded(
                      child: _buildInfoChip(
                        icon: Icons.location_on,
                        label: widget.favorite.locationsText,
                        color: Colors.blue,
                      ),
                    ),
                    
                    const SizedBox(width: 8),
                    
                    // نطاق السعر
                    Expanded(
                      child: _buildInfoChip(
                        icon: Icons.attach_money,
                        label: widget.favorite.priceRangeText,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // معلومات المالك والتاريخ
                Row(
                  children: [
                    // صاحب الطلب
                    Expanded(
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: 12,
                            backgroundColor: AppColors.primary.withValues(alpha: 0.2),
                            backgroundImage: widget.favorite.requestOwnerImage != null
                                ? NetworkImage(widget.favorite.requestOwnerImage!)
                                : null,
                            child: widget.favorite.requestOwnerImage == null
                                ? Icon(
                                    Icons.person,
                                    size: 14,
                                    color: AppColors.primary,
                                  )
                                : null,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              widget.favorite.requestOwnerName,
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: AppColors.textSecondary,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // تاريخ الإضافة
                    Text(
                      widget.favorite.ageText,
                      style: GoogleFonts.cairo(
                        fontSize: 11,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // أزرار الإجراءات
                Row(
                  children: [
                    // زر عرض التفاصيل
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: _viewDetails,
                        icon: const Icon(Icons.visibility, size: 16),
                        label: Text(
                          'عرض التفاصيل',
                          style: GoogleFonts.cairo(fontSize: 12),
                        ),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.primary,
                          side: BorderSide(color: AppColors.primary.withValues(alpha: 0.3)),
                          padding: const EdgeInsets.symmetric(vertical: 8),
                        ),
                      ),
                    ),
                    
                    const SizedBox(width: 8),
                    
                    // زر إزالة من المفضلة
                    OutlinedButton.icon(
                      onPressed: _isRemoving ? null : _removeFromFavorites,
                      icon: _isRemoving
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.favorite, size: 16),
                      label: Text(
                        'إزالة',
                        style: GoogleFonts.cairo(fontSize: 12),
                      ),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                        side: BorderSide(color: Colors.red.withValues(alpha: 0.3)),
                        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء رقاقة معلومات
  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 11,
                color: color,
                fontWeight: FontWeight.w600,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة نوع العقار
  IconData _getPropertyTypeIcon() {
    switch (widget.favorite.propertyType) {
      case 'شقة':
        return Icons.apartment;
      case 'فيلا':
      case 'منزل':
        return Icons.home;
      case 'أرض':
        return Icons.landscape;
      case 'مكتب':
        return Icons.business;
      case 'محل تجاري':
        return Icons.storefront;
      case 'مخزن':
        return Icons.warehouse;
      default:
        return Icons.home_work;
    }
  }
}
