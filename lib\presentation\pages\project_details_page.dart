import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/presentation/widgets/loading_widget.dart';
import 'package:kuwait_corners/presentation/widgets/project_background_widget.dart';
import 'package:kuwait_corners/presentation/pages/team_management_page.dart';
import 'package:kuwait_corners/presentation/pages/task_management_page.dart';
import 'package:kuwait_corners/presentation/pages/add_team_member_page.dart';
import 'package:kuwait_corners/presentation/pages/add_task_page.dart';
import 'package:kuwait_corners/presentation/pages/upload_document_page.dart';

/// صفحة تفاصيل المشروع
class ProjectDetailsPage extends StatefulWidget {
  final String projectId;

  const ProjectDetailsPage({
    super.key,
    required this.projectId,
  });

  @override
  State<ProjectDetailsPage> createState() => _ProjectDetailsPageState();
}

class _ProjectDetailsPageState extends State<ProjectDetailsPage> with TickerProviderStateMixin {
  Map<String, dynamic>? _projectData;
  bool _isLoading = true;
  late TabController _tabController;

  // بيانات الفريق والمهام
  List<Map<String, dynamic>> _teamMembers = [];
  List<Map<String, dynamic>> _projectTasks = [];
  bool _isLoadingTeam = false;
  bool _isLoadingTasks = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadProjectData();
    _loadTeamMembers();
    _loadProjectTasks();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadProjectData() async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('projects')
          .doc(widget.projectId)
          .get();

      if (doc.exists) {
        setState(() {
          _projectData = doc.data();
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadTeamMembers() async {
    setState(() {
      _isLoadingTeam = true;
    });

    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('projects')
          .doc(widget.projectId)
          .collection('team_members')
          .orderBy('joinDate', descending: false)
          .get();

      setState(() {
        _teamMembers = snapshot.docs.map((doc) {
          final data = doc.data();
          data['id'] = doc.id;
          return data;
        }).toList();
        _isLoadingTeam = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingTeam = false;
      });
    }
  }

  // إضافة alias للوظيفة
  void _loadTeamData() {
    _loadTeamMembers();
  }

  Future<void> _loadProjectTasks() async {
    setState(() {
      _isLoadingTasks = true;
    });

    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('projects')
          .doc(widget.projectId)
          .collection('tasks')
          .orderBy('createdAt', descending: false)
          .get();

      setState(() {
        _projectTasks = snapshot.docs.map((doc) {
          final data = doc.data();
          data['id'] = doc.id;
          return data;
        }).toList();
        _isLoadingTasks = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingTasks = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary.withValues(alpha: 0.02),
      appBar: AppBar(
        title: Text(
          'تفاصيل المشروع',
          style: CairoTextStyles.appBarTitle,
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          if (_projectData != null)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () async {
                final result = await Navigator.pushNamed(
                  context,
                  '/edit-project',
                  arguments: widget.projectId,
                );
                if (result == true) {
                  _loadProjectData();
                }
              },
            ),
        ],
        bottom: _isLoading || _projectData == null ? null : TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          labelStyle: CairoTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
          tabs: const [
            Tab(text: 'نظرة عامة', icon: Icon(Icons.dashboard, size: 20)),
            Tab(text: 'الفريق', icon: Icon(Icons.group, size: 20)),
            Tab(text: 'المهام', icon: Icon(Icons.task_alt, size: 20)),
            Tab(text: 'الوثائق', icon: Icon(Icons.folder, size: 20)),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: LoadingWidget())
          : _projectData == null
              ? _buildErrorState()
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildOverviewTab(),
                    _buildTeamTab(),
                    _buildTasksTab(),
                    _buildDocumentsTab(),
                  ],
                ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'لم يتم العثور على بيانات المشروع',
            style: CairoTextStyles.bodyLarge.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'العودة',
              style: CairoTextStyles.button,
            ),
          ),
        ],
      ),
    );
  }

  // تبويب النظرة العامة
  Widget _buildOverviewTab() {
    return ProjectBackgroundWidget(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProjectHeader(),
            const SizedBox(height: 20),
            _buildProjectInfo(),
            const SizedBox(height: 20),
            _buildProgressSection(),
            const SizedBox(height: 20),
            _buildDatesSection(),
          ],
        ),
      ),
    );
  }

  // تبويب الفريق
  Widget _buildTeamTab() {
    return ProjectBackgroundWidget(
      child: Column(
        children: [
          // شريط الإجراءات
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(color: Colors.grey[200]!, width: 1),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'أعضاء الفريق',
                    style: CairoTextStyles.titleLarge.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _addTeamMember,
                  icon: const Icon(Icons.person_add, size: 18),
                  label: Text(
                    'إضافة عضو',
                    style: CairoTextStyles.button.copyWith(fontSize: 14),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // قائمة أعضاء الفريق
          Expanded(
            child: _isLoadingTeam
                ? const Center(child: LoadingWidget())
                : _teamMembers.isEmpty
                    ? _buildEmptyTeamState()
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _teamMembers.length,
                        itemBuilder: (context, index) {
                          return _buildTeamMemberCard(_teamMembers[index]);
                        },
                      ),
          ),
        ],
      ),
    );
  }

  // تبويب المهام
  Widget _buildTasksTab() {
    return ProjectBackgroundWidget(
      child: Column(
        children: [
          // شريط الإجراءات
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(color: Colors.grey[200]!, width: 1),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'مهام المشروع',
                    style: CairoTextStyles.titleLarge.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _addTask,
                  icon: const Icon(Icons.add_task, size: 18),
                  label: Text(
                    'إضافة مهمة',
                    style: CairoTextStyles.button.copyWith(fontSize: 14),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // قائمة المهام
          Expanded(
            child: _isLoadingTasks
                ? const Center(child: LoadingWidget())
                : _projectTasks.isEmpty
                    ? _buildEmptyTasksState()
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _projectTasks.length,
                        itemBuilder: (context, index) {
                          return _buildTaskCard(_projectTasks[index]);
                        },
                      ),
          ),
        ],
      ),
    );
  }

  // تبويب الوثائق
  Widget _buildDocumentsTab() {
    return ProjectBackgroundWidget(
      child: Column(
        children: [
          // شريط الإجراءات
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(color: Colors.grey[200]!, width: 1),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'وثائق المشروع',
                    style: CairoTextStyles.titleLarge.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _uploadDocument,
                  icon: const Icon(Icons.upload_file, size: 18),
                  label: Text(
                    'رفع وثيقة',
                    style: CairoTextStyles.button.copyWith(fontSize: 14),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // قائمة الوثائق
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.folder_open, size: 64, color: Colors.grey[400]),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد وثائق مرفوعة',
                    style: CairoTextStyles.bodyLarge.copyWith(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'المشروع يفتقد لوثائق داعمة',
                    style: CairoTextStyles.bodyMedium.copyWith(color: Colors.orange),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProjectHeader() {
    final name = _projectData!['name'] ?? 'مشروع غير محدد';
    final description = _projectData!['description'] ?? '';
    final status = _projectData!['status'] ?? 'قيد التخطيط';
    final priority = _projectData!['priority'] ?? 'متوسط';
    final type = _projectData!['type'] ?? 'سكني';

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  name,
                  style: CairoTextStyles.headlineSmall,
                ),
              ),
              _buildStatusChip(status),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(Icons.category, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 6),
              Text(
                type,
                style: CairoTextStyles.bodyMedium,
              ),
              const Spacer(),
              _buildPriorityChip(priority),
            ],
          ),
          if (description.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'الوصف',
              style: CairoTextStyles.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: CairoTextStyles.bodyMedium,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProjectInfo() {
    final budget = _projectData!['budget'] ?? 0.0;
    final spentBudget = _projectData!['spentBudget'] ?? 0.0;
    final teamSize = _projectData!['teamSize'] ?? 0;
    final tasksCount = _projectData!['tasksCount'] ?? 0;
    final completedTasks = _projectData!['completedTasks'] ?? 0;
    final location = _projectData!['location'] ?? '';

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات المشروع',
            style: CairoTextStyles.titleLarge,
          ),
          const SizedBox(height: 16),

          // الميزانية
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'الميزانية الإجمالية',
                  '${budget.toStringAsFixed(0)} د.ك',
                  Icons.account_balance_wallet,
                  AppColors.primary,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildInfoItem(
                  'المبلغ المُنفق',
                  '${spentBudget.toStringAsFixed(0)} د.ك',
                  Icons.money_off,
                  Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // الفريق والمهام
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  'حجم الفريق',
                  '$teamSize عضو',
                  Icons.group,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildInfoItem(
                  'المهام',
                  '$completedTasks / $tasksCount',
                  Icons.task_alt,
                  Colors.purple,
                ),
              ),
            ],
          ),

          // الموقع
          if (location.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildInfoItem(
              'الموقع',
              location,
              Icons.location_on,
              Colors.red,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoItem(String title, String value, IconData icon, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 6),
            Text(
              title,
              style: CairoTextStyles.bodySmall.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: CairoTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildProgressSection() {
    final progress = _projectData!['progress'] ?? 0.0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'تقدم المشروع',
                style: CairoTextStyles.titleLarge,
              ),
              Text(
                '${progress.toStringAsFixed(1)}%',
                style: CairoTextStyles.titleLarge.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: progress / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              progress >= 75 ? Colors.green :
              progress >= 50 ? AppColors.primary : Colors.orange,
            ),
            minHeight: 8,
          ),
        ],
      ),
    );
  }

  Widget _buildDatesSection() {
    final startDate = _projectData!['startDate'] as Timestamp?;
    final endDate = _projectData!['endDate'] as Timestamp?;

    if (startDate == null && endDate == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التواريخ المهمة',
            style: CairoTextStyles.titleLarge,
          ),
          const SizedBox(height: 16),

          if (startDate != null)
            _buildDateItem(
              'تاريخ البداية',
              _formatDate(startDate.toDate()),
              Icons.play_arrow,
              Colors.green,
            ),

          if (startDate != null && endDate != null)
            const SizedBox(height: 12),

          if (endDate != null)
            _buildDateItem(
              'تاريخ الانتهاء المتوقع',
              _formatDate(endDate.toDate()),
              Icons.flag,
              Colors.red,
            ),

          if (startDate != null && endDate != null) ...[
            const SizedBox(height: 12),
            _buildDateItem(
              'المدة الإجمالية',
              '${endDate.toDate().difference(startDate.toDate()).inDays} يوم',
              Icons.timer,
              AppColors.primary,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDateItem(String title, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, size: 20, color: color),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: CairoTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                value,
                style: CairoTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إجراءات المشروع',
            style: CairoTextStyles.titleLarge,
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _navigateToTeam(),
                  icon: const Icon(Icons.group, size: 20),
                  label: Text(
                    'إدارة الفريق',
                    style: CairoTextStyles.button,
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _navigateToTasks(),
                  icon: const Icon(Icons.task_alt, size: 20),
                  label: Text(
                    'إدارة المهام',
                    style: CairoTextStyles.button,
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _archiveProject(),
                  icon: const Icon(Icons.archive, size: 20),
                  label: Text(
                    'أرشفة المشروع',
                    style: CairoTextStyles.button.copyWith(
                      color: Colors.orange,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.orange,
                    side: const BorderSide(color: Colors.orange),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _deleteProject(),
                  icon: const Icon(Icons.delete, size: 20),
                  label: Text(
                    'حذف المشروع',
                    style: CairoTextStyles.button.copyWith(
                      color: Colors.red,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.red,
                    side: const BorderSide(color: Colors.red),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    switch (status) {
      case 'قيد التنفيذ':
        color = Colors.blue;
        break;
      case 'مكتمل':
        color = Colors.green;
        break;
      case 'متوقف':
        color = Colors.orange;
        break;
      case 'ملغي':
        color = Colors.red;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        status,
        style: CairoTextStyles.bodySmall.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildPriorityChip(String priority) {
    Color color;
    IconData icon;
    switch (priority) {
      case 'عالي':
        color = Colors.red;
        icon = Icons.keyboard_arrow_up;
        break;
      case 'متوسط':
        color = Colors.orange;
        icon = Icons.remove;
        break;
      case 'منخفض':
        color = Colors.green;
        icon = Icons.keyboard_arrow_down;
        break;
      default:
        color = Colors.grey;
        icon = Icons.remove;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            priority,
            style: CairoTextStyles.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // وظائف مساعدة للعرض
  String _getRoleDisplayName(String role) {
    const roleNames = {
      'manager': 'مدير',
      'supervisor': 'مشرف',
      'engineer': 'مهندس',
      'architect': 'معماري',
      'contractor': 'مقاول',
      'worker': 'عامل',
      'consultant': 'استشاري',
    };
    return roleNames[role] ?? role;
  }

  Widget _buildTaskStatusChip(String status) {
    Color color;
    String displayText;

    switch (status) {
      case 'pending':
        color = Colors.orange;
        displayText = 'معلقة';
        break;
      case 'inProgress':
        color = Colors.blue;
        displayText = 'قيد التنفيذ';
        break;
      case 'completed':
        color = Colors.green;
        displayText = 'مكتملة';
        break;
      case 'cancelled':
        color = Colors.red;
        displayText = 'ملغية';
        break;
      case 'onHold':
        color = Colors.grey;
        displayText = 'متوقفة';
        break;
      default:
        color = Colors.grey;
        displayText = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color),
      ),
      child: Text(
        displayText,
        style: CairoTextStyles.bodySmall.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildTaskPriorityChip(String priority) {
    Color color;
    IconData icon;
    String displayText;

    switch (priority) {
      case 'urgent':
        color = Colors.purple;
        icon = Icons.priority_high;
        displayText = 'عاجل';
        break;
      case 'high':
        color = Colors.red;
        icon = Icons.keyboard_arrow_up;
        displayText = 'عالي';
        break;
      case 'medium':
        color = Colors.orange;
        icon = Icons.remove;
        displayText = 'متوسط';
        break;
      case 'low':
        color = Colors.green;
        icon = Icons.keyboard_arrow_down;
        displayText = 'منخفض';
        break;
      default:
        color = Colors.grey;
        icon = Icons.remove;
        displayText = priority;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            displayText,
            style: CairoTextStyles.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // معالجة إجراءات أعضاء الفريق
  void _handleTeamMemberAction(String action, String memberId) {
    switch (action) {
      case 'view':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'عرض تفاصيل العضو قيد التطوير',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
            ),
            backgroundColor: AppColors.primary,
          ),
        );
        break;
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تعديل العضو قيد التطوير',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
            ),
            backgroundColor: AppColors.primary,
          ),
        );
        break;
      case 'remove':
        _confirmRemoveTeamMember(memberId);
        break;
    }
  }

  // معالجة إجراءات المهام
  void _handleTaskAction(String action, String taskId) {
    switch (action) {
      case 'view':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'عرض تفاصيل المهمة قيد التطوير',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
            ),
            backgroundColor: AppColors.primary,
          ),
        );
        break;
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تعديل المهمة قيد التطوير',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
            ),
            backgroundColor: AppColors.primary,
          ),
        );
        break;
      case 'complete':
        _completeTask(taskId);
        break;
      case 'delete':
        _confirmDeleteTask(taskId);
        break;
    }
  }

  void _confirmRemoveTeamMember(String memberId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد الإزالة',
          style: CairoTextStyles.titleMedium,
        ),
        content: Text(
          'هل أنت متأكد من إزالة هذا العضو من فريق المشروع؟',
          style: CairoTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'إلغاء',
              style: CairoTextStyles.bodyMedium,
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'إزالة',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      // TODO: تنفيذ إزالة العضو
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم إزالة العضو من الفريق',
            style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _completeTask(String taskId) async {
    // TODO: تنفيذ إكمال المهمة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم إكمال المهمة',
          style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _confirmDeleteTask(String taskId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد الحذف',
          style: CairoTextStyles.titleMedium,
        ),
        content: Text(
          'هل أنت متأكد من حذف هذه المهمة؟ لا يمكن التراجع عن هذا الإجراء.',
          style: CairoTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'إلغاء',
              style: CairoTextStyles.bodyMedium,
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'حذف',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      // TODO: تنفيذ حذف المهمة
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم حذف المهمة',
            style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  // وظائف الإجراءات
  void _addTeamMember() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddTeamMemberPage(projectId: widget.projectId),
      ),
    );

    if (result == true) {
      // إعادة تحميل بيانات الفريق
      _loadTeamData();
    }
  }

  void _addTask() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddTaskPage(projectId: widget.projectId),
      ),
    );

    if (result == true) {
      // إعادة تحميل بيانات المهام
      _loadProjectTasks();
    }
  }

  void _uploadDocument() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UploadDocumentPage(projectId: widget.projectId),
      ),
    );

    if (result == true && mounted) {
      // إعادة تحميل بيانات الوثائق
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم رفع الوثيقة بنجاح',
            style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _navigateToTeam() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TeamManagementPage(),
      ),
    );
  }

  void _navigateToTasks() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TaskManagementPage(projectId: widget.projectId),
      ),
    );
  }

  // بناء حالات فارغة
  Widget _buildEmptyTeamState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.group_off, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'لا يوجد أعضاء في الفريق',
            style: CairoTextStyles.bodyLarge.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على "إضافة عضو" لبدء بناء فريق المشروع',
            style: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyTasksState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.task_alt, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'لا توجد مهام في المشروع',
            style: CairoTextStyles.bodyLarge.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على "إضافة مهمة" لبدء تنظيم مهام المشروع',
            style: CairoTextStyles.bodyMedium.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // بطاقة عضو الفريق
  Widget _buildTeamMemberCard(Map<String, dynamic> memberData) {
    final name = memberData['fullName'] ?? 'غير محدد';
    final role = memberData['role'] ?? 'عضو';
    final email = memberData['email'] ?? '';
    final image = memberData['image'];

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.1), width: 1),
      ),
      child: Row(
        children: [
          // صورة العضو
          CircleAvatar(
            radius: 24,
            backgroundColor: AppColors.primary.withValues(alpha: 0.1),
            backgroundImage: image != null ? NetworkImage(image) : null,
            child: image == null
                ? Icon(Icons.person, color: AppColors.primary, size: 24)
                : null,
          ),
          const SizedBox(width: 16),

          // معلومات العضو
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: CairoTextStyles.cardTitle,
                ),
                const SizedBox(height: 4),
                Text(
                  _getRoleDisplayName(role),
                  style: CairoTextStyles.bodySmall.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (email.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    email,
                    style: CairoTextStyles.bodySmall.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),

          // أزرار الإجراءات
          PopupMenuButton<String>(
            onSelected: (value) => _handleTeamMemberAction(value, memberData['id']),
            icon: Icon(Icons.more_vert, color: Colors.grey[600]),
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'view',
                child: Row(
                  children: [
                    const Icon(Icons.visibility, size: 16),
                    const SizedBox(width: 8),
                    Text('عرض', style: CairoTextStyles.bodyMedium),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    const Icon(Icons.edit, size: 16),
                    const SizedBox(width: 8),
                    Text('تعديل', style: CairoTextStyles.bodyMedium),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'remove',
                child: Row(
                  children: [
                    const Icon(Icons.remove_circle, size: 16, color: Colors.red),
                    const SizedBox(width: 8),
                    Text(
                      'إزالة',
                      style: CairoTextStyles.bodyMedium.copyWith(color: Colors.red),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بطاقة المهمة
  Widget _buildTaskCard(Map<String, dynamic> taskData) {
    final title = taskData['title'] ?? 'مهمة غير محددة';
    final status = taskData['status'] ?? 'pending';
    final priority = taskData['priority'] ?? 'medium';
    final progress = (taskData['progress'] ?? 0.0).toDouble();
    final assignedToName = taskData['assignedToName'] ?? '';
    final dueDate = taskData['dueDate'] as Timestamp?;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الصف الأول: العنوان والحالة
          Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: CairoTextStyles.cardTitle,
                ),
              ),
              const SizedBox(width: 12),
              _buildTaskStatusChip(status),
            ],
          ),
          const SizedBox(height: 12),

          // الصف الثاني: الأولوية والمسؤول
          Row(
            children: [
              _buildTaskPriorityChip(priority),
              const Spacer(),
              if (assignedToName.isNotEmpty) ...[
                Icon(Icons.person, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  assignedToName,
                  style: CairoTextStyles.bodySmall,
                ),
              ],
            ],
          ),
          const SizedBox(height: 12),

          // شريط التقدم
          Row(
            children: [
              Text(
                'التقدم',
                style: CairoTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                '${progress.toStringAsFixed(0)}%',
                style: CairoTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          LinearProgressIndicator(
            value: progress / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              progress >= 75 ? Colors.green :
              progress >= 50 ? AppColors.primary : Colors.orange,
            ),
          ),

          // تاريخ الاستحقاق
          if (dueDate != null) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 6),
                Text(
                  'الاستحقاق: ${_formatDate(dueDate.toDate())}',
                  style: CairoTextStyles.bodySmall,
                ),
                const Spacer(),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleTaskAction(value, taskData['id']),
                  icon: Icon(Icons.more_vert, color: Colors.grey[600], size: 20),
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'view',
                      child: Row(
                        children: [
                          const Icon(Icons.visibility, size: 16),
                          const SizedBox(width: 8),
                          Text('عرض', style: CairoTextStyles.bodyMedium),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          const Icon(Icons.edit, size: 16),
                          const SizedBox(width: 8),
                          Text('تعديل', style: CairoTextStyles.bodyMedium),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'complete',
                      child: Row(
                        children: [
                          const Icon(Icons.check_circle, size: 16, color: Colors.green),
                          const SizedBox(width: 8),
                          Text(
                            'إكمال',
                            style: CairoTextStyles.bodyMedium.copyWith(color: Colors.green),
                          ),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          const Icon(Icons.delete, size: 16, color: Colors.red),
                          const SizedBox(width: 8),
                          Text(
                            'حذف',
                            style: CairoTextStyles.bodyMedium.copyWith(color: Colors.red),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  void _archiveProject() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد الأرشفة',
          style: CairoTextStyles.titleMedium,
        ),
        content: Text(
          'هل أنت متأكد من أرشفة هذا المشروع؟',
          style: CairoTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'إلغاء',
              style: CairoTextStyles.bodyMedium,
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'أرشفة',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.orange),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await FirebaseFirestore.instance
            .collection('projects')
            .doc(widget.projectId)
            .update({
          'status': 'مؤرشف',
          'archivedAt': FieldValue.serverTimestamp(),
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم أرشفة المشروع بنجاح',
                style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
              ),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'حدث خطأ في أرشفة المشروع',
                style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _deleteProject() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد الحذف',
          style: CairoTextStyles.titleMedium,
        ),
        content: Text(
          'هل أنت متأكد من حذف هذا المشروع؟ لا يمكن التراجع عن هذا الإجراء.',
          style: CairoTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'إلغاء',
              style: CairoTextStyles.bodyMedium,
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'حذف',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await FirebaseFirestore.instance
            .collection('projects')
            .doc(widget.projectId)
            .delete();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم حذف المشروع بنجاح',
                style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
              ),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'حدث خطأ في حذف المشروع',
                style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
