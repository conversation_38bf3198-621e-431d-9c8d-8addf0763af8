{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAAA,gDAAgD;AAChD,wCAAwC;AAExC,uBAAuB;AACvB,KAAK,CAAC,aAAa,EAAE,CAAC;AAEtB,kBAAkB;AAClB,uFAAyF;AACzF,8DAA4D;AAE5D,gBAAgB;AACH,QAAA,oBAAoB,GAAG,wDAA6B,CAAC;AACrD,QAAA,aAAa,GAAG,gCAAe,CAAC;AAE7C,0DAA0D;AAC7C,QAAA,6BAA6B,GAAG,SAAS,CAAC,MAAM;KAC1D,QAAQ,CAAC,iBAAiB,CAAC;KAC3B,QAAQ,CAAC,aAAa,CAAC;KACvB,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;IACxB,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IAEnD,IAAI;QACF,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC7B,MAAM,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;QAE5C,6CAA6C;QAC7C,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,wBAAwB,CAAC;aACxD,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC;aAC/B,KAAK,CAAC,eAAe,EAAE,IAAI,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC;aAC1C,KAAK,CAAC,EAAE,CAAC,CAAC,mCAAmC;aAC7C,GAAG,EAAE,CAAC;QAET,IAAI,KAAK,CAAC,KAAK,EAAE;YACf,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YACjD,OAAO,IAAI,CAAC;SACb;QAED,OAAO,CAAC,GAAG,CAAC,oBAAoB,KAAK,CAAC,IAAI,CAAC,MAAM,iBAAiB,CAAC,CAAC;QAEpE,kBAAkB;QAClB,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;QACzB,MAAM,QAAQ,GAAoB,EAAE,CAAC;QAErC,KAAK,MAAM,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE;YAC5B,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;YAExB,IAAI,IAAI,CAAC,IAAI,KAAK,qBAAqB,EAAE;gBACvC,QAAQ,CAAC,IAAI,CAAC,8BAA8B,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;aAC7D;YAED,qCAAqC;YACrC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE;gBACpB,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE;aAC1B,CAAC,CAAC;YAEH,mCAAmC;YACnC,IAAI,IAAI,CAAC,IAAI,KAAK,qBAAqB,EAAE;gBACvC,QAAQ,CAAC,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,CAAC,CAAC;aAC1D;SACF;QAED,sBAAsB;QACtB,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,KAAK,CAAC,MAAM,EAAE;YACd,GAAG,QAAQ;SACZ,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC;KAEb;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC5D,MAAM,KAAK,CAAC;KACb;AACH,CAAC,CAAC,CAAC;AAEL;;GAEG;AACH,KAAK,UAAU,8BAA8B,CAAC,gBAAqB,EAAE,eAAuB;;IAC1F,IAAI;QACF,OAAO,CAAC,GAAG,CAAC,kCAAkC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE3E,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC7B,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEpC,+CAA+C;QAC/C,MAAM,SAAS,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC;QACtF,IAAI,CAAC,SAAS,CAAC,MAAM,KAAI,MAAA,SAAS,CAAC,IAAI,EAAE,0CAAE,UAAU,CAAA,EAAE;YACrD,OAAO,CAAC,GAAG,CAAC,aAAa,gBAAgB,CAAC,QAAQ,sCAAsC,CAAC,CAAC;YAC1F,OAAO;SACR;QAED,OAAO,CAAC,GAAG,CAAC,iCAAiC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE1E,iCAAiC;QACjC,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;QAChF,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAEhC,+BAA+B;QAC/B,MAAM,iBAAiB,GAAG,4BAA4B,CAAC;QACvD,MAAM,gBAAgB,GAAG,kDAAkD,gBAAgB,CAAC,WAAW;;;;;;;;;;;;;wCAanE,CAAC;QAErC,IAAI,QAAQ,IAAI,QAAQ,CAAC,QAAQ,EAAE;YACjC,mBAAmB;YACnB,MAAM,OAAO,GAAG;gBACd,KAAK,EAAE,QAAQ,CAAC,QAAQ;gBACxB,YAAY,EAAE;oBACZ,KAAK,EAAE,iBAAiB;oBACxB,IAAI,EAAE,mDAAmD;iBAC1D;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,qBAAqB;oBAC3B,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;oBACnC,WAAW,EAAE,gBAAgB,CAAC,WAAW;oBACzC,QAAQ,EAAE,gBAAgB;oBAC1B,KAAK,EAAE,kBAAkB;oBACzB,WAAW,EAAE,gBAAgB;iBAC9B;gBACD,OAAO,EAAE;oBACP,YAAY,EAAE;wBACZ,IAAI,EAAE,iBAAiB;wBACvB,KAAK,EAAE,SAAS;wBAChB,KAAK,EAAE,SAAS;wBAChB,SAAS,EAAE,2BAA2B;qBACvC;oBACD,QAAQ,EAAE,MAAgB;iBAC3B;gBACD,IAAI,EAAE;oBACJ,OAAO,EAAE;wBACP,GAAG,EAAE;4BACH,KAAK,EAAE,SAAS;4BAChB,KAAK,EAAE,CAAC;4BACR,KAAK,EAAE;gCACL,KAAK,EAAE,iBAAiB;gCACxB,IAAI,EAAE,mDAAmD;6BAC1D;yBACF;qBACF;iBACF;aACF,CAAC;YAEF,IAAI;gBACF,MAAM,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC9B,OAAO,CAAC,GAAG,CAAC,mCAAmC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;aAC3E;YAAC,OAAO,SAAS,EAAE;gBAClB,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,SAAS,CAAC,CAAC;aACxD;SACF;QAED,+CAA+C;QAC/C,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;aACzB,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC;aAC5B,UAAU,CAAC,eAAe,CAAC;aAC3B,GAAG,CAAC;YACH,KAAK,EAAE,iBAAiB;YACxB,IAAI,EAAE,gBAAgB;YACtB,IAAI,EAAE,qBAAqB;YAC3B,IAAI,EAAE;gBACJ,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;gBACnC,WAAW,EAAE,gBAAgB,CAAC,WAAW;gBACzC,WAAW,EAAE;oBACX,QAAQ,EAAE,gBAAgB;oBAC1B,KAAK,EAAE,kBAAkB;iBAC1B;aACF;YACD,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE;YAC1C,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;QAEL,OAAO,CAAC,GAAG,CAAC,uCAAuC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;QAE9E,oCAAoC;QACpC,MAAM,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC;YACpC,IAAI,EAAE,+BAA+B;YACrC,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;YACnC,MAAM,EAAE,gBAAgB,CAAC,MAAM;YAC/B,WAAW,EAAE,gBAAgB,CAAC,WAAW;YACzC,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE;YAC1C,OAAO,EAAE,8CAA8C;SACxD,CAAC,CAAC;KAEJ;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,oCAAoC,gBAAgB,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QACvF,MAAM,KAAK,CAAC;KACb;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,mCAAmC,CAAC,gBAAqB;IACtE,IAAI;QACF,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE7B,4CAA4C;QAC5C,MAAM,iBAAiB,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;QAEvE,gCAAgC;QAChC,MAAM,EAAE,CAAC,UAAU,CAAC,wBAAwB,CAAC,CAAC,GAAG,CAAC;YAChD,IAAI,EAAE,qBAAqB;YAC3B,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;YACnC,MAAM,EAAE,gBAAgB,CAAC,MAAM;YAC/B,WAAW,EAAE,gBAAgB,CAAC,WAAW;YACzC,aAAa,EAAE,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CAAC;YACpE,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE;YAC1C,SAAS,EAAE,KAAK;YAChB,gBAAgB,EAAE;gBAChB,KAAK,EAAE,4BAA4B;gBACnC,WAAW,EAAE;oBACX,QAAQ,EAAE,gBAAgB;oBAC1B,KAAK,EAAE,kBAAkB;iBAC1B;aACF;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,8CAA8C,gBAAgB,CAAC,QAAQ,OAAO,iBAAiB,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;KAE9H;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,2CAA2C,KAAK,EAAE,CAAC,CAAC;KACnE;AACH,CAAC"}