// lib/core/services/enhanced_subscription_service.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

import '../config/app_mode_config.dart';
import '../models/plan_model.dart';
import '../models/user_subscription_model.dart';
import 'notification_service.dart';

/// خدمة الاشتراكات المحسنة
/// توفر وظائف لإدارة اشتراكات المستخدمين والتحقق من الصلاحيات
class EnhancedSubscriptionService {
  /// مثيل Firestore
  final FirebaseFirestore _firestore;
  
  /// مثيل مصادقة Firebase
  final FirebaseAuth _auth;
  
  /// خدمة الإشعارات
  final NotificationService? _notificationService;
  
  /// القيم الافتراضية للباقات
  final Map<PlanType, Map<String, dynamic>> _subscriptionDefaults = {
    PlanType.free: {
      'allowedAds': 3,
      'allowedImagesPerAd': 5,
      'adDurationDays': 30,
      'price': 0.0,
    },
    PlanType.bronze: {
      'allowedAds': 10,
      'allowedImagesPerAd': 10,
      'adDurationDays': 30,
      'price': 10.0,
    },
    PlanType.silver: {
      'allowedAds': 20,
      'allowedImagesPerAd': 15,
      'adDurationDays': 45,
      'price': 20.0,
    },
    PlanType.gold: {
      'allowedAds': 50,
      'allowedImagesPerAd': 20,
      'adDurationDays': 60,
      'price': 30.0,
    },
  };

  EnhancedSubscriptionService({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
    NotificationService? notificationService,
  }) : _firestore = firestore ?? FirebaseFirestore.instance,
       _auth = auth ?? FirebaseAuth.instance,
       _notificationService = notificationService;

  /// الحصول على جميع الباقات المتاحة
  Future<List<PlanModel>> getAllPlans() async {
    try {
      final snapshot = await _firestore
          .collection('plans')
          .where('isActive', isEqualTo: true)
          .get();
      
      if (snapshot.docs.isEmpty) {
        // إذا لم تكن هناك باقات، استخدم الباقات الافتراضية
        return PlanModel.getDefaultPlans();
      }
      
      return snapshot.docs
          .map((doc) => PlanModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على الباقات: $e');
      // إرجاع الباقات الافتراضية في حالة الخطأ
      return PlanModel.getDefaultPlans();
    }
  }
  
  /// الحصول على باقة محددة
  Future<PlanModel?> getPlan(String planId) async {
    try {
      final doc = await _firestore
          .collection('plans')
          .doc(planId)
          .get();
      
      if (!doc.exists) {
        // إذا لم تكن الباقة موجودة، ابحث في الباقات الافتراضية
        final defaultPlans = PlanModel.getDefaultPlans();
        return defaultPlans.firstWhere(
          (plan) => plan.id == planId,
          orElse: () => defaultPlans.first);
      }
      
      return PlanModel.fromFirestore(doc);
    } catch (e) {
      debugPrint('خطأ في الحصول على الباقة: $e');
      return null;
    }
  }
  
  /// الحصول على اشتراك المستخدم الحالي
  Future<UserSubscriptionModel?> getCurrentSubscription() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return null;
      }
      
      final snapshot = await _firestore
          .collection('subscriptions')
          .where('userId', isEqualTo: user.uid)
          .where('isActive', isEqualTo: true)
          .orderBy('endDate', descending: true)
          .limit(1)
          .get();
      
      if (snapshot.docs.isEmpty) {
        // إنشاء اشتراك مجاني افتراضي إذا لم يكن هناك اشتراك نشط
        return await _createFreeSubscription(user.uid);
      }
      
      final subscription = UserSubscriptionModel.fromFirestore(snapshot.docs.first);
      
      // التحقق من انتهاء الاشتراك
      if (subscription.isExpired()) {
        await _deactivateSubscription(subscription.id);
        
        // إنشاء اشتراك مجاني جديد
        return await _createFreeSubscription(user.uid);
      }
      
      return subscription;
    } catch (e) {
      debugPrint('خطأ في الحصول على الاشتراك الحالي: $e');
      return null;
    }
  }
  
  /// إنشاء اشتراك مجاني
  Future<UserSubscriptionModel> _createFreeSubscription(String userId) async {
    try {
      final defaults = _subscriptionDefaults[PlanType.free]!;
      
      final subscriptionId = _firestore.collection('subscriptions').doc().id;
      
      final subscription = UserSubscriptionModel(
        id: subscriptionId,
        userId: userId,
        planId: 'free',
        planType: PlanType.free,
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 365)), // اشتراك مجاني لمدة سنة
        allowedAds: defaults['allowedAds'],
        remainingAds: defaults['allowedAds'],
        allowedImagesPerAd: defaults['allowedImagesPerAd'],
        adDurationDays: defaults['adDurationDays'],
        isActive: true,
        autoRenew: true,
        price: defaults['price'],
        currency: 'KWD',
        enabledFeatures: {
          'autoRepublish': false,
          'kuwaitCornersPin': false,
          'movingAd': false,
          'vipBadge': false,
          'pinnedOnHome': false,
        },
        metadata: {});
      
      await _firestore
          .collection('subscriptions')
          .doc(subscriptionId)
          .set(subscription.toFirestore());
      
      return subscription;
    } catch (e) {
      debugPrint('خطأ في إنشاء اشتراك مجاني: $e');
      
      // إرجاع اشتراك مجاني افتراضي في حالة الخطأ
      return UserSubscriptionModel(
        id: 'default',
        userId: userId,
        planId: 'free',
        planType: PlanType.free,
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 365)),
        allowedAds: 3,
        remainingAds: 3,
        allowedImagesPerAd: 5,
        adDurationDays: 30,
        isActive: true,
        autoRenew: true,
        price: 0.0,
        currency: 'KWD',
        enabledFeatures: {
          'autoRepublish': false,
          'kuwaitCornersPin': false,
          'movingAd': false,
          'vipBadge': false,
          'pinnedOnHome': false,
        },
        metadata: {});
    }
  }
  
  /// إلغاء تنشيط اشتراك
  Future<void> _deactivateSubscription(String subscriptionId) async {
    try {
      await _firestore
          .collection('subscriptions')
          .doc(subscriptionId)
          .update({
        'isActive': false,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('خطأ في إلغاء تنشيط الاشتراك: $e');
    }
  }
  
  /// التحقق مما إذا كان المستخدم يمكنه إنشاء إعلان - معطل في الوضع المعلوماتي
  Future<bool> canCreateAd(String? userId) async {
    // في الوضع المعلوماتي، السماح بإنشاء الإعلانات بدون قيود
    if (AppModeConfig.isInformationalOnly) {
      return true;
    }

    try {
      final user = userId != null ? await _firestore.collection('users').doc(userId).get() : null;
      final uid = userId ?? _auth.currentUser?.uid;

      if (uid == null) {
        return false;
      }

      final subscription = await getCurrentSubscription();
      if (subscription == null) {
        return false;
      }

      return subscription.remainingAds > 0;
    } catch (e) {
      debugPrint('خطأ في التحقق من إمكانية إنشاء إعلان: $e');
      return false;
    }
  }
  
  /// الحصول على عدد الإعلانات المتبقية - معطل في الوضع المعلوماتي
  Future<int> getRemainingAds(String? userId) async {
    // في الوضع المعلوماتي، إرجاع عدد غير محدود
    if (AppModeConfig.isInformationalOnly) {
      return 999;
    }

    try {
      final uid = userId ?? _auth.currentUser?.uid;

      if (uid == null) {
        return 0;
      }

      final subscription = await getCurrentSubscription();
      if (subscription == null) {
        return 0;
      }

      return subscription.remainingAds;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد الإعلانات المتبقية: $e');
      return 0;
    }
  }
  
  /// التحقق مما إذا كان المستخدم يمكنه استخدام ميزة
  Future<bool> canUseFeature(String featureId, String? userId) async {
    try {
      final uid = userId ?? _auth.currentUser?.uid;
      
      if (uid == null) {
        return false;
      }
      
      final subscription = await getCurrentSubscription();
      if (subscription == null) {
        return false;
      }
      
      return subscription.isFeatureEnabled(featureId);
    } catch (e) {
      debugPrint('خطأ في التحقق من إمكانية استخدام الميزة: $e');
      return false;
    }
  }
  
  /// التحقق مما إذا كانت الترقية مطلوبة لاستخدام ميزة
  Future<bool> upgradeRequired(String featureId, String? userId) async {
    try {
      final uid = userId ?? _auth.currentUser?.uid;
      
      if (uid == null) {
        return true;
      }
      
      final subscription = await getCurrentSubscription();
      if (subscription == null) {
        return true;
      }
      
      // إذا كانت الميزة مفعلة، لا حاجة للترقية
      if (subscription.isFeatureEnabled(featureId)) {
        return false;
      }
      
      // التحقق مما إذا كانت هناك باقة أعلى تتيح هذه الميزة
      final plans = await getAllPlans();
      
      // ترتيب الباقات حسب السعر (من الأقل إلى الأعلى)
      plans.sort((a, b) => a.price.compareTo(b.price));
      
      // البحث عن باقة تتيح هذه الميزة
      for (final plan in plans) {
        if (plan.price > subscription.price && plan.isFeatureAllowed(featureId)) {
          return true;
        }
      }
      
      return false;
    } catch (e) {
      debugPrint('خطأ في التحقق من الحاجة للترقية: $e');
      return true;
    }
  }
  
  /// تقليل عدد الإعلانات المتبقية
  Future<bool> decrementRemainingAds(String? userId) async {
    try {
      final uid = userId ?? _auth.currentUser?.uid;
      
      if (uid == null) {
        return false;
      }
      
      final subscription = await getCurrentSubscription();
      if (subscription == null || subscription.remainingAds <= 0) {
        return false;
      }
      
      // تقليل عدد الإعلانات المتبقية
      subscription.decrementRemainingAds();
      
      // تحديث الاشتراك في Firestore
      await _firestore
          .collection('subscriptions')
          .doc(subscription.id)
          .update({
        'remainingAds': subscription.remainingAds,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      return true;
    } catch (e) {
      debugPrint('خطأ في تقليل عدد الإعلانات المتبقية: $e');
      return false;
    }
  }
}
