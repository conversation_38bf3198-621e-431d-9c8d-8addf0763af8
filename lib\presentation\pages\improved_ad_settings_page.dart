// lib/presentation/pages/improved_ad_settings_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/config/app_mode_config.dart';
import '../../core/services/enhanced_ad_draft_service.dart';
import '../../core/services/enhanced_subscription_service.dart';
import '../../domain/entities/estate.dart';
import '../bloc/improved_ad_bloc.dart';
import '../widgets/ad_creation_navigation_buttons.dart';
import '../widgets/improved_ad_creation_progress.dart';
import 'improved_ad_preview_page.dart';
import 'in_flow_upgrade_page.dart';

/// صفحة إعدادات الإعلان المحسنة
class ImprovedAdSettingsPage extends StatefulWidget {
  final Estate estate;

  const ImprovedAdSettingsPage({super.key, required this.estate});

  @override
  State<ImprovedAdSettingsPage> createState() => _ImprovedAdSettingsPageState();
}

class _ImprovedAdSettingsPageState extends State<ImprovedAdSettingsPage> with SingleTickerProviderStateMixin {
  // خدمة المسودات المحسنة
  final _draftService = EnhancedAdDraftService();

  // خدمة الاشتراكات
  final _subscriptionService = EnhancedSubscriptionService();

  // متغيرات الإعدادات
  bool _hidePhone = false;
  bool _shareLocation = true;
  List<String> _extraPhones = [];

  // متغيرات الخطة
  String _selectedPlanId = "free";
  double _selectedPlanPrice = 0.0;
  int _selectedPlanDays = 30;

  // متغيرات الميزات الإضافية
  bool _autoRepublish = false;
  bool _kuwaitCornersPin = false;
  bool _movingAd = false;
  bool _vipBadge = false;
  bool _pinnedOnHome = false;

  // متغيرات التحقق من الصلاحيات
  bool _isCheckingPermissions = true;
  bool _canUseAutoRepublish = false;
  bool _canUseKuwaitCornersPin = false;
  bool _canUseMovingAd = false;
  bool _canUseVipBadge = false;
  bool _canUsePinnedOnHome = false;

  // وحدات التحكم في النص
  final _phoneController = TextEditingController();

  // متغيرات الرسوم المتحركة
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // قائمة الخطط
  final List<Map<String, dynamic>> _plans = [
    {
      "id": "free",
      "title": "الخطة المجانية",
      "price": 0.0,
      "days": 30,
      "description": "إعلان مجاني لمدة 30 يوم",
      "features": ["نشر الإعلان لمدة 30 يوم", "ظهور في نتائج البحث", "إمكانية التواصل المباشر"],
      "color": Colors.grey,
    },
    {
      "id": "basic",
      "title": "الخطة الأساسية",
      "price": 5.0,
      "days": 30,
      "description": "إعلان مميز لمدة 30 يوم",
      "features": ["كل مميزات الخطة المجانية", "ترتيب متقدم في نتائج البحث", "شارة مميزة على الإعلان"],
      "color": Colors.blue,
    },
    {
      "id": "premium",
      "title": "الخطة المميزة",
      "price": 15.0,
      "days": 60,
      "description": "إعلان مميز لمدة 60 يوم مع ميزات إضافية",
      "features": ["كل مميزات الخطة الأساسية", "مدة عرض 60 يوم", "إعادة نشر تلقائي كل أسبوع", "ظهور في الصفحة الرئيسية"],
      "color": Colors.purple,
    },
    {
      "id": "vip",
      "title": "الخطة الذهبية",
      "price": 30.0,
      "days": 90,
      "description": "أفضل خطة للإعلانات المميزة",
      "features": ["كل مميزات الخطة المميزة", "مدة عرض 90 يوم", "شارة VIP", "إعلان متحرك", "دعم أولوية"],
      "color": Colors.amber,
    },
  ];

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300));

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn));

    _animationController.forward();

    // تحميل المسودة
    _loadDraft();

    // التحقق من صلاحيات المستخدم
    _checkPermissions();
  }

  /// التحقق من صلاحيات المستخدم
  Future<void> _checkPermissions() async {
    setState(() {
      _isCheckingPermissions = true;
    });

    try {
      // التحقق من إمكانية استخدام الميزات
      final canUseAutoRepublish = await _subscriptionService.canUseFeature('autoRepublish', null);
      final canUseKuwaitCornersPin = await _subscriptionService.canUseFeature('kuwaitCornersPin', null);
      final canUseMovingAd = await _subscriptionService.canUseFeature('movingAd', null);
      final canUseVipBadge = await _subscriptionService.canUseFeature('vipBadge', null);
      final canUsePinnedOnHome = await _subscriptionService.canUseFeature('pinnedOnHome', null);

      setState(() {
        _canUseAutoRepublish = canUseAutoRepublish;
        _canUseKuwaitCornersPin = canUseKuwaitCornersPin;
        _canUseMovingAd = canUseMovingAd;
        _canUseVipBadge = canUseVipBadge;
        _canUsePinnedOnHome = canUsePinnedOnHome;
        _isCheckingPermissions = false;
      });
    } catch (e) {
      setState(() {
        _isCheckingPermissions = false;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  /// تحميل المسودة
  Future<void> _loadDraft() async {
    final lastDraft = await _draftService.getLastDraft();
    if (lastDraft != null && mounted) {
      setState(() {
        _hidePhone = lastDraft['hidePhone'] ?? false;
        _shareLocation = lastDraft['shareLocation'] ?? true;

        if (lastDraft.containsKey('extraPhones')) {
          _extraPhones = List<String>.from(lastDraft['extraPhones'] ?? []);
        }

        _selectedPlanId = lastDraft['planId'] ?? "free";
        _selectedPlanPrice = lastDraft['planPrice'] ?? 0.0;
        _selectedPlanDays = lastDraft['planDays'] ?? 30;

        _autoRepublish = lastDraft['autoRepublish'] ?? false;
        _kuwaitCornersPin = lastDraft['kuwaitCornersPin'] ?? false;
        _movingAd = lastDraft['movingAd'] ?? false;
        _vipBadge = lastDraft['vipBadge'] ?? false;
        _pinnedOnHome = lastDraft['pinnedOnHome'] ?? false;
      });
    }
  }

  /// حفظ المسودة
  Future<void> _saveDraft() async {
    final draftData = {
      'hidePhone': _hidePhone,
      'shareLocation': _shareLocation,
      'extraPhones': _extraPhones,
      'planId': _selectedPlanId,
      'planPrice': _selectedPlanPrice,
      'planDays': _selectedPlanDays,
      'autoRepublish': _autoRepublish,
      'kuwaitCornersPin': _kuwaitCornersPin,
      'movingAd': _movingAd,
      'vipBadge': _vipBadge,
      'pinnedOnHome': _pinnedOnHome,
      'step': 4,
    };

    await _draftService.saveDraft(draftData);
  }

  /// إضافة رقم هاتف إضافي
  void _addExtraPhone() {
    final phone = _phoneController.text.trim();
    if (phone.isNotEmpty) {
      setState(() {
        _extraPhones.add(phone);
        _phoneController.clear();
      });

      _saveDraft();
    }
  }

  /// حذف رقم هاتف إضافي
  void _removeExtraPhone(int index) {
    setState(() {
      _extraPhones.removeAt(index);
    });

    _saveDraft();
  }

  /// اختيار خطة
  void _selectPlan(String planId) {
    final plan = _plans.firstWhere((p) => p['id'] == planId);

    setState(() {
      _selectedPlanId = planId;
      _selectedPlanPrice = plan['price'];
      _selectedPlanDays = plan['days'];

      // تحديث الميزات الإضافية بناءً على الخطة
      if (planId == "premium" || planId == "vip") {
        _autoRepublish = true;
        _pinnedOnHome = true;
      }

      if (planId == "vip") {
        _vipBadge = true;
        _movingAd = true;
        _kuwaitCornersPin = true;
      }
    });

    _saveDraft();
  }

  /// الانتقال إلى الخطوة التالية
  void _goToNextStep() {
    // حفظ الإعدادات في BLoC
    context.read<ImprovedAdBloc>().add(
      SetAdSettings(
        userType: 'user', // يمكن تحديد نوع المستخدم هنا
        hidePhone: _hidePhone,
        shareLocation: _shareLocation,
        extraPhones: _extraPhones));

    // حفظ الخطة في BLoC - استخدام الخطة المجانية في الوضع المعلوماتي
    final planId = AppModeConfig.canAccessFeature('payment') ? _selectedPlanId : 'free';
    final planPrice = AppModeConfig.canAccessFeature('payment') ? _selectedPlanPrice : 0.0;
    final planDays = AppModeConfig.canAccessFeature('payment') ? _selectedPlanDays : 30;

    context.read<ImprovedAdBloc>().add(
      SetSelectedPlan(
        planId: planId,
        planPrice: planPrice,
        days: planDays));

    // الحصول على الحالة الحالية للمميزات من BLoC
    final currentState = context.read<ImprovedAdBloc>().state;

    // حفظ الميزات الإضافية في BLoC - تعطيل الميزات المدفوعة في الوضع المعلوماتي
    final autoRepublish = AppModeConfig.canAccessFeature('payment') ? _autoRepublish : false;
    final kuwaitCornersPin = AppModeConfig.canAccessFeature('payment') ? _kuwaitCornersPin : false;
    final movingAd = AppModeConfig.canAccessFeature('payment') ? _movingAd : false;
    final vipBadge = AppModeConfig.canAccessFeature('payment') ? _vipBadge : false;
    final pinnedOnHome = AppModeConfig.canAccessFeature('payment') ? _pinnedOnHome : false;

    context.read<ImprovedAdBloc>().add(
      SetExtraFeatures(
        autoRepublish: autoRepublish,
        kuwaitCornersPin: kuwaitCornersPin,
        movingAd: movingAd,
        vipBadge: vipBadge,
        pinnedOnHome: pinnedOnHome,
        discountCode: null,
        // الحفاظ على المميزات المحددة مسبقاً من صفحة التفاصيل
        hasGarage: currentState.hasGarage,
        hasCentralAC: currentState.hasCentralAC,
        hasMaidRoom: currentState.hasMaidRoom,
        isFullyFurnished: currentState.isFullyFurnished,
        hasSecurity: currentState.hasSecurity,
        allowPets: currentState.allowPets,
        hasElevator: currentState.hasElevator,
        hasSwimmingPool: currentState.hasSwimmingPool,
        hasBalcony: currentState.hasBalcony));

    // حفظ المسودة مع الإعدادات الكاملة
    _saveDraftWithSettings();

    // الانتقال إلى صفحة المعاينة
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => ImprovedAdPreviewPage(estate: widget.estate)));
  }

  /// حفظ المسودة مع الإعدادات الكاملة
  void _saveDraftWithSettings() {
    final adState = context.read<ImprovedAdBloc>().state;

    // استخدام القيم المناسبة حسب الوضع المعلوماتي
    final planId = AppModeConfig.canAccessFeature('payment') ? _selectedPlanId : 'free';
    final planPrice = AppModeConfig.canAccessFeature('payment') ? _selectedPlanPrice : 0.0;
    final planDays = AppModeConfig.canAccessFeature('payment') ? _selectedPlanDays : 30;
    final autoRepublish = AppModeConfig.canAccessFeature('payment') ? _autoRepublish : false;
    final kuwaitCornersPin = AppModeConfig.canAccessFeature('payment') ? _kuwaitCornersPin : false;
    final movingAd = AppModeConfig.canAccessFeature('payment') ? _movingAd : false;
    final vipBadge = AppModeConfig.canAccessFeature('payment') ? _vipBadge : false;
    final pinnedOnHome = AppModeConfig.canAccessFeature('payment') ? _pinnedOnHome : false;

    _draftService.autoSaveDraft({
      'mainCategory': adState.mainCategory,
      'subCategory': adState.subCategory,
      'imagePaths': adState.imagePaths,
      'title': adState.title,
      'description': adState.description,
      'price': adState.price,
      'governorate': adState.governorate,
      'city': adState.city,
      'piece': adState.piece,
      'usageType': adState.usageType,
      'hidePhone': _hidePhone,
      'shareLocation': _shareLocation,
      'extraPhones': _extraPhones,
      'planId': planId,
      'planPrice': planPrice,
      'planDays': planDays,
      'autoRepublish': autoRepublish,
      'kuwaitCornersPin': kuwaitCornersPin,
      'movingAd': movingAd,
      'vipBadge': vipBadge,
      'pinnedOnHome': pinnedOnHome,
      'hasCentralAC': adState.hasCentralAC,
      'hasSecurity': adState.hasSecurity,
      'allowPets': adState.allowPets,
      'hasElevator': adState.hasElevator,
      'hasSwimmingPool': adState.hasSwimmingPool,
      'hasMaidRoom': adState.hasMaidRoom,
      'hasGarage': adState.hasGarage,
      'hasBalcony': adState.hasBalcony,
      'isFullyFurnished': adState.isFullyFurnished,
      'step': 4,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      // مؤشر التقدم وأزرار التنقل أسفل الصفحة
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أزرار التنقل
          AdCreationNavigationButtons(
            onNext: _goToNextStep,
            onBack: () => Navigator.pop(context),
            nextText: "معاينة الإعلان",
            backText: "العودة",
            isNextDisabled: false),

          // مؤشر التقدم
          ImprovedAdCreationProgress(
            currentStep: 4,
            onStepTap: (step) {
              if (step < 4) {
                Navigator.pop(context);
              }
            }),
        ]),
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // ترويسة مع زر رجوع وعنوان
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.black87),
                      onPressed: () => Navigator.of(context).pop()),
                    const SizedBox(width: 8),
                    Text(
                      "إعدادات الإعلان",
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87)),
                  ]),
                const SizedBox(height: 24),

                // قسم إعدادات الاتصال
                _buildSectionTitle("إعدادات الاتصال", Icons.phone),
                const SizedBox(height: 16),

                // إخفاء رقم الهاتف
                SwitchListTile(
                  title: Text(
                    "إخفاء رقم الهاتف الأساسي",
                    style: GoogleFonts.cairo()),
                  subtitle: Text(
                    "لن يظهر رقم هاتفك الأساسي في الإعلان",
                    style: GoogleFonts.cairo(fontSize: 12)),
                  value: _hidePhone,
                  onChanged: (value) {
                    setState(() {
                      _hidePhone = value;
                    });
                    _saveDraft();
                  },
                  activeColor: Theme.of(context).primaryColor),

                // مشاركة الموقع
                SwitchListTile(
                  title: Text(
                    "مشاركة الموقع عند نشر الإعلان",
                    style: GoogleFonts.cairo()),
                  subtitle: Text(
                    "سيظهر موقع العقار على الخريطة",
                    style: GoogleFonts.cairo(fontSize: 12)),
                  value: _shareLocation,
                  onChanged: (value) {
                    setState(() {
                      _shareLocation = value;
                    });
                    _saveDraft();
                  },
                  activeColor: Theme.of(context).primaryColor),

                // أرقام هواتف إضافية
                const SizedBox(height: 16),
                Text(
                  "أرقام هواتف إضافية",
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),

                // إضافة رقم هاتف
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _phoneController,
                        keyboardType: TextInputType.phone,
                        decoration: InputDecoration(
                          labelText: "رقم الهاتف",
                          hintText: "أدخل رقم هاتف إضافي",
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8)),
                          prefixIcon: const Icon(Icons.phone),
                          labelStyle: GoogleFonts.cairo()))),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _addExtraPhone,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16)),
                      child: const Icon(Icons.add)),
                  ]),

                // قائمة أرقام الهواتف الإضافية
                if (_extraPhones.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  ...List.generate(_extraPhones.length, (index) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(8)),
                        child: Row(
                          children: [
                            const Icon(Icons.phone, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              _extraPhones[index],
                              style: GoogleFonts.cairo()),
                            const Spacer(),
                            IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                              onPressed: () => _removeExtraPhone(index)),
                          ])));
                  }),
                ],

                const SizedBox(height: 24),

                // قسم اختيار الخطة - مخفي في الوضع المعلوماتي
                if (AppModeConfig.canAccessFeature('payment')) ...[
                  _buildSectionTitle("اختيار الخطة", Icons.workspace_premium),
                  const SizedBox(height: 16),

                  // قائمة الخطط
                  ...List.generate(_plans.length, (index) {
                    final plan = _plans[index];
                    final isSelected = _selectedPlanId == plan['id'];

                    return Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: _buildPlanCard(
                        title: plan['title'],
                        price: plan['price'],
                        days: plan['days'],
                        description: plan['description'],
                        features: List<String>.from(plan['features']),
                        color: plan['color'],
                        isSelected: isSelected,
                        onTap: () => _selectPlan(plan['id'])));
                  }),

                  const SizedBox(height: 24),
                ] else ...[
                  // رسالة توضيحية في الوضع المعلوماتي
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.blue.shade600,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'سيتم نشر الإعلان مجاناً في الوضع المعلوماتي',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // قسم الميزات الإضافية - مخفي في الوضع المعلوماتي
                if (AppModeConfig.canAccessFeature('payment')) ...[
                  _buildSectionTitle("ميزات إضافية", Icons.star),
                  const SizedBox(height: 16),

                  // الميزات الإضافية
                  _buildFeatureSwitch(
                    title: "إعادة نشر تلقائي",
                    subtitle: "إعادة نشر الإعلان تلقائياً كل أسبوع",
                    value: _autoRepublish,
                    onChanged: (value) {
                      setState(() {
                        _autoRepublish = value;
                      });
                      _saveDraft();
                    },
                    isPremium: true,
                    isEnabled: _selectedPlanId == "premium" || _selectedPlanId == "vip"),

                  _buildFeatureSwitch(
                    title: "دبوس كويت كورنرز",
                    subtitle: "تمييز الإعلان بدبوس خاص على الخريطة",
                    value: _kuwaitCornersPin,
                    onChanged: (value) {
                      setState(() {
                        _kuwaitCornersPin = value;
                      });
                      _saveDraft();
                    },
                    isPremium: true,
                    isEnabled: _selectedPlanId == "vip"),

                  _buildFeatureSwitch(
                    title: "إعلان متحرك",
                    subtitle: "إضافة حركة للإعلان لجذب المزيد من الاهتمام",
                    value: _movingAd,
                    onChanged: (value) {
                      setState(() {
                        _movingAd = value;
                      });
                      _saveDraft();
                    },
                    isPremium: true,
                    isEnabled: _selectedPlanId == "vip"),

                  _buildFeatureSwitch(
                    title: "شارة VIP",
                    subtitle: "إضافة شارة VIP للإعلان",
                    value: _vipBadge,
                    onChanged: (value) {
                      setState(() {
                        _vipBadge = value;
                      });
                      _saveDraft();
                    },
                    isPremium: true,
                    isEnabled: _selectedPlanId == "vip"),

                  _buildFeatureSwitch(
                    title: "تثبيت في الصفحة الرئيسية",
                    subtitle: "عرض الإعلان في الصفحة الرئيسية",
                    value: _pinnedOnHome,
                    onChanged: (value) {
                      setState(() {
                        _pinnedOnHome = value;
                      });
                      _saveDraft();
                    },
                    isPremium: true,
                    isEnabled: _selectedPlanId == "premium" || _selectedPlanId == "vip"),
                ],

                const SizedBox(height: 80), // مساحة للزر العائم
              ])))));
  }

  /// بناء عنوان قسم
  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor),
        const SizedBox(width: 8),
        Text(
          title,
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor)),
      ]);
  }

  /// بناء بطاقة خطة
  Widget _buildPlanCard({
    required String title,
    required double price,
    required int days,
    required String description,
    required List<String> features,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? color.withValues(alpha: 0.1) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: isSelected ? 2 : 1),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: color.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4)),
                ]
              : null),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ترويسة الخطة
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.8),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(10),
                  topRight: Radius.circular(10))),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white)),
                        Text(
                          description,
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.white.withValues(alpha: 0.8))),
                      ])),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        price > 0 ? "${price.toStringAsFixed(1)} د.ك" : "مجاني",
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white)),
                      Text(
                        "$days يوم",
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.white.withValues(alpha: 0.8))),
                    ]),
                ])),

            // قائمة المميزات
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: features.map((feature) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 8.0),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check_circle,
                          color: color,
                          size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            feature,
                            style: GoogleFonts.cairo(fontSize: 14))),
                      ]));
                }).toList())),

            // زر الاختيار
            if (isSelected)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.8),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(10),
                    bottomRight: Radius.circular(10))),
                child: Center(
                  child: Text(
                    "تم الاختيار",
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white)))),
          ])));
  }

  /// بناء مفتاح ميزة
  Widget _buildFeatureSwitch({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required bool isPremium,
    required bool isEnabled,
  }) {
    // التحقق من صلاحيات المستخدم
    bool canUseFeature = false;

    switch (title) {
      case "إعادة نشر تلقائي":
        canUseFeature = _canUseAutoRepublish;
        break;
      case "دبوس كويت كورنرز":
        canUseFeature = _canUseKuwaitCornersPin;
        break;
      case "إعلان متحرك":
        canUseFeature = _canUseMovingAd;
        break;
      case "شارة VIP":
        canUseFeature = _canUseVipBadge;
        break;
      case "تثبيت في الصفحة الرئيسية":
        canUseFeature = _canUsePinnedOnHome;
        break;
      default:
        canUseFeature = true;
    }

    // إذا كان المستخدم لا يملك صلاحية استخدام الميزة، عرض زر الترقية
    if (!canUseFeature && isPremium) {
      return Card(
        margin: const EdgeInsets.only(bottom: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: Colors.grey.shade300)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold)),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      borderRadius: BorderRadius.circular(4)),
                    child: Text(
                      "مميز",
                      style: GoogleFonts.cairo(
                        fontSize: 10,
                        color: Colors.white))),
                  const Spacer(),
                  Icon(
                    Icons.lock,
                    color: Colors.grey.shade400,
                    size: 20),
                ]),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: Colors.grey.shade600)),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    // الانتقال إلى صفحة الترقية
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => InFlowUpgradePage(
                          onUpgradeComplete: () {
                            // إعادة التحقق من الصلاحيات بعد الترقية
                            _checkPermissions();
                            Navigator.pop(context);
                          })));
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8))),
                  child: Text(
                    "ترقية الباقة للاستخدام",
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      fontWeight: FontWeight.bold)))),
            ])));
    }

    return Opacity(
      opacity: isEnabled ? 1.0 : 0.5,
      child: SwitchListTile(
        title: Row(
          children: [
            Text(
              title,
              style: GoogleFonts.cairo()),
            if (isPremium) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.amber,
                  borderRadius: BorderRadius.circular(4)),
                child: Text(
                  "مميز",
                  style: GoogleFonts.cairo(
                    fontSize: 10,
                    color: Colors.white))),
            ],
          ]),
        subtitle: Text(
          subtitle,
          style: GoogleFonts.cairo(fontSize: 12)),
        value: value,
        onChanged: isEnabled ? onChanged : null,
        activeColor: Theme.of(context).primaryColor));
  }
}
