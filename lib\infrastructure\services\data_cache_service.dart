import 'dart:async';
import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'cache_manager.dart';
import 'performance_service.dart';

/// خدمة التخزين المؤقت للبيانات
class DataCacheService {
  static final DataCacheService _instance = DataCacheService._internal();

  factory DataCacheService() {
    return _instance;
  }

  DataCacheService._internal();

  final AppCacheManager _cacheManager = AppCacheManager();
  final PerformanceService _performanceService = PerformanceService();
  final Connectivity _connectivity = Connectivity();

  late Box<dynamic> _dataCache;
  late SharedPreferences _prefs;

  /// تهيئة خدمة التخزين المؤقت للبيانات
  Future<void> init() async {
    await Hive.initFlutter();
    _dataCache = await Hive.openBox('data_cache');
    _prefs = await SharedPreferences.getInstance();
  }

  /// تخزين بيانات في التخزين المؤقت
  Future<void> cacheData(String key, dynamic data, {Duration? expiry}) async {
    // تحديد مدة التخزين المؤقت المناسبة إذا لم يتم تحديدها
    expiry ??= _getAppropriateCacheDuration();

    // تخزين البيانات
    await _cacheManager.cacheData(key, data, expiry: expiry);
  }

  /// الحصول على بيانات من التخزين المؤقت
  dynamic getCachedData(String key) {
    return _cacheManager.getCachedData(key);
  }

  /// حذف بيانات من التخزين المؤقت
  Future<void> removeCachedData(String key) async {
    await _cacheManager.removeCachedData(key);
  }

  /// تخزين بيانات API في التخزين المؤقت
  Future<void> cacheApiResponse(String endpoint, dynamic data,
      {Duration? expiry}) async {
    // تحديد مدة التخزين المؤقت المناسبة إذا لم يتم تحديدها
    expiry ??= _getAppropriateCacheDuration();

    // تخزين البيانات
    await _cacheManager.cacheData('api:$endpoint', data, expiry: expiry);
  }

  /// الحصول على بيانات API من التخزين المؤقت
  dynamic getCachedApiResponse(String endpoint) {
    return _cacheManager.getCachedData('api:$endpoint');
  }

  /// تخزين قائمة عقارات في التخزين المؤقت
  Future<void> cacheEstatesList(String key, List<dynamic> estates,
      {Duration? expiry}) async {
    // تحديد مدة التخزين المؤقت المناسبة إذا لم يتم تحديدها
    expiry ??= _getAppropriateCacheDuration();

    // تخزين البيانات
    await _cacheManager.cacheData('estates:$key', estates, expiry: expiry);
  }

  /// الحصول على قائمة عقارات من التخزين المؤقت
  List<dynamic>? getCachedEstatesList(String key) {
    final data = _cacheManager.getCachedData('estates:$key');
    if (data != null && data is List) {
      return data;
    }
    return null;
  }

  /// تخزين عقار في التخزين المؤقت
  Future<void> cacheEstate(String estateId, dynamic estate,
      {Duration? expiry}) async {
    // تحديد مدة التخزين المؤقت المناسبة إذا لم يتم تحديدها
    expiry ??= _getAppropriateCacheDuration();

    // تخزين البيانات
    await _cacheManager.cacheData('estate:$estateId', estate, expiry: expiry);
  }

  /// الحصول على عقار من التخزين المؤقت
  dynamic getCachedEstate(String estateId) {
    return _cacheManager.getCachedData('estate:$estateId');
  }

  /// تخزين بيانات المستخدم في التخزين المؤقت
  Future<void> cacheUserData(String userId, dynamic userData,
      {Duration? expiry}) async {
    // تحديد مدة التخزين المؤقت المناسبة إذا لم يتم تحديدها
    expiry ??= const Duration(days: 7); // بيانات المستخدم تبقى لفترة أطول

    // تخزين البيانات
    await _cacheManager.cacheData('user:$userId', userData, expiry: expiry);
  }

  /// الحصول على بيانات المستخدم من التخزين المؤقت
  dynamic getCachedUserData(String userId) {
    return _cacheManager.getCachedData('user:$userId');
  }

  /// تخزين نتائج البحث في التخزين المؤقت
  Future<void> cacheSearchResults(String query, dynamic results,
      {Duration? expiry}) async {
    // تحديد مدة التخزين المؤقت المناسبة إذا لم يتم تحديدها
    expiry ??= const Duration(hours: 6); // نتائج البحث تبقى لفترة أقصر

    // تخزين البيانات
    await _cacheManager.cacheData('search:$query', results, expiry: expiry);
  }

  /// الحصول على نتائج البحث من التخزين المؤقت
  dynamic getCachedSearchResults(String query) {
    return _cacheManager.getCachedData('search:$query');
  }

  /// تخزين بيانات التصفح في التخزين المؤقت
  Future<void> cacheBrowsingHistory(
      String userId, List<dynamic> history) async {
    await _cacheManager.cacheData('history:$userId', history);
  }

  /// الحصول على بيانات التصفح من التخزين المؤقت
  List<dynamic>? getCachedBrowsingHistory(String userId) {
    final data = _cacheManager.getCachedData('history:$userId');
    if (data != null && data is List) {
      return data;
    }
    return null;
  }

  /// تخزين بيانات المفضلة في التخزين المؤقت
  Future<void> cacheFavorites(String userId, List<dynamic> favorites) async {
    await _cacheManager.cacheData('favorites:$userId', favorites);
  }

  /// الحصول على بيانات المفضلة من التخزين المؤقت
  List<dynamic>? getCachedFavorites(String userId) {
    final data = _cacheManager.getCachedData('favorites:$userId');
    if (data != null && data is List) {
      return data;
    }
    return null;
  }

  /// تخزين بيانات المقارنة في التخزين المؤقت
  Future<void> cacheComparisons(
      String userId, List<dynamic> comparisons) async {
    await _cacheManager.cacheData('comparisons:$userId', comparisons);
  }

  /// الحصول على بيانات المقارنة من التخزين المؤقت
  List<dynamic>? getCachedComparisons(String userId) {
    final data = _cacheManager.getCachedData('comparisons:$userId');
    if (data != null && data is List) {
      return data;
    }
    return null;
  }

  /// تخزين إعدادات المستخدم في التخزين المؤقت
  Future<void> cacheUserSettings(
      String userId, Map<String, dynamic> settings) async {
    await _cacheManager.cacheData('settings:$userId', settings);
  }

  /// الحصول على إعدادات المستخدم من التخزين المؤقت
  Map<String, dynamic>? getCachedUserSettings(String userId) {
    final data = _cacheManager.getCachedData('settings:$userId');
    if (data != null && data is Map<String, dynamic>) {
      return data;
    }
    return null;
  }

  /// تخزين حالة التطبيق في التخزين المؤقت
  Future<void> cacheAppState(Map<String, dynamic> state) async {
    await _cacheManager.cacheData('app:state', state);
  }

  /// الحصول على حالة التطبيق من التخزين المؤقت
  Map<String, dynamic>? getCachedAppState() {
    final data = _cacheManager.getCachedData('app:state');
    if (data != null && data is Map<String, dynamic>) {
      return data;
    }
    return null;
  }

  /// تنظيف التخزين المؤقت
  Future<void> clearCache() async {
    await _cacheManager.clearCache();
  }

  /// الحصول على حجم التخزين المؤقت
  Future<String> getCacheSize() async {
    return await _cacheManager.getReadableCacheSize();
  }

  /// الحصول على مدة التخزين المؤقت المناسبة
  Duration _getAppropriateCacheDuration() {
    return _performanceService.getAppropriateCacheDuration();
  }

  /// تحسين استخدام التخزين المؤقت
  Future<void> optimizeCache() async {
    // الحصول على حجم التخزين المؤقت
    final cacheSize = await _cacheManager.getCacheSize();

    // إذا كان حجم التخزين المؤقت كبيراً، نقوم بتنظيف البيانات القديمة
    if (cacheSize > 50 * 1024 * 1024) {
      // أكبر من 50 ميجابايت
      // تنظيف البيانات القديمة
      await _cleanOldCachedData();
    }
  }

  /// تنظيف البيانات القديمة من التخزين المؤقت
  Future<void> _cleanOldCachedData() async {
    // الحصول على جميع المفاتيح في التخزين المؤقت
    final keys = _dataCache.keys.toList();

    // الحصول على الوقت الحالي
    final now = DateTime.now().millisecondsSinceEpoch;

    // تنظيف البيانات القديمة
    for (final key in keys) {
      if (key.toString().endsWith(':expiry')) {
        final expiryTime = _dataCache.get(key);
        if (expiryTime != null && expiryTime is int) {
          // إذا كانت البيانات منتهية الصلاحية، نقوم بحذفها
          if (now > expiryTime) {
            final dataKey = key.toString().replaceAll(':expiry', ':data');
            await _dataCache.delete(dataKey);
            await _dataCache.delete(key);
          }
        }
      }
    }
  }

  /// تحسين استخدام البيانات
  Future<void> optimizeDataUsage() async {
    // الحصول على نوع الاتصال
    final connectivityResult = await _connectivity.checkConnectivity();

    // إذا كان الاتصال بيانات خلوية، نقوم بتقليل استخدام البيانات
    if (connectivityResult == ConnectivityResult.mobile) {
      // تنظيف التخزين المؤقت للصور
      await _cleanImageCache();
    }
  }

  /// تنظيف التخزين المؤقت للصور
  Future<void> _cleanImageCache() async {
    // تنظيف التخزين المؤقت للصور
    await DefaultCacheManager().emptyCache();
  }
}

/// مدير التخزين المؤقت الافتراضي
class DefaultCacheManager {
  static final DefaultCacheManager _instance = DefaultCacheManager._internal();

  factory DefaultCacheManager() {
    return _instance;
  }

  DefaultCacheManager._internal();

  /// تنظيف التخزين المؤقت
  Future<void> emptyCache() async {
    // هذه طريقة تقريبية لتنظيف التخزين المؤقت للصور
    // في التطبيقات الحقيقية، يمكن استخدام مكتبات خارجية
  }
}

/// امتدادات لتسهيل استخدام خدمة التخزين المؤقت للبيانات
extension DataCacheServiceExtensions on DataCacheService {
  /// تخزين بيانات مع مفتاح مركب
  Future<void> cacheDataWithCompoundKey(List<String> keyParts, dynamic data,
      {Duration? expiry}) async {
    final key = keyParts.join(':');
    await cacheData(key, data, expiry: expiry);
  }

  /// الحصول على بيانات بمفتاح مركب
  dynamic getCachedDataWithCompoundKey(List<String> keyParts) {
    final key = keyParts.join(':');
    return getCachedData(key);
  }

  /// تخزين بيانات مع مفتاح مركب ونسخة
  Future<void> cacheDataWithVersion(String key, dynamic data, int version,
      {Duration? expiry}) async {
    await cacheData('$key:v$version', data, expiry: expiry);
    await cacheData('$key:version', version);
  }

  /// الحصول على بيانات بمفتاح مركب ونسخة
  dynamic getCachedDataWithVersion(String key) {
    final version = getCachedData('$key:version');
    if (version == null) {
      return null;
    }

    return getCachedData('$key:v$version');
  }

  /// تخزين بيانات JSON في التخزين المؤقت
  Future<void> cacheJsonData(String key, Map<String, dynamic> jsonData,
      {Duration? expiry}) async {
    final jsonString = json.encode(jsonData);
    await cacheData(key, jsonString, expiry: expiry);
  }

  /// الحصول على بيانات JSON من التخزين المؤقت
  Map<String, dynamic>? getCachedJsonData(String key) {
    final jsonString = getCachedData(key);
    if (jsonString != null && jsonString is String) {
      try {
        return json.decode(jsonString) as Map<String, dynamic>;
      } catch (e) {
        return null;
      }
    }
    return null;
  }
}

/// امتدادات لتسهيل استخدام خدمة التخزين المؤقت للبيانات في واجهة المستخدم
extension DataCacheUIExtensions on DataCacheService {
  /// تخزين حالة واجهة المستخدم في التخزين المؤقت
  Future<void> cacheUIState(
      String screenName, Map<String, dynamic> state) async {
    await cacheData('ui:$screenName', state);
  }

  /// الحصول على حالة واجهة المستخدم من التخزين المؤقت
  Map<String, dynamic>? getCachedUIState(String screenName) {
    final data = getCachedData('ui:$screenName');
    if (data != null && data is Map<String, dynamic>) {
      return data;
    }
    return null;
  }

  /// تخزين موضع التمرير في التخزين المؤقت
  Future<void> cacheScrollPosition(String screenName, double position) async {
    await cacheData('scroll:$screenName', position);
  }

  /// الحصول على موضع التمرير من التخزين المؤقت
  double? getCachedScrollPosition(String screenName) {
    final data = getCachedData('scroll:$screenName');
    if (data != null && data is double) {
      return data;
    }
    return null;
  }

  /// تخزين حالة التصفية في التخزين المؤقت
  Future<void> cacheFilterState(
      String screenName, Map<String, dynamic> filters) async {
    await cacheData('filter:$screenName', filters);
  }

  /// الحصول على حالة التصفية من التخزين المؤقت
  Map<String, dynamic>? getCachedFilterState(String screenName) {
    final data = getCachedData('filter:$screenName');
    if (data != null && data is Map<String, dynamic>) {
      return data;
    }
    return null;
  }
}
