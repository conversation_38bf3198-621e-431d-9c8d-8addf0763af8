# دليل المطور - تطبيق Krea

## 📋 نظرة عامة

هذا الدليل مخصص للمطورين الذين يعملون على تطبيق Krea أو يرغبون في فهم بنية التطبيق التقنية.

---

## 🏗️ بنية المشروع

### التنظيم العام
```
lib/
├── core/                    # المكونات الأساسية المشتركة
│   ├── config/             # إعدادات التطبيق
│   ├── constants/          # الثوابت والقيم الثابتة
│   ├── enums/              # التعدادات
│   ├── error/              # إدارة الأخطاء
│   ├── middleware/         # الوسطاء
│   ├── models/             # النماذج الأساسية
│   ├── repositories/       # المستودعات الأساسية
│   ├── routes/             # مسارات التطبيق
│   ├── security/           # الأمان والتشفير
│   ├── services/           # الخدمات الأساسية
│   ├── theme/              # إعدادات التصميم
│   ├── utils/              # الأدوات المساعدة
│   ├── validators/         # التحقق من البيانات
│   └── widgets/            # المكونات المشتركة
├── data/                   # طبقة البيانات
│   ├── datasources/        # مصادر البيانات
│   ├── models/             # نماذج البيانات
│   ├── repositories_impl/  # تنفيذ المستودعات
│   └── services/           # خدمات البيانات
├── domain/                 # طبقة المنطق التجاري
│   ├── entities/           # الكيانات
│   ├── enums/              # تعدادات المجال
│   ├── models/             # نماذج المجال
│   ├── repositories/       # واجهات المستودعات
│   ├── services/           # واجهات الخدمات
│   └── usecases/           # حالات الاستخدام
├── infrastructure/         # طبقة البنية التحتية
│   ├── repositories/       # تنفيذ مستودعات البنية التحتية
│   └── services/           # خدمات البنية التحتية
├── presentation/           # طبقة العرض
│   ├── bloc/               # إدارة الحالة بـ BLoC
│   ├── models/             # نماذج العرض
│   ├── pages/              # صفحات التطبيق
│   ├── providers/          # مزودات الحالة
│   ├── screens/            # شاشات التطبيق
│   ├── utils/              # أدوات العرض
│   └── widgets/            # مكونات واجهة المستخدم
├── tools/                  # أدوات التطوير
├── firebase_options.dart   # إعدادات Firebase
└── main.dart              # نقطة دخول التطبيق
```

---

## 🔧 إعداد بيئة التطوير

### المتطلبات الأساسية
- **Flutter SDK**: 3.6.1 أو أحدث
- **Dart SDK**: 3.2.0 أو أحدث
- **Android Studio**: 2023.1 أو أحدث
- **VS Code**: مع إضافات Flutter و Dart
- **Git**: لإدارة الإصدارات

### إعداد المشروع
```bash
# استنساخ المشروع
git clone https://github.com/krea-app/krea-mobile.git
cd krea-mobile

# تثبيت التبعيات
flutter pub get

# تشغيل مولد الكود
flutter packages pub run build_runner build

# تشغيل التطبيق
flutter run
```

### إعداد Firebase
1. إنشاء مشروع Firebase جديد
2. إضافة تطبيقات Android و iOS
3. تحميل ملفات التكوين:
   - `android/app/google-services.json`
   - `ios/Runner/GoogleService-Info.plist`
4. تفعيل الخدمات المطلوبة:
   - Authentication
   - Firestore Database
   - Storage
   - Cloud Functions
   - Analytics

---

## 🏛️ معمارية التطبيق

### Clean Architecture
التطبيق يتبع مبادئ Clean Architecture مع الطبقات التالية:

#### 1. طبقة العرض (Presentation Layer)
- **المسؤولية**: واجهة المستخدم وإدارة الحالة
- **المكونات**: Pages, Widgets, BLoC, Providers
- **التبعيات**: تعتمد على طبقة Domain فقط

#### 2. طبقة المجال (Domain Layer)
- **المسؤولية**: المنطق التجاري وقواعد العمل
- **المكونات**: Entities, Use Cases, Repository Interfaces
- **التبعيات**: مستقلة تماماً عن الطبقات الأخرى

#### 3. طبقة البيانات (Data Layer)
- **المسؤولية**: الوصول للبيانات والتخزين
- **المكونات**: Repository Implementations, Data Sources, Models
- **التبعيات**: تعتمد على طبقة Domain

#### 4. طبقة البنية التحتية (Infrastructure Layer)
- **المسؤولية**: الخدمات الخارجية والأدوات
- **المكونات**: External Services, Third-party Integrations
- **التبعيات**: تعتمد على طبقات Domain و Data

---

## 🔄 إدارة الحالة

### BLoC Pattern
```dart
// مثال على BLoC للعقارات
class EstateBloc extends Bloc<EstateEvent, EstateState> {
  final GetAllEstates getAllEstates;
  final CreateEstate createEstate;
  
  EstateBloc({
    required this.getAllEstates,
    required this.createEstate,
  }) : super(EstateInitial()) {
    on<LoadEstatesEvent>(_onLoadEstates);
    on<CreateEstateEvent>(_onCreateEstate);
  }
  
  Future<void> _onLoadEstates(
    LoadEstatesEvent event,
    Emitter<EstateState> emit,
  ) async {
    emit(EstateLoading());
    try {
      final estates = await getAllEstates();
      emit(EstateLoaded(estates));
    } catch (e) {
      emit(EstateError(e.toString()));
    }
  }
}
```

### Provider Pattern
```dart
// مثال على Provider للمصادقة
class AuthProvider extends ChangeNotifier {
  User? _currentUser;
  bool _isLoading = false;
  
  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  
  Future<void> signIn(String email, String password) async {
    _isLoading = true;
    notifyListeners();
    
    try {
      _currentUser = await _authService.signIn(email, password);
    } catch (e) {
      throw e;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
```

---

## 🗄️ إدارة البيانات

### Firestore Collections
```
users/                      # بيانات المستخدمين
├── {userId}/
│   ├── profile             # الملف الشخصي
│   ├── preferences         # التفضيلات
│   └── notifications/      # الإشعارات
│       └── {notificationId}

estates/                    # العقارات
├── {estateId}/
│   ├── details             # تفاصيل العقار
│   ├── images              # الصور
│   ├── location            # الموقع
│   └── analytics           # التحليلات

forum/                      # المنتدى
├── categories/             # الفئات
├── topics/                 # الموضوعات
└── replies/                # الردود

projects/                   # المشاريع
├── {projectId}/
│   ├── details             # تفاصيل المشروع
│   ├── team/               # الفريق
│   ├── tasks/              # المهام
│   └── documents/          # المستندات
```

### Repository Pattern
```dart
abstract class EstateRepository {
  Future<List<Estate>> getAllEstates();
  Future<Estate> getEstateById(String id);
  Future<String> createEstate(Estate estate);
  Future<void> updateEstate(Estate estate);
  Future<void> deleteEstate(String id);
}

class EstateRepositoryImpl implements EstateRepository {
  final EstateRemoteDataSource remoteDataSource;
  
  EstateRepositoryImpl(this.remoteDataSource);
  
  @override
  Future<List<Estate>> getAllEstates() async {
    try {
      final estateModels = await remoteDataSource.getAllEstates();
      return estateModels.map((model) => model.toEntity()).toList();
    } catch (e) {
      throw ServerException(e.toString());
    }
  }
}
```

---

## 🔐 الأمان والمصادقة

### Firebase Authentication
```dart
class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  // تسجيل الدخول
  Future<User?> signInWithEmailAndPassword(
    String email, 
    String password
  ) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return credential.user;
    } on FirebaseAuthException catch (e) {
      throw AuthException(_getErrorMessage(e.code));
    }
  }
  
  // تسجيل الدخول بـ Google
  Future<User?> signInWithGoogle() async {
    final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();
    if (googleUser == null) return null;
    
    final GoogleSignInAuthentication googleAuth = 
        await googleUser.authentication;
    
    final credential = GoogleAuthProvider.credential(
      accessToken: googleAuth.accessToken,
      idToken: googleAuth.idToken,
    );
    
    final userCredential = await _auth.signInWithCredential(credential);
    return userCredential.user;
  }
}
```

### التشفير والحماية
```dart
class SecurityService {
  static const _key = 'your-encryption-key';
  
  // تشفير البيانات الحساسة
  String encryptData(String data) {
    final key = encrypt.Key.fromBase64(_key);
    final iv = encrypt.IV.fromSecureRandom(16);
    final encrypter = encrypt.Encrypter(encrypt.AES(key));
    
    final encrypted = encrypter.encrypt(data, iv: iv);
    return encrypted.base64;
  }
  
  // فك تشفير البيانات
  String decryptData(String encryptedData) {
    final key = encrypt.Key.fromBase64(_key);
    final encrypter = encrypt.Encrypter(encrypt.AES(key));
    
    final encrypted = encrypt.Encrypted.fromBase64(encryptedData);
    return encrypter.decrypt(encrypted);
  }
}
```

---

## 🎨 التصميم والثيمات

### نظام الألوان
```dart
class AppColors {
  // الألوان الأساسية
  static const Color primary = Color(0xFF4CAF50);
  static const Color primaryDark = Color(0xFF388E3C);
  static const Color primaryLight = Color(0xFF81C784);
  
  // الألوان الثانوية
  static const Color secondary = Color(0xFF2196F3);
  static const Color accent = Color(0xFFFF9800);
  
  // ألوان النظام
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color error = Color(0xFFE53935);
  
  // ألوان النص
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
}
```

### الثيمات
```dart
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primarySwatch: Colors.green,
      primaryColor: AppColors.primary,
      backgroundColor: AppColors.background,
      scaffoldBackgroundColor: AppColors.background,
      
      // تخصيص AppBar
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      
      // تخصيص الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      
      // تخصيص النصوص
      textTheme: GoogleFonts.cairoTextTheme(),
    );
  }
  
  static ThemeData get darkTheme {
    return ThemeData.dark().copyWith(
      primaryColor: AppColors.primary,
      textTheme: GoogleFonts.cairoTextTheme(ThemeData.dark().textTheme),
    );
  }
}
```

---

## 📱 التنقل والمسارات

### Go Router Configuration
```dart
final GoRouter router = GoRouter(
  initialLocation: '/splash',
  routes: [
    GoRoute(
      path: '/splash',
      builder: (context, state) => const SplashPage(),
    ),
    GoRoute(
      path: '/login',
      builder: (context, state) => const LoginPage(),
    ),
    GoRoute(
      path: '/home',
      builder: (context, state) => const HomePage(),
      routes: [
        GoRoute(
          path: '/estate/:id',
          builder: (context, state) {
            final estateId = state.pathParameters['id']!;
            return EstateDetailsPage(estateId: estateId);
          },
        ),
      ],
    ),
  ],
  redirect: (context, state) {
    final isLoggedIn = FirebaseAuth.instance.currentUser != null;
    final isLoggingIn = state.location == '/login';
    
    if (!isLoggedIn && !isLoggingIn) {
      return '/login';
    }
    
    if (isLoggedIn && isLoggingIn) {
      return '/home';
    }
    
    return null;
  },
);
```

---

## 🧪 الاختبارات

### Unit Tests
```dart
// اختبار Use Case
void main() {
  group('GetAllEstates', () {
    late GetAllEstates usecase;
    late MockEstateRepository mockRepository;
    
    setUp(() {
      mockRepository = MockEstateRepository();
      usecase = GetAllEstates(mockRepository);
    });
    
    test('should get estates from repository', () async {
      // arrange
      final tEstates = [Estate(id: '1', title: 'Test Estate')];
      when(mockRepository.getAllEstates())
          .thenAnswer((_) async => tEstates);
      
      // act
      final result = await usecase();
      
      // assert
      expect(result, tEstates);
      verify(mockRepository.getAllEstates());
      verifyNoMoreInteractions(mockRepository);
    });
  });
}
```

### Widget Tests
```dart
void main() {
  group('EstateCard Widget', () {
    testWidgets('should display estate information', (tester) async {
      // arrange
      const estate = Estate(
        id: '1',
        title: 'Test Estate',
        price: 1000,
        location: 'Kuwait City',
      );
      
      // act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EstateCard(estate: estate),
          ),
        ),
      );
      
      // assert
      expect(find.text('Test Estate'), findsOneWidget);
      expect(find.text('1000 د.ك'), findsOneWidget);
      expect(find.text('Kuwait City'), findsOneWidget);
    });
  });
}
```

---

## 🚀 النشر والتوزيع

### Android Build
```bash
# بناء APK للاختبار
flutter build apk --release

# بناء App Bundle للنشر
flutter build appbundle --release
```

### iOS Build
```bash
# بناء للمحاكي
flutter build ios --simulator

# بناء للجهاز
flutter build ios --release
```

### إعداد CI/CD
```yaml
# .github/workflows/build.yml
name: Build and Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.6.1'
    - run: flutter pub get
    - run: flutter test
    - run: flutter build apk --release
```

---

## 👨‍💻 حول فريق التطوير

### Codnet Moroccan
هذا التطبيق تم تطويره بواسطة فريق **Codnet Moroccan**، فريق متخصص في تطوير التطبيقات المتقدمة والحلول التقنية المبتكرة.

### منهجية التطوير
- **Clean Architecture**: لضمان قابلية الصيانة والتوسع
- **Test-Driven Development**: لضمان جودة الكود
- **Agile Methodology**: للتطوير السريع والمرن
- **Code Review**: لضمان جودة ومعايير الكود
- **Continuous Integration**: للتكامل والنشر المستمر

### معايير الجودة
- **Code Coverage**: 80% كحد أدنى للاختبارات
- **Performance**: أوقات استجابة أقل من 2 ثانية
- **Security**: اتباع أفضل ممارسات الأمان
- **Accessibility**: دعم إمكانية الوصول للجميع
- **Internationalization**: دعم متعدد اللغات

### التواصل مع الفريق
- **البريد الإلكتروني**: <EMAIL>
- **Slack**: #codnet-krea-dev
- **GitHub**: @codnet-moroccan

---

*تم إنشاء هذا الدليل بواسطة فريق Codnet Moroccan - للمزيد من التفاصيل التقنية، راجع الكود المصدري والتعليقات داخل الملفات*
