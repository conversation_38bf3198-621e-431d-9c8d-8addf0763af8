import '../entities/virtual_tour.dart';
import '../repositories/virtual_tour_repository.dart';

/// حالة استخدام للحصول على جولات العقار الافتراضية
class GetEstateVirtualTours {
  final VirtualTourRepository repository;

  GetEstateVirtualTours(this.repository);

  /// الحصول على جولات العقار الافتراضية
  /// [estateId] هو معرف العقار
  /// يعيد قائمة بجولات العقار الافتراضية
  Future<List<VirtualTour>> call(String estateId) async {
    if (estateId.isEmpty) {
      throw Exception('معرف العقار مطلوب');
    }
    
    return await repository.getEstateVirtualTours(estateId);
  }
}
