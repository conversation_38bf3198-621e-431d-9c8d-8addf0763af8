// lib/core/services/user_rating_service.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../domain/models/rating/user_rating_model.dart';
import '../../domain/models/rating/user_rating_summary_model.dart';
import 'realtime_notification_service.dart';

/// خدمة تقييم المستخدمين
class UserRatingService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final RealtimeNotificationService _notificationService = RealtimeNotificationService();

  /// إضافة تقييم جديد
  Future<String> addUserRating(UserRatingModel rating) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('يجب تسجيل الدخول لإضافة تقييم');
    }

    // التحقق من عدم تقييم المستخدم لنفسه
    if (rating.reviewerId == rating.targetUserId) {
      throw Exception('لا يمكن تقييم نفسك');
    }

    // التحقق من عدم وجود تقييم سابق لنفس التفاعل
    if (rating.relatedRequestId != null) {
      final existingRating = await _firestore
          .collection('user_ratings')
          .where('reviewerId', isEqualTo: rating.reviewerId)
          .where('targetUserId', isEqualTo: rating.targetUserId)
          .where('relatedRequestId', isEqualTo: rating.relatedRequestId)
          .get();

      if (existingRating.docs.isNotEmpty) {
        throw Exception('لقد قمت بتقييم هذا المستخدم مسبقاً لهذا التفاعل');
      }
    }

    // التحقق من صحة البيانات
    if (!rating.isValid) {
      throw Exception('بيانات التقييم غير صحيحة');
    }

    try {
      // إضافة التقييم
      final docRef = await _firestore.collection('user_ratings').add(rating.toFirestore());

      // تحديث ملخص تقييمات المستخدم المُقيَّم
      await _updateUserRatingSummary(rating.targetUserId);

      // إرسال إشعار للمستخدم المُقيَّم
      await _notificationService.sendNotification(
        recipientId: rating.targetUserId,
        type: RealtimeNotificationType.newRating,
        title: 'تقييم جديد',
        body: 'تلقيت تقييماً جديداً من ${rating.reviewerName}',
        data: {
          'ratingId': docRef.id,
          'reviewerId': rating.reviewerId,
          'overallRating': rating.overallRating.toString(),
        },
      );

      return docRef.id;
    } catch (e) {
      throw Exception('خطأ في إضافة التقييم: $e');
    }
  }

  /// الحصول على تقييمات مستخدم
  Future<List<UserRatingModel>> getUserRatings(
    String userId, {
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      Query query = _firestore
          .collection('user_ratings')
          .where('targetUserId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => UserRatingModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('خطأ في جلب التقييمات: $e');
    }
  }

  /// الحصول على ملخص تقييمات المستخدم
  Future<UserRatingSummaryModel?> getUserRatingSummary(String userId) async {
    try {
      final doc = await _firestore
          .collection('user_rating_summaries')
          .doc(userId)
          .get();

      if (doc.exists) {
        return UserRatingSummaryModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('خطأ في جلب ملخص التقييمات: $e');
    }
  }

  /// إضافة رد على التقييم
  Future<void> addOwnerResponse(String ratingId, String response) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('يجب تسجيل الدخول لإضافة رد');
    }

    try {
      // التحقق من أن المستخدم هو صاحب التقييم
      final ratingDoc = await _firestore
          .collection('user_ratings')
          .doc(ratingId)
          .get();

      if (!ratingDoc.exists) {
        throw Exception('التقييم غير موجود');
      }

      final rating = UserRatingModel.fromFirestore(ratingDoc);
      if (rating.targetUserId != user.uid) {
        throw Exception('لا يمكنك الرد على هذا التقييم');
      }

      // إضافة الرد
      await _firestore.collection('user_ratings').doc(ratingId).update({
        'ownerResponse': response,
        'ownerResponseDate': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // إرسال إشعار للمُقيِّم
      await _notificationService.sendNotification(
        recipientId: rating.reviewerId,
        type: RealtimeNotificationType.ratingResponse,
        title: 'رد على التقييم',
        body: 'رد ${rating.targetUserName} على تقييمك',
        data: {
          'ratingId': ratingId,
          'targetUserId': rating.targetUserId,
        },
      );
    } catch (e) {
      throw Exception('خطأ في إضافة الرد: $e');
    }
  }

  /// تعليم التقييم كمفيد
  Future<void> markRatingAsHelpful(String ratingId) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('يجب تسجيل الدخول');
    }

    try {
      // التحقق من عدم تعليم التقييم مسبقاً
      final helpfulDoc = await _firestore
          .collection('rating_helpful')
          .where('ratingId', isEqualTo: ratingId)
          .where('userId', isEqualTo: user.uid)
          .get();

      if (helpfulDoc.docs.isNotEmpty) {
        throw Exception('لقد قمت بتعليم هذا التقييم كمفيد مسبقاً');
      }

      // إضافة التعليم كمفيد
      await _firestore.collection('rating_helpful').add({
        'ratingId': ratingId,
        'userId': user.uid,
        'createdAt': FieldValue.serverTimestamp(),
      });

      // تحديث عداد المفيد
      await _firestore.collection('user_ratings').doc(ratingId).update({
        'helpfulCount': FieldValue.increment(1),
      });
    } catch (e) {
      throw Exception('خطأ في تعليم التقييم كمفيد: $e');
    }
  }

  /// الإبلاغ عن تقييم
  Future<void> reportRating(String ratingId, String reason) async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('يجب تسجيل الدخول للإبلاغ');
    }

    try {
      // إضافة البلاغ
      await _firestore.collection('rating_reports').add({
        'ratingId': ratingId,
        'reporterId': user.uid,
        'reason': reason,
        'createdAt': FieldValue.serverTimestamp(),
        'status': 'pending', // pending, reviewed, resolved
      });

      // تعليم التقييم كمُبلغ عنه
      await _firestore.collection('user_ratings').doc(ratingId).update({
        'isReported': true,
      });
    } catch (e) {
      throw Exception('خطأ في الإبلاغ عن التقييم: $e');
    }
  }

  /// التحقق من إمكانية تقييم المستخدم
  Future<bool> canRateUser(String targetUserId, {String? relatedRequestId}) async {
    final user = _auth.currentUser;
    if (user == null) return false;

    if (user.uid == targetUserId) return false;

    // التحقق من وجود تفاعل سابق
    if (relatedRequestId != null) {
      // التحقق من عدم وجود تقييم سابق لنفس التفاعل
      final existingRating = await _firestore
          .collection('user_ratings')
          .where('reviewerId', isEqualTo: user.uid)
          .where('targetUserId', isEqualTo: targetUserId)
          .where('relatedRequestId', isEqualTo: relatedRequestId)
          .get();

      return existingRating.docs.isEmpty;
    }

    return true;
  }

  /// تحديث ملخص تقييمات المستخدم
  Future<void> _updateUserRatingSummary(String userId) async {
    try {
      // جلب جميع تقييمات المستخدم
      final ratingsSnapshot = await _firestore
          .collection('user_ratings')
          .where('targetUserId', isEqualTo: userId)
          .get();

      if (ratingsSnapshot.docs.isEmpty) {
        return;
      }

      final ratings = ratingsSnapshot.docs
          .map((doc) => UserRatingModel.fromFirestore(doc))
          .toList();

      // حساب المتوسطات
      final totalRatings = ratings.length;
      final averageOverall = ratings.map((r) => r.overallRating).reduce((a, b) => a + b) / totalRatings;
      final averageCommunication = ratings.map((r) => r.communicationRating).reduce((a, b) => a + b) / totalRatings;
      final averageReliability = ratings.map((r) => r.reliabilityRating).reduce((a, b) => a + b) / totalRatings;
      final averageProfessionalism = ratings.map((r) => r.professionalismRating).reduce((a, b) => a + b) / totalRatings;
      final averageResponsiveness = ratings.map((r) => r.responsivenessRating).reduce((a, b) => a + b) / totalRatings;

      // حساب توزيع النجوم
      final starDistribution = <int, int>{};
      for (int i = 1; i <= 5; i++) {
        starDistribution[i] = ratings.where((r) => r.overallRating.round() == i).length;
      }

      // إنشاء أو تحديث الملخص
      final summary = UserRatingSummaryModel(
        userId: userId,
        totalRatings: totalRatings,
        averageOverallRating: averageOverall,
        averageCommunicationRating: averageCommunication,
        averageReliabilityRating: averageReliability,
        averageProfessionalismRating: averageProfessionalism,
        averageResponsivenessRating: averageResponsiveness,
        starDistribution: starDistribution,
        lastUpdated: DateTime.now(),
      );

      await _firestore
          .collection('user_rating_summaries')
          .doc(userId)
          .set(summary.toFirestore(), SetOptions(merge: true));
    } catch (e) {
      // تجاهل أخطاء تحديث الملخص
    }
  }
}
