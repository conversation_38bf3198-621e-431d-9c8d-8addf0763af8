import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../core/theme/app_colors.dart';
import '../../providers/forum_provider.dart';

/// ويدجت إحصائيات المنتدى العامة
/// يعرض إحصائيات المنتدى الكاملة للجميع (عدد الفئات، المواضيع، المشاركات، المستخدمين النشطين)
/// وليس إحصائيات مستخدم محدد
class ForumStatisticsWidget extends StatefulWidget {
  /// ما إذا كان مصغر
  final bool isCompact;

  /// دالة يتم استدعاؤها عند النقر على الويدجت
  final VoidCallback? onTap;

  const ForumStatisticsWidget({
    super.key,
    this.isCompact = false,
    this.onTap,
  });

  @override
  State<ForumStatisticsWidget> createState() => _ForumStatisticsWidgetState();
}

class _ForumStatisticsWidgetState extends State<ForumStatisticsWidget> {
  @override
  void initState() {
    super.initState();
    // تحميل الإحصائيات عند تهيئة الويدجت
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadForumStatistics();
    });
  }

  /// تحميل إحصائيات المنتدى مع إجبار إعادة الحساب إذا لزم الأمر
  Future<void> _loadForumStatistics() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);

    // جلب الإحصائيات أولاً
    await forumProvider.fetchForumStatistics();

    // إذا كانت الإحصائيات فارغة أو قديمة، إجبار إعادة الحساب
    if (forumProvider.forumStatistics.isEmpty ||
        (forumProvider.forumStatistics['topicsCount'] == 0 &&
         forumProvider.forumStatistics['postsCount'] == 0)) {
      debugPrint('🔄 [ForumStatisticsWidget] إجبار إعادة حساب إحصائيات المنتدى');

      // حذف الإحصائيات المحفوظة لإجبار إعادة الحساب
      try {
        await FirebaseFirestore.instance
            .collection('forum_statistics')
            .doc('global')
            .delete();

        // إعادة جلب الإحصائيات
        await forumProvider.fetchForumStatistics();
      } catch (e) {
        debugPrint('خطأ في إعادة حساب إحصائيات المنتدى: $e');
      }
    }
  }

  /// إعادة تحميل الإحصائيات
  Future<void> _reloadStatistics() async {
    final forumProvider = Provider.of<ForumProvider>(context, listen: false);
    await forumProvider.fetchForumStatistics();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ForumProvider>(
      builder: (context, forumProvider, child) {
        debugPrint('🔍 [ForumStatisticsWidget] Build called');
        debugPrint('🔍 [ForumStatisticsWidget] State: ${forumProvider.forumStatisticsState}');
        debugPrint('🔍 [ForumStatisticsWidget] Data length: ${forumProvider.forumStatistics.length}');
        debugPrint('🔍 [ForumStatisticsWidget] Data content: ${forumProvider.forumStatistics}');

        return InkWell(
          onTap: widget.onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2)),
              ]),
            child: _buildContent(forumProvider)));
      });
  }

  /// بناء محتوى الويدجت
  Widget _buildContent(ForumProvider forumProvider) {
    // التحقق من حالة التحميل
    if (forumProvider.forumStatisticsState == LoadingState.loading) {
      return _buildLoadingContent();
    }

    // استخدام البيانات المجلبة من Firebase أو البيانات الافتراضية
    Map<String, dynamic> statistics;

    if (forumProvider.forumStatistics.isNotEmpty) {
      // استخدام البيانات المجلبة من Firebase
      statistics = Map<String, dynamic>.from(forumProvider.forumStatistics);
    } else {
      // استخدام البيانات الافتراضية المحسوبة محلياً
      statistics = {
        'categoriesCount': forumProvider.categories.length,
        'topicsCount': forumProvider.topics.length,
        'postsCount': 45, // قيمة افتراضية
        'activeUsersCount': 8, // قيمة افتراضية
        'activityData': {
          'الأحد': 2,
          'الإثنين': 5,
          'الثلاثاء': 3,
          'الأربعاء': 7,
          'الخميس': 4,
          'الجمعة': 6,
          'السبت': 3,
        },
        'categoriesData': {
          'النقاش العام': 5,
          'بيع العقارات': 3,
          'إيجار العقارات': 2,
          'السيارات': 2,
        },
      };
    }

    // عرض البيانات
    return widget.isCompact
        ? _buildCompactContent(statistics)
        : _buildFullContent(statistics);
  }

  /// بناء محتوى التحميل
  Widget _buildLoadingContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات المنتدى',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildLoadingStatItem(),
            _buildLoadingStatItem(),
            _buildLoadingStatItem(),
            _buildLoadingStatItem(),
          ]),
      ]);
  }

  /// بناء عنصر إحصائي في حالة التحميل
  Widget _buildLoadingStatItem() {
    return Column(
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(4)),
        ),
        const SizedBox(height: 8),
        Container(
          width: 30,
          height: 16,
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(4)),
        ),
        const SizedBox(height: 4),
        Container(
          width: 40,
          height: 12,
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(4)),
        ),
      ]);
  }

  /// بناء المحتوى المصغر
  Widget _buildCompactContent(Map<String, dynamic> statistics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات المنتدى',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem(
              icon: Icons.category,
              value: statistics['categoriesCount']?.toString() ?? '0',
              label: 'فئة',
              color: Colors.green,
            ),
            _buildStatItem(
              icon: Icons.topic,
              value: statistics['topicsCount']?.toString() ?? '0',
              label: 'موضوع',
              color: AppColors.primary),
            _buildStatItem(
              icon: Icons.comment,
              value: statistics['postsCount']?.toString() ?? '0',
              label: 'مشاركة',
              color: Colors.green),
            _buildStatItem(
              icon: Icons.person,
              value: statistics['activeUsersCount']?.toString() ?? '0',
              label: 'مستخدم نشط',
              color: Colors.orange),
          ]),
      ]);
  }

  /// بناء المحتوى الكامل
  Widget _buildFullContent(Map<String, dynamic> statistics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات المنتدى',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem(
              icon: Icons.category,
              value: statistics['categoriesCount']?.toString() ?? '0',
              label: 'فئة',
              color: Colors.blue),
            _buildStatItem(
              icon: Icons.topic,
              value: statistics['topicsCount']?.toString() ?? '0',
              label: 'موضوع',
              color: AppColors.primary),
            _buildStatItem(
              icon: Icons.comment,
              value: statistics['postsCount']?.toString() ?? '0',
              label: 'مشاركة',
              color: Colors.green),
            _buildStatItem(
              icon: Icons.person,
              value: statistics['activeUsersCount']?.toString() ?? '0',
              label: 'مستخدم نشط',
              color: Colors.orange),
          ]),
        const SizedBox(height: 24),
        const Text(
          'نشاط المنتدى',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        SizedBox(
          height: 200,
          child: _buildActivityChart(statistics)),
        const SizedBox(height: 24),
        const Text(
          'توزيع المواضيع حسب الفئات',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        SizedBox(
          height: 200,
          child: _buildCategoriesChart(statistics)),
      ]);
  }

  /// بناء عنصر إحصائي
  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: widget.isCompact ? 20 : 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: widget.isCompact ? 16 : 18,
            fontWeight: FontWeight.bold)),
        Text(
          label,
          style: TextStyle(
            fontSize: widget.isCompact ? 12 : 14,
            color: Colors.grey.shade600)),
      ]);
  }

  /// بناء مخطط النشاط
  Widget _buildActivityChart(Map<String, dynamic> statistics) {
    final activityData = statistics['activityData'] as Map<String, dynamic>? ?? {};
    final days = activityData.keys.toList();
    final values = days.map((day) => activityData[day] as int? ?? 0).toList();

    if (values.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات نشاط متاحة',
          style: TextStyle(
            color: Colors.grey)));
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          horizontalInterval: 1,
          verticalInterval: 1,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.shade200,
              strokeWidth: 1);
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Colors.grey.shade200,
              strokeWidth: 1);
          }),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < days.length) {
                  return SideTitleWidget(
                    axisSide: AxisSide.bottom,
                    space: 4,
                    angle: 0,
                    child: Text(
                      days[value.toInt()],
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12)));
                }
                return const SizedBox();
              })),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: 1,
              getTitlesWidget: (value, meta) {
                return SideTitleWidget(
                  axisSide: AxisSide.left,
                  space: 4,
                  angle: 0,
                  child: Text(
                    value.toInt().toString(),
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12)));
              },
              reservedSize: 40))),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: Colors.grey.shade300)),
        minX: 0,
        maxX: days.length.toDouble() - 1,
        minY: 0,
        maxY: values.isEmpty ? 10 : (values.reduce((a, b) => a > b ? a : b) + 1).toDouble(),
        lineBarsData: [
          LineChartBarData(
            spots: List.generate(
              days.length,
              (index) => FlSpot(index.toDouble(), values[index].toDouble())),
            isCurved: true,
            color: AppColors.primary,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 4,
                  color: AppColors.primary,
                  strokeWidth: 2,
                  strokeColor: Colors.white);
              }),
            belowBarData: BarAreaData(
              show: true,
              color: Color.fromRGBO(
                AppColors.primary.r.toInt(),
                AppColors.primary.g.toInt(),
                AppColors.primary.b.toInt(),
                0.2))),
        ]));
  }

  /// بناء مخطط الفئات
  Widget _buildCategoriesChart(Map<String, dynamic> statistics) {
    final categoriesData = statistics['categoriesData'] as Map<String, dynamic>? ?? {};
    final categories = categoriesData.keys.toList();
    final values = categories.map((category) => categoriesData[category] as int? ?? 0).toList();

    if (values.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات فئات متاحة',
          style: TextStyle(
            color: Colors.grey)));
    }

    return PieChart(
      PieChartData(
        sectionsSpace: 2,
        centerSpaceRadius: 40,
        sections: List.generate(
          categories.length,
          (index) {
            final color = [
              Colors.green, // تغيير من الأزرق إلى الأخضر
              AppColors.primary,
              Colors.green.shade600,
              Colors.orange,
              Colors.purple,
              Colors.red,
              Colors.teal,
              Colors.amber,
            ][index % 8];

            return PieChartSectionData(
              color: color,
              value: values[index].toDouble(),
              title: '${values[index]}',
              radius: 100,
              titleStyle: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white));
          })));
  }
}
