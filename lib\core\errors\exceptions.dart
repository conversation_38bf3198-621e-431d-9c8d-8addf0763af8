/// استثناء عدم وجود اتصال بالإنترنت
class NoInternetException implements Exception {
  final String message;

  NoInternetException([this.message = 'لا يوجد اتصال بالإنترنت']);

  @override
  String toString() => message;
}

/// استثناء طلب غير صالح
class BadRequestException implements Exception {
  final String message;

  BadRequestException([this.message = 'طلب غير صالح']);

  @override
  String toString() => message;
}

/// استثناء غير مصرح به
class UnauthorizedException implements Exception {
  final String message;

  UnauthorizedException([this.message = 'غير مصرح لك بالوصول']);

  @override
  String toString() => message;
}

/// استثناء ممنوع
class ForbiddenException implements Exception {
  final String message;

  ForbiddenException([this.message = 'الوصول ممنوع']);

  @override
  String toString() => message;
}

/// استثناء غير موجود
class NotFoundException implements Exception {
  final String message;

  NotFoundException([this.message = 'المورد غير موجود']);

  @override
  String toString() => message;
}

/// استثناء خطأ في الخادم
class ServerException implements Exception {
  final String message;

  ServerException([this.message = 'حدث خطأ في الخادم']);

  @override
  String toString() => message;
}

/// استثناء خطأ في التخزين المؤقت
class CacheException implements Exception {
  final String message;

  CacheException([this.message = 'حدث خطأ في التخزين المؤقت']);

  @override
  String toString() => message;
}

/// استثناء خطأ في المصادقة
class AuthenticationException implements Exception {
  final String message;

  AuthenticationException([this.message = 'حدث خطأ في المصادقة']);

  @override
  String toString() => message;
}

/// استثناء خطأ في الدفع
class PaymentException implements Exception {
  final String message;

  PaymentException([this.message = 'حدث خطأ في عملية الدفع']);

  @override
  String toString() => message;
}

/// استثناء خطأ في التكامل مع الخدمات الخارجية
class IntegrationException implements Exception {
  final String message;
  final String service;

  IntegrationException(this.service, [this.message = 'حدث خطأ في التكامل مع الخدمة الخارجية']);

  @override
  String toString() => '$message: $service';
}

/// استثناء خطأ في التحقق من الملكية
class OwnershipVerificationException implements Exception {
  final String message;

  OwnershipVerificationException([this.message = 'حدث خطأ في التحقق من الملكية']);

  @override
  String toString() => message;
}

/// استثناء خطأ في التقييم
class ValuationException implements Exception {
  final String message;

  ValuationException([this.message = 'حدث خطأ في عملية التقييم']);

  @override
  String toString() => message;
}

/// استثناء خطأ في الخدمات الحكومية
class GovernmentServiceException implements Exception {
  final String message;
  final String service;

  GovernmentServiceException(this.service, [this.message = 'حدث خطأ في الخدمة الحكومية']);

  @override
  String toString() => '$message: $service';
}

/// استثناء خطأ في خدمات النقل
class TransportationServiceException implements Exception {
  final String message;

  TransportationServiceException([this.message = 'حدث خطأ في خدمة النقل']);

  @override
  String toString() => message;
}

/// استثناء خطأ في خدمات المرافق
class UtilitiesServiceException implements Exception {
  final String message;

  UtilitiesServiceException([this.message = 'حدث خطأ في خدمة المرافق']);

  @override
  String toString() => message;
}
