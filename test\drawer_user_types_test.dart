// test/drawer_user_types_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:kuwait_corners/core/constants/user_types.dart';

void main() {
  group('UserTypeConstants Tests', () {
    test('canViewPropertyRequests should work correctly', () {
      // الباحثون لا يمكنهم رؤية طلبات العقارات العامة
      expect(UserTypeConstants.canViewPropertyRequests(UserTypeConstants.seeker), false);
      
      // المستثمرون يمكنهم رؤية طلبات العقارات
      expect(UserTypeConstants.canViewPropertyRequests(UserTypeConstants.agent), true);
      
      // المالكون يمكنهم رؤية طلبات العقارات
      expect(UserTypeConstants.canViewPropertyRequests(UserTypeConstants.owner), true);
      
      // الشركات يمكنها رؤية طلبات العقارات
      expect(UserTypeConstants.canViewPropertyRequests(UserTypeConstants.company), true);
    });

    test('canCreatePropertyRequests should work correctly', () {
      // الباحثون فقط يمكنهم إنشاء طلبات العقارات
      expect(UserTypeConstants.canCreatePropertyRequests(UserTypeConstants.seeker), true);
      
      // المستثمرون لا يمكنهم إنشاء طلبات العقارات
      expect(UserTypeConstants.canCreatePropertyRequests(UserTypeConstants.agent), false);
      
      // المالكون لا يمكنهم إنشاء طلبات العقارات
      expect(UserTypeConstants.canCreatePropertyRequests(UserTypeConstants.owner), false);
      
      // الشركات لا يمكنها إنشاء طلبات العقارات
      expect(UserTypeConstants.canCreatePropertyRequests(UserTypeConstants.company), false);
    });

    test('canAccessFavorites should work correctly', () {
      // الباحثون يمكنهم الوصول للمفضلة
      expect(UserTypeConstants.canAccessFavorites(UserTypeConstants.seeker), true);
      
      // المستثمرون يمكنهم الوصول للمفضلة
      expect(UserTypeConstants.canAccessFavorites(UserTypeConstants.agent), true);
      
      // المالكون يمكنهم الوصول للمفضلة
      expect(UserTypeConstants.canAccessFavorites(UserTypeConstants.owner), true);
      
      // الشركات لا يمكنها الوصول للمفضلة
      expect(UserTypeConstants.canAccessFavorites(UserTypeConstants.company), false);
    });

    test('canPostAds should work correctly', () {
      // الباحثون لا يمكنهم نشر إعلانات
      expect(UserTypeConstants.canPostAds(UserTypeConstants.seeker), false);
      
      // المستثمرون يمكنهم نشر إعلانات
      expect(UserTypeConstants.canPostAds(UserTypeConstants.agent), true);
      
      // المالكون يمكنهم نشر إعلانات
      expect(UserTypeConstants.canPostAds(UserTypeConstants.owner), true);
      
      // الشركات يمكنها نشر إعلانات
      expect(UserTypeConstants.canPostAds(UserTypeConstants.company), true);
    });

    test('normalizeUserType should work correctly', () {
      // اختبار تطبيع أنواع المستخدمين المختلفة
      expect(UserTypeConstants.normalizeUserType('seeker'), UserTypeConstants.seeker);
      expect(UserTypeConstants.normalizeUserType('user'), UserTypeConstants.seeker);
      expect(UserTypeConstants.normalizeUserType('property_seeker'), UserTypeConstants.seeker);
      expect(UserTypeConstants.normalizeUserType('باحث عن عقار'), UserTypeConstants.seeker);
      
      expect(UserTypeConstants.normalizeUserType('agent'), UserTypeConstants.agent);
      expect(UserTypeConstants.normalizeUserType('investor'), UserTypeConstants.agent);
      expect(UserTypeConstants.normalizeUserType('وسيط عقاري'), UserTypeConstants.agent);
      expect(UserTypeConstants.normalizeUserType('مستثمر'), UserTypeConstants.agent);
      
      expect(UserTypeConstants.normalizeUserType('owner'), UserTypeConstants.owner);
      expect(UserTypeConstants.normalizeUserType('مالك عقار'), UserTypeConstants.owner);
      expect(UserTypeConstants.normalizeUserType('property_owner'), UserTypeConstants.owner);
      
      expect(UserTypeConstants.normalizeUserType('company'), UserTypeConstants.company);
      expect(UserTypeConstants.normalizeUserType('شركة عقارية'), UserTypeConstants.company);
      expect(UserTypeConstants.normalizeUserType('real_estate_company'), UserTypeConstants.company);
      
      // اختبار القيم غير المعروفة
      expect(UserTypeConstants.normalizeUserType('unknown'), UserTypeConstants.seeker);
      expect(UserTypeConstants.normalizeUserType(''), UserTypeConstants.seeker);
    });

    test('getArabicName should return correct names', () {
      expect(UserTypeConstants.getArabicName(UserTypeConstants.seeker), 'باحث عن عقار');
      expect(UserTypeConstants.getArabicName(UserTypeConstants.agent), 'مستثمر');
      expect(UserTypeConstants.getArabicName(UserTypeConstants.owner), 'مالك عقار');
      expect(UserTypeConstants.getArabicName(UserTypeConstants.company), 'شركة عقارية');
      expect(UserTypeConstants.getArabicName('unknown'), 'باحث عن عقار');
    });

    test('isValidUserType should work correctly', () {
      expect(UserTypeConstants.isValidUserType(UserTypeConstants.seeker), true);
      expect(UserTypeConstants.isValidUserType(UserTypeConstants.agent), true);
      expect(UserTypeConstants.isValidUserType(UserTypeConstants.owner), true);
      expect(UserTypeConstants.isValidUserType(UserTypeConstants.company), true);
      expect(UserTypeConstants.isValidUserType('unknown'), false);
      expect(UserTypeConstants.isValidUserType(''), false);
    });
  });

  group('User Type Logic Tests', () {
    test('Seeker user type logic', () {
      const userType = UserTypeConstants.seeker;
      
      // الباحثون يمكنهم:
      expect(UserTypeConstants.canCreatePropertyRequests(userType), true);
      expect(UserTypeConstants.canAccessFavorites(userType), true);
      expect(UserTypeConstants.canAccessComparison(userType), true);
      
      // الباحثون لا يمكنهم:
      expect(UserTypeConstants.canViewPropertyRequests(userType), false);
      expect(UserTypeConstants.canPostAds(userType), false);
      expect(UserTypeConstants.canManageClients(userType), false);
      expect(UserTypeConstants.canAccessMarketAnalysis(userType), false);
      expect(UserTypeConstants.canCopyProperties(userType), false);
      expect(UserTypeConstants.canManageProjects(userType), false);
      expect(UserTypeConstants.canManageTeam(userType), false);
      expect(UserTypeConstants.canAccessReports(userType), false);
    });

    test('Agent user type logic', () {
      const userType = UserTypeConstants.agent;
      
      // المستثمرون يمكنهم:
      expect(UserTypeConstants.canViewPropertyRequests(userType), true);
      expect(UserTypeConstants.canPostAds(userType), true);
      expect(UserTypeConstants.canAccessFavorites(userType), true);
      expect(UserTypeConstants.canAccessComparison(userType), true);
      expect(UserTypeConstants.canManageClients(userType), true);
      expect(UserTypeConstants.canAccessMarketAnalysis(userType), true);
      expect(UserTypeConstants.canCopyProperties(userType), true);
      
      // المستثمرون لا يمكنهم:
      expect(UserTypeConstants.canCreatePropertyRequests(userType), false);
      expect(UserTypeConstants.canManageProjects(userType), false);
      expect(UserTypeConstants.canManageTeam(userType), false);
      expect(UserTypeConstants.canAccessReports(userType), false);
    });

    test('Owner user type logic', () {
      const userType = UserTypeConstants.owner;
      
      // المالكون يمكنهم:
      expect(UserTypeConstants.canViewPropertyRequests(userType), true);
      expect(UserTypeConstants.canPostAds(userType), true);
      expect(UserTypeConstants.canAccessFavorites(userType), true);
      expect(UserTypeConstants.canAccessComparison(userType), true);
      
      // المالكون لا يمكنهم:
      expect(UserTypeConstants.canCreatePropertyRequests(userType), false);
      expect(UserTypeConstants.canManageClients(userType), false);
      expect(UserTypeConstants.canAccessMarketAnalysis(userType), false);
      expect(UserTypeConstants.canCopyProperties(userType), false);
      expect(UserTypeConstants.canManageProjects(userType), false);
      expect(UserTypeConstants.canManageTeam(userType), false);
      expect(UserTypeConstants.canAccessReports(userType), false);
    });

    test('Company user type logic', () {
      const userType = UserTypeConstants.company;
      
      // الشركات يمكنها:
      expect(UserTypeConstants.canViewPropertyRequests(userType), true);
      expect(UserTypeConstants.canPostAds(userType), true);
      expect(UserTypeConstants.canManageClients(userType), true);
      expect(UserTypeConstants.canAccessMarketAnalysis(userType), true);
      expect(UserTypeConstants.canManageProjects(userType), true);
      expect(UserTypeConstants.canManageTeam(userType), true);
      expect(UserTypeConstants.canAccessReports(userType), true);
      
      // الشركات لا يمكنها:
      expect(UserTypeConstants.canCreatePropertyRequests(userType), false);
      expect(UserTypeConstants.canAccessFavorites(userType), false);
      expect(UserTypeConstants.canAccessComparison(userType), false);
      expect(UserTypeConstants.canCopyProperties(userType), false);
    });
  });
}
