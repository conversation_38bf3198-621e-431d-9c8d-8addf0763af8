import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
// import 'package:file_picker/file_picker.dart';  // Temporarily disabled
import 'package:kuwait_corners/core/theme/app_colors.dart';
import 'package:kuwait_corners/core/theme/cairo_text_styles.dart';
import 'package:kuwait_corners/presentation/widgets/project_background_widget.dart';

/// صفحة إضافة مشروع جديد
class AddProjectPage extends StatefulWidget {
  const AddProjectPage({super.key});

  @override
  State<AddProjectPage> createState() => _AddProjectPageState();
}

class _AddProjectPageState extends State<AddProjectPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();
  final _budgetController = TextEditingController();
  final _teamSizeController = TextEditingController();

  String _selectedType = 'سكني';
  String _selectedStatus = 'قيد التخطيط';
  String _selectedPriority = 'متوسط';
  bool _isUrgent = false;
  bool _isLoading = false;
  DateTime? _startDate;
  DateTime? _endDate;

  // قائمة الوثائق المرفوعة
  final List<Map<String, dynamic>> _uploadedDocuments = [];
  bool _isUploadingDocument = false;

  final List<String> _projectTypes = [
    'سكني',
    'تجاري',
    'إداري',
    'صناعي',
    'مختلط',
    'ترفيهي',
    'تعليمي',
    'صحي',
  ];

  final List<String> _projectStatuses = [
    'قيد التخطيط',
    'قيد التنفيذ',
    'متوقف',
    'مكتمل',
  ];

  final List<String> _priorities = [
    'منخفض',
    'متوسط',
    'عالي',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
            title: Text('إضافة مشروع جديد', style: CairoTextStyles.appBarTitle),
            backgroundColor: AppColors.primary,
            elevation: 0,
            iconTheme: const IconThemeData(color: Colors.white)),
        body: ProjectBackgroundWidget(
            child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildBasicInfoSection(),
                          const SizedBox(height: 20),
                          _buildBudgetAndTeamSection(),
                          const SizedBox(height: 20),
                          _buildDatesSection(),
                          const SizedBox(height: 20),
                          _buildPrioritySection(),
                          const SizedBox(height: 20),
                          _buildDocumentsSection(),
                          const SizedBox(height: 32),
                          _buildActionButtons(),
                        ])))));
  }

  Widget _buildBasicInfoSection() {
    return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.1), width: 1),
        ),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(children: [
            Icon(Icons.info, color: AppColors.primary, size: 24),
            const SizedBox(width: 12),
            Text(
              'المعلومات الأساسية',
              style: CairoTextStyles.titleLarge.copyWith(
                color: AppColors.primary,
              ),
            ),
          ]),
          const SizedBox(height: 20),
          TextFormField(
              controller: _nameController,
              style: CairoTextStyles.bodyMedium,
              decoration: InputDecoration(
                  labelText: 'اسم المشروع *',
                  labelStyle: CairoTextStyles.bodyMedium,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                        color: AppColors.primary.withValues(alpha: 0.3)),
                  ),
                  focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide:
                          BorderSide(color: AppColors.primary, width: 2)),
                  prefixIcon:
                      Icon(Icons.business_center, color: AppColors.primary)),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم المشروع';
                }
                return null;
              }),
          const SizedBox(height: 16),
          TextFormField(
              controller: _descriptionController,
              style: CairoTextStyles.bodyMedium,
              maxLines: 3,
              decoration: InputDecoration(
                  labelText: 'وصف المشروع',
                  labelStyle: CairoTextStyles.bodyMedium,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                        color: AppColors.primary.withValues(alpha: 0.3)),
                  ),
                  focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide:
                          BorderSide(color: AppColors.primary, width: 2)),
                  prefixIcon: Icon(Icons.description, color: AppColors.primary),
                  alignLabelWithHint: true)),
          const SizedBox(height: 16),
          Row(children: [
            Expanded(
                child: DropdownButtonFormField<String>(
                    value: _selectedType,
                    style: CairoTextStyles.bodyMedium,
                    decoration: InputDecoration(
                        labelText: 'نوع المشروع',
                        labelStyle: CairoTextStyles.bodyMedium,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                              color: AppColors.primary.withValues(alpha: 0.3)),
                        ),
                        focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide:
                                BorderSide(color: AppColors.primary, width: 2)),
                        prefixIcon:
                            Icon(Icons.category, color: AppColors.primary)),
                    items: _projectTypes.map((type) {
                      return DropdownMenuItem(
                          value: type,
                          child: Text(type, style: CairoTextStyles.bodyMedium));
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedType = value!;
                      });
                    })),
            const SizedBox(width: 16),
            Expanded(
                child: DropdownButtonFormField<String>(
                    value: _selectedStatus,
                    style: CairoTextStyles.bodyMedium,
                    decoration: InputDecoration(
                        labelText: 'حالة المشروع',
                        labelStyle: CairoTextStyles.bodyMedium,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                              color: AppColors.primary.withValues(alpha: 0.3)),
                        ),
                        focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide:
                                BorderSide(color: AppColors.primary, width: 2)),
                        prefixIcon: Icon(Icons.flag, color: AppColors.primary)),
                    items: _projectStatuses.map((status) {
                      return DropdownMenuItem(
                          value: status,
                          child:
                              Text(status, style: CairoTextStyles.bodyMedium));
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedStatus = value!;
                      });
                    })),
          ]),
          const SizedBox(height: 16),
          TextFormField(
              controller: _locationController,
              style: CairoTextStyles.bodyMedium,
              decoration: InputDecoration(
                  labelText: 'موقع المشروع',
                  labelStyle: CairoTextStyles.bodyMedium,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                        color: AppColors.primary.withValues(alpha: 0.3)),
                  ),
                  focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide:
                          BorderSide(color: AppColors.primary, width: 2)),
                  prefixIcon:
                      Icon(Icons.location_on, color: AppColors.primary))),
        ]));
  }

  Widget _buildBudgetAndTeamSection() {
    return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.1), width: 1),
        ),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(children: [
            Icon(Icons.account_balance_wallet,
                color: AppColors.primary, size: 24),
            const SizedBox(width: 12),
            Text(
              'الميزانية والفريق',
              style: CairoTextStyles.titleLarge.copyWith(
                color: AppColors.primary,
              ),
            ),
          ]),
          const SizedBox(height: 20),
          Row(children: [
            Expanded(
                child: TextFormField(
                    controller: _budgetController,
                    style: CairoTextStyles.bodyMedium,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                        labelText: 'الميزانية الإجمالية (د.ك) *',
                        labelStyle: CairoTextStyles.bodyMedium,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                              color: AppColors.primary.withValues(alpha: 0.3)),
                        ),
                        focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide:
                                BorderSide(color: AppColors.primary, width: 2)),
                        prefixIcon:
                            Icon(Icons.attach_money, color: AppColors.primary),
                        hintText: '0',
                        hintStyle: CairoTextStyles.bodyMedium
                            .copyWith(color: Colors.grey[500])),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال الميزانية';
                      }
                      if (double.tryParse(value) == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      return null;
                    })),
            const SizedBox(width: 16),
            Expanded(
                child: TextFormField(
                    controller: _teamSizeController,
                    style: CairoTextStyles.bodyMedium,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                        labelText: 'حجم الفريق المتوقع',
                        labelStyle: CairoTextStyles.bodyMedium,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                              color: AppColors.primary.withValues(alpha: 0.3)),
                        ),
                        focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide:
                                BorderSide(color: AppColors.primary, width: 2)),
                        prefixIcon: Icon(Icons.group, color: AppColors.primary),
                        hintText: '0',
                        hintStyle: CairoTextStyles.bodyMedium
                            .copyWith(color: Colors.grey[500])))),
          ]),
        ]));
  }

  Widget _buildDatesSection() {
    return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.1), width: 1),
        ),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(children: [
            Icon(Icons.date_range, color: AppColors.primary, size: 24),
            const SizedBox(width: 12),
            Text(
              'التواريخ المهمة',
              style: CairoTextStyles.titleLarge.copyWith(
                color: AppColors.primary,
              ),
            ),
          ]),
          const SizedBox(height: 20),
          Row(children: [
            Expanded(
                child: InkWell(
                    onTap: () => _selectStartDate(),
                    child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                            border: Border.all(
                                color:
                                    AppColors.primary.withValues(alpha: 0.3)),
                            borderRadius: BorderRadius.circular(8)),
                        child: Row(children: [
                          Icon(Icons.play_arrow, color: Colors.green[600]),
                          const SizedBox(width: 12),
                          Expanded(
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                Text('تاريخ البداية',
                                    style: CairoTextStyles.bodyMedium.copyWith(
                                      fontWeight: FontWeight.w600,
                                    )),
                                const SizedBox(height: 4),
                                Text(
                                    _startDate != null
                                        ? _formatDate(_startDate!)
                                        : 'اختر تاريخ البداية',
                                    style: CairoTextStyles.bodyMedium.copyWith(
                                        color: _startDate != null
                                            ? Colors.green[600]
                                            : Colors.grey[500],
                                        fontWeight: FontWeight.w500)),
                              ])),
                          Icon(Icons.calendar_today, color: Colors.grey[400]),
                        ])))),
            const SizedBox(width: 16),
            Expanded(
                child: InkWell(
                    onTap: () => _selectEndDate(),
                    child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                            border: Border.all(
                                color:
                                    AppColors.primary.withValues(alpha: 0.3)),
                            borderRadius: BorderRadius.circular(8)),
                        child: Row(children: [
                          Icon(Icons.flag, color: Colors.red[600]),
                          const SizedBox(width: 12),
                          Expanded(
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                Text('تاريخ الانتهاء',
                                    style: CairoTextStyles.bodyMedium.copyWith(
                                      fontWeight: FontWeight.w600,
                                    )),
                                const SizedBox(height: 4),
                                Text(
                                    _endDate != null
                                        ? _formatDate(_endDate!)
                                        : 'اختر تاريخ الانتهاء',
                                    style: CairoTextStyles.bodyMedium.copyWith(
                                        color: _endDate != null
                                            ? Colors.red[600]
                                            : Colors.grey[500],
                                        fontWeight: FontWeight.w500)),
                              ])),
                          Icon(Icons.calendar_today, color: Colors.grey[400]),
                        ])))),
          ]),
          if (_startDate != null && _endDate != null) ...[
            const SizedBox(height: 16),
            Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                        color: AppColors.primary.withValues(alpha: 0.3))),
                child: Row(children: [
                  Icon(Icons.timer, color: AppColors.primary),
                  const SizedBox(width: 8),
                  Text(
                      'مدة المشروع: ${_endDate!.difference(_startDate!).inDays} يوم',
                      style: CairoTextStyles.bodyMedium.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w600)),
                ])),
          ],
        ]));
  }

  Widget _buildPrioritySection() {
    return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.1), width: 1),
        ),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(children: [
            Icon(Icons.priority_high, color: AppColors.primary, size: 24),
            const SizedBox(width: 12),
            Text(
              'الأولوية والإعدادات',
              style: CairoTextStyles.titleLarge.copyWith(
                color: AppColors.primary,
              ),
            ),
          ]),
          const SizedBox(height: 20),
          DropdownButtonFormField<String>(
              value: _selectedPriority,
              style: CairoTextStyles.bodyMedium,
              decoration: InputDecoration(
                  labelText: 'أولوية المشروع',
                  labelStyle: CairoTextStyles.bodyMedium,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                        color: AppColors.primary.withValues(alpha: 0.3)),
                  ),
                  focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide:
                          BorderSide(color: AppColors.primary, width: 2)),
                  prefixIcon: Icon(Icons.star, color: AppColors.primary)),
              items: _priorities.map((priority) {
                return DropdownMenuItem(
                    value: priority,
                    child: Row(children: [
                      Icon(
                          priority == 'عالي'
                              ? Icons.keyboard_arrow_up
                              : priority == 'متوسط'
                                  ? Icons.remove
                                  : Icons.keyboard_arrow_down,
                          color: priority == 'عالي'
                              ? Colors.red
                              : priority == 'متوسط'
                                  ? Colors.orange
                                  : Colors.green,
                          size: 16),
                      const SizedBox(width: 8),
                      Text(priority, style: CairoTextStyles.bodyMedium),
                    ]));
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedPriority = value!;
                });
              }),
          const SizedBox(height: 16),
          Row(children: [
            Checkbox(
                value: _isUrgent,
                onChanged: (value) {
                  setState(() {
                    _isUrgent = value!;
                  });
                },
                activeColor: Colors.red),
            Text('مشروع عاجل',
                style: CairoTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                )),
            const SizedBox(width: 8),
            if (_isUrgent)
              Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(12)),
                  child: Text('عاجل',
                      style: CairoTextStyles.bodySmall.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ))),
          ]),
        ]));
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: BorderSide(color: AppColors.primary),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'إلغاء',
              style: CairoTextStyles.button.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveProject,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    'حفظ المشروع',
                    style: CairoTextStyles.button,
                  ),
          ),
        ),
      ],
    );
  }

  // Helper Methods
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: _startDate ?? DateTime.now(),
        firstDate: DateTime.now(),
        lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
        locale: const Locale('ar'));
    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;
        // إذا كان تاريخ الانتهاء قبل تاريخ البداية، قم بمسحه
        if (_endDate != null && _endDate!.isBefore(_startDate!)) {
          _endDate = null;
        }
      });
    }
  }

  void _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: _endDate ??
            (_startDate?.add(const Duration(days: 30)) ??
                DateTime.now().add(const Duration(days: 30))),
        firstDate: _startDate ?? DateTime.now(),
        lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
        locale: const Locale('ar'));
    if (picked != null && picked != _endDate) {
      setState(() {
        _endDate = picked;
      });
    }
  }

  void _saveProject() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      final projectData = {
        'name': _nameController.text.trim(),
        'description': _descriptionController.text.trim(),
        'location': _locationController.text.trim(),
        'type': _selectedType,
        'status': _selectedStatus,
        'priority': _selectedPriority,
        'budget': double.tryParse(_budgetController.text) ?? 0.0,
        'spentBudget': 0.0,
        'teamSize': int.tryParse(_teamSizeController.text) ?? 0,
        'tasksCount': 0,
        'completedTasks': 0,
        'progress': 0.0,
        'isUrgent': _isUrgent,
        'companyId': currentUser.uid,
        'createdAt': FieldValue.serverTimestamp(),
        'startDate':
            _startDate != null ? Timestamp.fromDate(_startDate!) : null,
        'endDate': _endDate != null ? Timestamp.fromDate(_endDate!) : null,
        'teamMembers': [],
        'tasks': [],
        'milestones': [],
        'documents': [],
        'notes': [],
      };

      await FirebaseFirestore.instance.collection('projects').add(projectData);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text(
              'تم إضافة المشروع بنجاح',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8))));
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text(
              'حدث خطأ في إضافة المشروع: ${e.toString()}',
              style: CairoTextStyles.bodyMedium.copyWith(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8))));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // قسم الوثائق
  Widget _buildDocumentsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
            color: AppColors.primary.withValues(alpha: 0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(children: [
            Icon(Icons.folder, color: AppColors.primary, size: 24),
            const SizedBox(width: 12),
            Text(
              'الوثائق والمرفقات',
              style: CairoTextStyles.titleLarge.copyWith(
                color: AppColors.primary,
              ),
            ),
          ]),
          const SizedBox(height: 20),

          // زر إضافة وثيقة
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _isUploadingDocument ? null : _uploadDocument,
              icon: _isUploadingDocument
                  ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor:
                            AlwaysStoppedAnimation<Color>(AppColors.primary),
                      ),
                    )
                  : Icon(Icons.upload_file, color: AppColors.primary),
              label: Text(
                _isUploadingDocument ? 'جاري الرفع...' : 'رفع وثيقة',
                style: CairoTextStyles.bodyMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: AppColors.primary),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),

          // قائمة الوثائق المرفوعة
          if (_uploadedDocuments.isNotEmpty) ...[
            const SizedBox(height: 16),
            Text(
              'الوثائق المرفوعة (${_uploadedDocuments.length})',
              style: CairoTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            ...List.generate(_uploadedDocuments.length, (index) {
              final doc = _uploadedDocuments[index];
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getFileIcon(doc['type']),
                      color: AppColors.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            doc['name'],
                            style: CairoTextStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            doc['size'],
                            style: CairoTextStyles.bodySmall.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => _removeDocument(index),
                      icon:
                          const Icon(Icons.delete, color: Colors.red, size: 20),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              );
            }),
          ],

          // نصائح
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.info, color: Colors.blue[600], size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'يمكنك رفع المخططات، العقود، التصاريح، والوثائق الأخرى المتعلقة بالمشروع',
                    style: CairoTextStyles.bodySmall.copyWith(
                      color: Colors.blue[700],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // رفع وثيقة
  void _uploadDocument() async {
    setState(() {
      _isUploadingDocument = true;
    });

    // Temporarily disabled due to file_picker v1 embedding issues
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('رفع الوثائق غير متاح مؤقتاً'),
          backgroundColor: Colors.orange,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }

    setState(() {
      _isUploadingDocument = false;
    });
  }

  // إزالة وثيقة
  void _removeDocument(int index) {
    setState(() {
      _uploadedDocuments.removeAt(index);
    });
  }

  // الحصول على أيقونة الملف
  IconData _getFileIcon(String type) {
    switch (type.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'jpg':
      case 'jpeg':
      case 'png':
        return Icons.image;
      default:
        return Icons.insert_drive_file;
    }
  }

  // تنسيق حجم الملف
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _budgetController.dispose();
    _teamSizeController.dispose();
    super.dispose();
  }
}
