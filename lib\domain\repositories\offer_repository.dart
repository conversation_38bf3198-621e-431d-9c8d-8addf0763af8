import '../entities/offer.dart';

/// واجهة مستودع العروض
abstract class OfferRepository {
  /// إنشاء عرض جديد
  Future<String> createOffer(Offer offer);
  
  /// تحديث عرض موجود
  Future<void> updateOffer(Offer offer);
  
  /// حذف عرض
  Future<void> deleteOffer(String offerId);
  
  /// الحصول على عرض بواسطة المعرف
  Future<Offer?> getOfferById(String offerId);
  
  /// الحصول على عروض العقار
  Future<List<Offer>> getEstateOffers(String estateId);
  
  /// الحصول على عروض المستخدم المقدمة
  Future<List<Offer>> getUserSubmittedOffers(String userId);
  
  /// الحصول على عروض المستخدم المستلمة
  Future<List<Offer>> getUserReceivedOffers(String userId);
  
  /// الحصول على عروض المستخدم المقدمة بالتحميل المتدرج
  /// [userId] معرف المستخدم
  /// [limit] عدد العروض في كل صفحة
  /// [lastOfferId] معرف آخر عرض تم تحميله (للصفحات التالية)
  /// [status] حالة العروض المطلوبة
  /// يعيد Map تحتوي على:
  /// - 'offers': قائمة العروض
  /// - 'lastOfferId': معرف آخر عرض (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من العروض
  Future<Map<String, dynamic>> getUserSubmittedOffersPaginated({
    required String userId,
    int limit = 20,
    String? lastOfferId,
    OfferStatus? status,
  });
  
  /// الحصول على عروض المستخدم المستلمة بالتحميل المتدرج
  /// [userId] معرف المستخدم
  /// [limit] عدد العروض في كل صفحة
  /// [lastOfferId] معرف آخر عرض تم تحميله (للصفحات التالية)
  /// [status] حالة العروض المطلوبة
  /// يعيد Map تحتوي على:
  /// - 'offers': قائمة العروض
  /// - 'lastOfferId': معرف آخر عرض (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من العروض
  Future<Map<String, dynamic>> getUserReceivedOffersPaginated({
    required String userId,
    int limit = 20,
    String? lastOfferId,
    OfferStatus? status,
  });
  
  /// مراجعة عرض
  Future<void> reviewOffer(String offerId, String updatedBy);
  
  /// قبول عرض
  Future<void> acceptOffer(String offerId, String updatedBy);
  
  /// رفض عرض
  Future<void> rejectOffer(String offerId, String updatedBy, String reason);
  
  /// سحب عرض
  Future<void> withdrawOffer(String offerId, String updatedBy);
  
  /// إكمال عرض
  Future<void> completeOffer(String offerId, String updatedBy, String contractId);
  
  /// التحقق من العروض المنتهية وتحديث حالتها
  Future<int> checkExpiredOffers();
  
  /// الحصول على أعلى عرض للعقار
  Future<Offer?> getHighestOfferForEstate(String estateId);
  
  /// الحصول على عدد العروض للعقار
  Future<int> getOffersCountForEstate(String estateId);
  
  /// الحصول على عدد العروض للمستخدم
  Future<Map<String, int>> getOffersCountForUser(String userId);
  
  /// الحصول على إحصائيات العروض
  Future<Map<String, dynamic>> getOffersStatistics(String userId);
  
  /// إنشاء عقد من عرض
  Future<String> createContractFromOffer(String offerId);
  
  /// إرسال إشعار بعرض جديد
  Future<void> sendOfferNotification(String offerId);
  
  /// إرسال تذكير بعرض
  Future<void> sendOfferReminder(String offerId);
}
