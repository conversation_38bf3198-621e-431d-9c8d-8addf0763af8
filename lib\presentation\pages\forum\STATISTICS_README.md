# نظام الإحصائيات في المنتدى

## نوعان من الإحصائيات

### 1. إحصائيات المنتدى العامة (ForumStatisticsWidget)
**الموقع:** قسم المواضيع في الصفحة الرئيسية للمنتدى
**الغرض:** عرض إحصائيات المنتدى الكاملة للجميع
**البيانات المعروضة:**
- عدد الفئات الكلي
- عدد المواضيع الكلي
- عدد المشاركات الكلي
- عدد المستخدمين النشطين
- نشاط المنتدى العام
- توزيع المواضيع حسب الفئات

**الملفات:**
- `lib/presentation/widgets/forum/forum_statistics_widget.dart`
- يستدعي `forumProvider.fetchForumStatistics()`

### 2. إحصائيات المستخدم الشخصية (ForumStatisticsTab)
**الموقع:** تبويب "الإحصائيات" في الصفحة الرئيسية للمنتدى
**الغرض:** عرض إحصائيات نشاط المستخدم الحالي فقط
**البيانات المعروضة:**
- مواضيع المستخدم
- مشاركات المستخدم
- إعجابات المستخدم (مستلمة ومرسلة)
- شارات المستخدم
- إنجازات المستخدم
- مستوى المستخدم ونقاطه
- نشاط المستخدم الشخصي

**الملفات:**
- `lib/presentation/pages/forum/forum_statistics_tab.dart`
- يستدعي `forumProvider.fetchUserStatistics(userId)`

## التبويبات في إحصائيات المستخدم

### 1. تبويب الملخص
- الشارات المحققة
- إحصائيات المنتدى الشخصية
- رسم بياني للنشاط الشخصي

### 2. تبويب النشاطات
- معلومات العضوية
- إحصائيات النشاط التفصيلية
- معدلات النشاط (يومي، أسبوعي، شهري)
- رسم بياني للنشاط الشهري
- أحدث النشاطات

### 3. تبويب الإنجازات
- الشارات المحققة
- الإنجازات المحققة
- الإنجازات القادمة
- إحصائيات الإنجازات

## الفرق الأساسي

| الجانب | إحصائيات المنتدى العامة | إحصائيات المستخدم الشخصية |
|--------|------------------------|---------------------------|
| البيانات | جميع المستخدمين | المستخدم الحالي فقط |
| الوصول | متاح للجميع | يتطلب تسجيل الدخول |
| الموقع | قسم المواضيع | تبويب الإحصائيات |
| الغرض | نظرة عامة على المنتدى | تتبع النشاط الشخصي |
