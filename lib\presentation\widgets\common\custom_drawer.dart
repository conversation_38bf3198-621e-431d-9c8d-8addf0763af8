import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

import '../../../core/theme/app_colors.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/constants/user_types.dart';
import '../../../domain/entities/user.dart' as domain_user;
import '../../../domain/services/user_interface_customization_service.dart';
import '../../providers/auth_provider.dart' as app_auth;


/// درج التنقل المخصص
class CustomDrawer extends StatefulWidget {
  const CustomDrawer({super.key});

  @override
  State<CustomDrawer> createState() => _CustomDrawerState();
}

class _CustomDrawerState extends State<CustomDrawer> {
  late Timer _timer;
  Map<String, dynamic>? _userProfile;
  final ValueNotifier<String> _timeNotifier = ValueNotifier<String>('');

  @override
  void initState() {
    super.initState();
    _updateTime();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTime();
    });
    _loadUserProfile();
  }

  @override
  void dispose() {
    _timer.cancel();
    _timeNotifier.dispose();
    super.dispose();
  }

  /// تحديث الوقت الحالي (بدون setState لتجنب إعادة بناء الـ drawer كاملاً)
  void _updateTime() {
    final now = DateTime.now();
    final formatter = DateFormat('HH:mm:ss', 'ar');
    final newTime = formatter.format(now);
    _timeNotifier.value = newTime;
  }

  /// تحميل بيانات المستخدم
  Future<void> _loadUserProfile() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final doc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();
        if (doc.exists) {
          setState(() {
            _userProfile = doc.data();
          });
        }
      }
    } catch (e) {
      print('Error loading user profile: $e');
    }
  }

  /// جلب نوع المستخدم باستخدام UserInterfaceCustomizationService
  Future<domain_user.UserType?> _getUserType(String userId) async {
    try {
      final uiService = UserInterfaceCustomizationService();
      final userType = await uiService.getCurrentUserType();
      print('=== Drawer Debug ===');
      print('User ID: $userId');
      print('Detected User Type: $userType');
      print('Type Name: ${userType.toString()}');
      print('==================');
      return userType;
    } catch (e) {
      print('Drawer - Error getting user type: $e');
      return domain_user.UserType.seeker;
    }
  }



  /// التحقق من إمكانية الوصول للمفضلة
  bool _canAccessFavorites(domain_user.UserType userType) {
    final userTypeString = userType.toString().split('.').last;
    return UserTypeConstants.canAccessFavorites(userTypeString);
  }

  /// التحقق من إمكانية إضافة إعلانات
  bool _canPostAds(domain_user.UserType userType) {
    final userTypeString = userType.toString().split('.').last;
    return UserTypeConstants.canPostAds(userTypeString);
  }

  /// التحقق من إمكانية إدارة العملاء
  bool _canManageClients(domain_user.UserType userType) {
    final userTypeString = userType.toString().split('.').last;
    return UserTypeConstants.canManageClients(userTypeString);
  }

  /// التحقق من إمكانية تحليل السوق
  bool _canAccessMarketAnalysis(domain_user.UserType userType) {
    final userTypeString = userType.toString().split('.').last;
    return UserTypeConstants.canAccessMarketAnalysis(userTypeString);
  }

  /// التحقق من إمكانية نسخ العقارات
  bool _canCopyProperties(domain_user.UserType userType) {
    final userTypeString = userType.toString().split('.').last;
    return UserTypeConstants.canCopyProperties(userTypeString);
  }

  /// التحقق من إمكانية إدارة المشاريع
  bool _canManageProjects(domain_user.UserType userType) {
    final userTypeString = userType.toString().split('.').last;
    return UserTypeConstants.canManageProjects(userTypeString);
  }

  /// التحقق من إمكانية إدارة فريق العمل
  bool _canManageTeam(domain_user.UserType userType) {
    final userTypeString = userType.toString().split('.').last;
    return UserTypeConstants.canManageTeam(userTypeString);
  }

  /// التحقق من إمكانية الوصول للتقارير
  bool _canAccessReports(domain_user.UserType userType) {
    final userTypeString = userType.toString().split('.').last;
    return UserTypeConstants.canAccessReports(userTypeString);
  }

  /// التحقق من إمكانية إنشاء طلبات العقارات
  bool _canCreatePropertyRequests(domain_user.UserType userType) {
    final userTypeString = userType.toString().split('.').last;
    return UserTypeConstants.canCreatePropertyRequests(userTypeString);
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<app_auth.AuthProvider>(context);
    final user = authProvider.user;
    final isLoggedIn = authProvider.isLoggedIn;

    return Drawer(
      child: Column(
        children: [
          _buildModernHeader(context, user, isLoggedIn),
          Expanded(
            child: SafeArea(
              top: false, // لا نريد SafeArea في الأعلى لأن الـ header يغطي المنطقة
              child: FutureBuilder<domain_user.UserType?>(
              future: isLoggedIn ? _getUserType(user!.uid) : Future.value(null),
              builder: (context, snapshot) {
                // معالجة حالات التحميل والأخطاء
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(20.0),
                      child: CircularProgressIndicator()));
                }

                final userType = snapshot.data;
                print('========================');

                return SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                    // الأقسام الرئيسية
                    _buildDivider(label: 'التصفح'),
                    _buildMenuItem(
                      context,
                      title: 'الرئيسية',
                      icon: Icons.home_outlined,
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushReplacementNamed(context, AppRoutes.home);
                      },
                      isSpecial: true,
                    ),

                    _buildMenuItem(
                      context,
                      title: 'Lobby',
                      icon: Icons.forum_outlined,
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, AppRoutes.modernForum);
                      }),
                    _buildMenuItem(
                      context,
                      title: 'طلبات العقارات',
                      icon: Icons.assignment_outlined,
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, '/property-requests');
                      }),
                    _buildMenuItem(
                      context,
                      title: 'الجولات الافتراضية',
                      icon: Icons.view_in_ar_outlined,
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, AppRoutes.virtualToursManagement);
                      }),

                    // الأقسام المخصصة حسب نوع المستخدم
                    if (isLoggedIn && userType != null) ...[
                      _buildDivider(label: 'أدواتي'),
                      ..._buildUserTypeSpecificSections(context, userType),
                    ],

                    // أقسام المنتدى
                    if (isLoggedIn) ...[
                      _buildDivider(label: 'المنتدى'),
                      _buildMenuItem(
                        context,
                        title: 'مواضيعي',
                        icon: Icons.topic_outlined,
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, AppRoutes.forumUserTopics);
                        }),
                      _buildMenuItem(
                        context,
                        title: 'المواضيع المحفوظة',
                        icon: Icons.bookmark_outline,
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, AppRoutes.forumSavedTopics);
                        }),
                    ],

                    // أقسام عامة أخرى
                    _buildDivider(label: 'عام'),
                    _buildMenuItem(
                      context,
                      title: 'المحادثات',
                      icon: Icons.chat_outlined,
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, AppRoutes.conversations);
                      }),

                    // برامج الولاء والإحالة
                    if (isLoggedIn && userType != null && userType != domain_user.UserType.company) ...[
                      _buildDivider(label: 'المكافآت'),
                      _buildMenuItem(
                        context,
                        title: 'برنامج الولاء',
                        icon: Icons.loyalty_outlined,
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, AppRoutes.loyaltyProgram);
                        }),
                      _buildMenuItem(
                        context,
                        title: 'برنامج الإحالة',
                        icon: Icons.people_outline,
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, AppRoutes.referralProgram);
                        }),
                    ],
                _buildDivider(label: 'الحساب'),
                _buildMenuItem(
                  context,
                  title: 'الإعدادات',
                  icon: Icons.settings_outlined,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.settings);
                  }),

                if (isLoggedIn)
                  _buildMenuItem(
                    context,
                    title: 'تسجيل الخروج',
                    icon: Icons.logout_outlined,
                    onTap: () => _performEnhancedLogout(context),
                    isSpecial: true,
                  )
                else
                  _buildMenuItem(
                    context,
                    title: 'تسجيل الدخول',
                    icon: Icons.login_outlined,
                    onTap: () {
                      Navigator.pop(context);
                      // التنقل إلى صفحة تسجيل الدخول
                    },
                    isSpecial: true,
                  ),
                    ],
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الأقسام المخصصة حسب نوع المستخدم
  List<Widget> _buildUserTypeSpecificSections(BuildContext context, domain_user.UserType userType) {
    switch (userType) {
      case domain_user.UserType.seeker:
        return _buildSeekerSections(context);
      case domain_user.UserType.agent:
        return _buildAgentSections(context);
      case domain_user.UserType.owner:
        return _buildOwnerSections(context);
      case domain_user.UserType.company:
        return _buildCompanySections(context);
    }
  }

  /// أقسام الباحثين عن العقارات
  List<Widget> _buildSeekerSections(BuildContext context) {
    return [
      // المفضلة (متاحة للباحثين)
      if (_canAccessFavorites(domain_user.UserType.seeker))
        _buildMenuItem(
          context,
          title: 'المفضلة',
          icon: Icons.favorite_outline,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.favorites);
          }),
      // طلباتي (عرض طلبات المستخدم الشخصية - خاص بالباحثين فقط)
      if (_canCreatePropertyRequests(domain_user.UserType.seeker))
        _buildMenuItem(
          context,
          title: 'طلباتي',
          icon: Icons.assignment_ind_outlined,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.myPropertyRequests);
          }),
      // إنشاء طلب عقار (خاص بالباحثين فقط)
      if (_canCreatePropertyRequests(domain_user.UserType.seeker))
        _buildMenuItem(
          context,
          title: 'إنشاء طلب عقار',
          icon: Icons.add_home_outlined,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.createPropertyRequest);
          },
          isSpecial: true,
        ),
    ];
  }

  /// أقسام الوكلاء العقاريين/المستثمرين
  List<Widget> _buildAgentSections(BuildContext context) {
    return [
      // المفضلة (متاحة للوكلاء)
      if (_canAccessFavorites(domain_user.UserType.agent))
        _buildMenuItem(
          context,
          title: 'المفضلة',
          icon: Icons.favorite_outline,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.favorites);
          }),
      // إضافة عقار (متاح للوكلاء)
      if (_canPostAds(domain_user.UserType.agent))
        _buildMenuItem(
          context,
          title: 'إضافة عقار',
          icon: Icons.add_circle_outline,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.improvedAdCreation);
          },
          isSpecial: true,
        ),
      // إدارة العقارات
      _buildMenuItem(
        context,
        title: 'إدارة العقارات',
        icon: Icons.real_estate_agent_outlined,
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.myProperties);
        }),
      // إدارة العملاء (خاص بالوكلاء والشركات)
      if (_canManageClients(domain_user.UserType.agent))
        _buildMenuItem(
          context,
          title: 'إدارة العملاء',
          icon: Icons.people_outline,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.clients);
          }),
      // تحليل السوق (خاص بالوكلاء والشركات)
      if (_canAccessMarketAnalysis(domain_user.UserType.agent))
        _buildMenuItem(
          context,
          title: 'تحليل السوق',
          icon: Icons.analytics_outlined,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.marketAnalysis);
          }),
      // نسخ عقار (خاص بالمستثمرين فقط)
      if (_canCopyProperties(domain_user.UserType.agent))
        _buildMenuItem(
          context,
          title: 'نسخ عقار',
          icon: Icons.copy_outlined,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.copyProperty);
          }),
      // العقارات المنسوخة (خاص بالمستثمرين فقط)
      if (_canCopyProperties(domain_user.UserType.agent))
        _buildMenuItem(
          context,
          title: 'العقارات المنسوخة',
          icon: Icons.content_copy_outlined,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.copiedProperties);
          }),
      // تحليلات النسخ (خاص بالمستثمرين فقط)
      if (_canCopyProperties(domain_user.UserType.agent))
        _buildMenuItem(
          context,
          title: 'تحليلات النسخ',
          icon: Icons.bar_chart_outlined,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.copyAnalytics);
          }),
    ];
  }

  /// أقسام مالكي العقارات
  List<Widget> _buildOwnerSections(BuildContext context) {
    return [
      // المفضلة (متاحة للمالكين)
      if (_canAccessFavorites(domain_user.UserType.owner))
        _buildMenuItem(
          context,
          title: 'المفضلة',
          icon: Icons.favorite_outline,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.favorites);
          }),
      // إضافة عقار (متاح للمالكين)
      if (_canPostAds(domain_user.UserType.owner))
        _buildMenuItem(
          context,
          title: 'إضافة عقار',
          icon: Icons.add_home_outlined,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.improvedAdCreation);
          },
          isSpecial: true,
        ),
      // إدارة العقارات
      _buildMenuItem(
        context,
        title: 'إدارة العقارات',
        icon: Icons.home_work_outlined,
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.myProperties);
        }),
    ];
  }

  /// أقسام الشركات العقارية
  List<Widget> _buildCompanySections(BuildContext context) {
    return [
      // إضافة عقار (متاح للشركات)
      if (_canPostAds(domain_user.UserType.company))
        _buildMenuItem(
          context,
          title: 'إضافة عقار',
          icon: Icons.add_circle_outline,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.improvedAdCreation);
          },
          isSpecial: true,
        ),
      // إدارة العقارات
      _buildMenuItem(
        context,
        title: 'إدارة العقارات',
        icon: Icons.real_estate_agent_outlined,
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.myProperties);
        }),
      // إدارة العملاء (خاص بالشركات والوكلاء)
      if (_canManageClients(domain_user.UserType.company))
        _buildMenuItem(
          context,
          title: 'إدارة العملاء',
          icon: Icons.people_outline,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.clients);
          }),
      // تحليل السوق (خاص بالشركات والوكلاء)
      if (_canAccessMarketAnalysis(domain_user.UserType.company))
        _buildMenuItem(
          context,
          title: 'تحليل السوق',
          icon: Icons.analytics_outlined,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.marketAnalysis);
          }),
      // إدارة المشاريع (خاص بالشركات فقط)
      if (_canManageProjects(domain_user.UserType.company))
        _buildMenuItem(
          context,
          title: 'إدارة المشاريع',
          icon: Icons.business_outlined,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.projects);
          }),
      // فريق العمل (خاص بالشركات فقط)
      if (_canManageTeam(domain_user.UserType.company))
        _buildMenuItem(
          context,
          title: 'فريق العمل',
          icon: Icons.group_outlined,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.team);
          }),
      // التقارير (خاص بالشركات فقط)
      if (_canAccessReports(domain_user.UserType.company))
        _buildMenuItem(
          context,
          title: 'التقارير',
          icon: Icons.assessment_outlined,
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.reports);
          }),
    ];
  }

  /// بناء رأس الدرج العصري
  Widget _buildModernHeader(BuildContext context, User? user, bool isLoggedIn) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.35,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primaryLight,
            AppColors.primaryDark,
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
      child: Stack(
        children: [
          // خلفية بأشكال هندسية متحركة
          _buildAnimatedBackground(),

          // المحتوى الرئيسي
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: MediaQuery.of(context).padding.top + 10), // مساحة للـ status bar

                // الساعة الذكية
                _buildSmartClock(),

                const SizedBox(height: 10),

                // معلومات المستخدم
                _buildUserInfo(user, isLoggedIn),

                const SizedBox(height: 10),

                // زر الملف الشخصي
                if (isLoggedIn) _buildProfileButton(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الخلفية المتحركة
  Widget _buildAnimatedBackground() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _GeometricShapesPainter(),
      ),
    );
  }

  /// بناء الساعة الذكية
  Widget _buildSmartClock() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.access_time,
            color: Colors.white,
            size: 18,
          ),
          const SizedBox(width: 8),
          ValueListenableBuilder<String>(
            valueListenable: _timeNotifier,
            builder: (context, time, child) {
              return Text(
                time,
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// بناء معلومات المستخدم
  Widget _buildUserInfo(User? user, bool isLoggedIn) {
    return Row(
      children: [
        // صورة المستخدم
        _buildUserAvatar(user, isLoggedIn),

        const SizedBox(width: 16),

        // معلومات النص
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // اسم المستخدم
              Text(
                _getUserDisplayName(user, isLoggedIn),
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 4),

              // نوع المستخدم
              _buildUserTypeChip(),

              const SizedBox(height: 8),

              // البريد الإلكتروني
              if (isLoggedIn && user?.email != null)
                Text(
                  user!.email!,
                  style: GoogleFonts.cairo(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 12,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء صورة المستخدم
  Widget _buildUserAvatar(User? user, bool isLoggedIn) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: CircleAvatar(
        radius: 27,
        backgroundColor: Colors.white.withValues(alpha: 0.2),
        backgroundImage: _getUserProfileImage(user, isLoggedIn),
        child: _getUserProfileImage(user, isLoggedIn) == null
            ? Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primaryLight.withValues(alpha: 0.8),
                      AppColors.primary.withValues(alpha: 0.8),
                    ],
                  ),
                ),
                child: Icon(
                  Icons.person,
                  size: 30,
                  color: Colors.white,
                ),
              )
            : null,
      ),
    );
  }

  /// بناء شريحة نوع المستخدم
  Widget _buildUserTypeChip() {
    return FutureBuilder<domain_user.UserType?>(
      future: FirebaseAuth.instance.currentUser != null
          ? _getUserType(FirebaseAuth.instance.currentUser!.uid)
          : Future.value(null),
      builder: (context, snapshot) {
        final userType = snapshot.data;
        final userTypeName = _getUserTypeName(userType);

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Text(
            userTypeName,
            style: GoogleFonts.cairo(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        );
      },
    );
  }

  /// بناء زر الملف الشخصي
  Widget _buildProfileButton(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.profile);
          },
          borderRadius: BorderRadius.circular(25),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.person,
                  size: 16,
                  color: Colors.white,
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Text(
                    'عرض الملف الشخصي',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// الحصول على اسم المستخدم للعرض
  String _getUserDisplayName(User? user, bool isLoggedIn) {
    if (!isLoggedIn) return 'زائر';

    // محاولة الحصول على الاسم من بيانات المستخدم المحملة
    if (_userProfile != null) {
      final fullName = _userProfile!['fullNameOrCompanyName'] as String?;
      if (fullName != null && fullName.isNotEmpty) {
        return fullName;
      }
    }

    // العودة إلى اسم Firebase Auth
    return user?.displayName ?? 'مستخدم';
  }

  /// الحصول على صورة المستخدم
  ImageProvider? _getUserProfileImage(User? user, bool isLoggedIn) {
    if (!isLoggedIn) return null;

    // محاولة الحصول على الصورة من بيانات المستخدم المحملة
    if (_userProfile != null) {
      final profileImageUrl = _userProfile!['profileImageUrl'] as String?;
      if (profileImageUrl != null && profileImageUrl.isNotEmpty) {
        return NetworkImage(profileImageUrl);
      }
    }

    // العودة إلى صورة Firebase Auth
    if (user?.photoURL != null) {
      return NetworkImage(user!.photoURL!);
    }

    return null;
  }

  /// الحصول على اسم نوع المستخدم
  String _getUserTypeName(domain_user.UserType? userType) {
    if (userType == null) return 'زائر';

    switch (userType) {
      case domain_user.UserType.seeker:
        return 'باحث عن عقار';
      case domain_user.UserType.agent:
        return 'مستثمر';
      case domain_user.UserType.owner:
        return 'مالك عقار';
      case domain_user.UserType.company:
        return 'شركة عقارية';
    }
  }

  /// تسجيل الخروج الاحترافي مع حوار التأكيد
  Future<void> _performProfessionalLogout(BuildContext context) async {
    // إغلاق الـ drawer أولاً
    Navigator.pop(context);

    // عرض حوار التأكيد
    final bool? shouldLogout = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Icon(
                Icons.logout,
                color: AppColors.primary,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                'تسجيل الخروج',
                style: GoogleFonts.cairo(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          content: Text(
            'هل أنت متأكد من رغبتك في تسجيل الخروج؟\n\nسيتم إلغاء تفعيل خاصية "تذكرني" وستحتاج لإدخال بيانات الدخول مرة أخرى.',
            style: GoogleFonts.cairo(
              fontSize: 16,
              height: 1.5,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
              child: Text(
                'إلغاء',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[600],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'تسجيل الخروج',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );

    // إذا أكد المستخدم تسجيل الخروج
    if (shouldLogout == true && context.mounted) {
      try {
        // عرض مؤشر التحميل
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext loadingContext) {
            return Center(
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'جاري تسجيل الخروج...',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );

        // تسجيل الخروج من Firebase
        await FirebaseAuth.instance.signOut();

        // مسح بيانات "تذكرني" من التخزين المحلي
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('rememberMe', false);
        await prefs.remove('savedUserType');

        // إغلاق مؤشر التحميل
        if (context.mounted) {
          Navigator.of(context).pop();
        }

        // عرض رسالة نجاح
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 12),
                  Text(
                    'تم تسجيل الخروج بنجاح',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }

        // التوجه لصفحة تسجيل الدخول
        if (context.mounted) {
          Navigator.pushNamedAndRemoveUntil(
            context,
            AppRoutes.login,
            (route) => false,
          );
        }
      } catch (e) {
        // إغلاق مؤشر التحميل في حالة الخطأ
        if (context.mounted) {
          Navigator.of(context).pop();
        }

        // عرض رسالة خطأ
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.error, color: Colors.white),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'حدث خطأ أثناء تسجيل الخروج: $e',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }
      }
    }
  }

  /// تسجيل الخروج المحسن باستخدام الخدمة المتقدمة
  Future<void> _performEnhancedLogout(BuildContext context) async {
    try {
      // عرض حوار التأكيد أولاً
      final shouldLogout = await _showCustomLogoutConfirmationDialog(context);
      if (shouldLogout != true) {
        return;
      }

      // التحقق من أن السياق ما زال صالحاً قبل إغلاق الـ drawer
      if (!context.mounted) return;

      // إغلاق الـ drawer بعد التأكيد
      Navigator.pop(context);

      // التحقق من أن السياق ما زال صالحاً قبل تنفيذ عملية تسجيل الخروج
      if (!context.mounted) return;

      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  'جاري تسجيل الخروج...',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          );
        },
      );

      // تنفيذ عملية تسجيل الخروج
      await FirebaseAuth.instance.signOut();

      // مسح بيانات "تذكرني" من التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('rememberMe', false);
      await prefs.remove('savedUserType');
      await prefs.remove('userEmail');
      await prefs.remove('userPassword');

      // إغلاق مؤشر التحميل فوراً
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // التوجه لصفحة تسجيل الدخول فوراً بدون انتظار
      if (context.mounted) {
        Navigator.pushNamedAndRemoveUntil(
          context,
          AppRoutes.login,
          (route) => false,
        );
      }

      // عرض رسالة نجاح بعد التوجه
      await Future.delayed(const Duration(milliseconds: 300));
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 12),
                Text(
                  'تم تسجيل الخروج بنجاح',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // عرض رسالة خطأ
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'حدث خطأ أثناء تسجيل الخروج: $e',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }

  /// عرض حوار تأكيد تسجيل الخروج المخصص
  Future<bool?> _showCustomLogoutConfirmationDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: AppColors.primaryGradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.logout_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'تسجيل الخروج',
                style: GoogleFonts.cairo(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'هل أنت متأكد من رغبتك في تسجيل الخروج؟',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppColors.primary.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: AppColors.primary,
                          size: 18,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'سيتم تنفيذ الإجراءات التالية:',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    _buildLogoutActionItem('إنهاء جميع الجلسات النشطة'),
                    _buildLogoutActionItem('مسح البيانات المحفوظة محلياً'),
                    _buildLogoutActionItem('التوجه لصفحة تسجيل الدخول'),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
              child: Text(
                'إلغاء',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'تسجيل الخروج',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// بناء عنصر إجراء تسجيل الخروج
  Widget _buildLogoutActionItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Icon(
            Icons.check_circle_outline,
            color: AppColors.primary,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: GoogleFonts.cairo(
                fontSize: 13,
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر القائمة المحسن
  Widget _buildMenuItem(
    BuildContext context, {
    required String title,
    required IconData icon,
    required VoidCallback onTap,
    bool isSpecial = false,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.transparent,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          splashColor: AppColors.primary.withValues(alpha: 0.1),
          highlightColor: AppColors.primary.withValues(alpha: 0.05),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            child: Row(
              children: [
                // أيقونة محسنة مع خلفية دائرية
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: isSpecial
                        ? AppColors.primaryGradient
                        : LinearGradient(
                            colors: [
                              AppColors.primary.withValues(alpha: 0.1),
                              AppColors.primaryLight.withValues(alpha: 0.05),
                            ],
                          ),
                    border: Border.all(
                      color: AppColors.primary.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    icon,
                    size: 20,
                    color: isSpecial
                        ? Colors.white
                        : AppColors.primary,
                  ),
                ),

                const SizedBox(width: 16),

                // النص
                Expanded(
                  child: Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isSpecial
                          ? AppColors.primary
                          : AppColors.textPrimary,
                    ),
                  ),
                ),

                // سهم للإشارة
                Icon(
                  Icons.arrow_forward_ios,
                  size: 14,
                  color: AppColors.textLight,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء فاصل أنيق
  Widget _buildDivider({String? label}) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          if (label != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 1,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.transparent,
                          AppColors.primary.withValues(alpha: 0.3),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Text(
                    label,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 1,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.transparent,
                          AppColors.primary.withValues(alpha: 0.3),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
          ] else
            Container(
              height: 1,
              margin: const EdgeInsets.symmetric(horizontal: 24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    AppColors.border,
                    Colors.transparent,
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// رسام الأشكال الهندسية للخلفية
class _GeometricShapesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    final strokePaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.15)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    // رسم أشكال هندسية متنوعة
    _drawCircles(canvas, size, paint, strokePaint);
    _drawTriangles(canvas, size, strokePaint);
    _drawRectangles(canvas, size, paint, strokePaint);
    _drawLines(canvas, size, strokePaint);
  }

  /// رسم الدوائر
  void _drawCircles(Canvas canvas, Size size, Paint fillPaint, Paint strokePaint) {
    // دائرة كبيرة في الأعلى
    canvas.drawCircle(
      Offset(size.width * 0.8, size.height * 0.2),
      30,
      fillPaint,
    );

    // دائرة متوسطة في الوسط
    canvas.drawCircle(
      Offset(size.width * 0.1, size.height * 0.6),
      20,
      strokePaint,
    );

    // دائرة صغيرة في الأسفل
    canvas.drawCircle(
      Offset(size.width * 0.9, size.height * 0.8),
      15,
      fillPaint,
    );
  }

  /// رسم المثلثات
  void _drawTriangles(Canvas canvas, Size size, Paint strokePaint) {
    final path = Path();

    // مثلث في الأعلى اليسار
    path.moveTo(size.width * 0.05, size.height * 0.15);
    path.lineTo(size.width * 0.15, size.height * 0.15);
    path.lineTo(size.width * 0.1, size.height * 0.05);
    path.close();
    canvas.drawPath(path, strokePaint);

    // مثلث في الوسط اليمين
    final path2 = Path();
    path2.moveTo(size.width * 0.85, size.height * 0.45);
    path2.lineTo(size.width * 0.95, size.height * 0.55);
    path2.lineTo(size.width * 0.75, size.height * 0.55);
    path2.close();
    canvas.drawPath(path2, strokePaint);
  }

  /// رسم المستطيلات
  void _drawRectangles(Canvas canvas, Size size, Paint fillPaint, Paint strokePaint) {
    // مستطيل مائل في الوسط
    canvas.save();
    canvas.translate(size.width * 0.3, size.height * 0.4);
    canvas.rotate(0.5);
    canvas.drawRect(
      const Rect.fromLTWH(-15, -10, 30, 20),
      fillPaint,
    );
    canvas.restore();

    // مستطيل صغير في الأسفل
    canvas.drawRect(
      Rect.fromLTWH(
        size.width * 0.05,
        size.height * 0.85,
        25,
        15,
      ),
      strokePaint,
    );
  }

  /// رسم الخطوط
  void _drawLines(Canvas canvas, Size size, Paint strokePaint) {
    // خط قطري
    canvas.drawLine(
      Offset(size.width * 0.6, size.height * 0.1),
      Offset(size.width * 0.8, size.height * 0.3),
      strokePaint,
    );

    // خط أفقي
    canvas.drawLine(
      Offset(size.width * 0.1, size.height * 0.3),
      Offset(size.width * 0.4, size.height * 0.3),
      strokePaint,
    );

    // خط عمودي
    canvas.drawLine(
      Offset(size.width * 0.7, size.height * 0.6),
      Offset(size.width * 0.7, size.height * 0.9),
      strokePaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
