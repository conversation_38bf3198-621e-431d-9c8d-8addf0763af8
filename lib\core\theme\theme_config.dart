import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app_colors.dart';
import 'app_theme.dart';

/// إعدادات Theme للتطبيق - يضمن ثبات الألوان في جميع الأجهزة
class ThemeConfig {
  ThemeConfig._();

  /// تطبيق إعدادات النظام للتطبيق
  static void configureSystemUI() {
    // تعيين ألوان شريط الحالة والتنقل
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        // شريط الحالة
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,

        // شريط التنقل
        systemNavigationBarColor: AppColors.cardBackground,
        systemNavigationBarIconBrightness: Brightness.dark,
        systemNavigationBarDividerColor: AppColors.border));

    // منع تدوير الشاشة (اختياري)
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  /// تطبيق إعدادات النظام للوضع الداكن
  static void configureSystemUIDark() {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        // شريط الحالة
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,

        // شريط التنقل
        systemNavigationBarColor: AppColors.darkCardBackground,
        systemNavigationBarIconBrightness: Brightness.light,
        systemNavigationBarDividerColor: AppColors.borderDark));
  }

  /// الحصول على Theme المناسب حسب الوضع
  static ThemeData getTheme(bool isDarkMode) {
    if (isDarkMode) {
      configureSystemUIDark();
      return AppTheme.darkTheme;
    } else {
      configureSystemUI();
      return AppTheme.lightTheme;
    }
  }

  /// إعدادات إضافية للتطبيق
  static void configureApp() {
    // تعطيل debug banner في release mode
    WidgetsFlutterBinding.ensureInitialized();

    // تطبيق إعدادات النظام
    configureSystemUI();
  }

  /// ألوان ثابتة للاستخدام المباشر (لا تتأثر بالـ Theme)
  static const Map<String, Color> fixedColors = {
    'primary': AppColors.primary,
    'secondary': AppColors.secondary,
    'background': AppColors.background,
    'surface': AppColors.cardBackground,
    'error': AppColors.error,
    'success': AppColors.success,
    'warning': AppColors.warning,
    'info': AppColors.info,
    'textPrimary': AppColors.textPrimary,
    'textSecondary': AppColors.textSecondary,
    'border': AppColors.border,
  };

  /// الحصول على لون ثابت
  static Color getFixedColor(String colorName) {
    return fixedColors[colorName] ?? AppColors.primary;
  }

  /// تطبيق ألوان ثابتة على Widget
  static Widget withFixedColors({
    required Widget child,
    Color? backgroundColor,
    Color? textColor,
  }) {
    return Container(
        color: backgroundColor ?? AppColors.background,
        child: DefaultTextStyle(
            style: TextStyle(color: textColor ?? AppColors.textPrimary),
            child: child));
  }

  /// إنشاء MaterialApp مع إعدادات ثابتة
  static MaterialApp createApp({
    required Widget home,
    String? title,
    Map<String, WidgetBuilder>? routes,
    bool debugShowCheckedModeBanner = false,
    ThemeMode themeMode = ThemeMode.light,
  }) {
    return MaterialApp(
        title: title ?? 'KREA',
        debugShowCheckedModeBanner: debugShowCheckedModeBanner,

        // Themes ثابتة
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: themeMode,

        // إعدادات إضافية
        home: home,
        routes: routes ?? {},

        // إعدادات اللغة والمنطقة
        locale: const Locale('ar', 'SA'),

        // Builder لضمان تطبيق الألوان الثابتة
        builder: (context, child) {
          return withFixedColors(child: child ?? const SizedBox());
        });
  }

  /// إنشاء Scaffold مع ألوان ثابتة
  static Scaffold createScaffold({
    PreferredSizeWidget? appBar,
    Widget? body,
    Widget? floatingActionButton,
    Widget? drawer,
    Widget? bottomNavigationBar,
    Color? backgroundColor,
  }) {
    return Scaffold(
        backgroundColor: backgroundColor ?? AppColors.background,
        appBar: appBar,
        body: body,
        floatingActionButton: floatingActionButton,
        drawer: drawer,
        bottomNavigationBar: bottomNavigationBar);
  }

  /// إنشاء AppBar مع ألوان ثابتة
  static AppBar createAppBar({
    String? title,
    List<Widget>? actions,
    Widget? leading,
    bool automaticallyImplyLeading = true,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    return AppBar(
        title: title != null ? Text(title) : null,
        actions: actions,
        leading: leading,
        automaticallyImplyLeading: automaticallyImplyLeading,
        backgroundColor: backgroundColor ?? AppColors.primary,
        foregroundColor: foregroundColor ?? Colors.white,
        elevation: 0,
        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle.light);
  }

  /// إنشاء Card مع ألوان ثابتة
  static Card createCard({
    Widget? child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    Color? color,
    double? elevation,
  }) {
    return Card(
        color: color ?? AppColors.cardBackground,
        elevation: elevation ?? 2,
        margin: margin ?? const EdgeInsets.all(8),
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: const BorderSide(color: AppColors.border, width: 0.5)),
        child:
            padding != null ? Padding(padding: padding, child: child) : child);
  }

  /// إنشاء Button مع ألوان ثابتة
  static ElevatedButton createPrimaryButton({
    required VoidCallback? onPressed,
    required String text,
    Color? backgroundColor,
    Color? textColor,
    EdgeInsetsGeometry? padding,
  }) {
    return ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
            backgroundColor: backgroundColor ?? AppColors.primary,
            foregroundColor: textColor ?? Colors.white,
            padding: padding ??
                const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8))),
        child: Text(text));
  }

  /// إنشاء TextField مع ألوان ثابتة
  static TextField createTextField({
    TextEditingController? controller,
    String? hintText,
    String? labelText,
    bool obscureText = false,
    TextInputType? keyboardType,
    ValueChanged<String>? onChanged,
    Color? fillColor,
    Color? borderColor,
  }) {
    return TextField(
        controller: controller,
        obscureText: obscureText,
        keyboardType: keyboardType,
        onChanged: onChanged,
        decoration: InputDecoration(
            hintText: hintText,
            labelText: labelText,
            filled: true,
            fillColor: fillColor ?? AppColors.cardBackground,
            border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: borderColor ?? AppColors.border)),
            enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: borderColor ?? AppColors.border)),
            focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide:
                    const BorderSide(color: AppColors.primary, width: 2))));
  }
}
