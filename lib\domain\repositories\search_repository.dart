import '../entities/estate_base.dart';
import '../entities/search_criteria.dart';

/// واجهة مستودع البحث
abstract class SearchRepository {
  /// البحث عن العقارات باستخدام معايير البحث
  Future<List<EstateBase>> searchEstates(SearchCriteria criteria);
  
  /// البحث عن العقارات بالتحميل المتدرج
  /// [criteria] معايير البحث
  /// [limit] عدد العقارات في كل صفحة
  /// [lastDocumentId] معرف آخر مستند تم تحميله (للصفحات التالية)
  /// يعيد Map تحتوي على:
  /// - 'estates': قائمة العقارات
  /// - 'lastDocumentId': معرف آخر مستند (للاستخدام في الصفحة التالية)
  /// - 'hasMore': هل هناك المزيد من العقارات
  Future<Map<String, dynamic>> searchEstatesPaginated({
    required SearchCriteria criteria,
    int limit = 10,
    String? lastDocumentId,
  });
  
  /// حفظ معايير البحث
  Future<String> saveSearchCriteria(SearchCriteria criteria);
  
  /// تحديث معايير البحث المحفوظة
  Future<void> updateSearchCriteria(SearchCriteria criteria);
  
  /// حذف معايير البحث المحفوظة
  Future<void> deleteSearchCriteria(String criteriaId);
  
  /// الحصول على معايير البحث المحفوظة للمستخدم
  Future<List<SearchCriteria>> getSavedSearchCriteria(String userId);
  
  /// الحصول على معايير البحث بواسطة المعرف
  Future<SearchCriteria?> getSearchCriteriaById(String criteriaId);
  
  /// تفعيل/تعطيل الإشعارات لمعايير البحث
  Future<void> toggleSearchNotifications(String criteriaId, bool enabled);
  
  /// تحديث تاريخ آخر استخدام لمعايير البحث
  Future<void> updateLastUsedDate(String criteriaId);
  
  /// الحصول على اقتراحات البحث
  Future<List<String>> getSearchSuggestions(String query);
  
  /// الحصول على الفلاتر المتاحة
  Future<Map<String, List<String>>> getAvailableFilters();
  
  /// الحصول على إحصائيات البحث
  Future<Map<String, dynamic>> getSearchStatistics(SearchCriteria criteria);
}
