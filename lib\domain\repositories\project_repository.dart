import 'dart:io';
import '../entities/project.dart';
import '../entities/project_task.dart';
import '../entities/project_milestone.dart';
import '../entities/project_document.dart';
import '../entities/team_member.dart';

/// واجهة مستودع المشاريع
abstract class ProjectRepository {
  // إدارة المشاريع الأساسية
  
  /// إنشاء مشروع جديد
  Future<String> createProject(Project project);
  
  /// تحديث مشروع موجود
  Future<void> updateProject(Project project);
  
  /// حذف مشروع
  Future<void> deleteProject(String projectId);
  
  /// الحصول على مشروع بواسطة المعرف
  Future<Project?> getProjectById(String projectId);
  
  /// الحصول على مشاريع الشركة
  Future<List<Project>> getCompanyProjects(String companyId);
  
  /// الحصول على مشاريع الشركة بالتحميل المتدرج
  Future<Map<String, dynamic>> getCompanyProjectsPaginated({
    required String companyId,
    int limit = 20,
    String? lastProjectId,
    ProjectStatus? status,
    String? query,
  });
  
  /// البحث عن مشاريع
  Future<List<Project>> searchProjects({
    required String companyId,
    String? query,
    ProjectStatus? status,
    ProjectType? type,
    ProjectPriority? priority,
  });
  
  /// الحصول على مشاريع المستخدم (كعضو فريق)
  Future<List<Project>> getUserProjects(String userId);
  
  // إدارة المهام
  
  /// إضافة مهمة جديدة للمشروع
  Future<String> addProjectTask(String projectId, ProjectTask task);
  
  /// تحديث مهمة المشروع
  Future<void> updateProjectTask(ProjectTask task);
  
  /// حذف مهمة من المشروع
  Future<void> deleteProjectTask(String projectId, String taskId);
  
  /// الحصول على مهام المشروع
  Future<List<ProjectTask>> getProjectTasks(String projectId);
  
  /// الحصول على مهمة بواسطة المعرف
  Future<ProjectTask?> getTaskById(String taskId);
  
  /// تحديث حالة المهمة
  Future<void> updateTaskStatus(String taskId, TaskStatus status);
  
  /// تحديث تقدم المهمة
  Future<void> updateTaskProgress(String taskId, double progress);
  
  /// تعيين مهمة لعضو فريق
  Future<void> assignTaskToMember(String taskId, String memberId, String memberName);
  
  // إدارة المعالم
  
  /// إضافة معلم جديد للمشروع
  Future<String> addProjectMilestone(String projectId, ProjectMilestone milestone);
  
  /// تحديث معلم المشروع
  Future<void> updateProjectMilestone(ProjectMilestone milestone);
  
  /// حذف معلم من المشروع
  Future<void> deleteProjectMilestone(String projectId, String milestoneId);
  
  /// الحصول على معالم المشروع
  Future<List<ProjectMilestone>> getProjectMilestones(String projectId);
  
  /// الحصول على معلم بواسطة المعرف
  Future<ProjectMilestone?> getMilestoneById(String milestoneId);
  
  /// تحديث حالة المعلم
  Future<void> updateMilestoneStatus(String milestoneId, MilestoneStatus status);
  
  // إدارة الوثائق
  
  /// إضافة وثيقة جديدة للمشروع
  Future<String> addProjectDocument(String projectId, ProjectDocument document, File file);
  
  /// تحديث وثيقة المشروع
  Future<void> updateProjectDocument(ProjectDocument document, {File? newFile});
  
  /// حذف وثيقة من المشروع
  Future<void> deleteProjectDocument(String projectId, String documentId);
  
  /// الحصول على وثائق المشروع
  Future<List<ProjectDocument>> getProjectDocuments(String projectId);
  
  /// الحصول على وثيقة بواسطة المعرف
  Future<ProjectDocument?> getDocumentById(String documentId);
  
  /// تحميل ملف وثيقة
  Future<String> uploadDocumentFile(File file, String projectId, String documentType);
  
  // إدارة الفريق
  
  /// إضافة عضو للمشروع
  Future<void> addProjectMember(String projectId, String memberId);
  
  /// إزالة عضو من المشروع
  Future<void> removeProjectMember(String projectId, String memberId);
  
  /// الحصول على أعضاء المشروع
  Future<List<TeamMember>> getProjectMembers(String projectId);
  
  /// تحديث دور عضو في المشروع
  Future<void> updateMemberRole(String projectId, String memberId, String role);
  
  // التقارير والإحصائيات
  
  /// الحصول على إحصائيات المشروع
  Future<Map<String, dynamic>> getProjectStatistics(String projectId);
  
  /// الحصول على إحصائيات مشاريع الشركة
  Future<Map<String, dynamic>> getCompanyProjectsStatistics(String companyId);
  
  /// الحصول على تقرير تقدم المشروع
  Future<Map<String, dynamic>> getProjectProgressReport(String projectId);
  
  /// الحصول على تقرير الميزانية
  Future<Map<String, dynamic>> getProjectBudgetReport(String projectId);
  
  /// الحصول على تقرير الأداء
  Future<Map<String, dynamic>> getProjectPerformanceReport(String projectId);
  
  // الجدولة والتذكيرات
  
  /// الحصول على المهام المتأخرة
  Future<List<ProjectTask>> getOverdueTasks(String projectId);
  
  /// الحصول على المهام القادمة
  Future<List<ProjectTask>> getUpcomingTasks(String projectId, {int daysAhead = 7});
  
  /// الحصول على المعالم المتأخرة
  Future<List<ProjectMilestone>> getOverdueMilestones(String projectId);
  
  /// الحصول على المعالم القادمة
  Future<List<ProjectMilestone>> getUpcomingMilestones(String projectId, {int daysAhead = 7});
  
  /// إرسال تذكيرات المهام
  Future<void> sendTaskReminders(String projectId);
  
  /// إرسال تذكيرات المعالم
  Future<void> sendMilestoneReminders(String projectId);
  
  // التصدير والاستيراد
  
  /// تصدير بيانات المشروع
  Future<String> exportProjectData(String projectId, String format);
  
  /// استيراد مهام من ملف
  Future<int> importTasksFromFile(String projectId, File file);
  
  /// تصدير تقرير المشروع
  Future<String> exportProjectReport(String projectId, String reportType);
  
  // الاستماع للتغييرات
  
  /// الاستماع لتغييرات المشروع
  Stream<Project?> listenToProject(String projectId);
  
  /// الاستماع لتغييرات مهام المشروع
  Stream<List<ProjectTask>> listenToProjectTasks(String projectId);
  
  /// الاستماع لتغييرات معالم المشروع
  Stream<List<ProjectMilestone>> listenToProjectMilestones(String projectId);
  
  /// الاستماع لتغييرات وثائق المشروع
  Stream<List<ProjectDocument>> listenToProjectDocuments(String projectId);
}
