import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// تهيئة Firebase Admin
admin.initializeApp();

// استيراد الوظائف
import { processScheduledNotifications } from './notifications/promotionalNotifications';
import { onEstateCreated } from './triggers/estateTriggers';

// تصدير الوظائف
export const processNotifications = processScheduledNotifications;
export const estateCreated = onEstateCreated;

// وظيفة مجدولة تعمل كل دقيقة للتحقق من الإشعارات المجدولة
export const scheduleNotificationProcessor = functions.pubsub
  .schedule('every 1 minutes')
  .timeZone('Asia/Kuwait')
  .onRun(async (_context) => {
    console.log('🔄 بدء معالجة الإشعارات المجدولة...');
    
    try {
      const db = admin.firestore();
      const now = admin.firestore.Timestamp.now();
      
      // البحث عن الإشعارات المجدولة التي حان وقتها
      const query = await db.collection('scheduledNotifications')
        .where('processed', '==', false)
        .where('scheduledTime', '<=', now.toDate())
        .limit(50) // معالجة 50 إشعار في المرة الواحدة
        .get();

      if (query.empty) {
        console.log('✅ لا توجد إشعارات مجدولة للمعالجة');
        return null;
      }

      console.log(`📋 تم العثور على ${query.docs.length} إشعار للمعالجة`);

      // معالجة كل إشعار
      const batch = db.batch();
      const promises: Promise<void>[] = [];

      for (const doc of query.docs) {
        const data = doc.data();

        if (data.type === 'promotional_upgrade') {
          promises.push(processPromotionalNotification(data, doc.id));
        }

        // تحديد الإشعار كمعالج وجدولة التالي
        batch.update(doc.ref, {
          processed: true,
          processedAt: now.toDate()
        });

        // جدولة الإشعار التالي بعد 24 ساعة
        if (data.type === 'promotional_upgrade') {
          promises.push(scheduleNextPromotionalNotification(data));
        }
      }
      
      // تنفيذ جميع العمليات
      await Promise.all([
        batch.commit(),
        ...promises
      ]);
      
      console.log('✅ تم معالجة جميع الإشعارات المجدولة بنجاح');
      return null;
      
    } catch (error) {
      console.error('❌ خطأ في معالجة الإشعارات المجدولة:', error);
      throw error;
    }
  });

/**
 * معالجة إشعار ترويجي للترقية
 */
async function processPromotionalNotification(notificationData: any, _notificationId: string): Promise<void> {
  try {
    console.log(`🚀 معالجة إشعار ترويجي للعقار: ${notificationData.estateId}`);

    const db = admin.firestore();
    const messaging = admin.messaging();

    // التحقق من أن العقار لا يزال موجود وغير مؤرشف
    const estateDoc = await db.collection('estates').doc(notificationData.estateId).get();
    if (!estateDoc.exists || estateDoc.data()?.isArchived) {
      console.log(`⚠️ العقار ${notificationData.estateId} غير موجود أو مؤرشف، تم تخطي الإشعار`);
      return;
    }

    console.log(`📢 إرسال إشعار ترويجي للعقار: ${notificationData.estateId}`);
    
    // الحصول على رمز الجهاز للمستخدم
    const userDoc = await db.collection('users').doc(notificationData.userId).get();
    const userData = userDoc.data();

    // إنشاء رسالة الإشعار الترويجي
    const notificationTitle = 'اجعل إعلانك أكثر تميزاً! ⭐';
    const notificationBody = `هل تريد المزيد من المشترين والمستأجرين لعقارك "${notificationData.estateTitle}"؟

🚀 استفد من خدماتنا المميزة:
📌 تثبيت الإعلان في المقدمة
⭐ تمييز الإعلان بشارة خاصة
🔝 ظهور أولوي في نتائج البحث

💰 احصل على المزيد من العروض والاستفسارات!

للترقية، تواصل معنا:
📱 واتساب: +965 9929 8821
📧 إيميل: <EMAIL>

تطبيق Krea - شريكك في النجاح العقاري 🏠`;

    if (userData && userData.fcmToken) {
      // إرسال إشعار push
      const message = {
        token: userData.fcmToken,
        notification: {
          title: notificationTitle,
          body: 'اجعل إعلانك أكثر تميزاً! اضغط للمزيد من التفاصيل.'
        },
        data: {
          type: 'promotional_upgrade',
          estateId: notificationData.estateId,
          estateTitle: notificationData.estateTitle,
          whatsapp: '+965 9929 8821',
          email: '<EMAIL>',
          fullMessage: notificationBody
        },
        android: {
          notification: {
            icon: 'ic_notification',
            color: '#2E7D32',
            sound: 'default',
            channelId: 'promotional_notifications'
          },
          priority: 'high' as 'high'
        },
        apns: {
          payload: {
            aps: {
              sound: 'default',
              badge: 1,
              alert: {
                title: notificationTitle,
                body: 'اجعل إعلانك أكثر تميزاً! اضغط للمزيد من التفاصيل.'
              }
            }
          }
        }
      };
      
      try {
        await messaging.send(message);
        console.log(`✅ تم إرسال إشعار push للمستخدم: ${notificationData.userId}`);
      } catch (pushError) {
        console.error('❌ خطأ في إرسال إشعار push:', pushError);
      }
    }

    // حفظ الإشعار في مجموعة notifications للمستخدم
    await db.collection('users')
      .doc(notificationData.userId)
      .collection('notifications')
      .add({
        title: notificationTitle,
        body: notificationBody,
        type: 'promotional_upgrade',
        data: {
          estateId: notificationData.estateId,
          estateTitle: notificationData.estateTitle,
          contactInfo: {
            whatsapp: '+965 9929 8821',
            email: '<EMAIL>'
          }
        },
        timestamp: admin.firestore.Timestamp.now(),
        isRead: false,
        priority: 'normal'
      });

    console.log(`✅ تم حفظ الإشعار الترويجي للمستخدم: ${notificationData.userId}`);

    // إضافة سجل في مجموعة logs للمراقبة
    await db.collection('systemLogs').add({
      type: 'promotional_notification_sent',
      estateId: notificationData.estateId,
      userId: notificationData.userId,
      estateTitle: notificationData.estateTitle,
      timestamp: admin.firestore.Timestamp.now(),
      details: 'تم إرسال إشعار ترويجي للمستخدم لترقية إعلانه'
    });

  } catch (error) {
    console.error(`❌ خطأ في معالجة الإشعار الترويجي ${notificationData.estateId}:`, error);
    throw error;
  }
}

/**
 * جدولة الإشعار الترويجي التالي بعد 24 ساعة
 */
async function scheduleNextPromotionalNotification(notificationData: any): Promise<void> {
  try {
    const db = admin.firestore();

    // حساب وقت الإشعار التالي (24 ساعة من الآن)
    const nextScheduledTime = new Date(Date.now() + (24 * 60 * 60 * 1000));

    // إنشاء إشعار ترويجي جديد مجدول
    await db.collection('scheduledNotifications').add({
      type: 'promotional_upgrade',
      estateId: notificationData.estateId,
      userId: notificationData.userId,
      estateTitle: notificationData.estateTitle,
      scheduledTime: admin.firestore.Timestamp.fromDate(nextScheduledTime),
      createdAt: admin.firestore.Timestamp.now(),
      processed: false,
      notificationData: {
        title: 'اجعل إعلانك أكثر تميزاً! ⭐',
        contactInfo: {
          whatsapp: '+965 9929 8821',
          email: '<EMAIL>'
        }
      }
    });

    console.log(`✅ تم جدولة الإشعار الترويجي التالي للعقار: ${notificationData.estateId} في ${nextScheduledTime.toISOString()}`);

  } catch (error) {
    console.error(`❌ خطأ في جدولة الإشعار الترويجي التالي: ${error}`);
  }
}
