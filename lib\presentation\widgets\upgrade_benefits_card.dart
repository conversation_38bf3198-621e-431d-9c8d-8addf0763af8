// lib/presentation/widgets/upgrade_benefits_card.dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/config/app_mode_config.dart';
import '../../core/models/plan_model.dart';

/// بطاقة فوائد الترقية
/// تعرض فوائد الترقية إلى باقة أعلى
class UpgradeBenefitsCard extends StatelessWidget {
  /// الباقة الحالية
  final PlanModel currentPlan;
  
  /// الباقة المستهدفة
  final PlanModel targetPlan;
  
  /// دالة يتم استدعاؤها عند النقر على زر الترقية
  final VoidCallback? onUpgrade;
  
  /// ما إذا كان يجب عرض زر الترقية
  final bool showUpgradeButton;
  
  /// نص زر الترقية
  final String upgradeButtonText;

  const UpgradeBenefitsCard({
    super.key,
    required this.currentPlan,
    required this.targetPlan,
    this.onUpgrade,
    this.showUpgradeButton = true,
    this.upgradeButtonText = "ترقية الآن",
  });

  @override
  Widget build(BuildContext context) {
    // حساب الفروق بين الباقتين
    final adsDifference = targetPlan.maxAds - currentPlan.maxAds;
    final imagesDifference = targetPlan.maxImagesPerAd - currentPlan.maxImagesPerAd;
    final durationDifference = targetPlan.adDurationDays - currentPlan.adDurationDays;
    
    // الميزات الإضافية في الباقة المستهدفة
    final additionalFeatures = <String>[];
    
    if (targetPlan.isFeatureAllowed('autoRepublish') && !currentPlan.isFeatureAllowed('autoRepublish')) {
      additionalFeatures.add("إعادة نشر تلقائي");
    }
    
    if (targetPlan.isFeatureAllowed('kuwaitCornersPin') && !currentPlan.isFeatureAllowed('kuwaitCornersPin')) {
      additionalFeatures.add("دبوس كويت كورنرز");
    }
    
    if (targetPlan.isFeatureAllowed('movingAd') && !currentPlan.isFeatureAllowed('movingAd')) {
      additionalFeatures.add("إعلان متحرك");
    }
    
    if (targetPlan.isFeatureAllowed('vipBadge') && !currentPlan.isFeatureAllowed('vipBadge')) {
      additionalFeatures.add("شارة VIP");
    }
    
    if (targetPlan.isFeatureAllowed('pinnedOnHome') && !currentPlan.isFeatureAllowed('pinnedOnHome')) {
      additionalFeatures.add("تثبيت في الصفحة الرئيسية");
    }
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: targetPlan.color.withAlpha(100),
          width: 1)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان البطاقة
            Row(
              children: [
                Icon(
                  Icons.upgrade,
                  color: targetPlan.color,
                  size: 24),
                const SizedBox(width: 8),
                Text(
                  "فوائد الترقية",
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800)),
                const Spacer(),
                // شارة الباقة المستهدفة
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4),
                  decoration: BoxDecoration(
                    color: targetPlan.color.withAlpha(30),
                    borderRadius: BorderRadius.circular(16)),
                  child: Text(
                    targetPlan.nameAr,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: targetPlan.color))),
              ]),
            
            const Divider(height: 24),
            
            // فوائد الترقية
            if (adsDifference > 0)
              _buildBenefitRow(
                context,
                "إعلانات إضافية",
                "+$adsDifference إعلان",
                Icons.add_circle),
            
            if (imagesDifference > 0)
              _buildBenefitRow(
                context,
                "صور إضافية لكل إعلان",
                "+$imagesDifference صورة",
                Icons.image),
            
            if (durationDifference > 0)
              _buildBenefitRow(
                context,
                "مدة عرض أطول",
                "+$durationDifference يوم",
                Icons.calendar_today),
            
            if (additionalFeatures.isNotEmpty) ...[
              const SizedBox(height: 16),
              
              // عنوان الميزات الإضافية
              Text(
                "ميزات إضافية",
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800)),
              
              const SizedBox(height: 8),
              
              // قائمة الميزات الإضافية
              ...additionalFeatures.map((feature) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 20),
                    const SizedBox(width: 8),
                    Text(
                      feature,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey.shade800)),
                  ]))),
            ],
            
            const SizedBox(height: 16),
            
            // معلومات السعر
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "سعر الترقية:",
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800)),
                  Text(
                    "${targetPlan.price.toStringAsFixed(3)} د.ك",
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: targetPlan.color)),
                ])),
            
            // إخفاء زر الترقية في الوضع المعلوماتي
            if (showUpgradeButton && onUpgrade != null && AppModeConfig.shouldShowUpgradeButtons()) ...[
              const SizedBox(height: 16),

              // زر الترقية
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: onUpgrade,
                  icon: const Icon(Icons.upgrade),
                  label: Text(
                    upgradeButtonText,
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: targetPlan.color,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12))))),
            ],
          ])));
  }
  
  /// بناء صف فائدة
  Widget _buildBenefitRow(
    BuildContext context,
    String label,
    String value,
    IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: targetPlan.color),
          const SizedBox(width: 8),
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.grey.shade800)),
          const Spacer(),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: targetPlan.color)),
        ]));
  }
}
