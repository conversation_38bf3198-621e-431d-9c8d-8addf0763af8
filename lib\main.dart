import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'package:google_fonts/google_fonts.dart';
import 'package:kuwait_corners/core/constants/plan_constants.dart';
import 'package:kuwait_corners/core/services/enhanced_auth_service.dart';
import 'package:kuwait_corners/core/services/forum_service.dart';
import 'package:kuwait_corners/core/services/loyalty_program_service.dart';
import 'package:kuwait_corners/core/services/notification_service.dart';
import 'package:kuwait_corners/core/services/property_request_service.dart';
import 'package:kuwait_corners/core/services/rating_review_service.dart';
import 'package:kuwait_corners/core/services/realtime_notification_service.dart';
import 'package:kuwait_corners/core/services/referral_program_service.dart';
import 'package:kuwait_corners/core/services/user_verification_service.dart';
import 'package:kuwait_corners/core/services/messaging_service.dart';
import 'package:kuwait_corners/core/services/system_ui_service.dart';

import 'package:kuwait_corners/core/config/database_config.dart';
import 'package:kuwait_corners/core/services/app_optimization_service.dart';
import 'package:kuwait_corners/data/repositories_impl/forum_repository_impl.dart';
import 'package:kuwait_corners/data/repositories_impl/property_request_repository_impl.dart';
import 'package:kuwait_corners/data/repositories_impl/messaging_repository_impl.dart';
import 'package:kuwait_corners/domain/repositories/lobby_repository.dart';
import 'package:kuwait_corners/domain/repositories/property_request_repository.dart';
import 'package:kuwait_corners/presentation/providers/auth_provider.dart'
    as app_auth;
import 'package:kuwait_corners/presentation/providers/forum_provider.dart';
import 'package:kuwait_corners/presentation/providers/property_request_provider.dart';
import 'package:kuwait_corners/presentation/providers/theme_provider.dart';
import 'package:kuwait_corners/presentation/providers/project_provider.dart';
import 'package:kuwait_corners/presentation/providers/client_interaction_provider.dart';
import 'package:kuwait_corners/data/repositories_impl/project_repository_impl.dart';
import 'package:kuwait_corners/data/repositories_impl/client_interaction_repository_impl.dart';
import 'package:kuwait_corners/core/services/forum_activity_tracker.dart';
import 'package:kuwait_corners/core/services/user_achievements_service.dart';
import 'package:provider/provider.dart';

import 'package:kuwait_corners/infrastructure/services/external/external_services_manager.dart';
import 'package:kuwait_corners/infrastructure/services/analytics_service.dart';
import 'package:kuwait_corners/infrastructure/repositories/firestore_analytics_repository.dart';
import 'package:kuwait_corners/domain/repositories/analytics_repository.dart';
import 'package:kuwait_corners/core/theme/theme_config.dart';
import 'package:kuwait_corners/core/theme/release_theme_fix.dart';
import 'package:kuwait_corners/data/repositories_impl/estate_repository_impl.dart';

import 'package:kuwait_corners/domain/usecases/copy_estate.dart';

import 'package:kuwait_corners/domain/usecases/create_estate_new.dart';
import 'package:kuwait_corners/domain/repositories/estate_repository.dart';

import 'package:kuwait_corners/domain/usecases/delete_estate.dart';
import 'package:kuwait_corners/domain/usecases/get_all_estates.dart';
import 'package:kuwait_corners/domain/usecases/get_paginated_estates.dart';
import 'package:kuwait_corners/domain/usecases/update_estate.dart' as usecases;
import 'package:kuwait_corners/firebase_options.dart';

import 'package:kuwait_corners/presentation/bloc/auth_bloc.dart';
import 'package:kuwait_corners/presentation/bloc/estate_bloc.dart';
import 'package:kuwait_corners/presentation/bloc/improved_ad_bloc.dart';

import 'package:kuwait_corners/core/routes/app_routes.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'data/repositories_impl/estate_remote_data_source_impl.dart';
import 'data/repositories_impl/user_repository_impl.dart';
import 'domain/repositories/user_repository.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تطبيق إعدادات النظام الثابتة
  ThemeConfig.configureApp();

  // تطبيق إصلاحات نسخة Release
  ReleaseThemeFix.applyReleaseFixes();

  // تطبيق إعدادات شريطي النظام الافتراضية
  await SystemUIService.instance.applyDefaultSystemUI();

  // تحميل متغيرات البيئة
  try {
    await dotenv.load(fileName: '.env');
  } catch (e) {
    // إذا لم يتم العثور على ملف .env، نستخدم القيم الافتراضية
    // تم إزالة print لتجنب استخدامها في الإنتاج
  }

  try {
    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);

    // تهيئة تكوين قاعدة البيانات المحسن
    await DatabaseConfig().initialize();

  } catch (e) {
    // Error initializing Firebase
  }

  // التحقق من حالة "تذكرني"
  final prefs = await SharedPreferences.getInstance();
  final bool rememberMe = prefs.getBool('rememberMe') ?? false;

  // إذا لم يكن "تذكرني" مفعلاً، نقوم بتسجيل الخروج
  if (!rememberMe) {
    await FirebaseAuth.instance.signOut();
  }



  // إنشاء RemoteDataSource مع Firestore و FirebaseStorage
  final estateRemoteDataSource = EstateRemoteDataSourceImpl(
    FirebaseFirestore.instance,
    FirebaseStorage.instance);

  // إنشاء EstateRepository بالاعتماد على remoteDataSource
  final estateRepository = EstateRepositoryImpl(estateRemoteDataSource);

  // تهيئة خدمات التطبيق الجديدة
  final notificationService = NotificationService();
  await notificationService.initialize();

  final realtimeNotificationService = RealtimeNotificationService();
  await realtimeNotificationService.initialize();

  // تحديث FCM Token للمستخدم الحالي إذا كان مسجل الدخول
  final currentUser = FirebaseAuth.instance.currentUser;
  if (currentUser != null) {
    try {
      final fcmToken = await FirebaseMessaging.instance.getToken();
      if (fcmToken != null) {
        await FirebaseFirestore.instance.collection('users').doc(currentUser.uid).update({
          'fcmToken': fcmToken,
          'deviceTokens': FieldValue.arrayUnion([fcmToken]),
          'lastTokenUpdate': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      // تجاهل خطأ تحديث FCM Token
    }
  }



  // إنشاء مستودع اللوبي
  final lobbyRepository = LobbyRepositoryImpl(
    firestore: FirebaseFirestore.instance,
    storage: FirebaseStorage.instance);

  // إنشاء خدمة اللوبي
  final forumService = ForumService(repository: lobbyRepository);

  // إنشاء مستودع طلبات العقارات
  final propertyRequestRepository = PropertyRequestRepositoryImpl(
    firestore: FirebaseFirestore.instance,
    storage: FirebaseStorage.instance);

  // إنشاء مستودع المراسلة الحقيقي
  final messagingRepository = MessagingRepositoryImpl();

  // إنشاء خدمة المراسلة
  final messagingService = MessagingService(
    messagingRepository: messagingRepository);

  // إنشاء خدمة طلبات العقارات
  final propertyRequestService = PropertyRequestService(
    repository: propertyRequestRepository,
    messagingService: messagingService);

  // إنشاء خدمة تتبع نشاطات المنتدى
  final forumActivityTracker = ForumActivityTracker(
    firestore: FirebaseFirestore.instance,
    auth: FirebaseAuth.instance);

  // إنشاء خدمة إنجازات المستخدم
  final userAchievementsService = UserAchievementsService(
    firestore: FirebaseFirestore.instance,
    auth: FirebaseAuth.instance);

  final enhancedAuthService = EnhancedAuthService();
  final userVerificationService = UserVerificationService();
  final loyaltyProgramService = LoyaltyProgramService();
  final referralProgramService = ReferralProgramService();
  final ratingReviewService = RatingReviewService();

  // Use cases الخاصة بالعقار
  final getAllEstates = GetAllEstates(estateRepository);
  final getPaginatedEstates = GetPaginatedEstates(estateRepository);
  // تمرير firestore و loyaltyProgramService إلى CreateEstate
  // إنشاء CreateEstateNew
  final createEstateNew = CreateEstateNew(
      estateRepository, FirebaseFirestore.instance,
      loyaltyService: loyaltyProgramService);
  // تمرير loyaltyProgramService إلى UpdateEstate
  final updateEstate = usecases.UpdateEstate(estateRepository, loyaltyService: loyaltyProgramService);
  final deleteEstate = DeleteEstate(estateRepository);
  final copyEstate = CopyEstate(estateRepository, FirebaseFirestore.instance);

  // تم نقل التعامل مع rememberMe إلى بداية الملف

  // إنشاء مستودع التحليلات باستخدام Firestore
  final analyticsRepository = FirestoreAnalyticsRepository();

  // إنشاء خدمات التحليلات
  final analyticsService = AnalyticsService(analyticsRepository);

  // تهيئة مدير الخدمات الخارجية
  final externalServicesManager = ExternalServicesManager();
  await externalServicesManager.init();

  // تهيئة خدمة التحسين الشاملة
  final appOptimizationService = AppOptimizationService();
  await appOptimizationService.initialize();

  // إنشاء مستودعات النظام الجديد
  final projectRepository = ProjectRepositoryImpl();
  final clientInteractionRepository = ClientInteractionRepositoryImpl();

  runApp(
    MultiRepositoryProvider(
      providers: [
        RepositoryProvider<EstateRepositoryImpl>.value(
          value: estateRepository),
        // إضافة مزودي الخدمات الجديدة
        RepositoryProvider<NotificationService>.value(
          value: notificationService),
        RepositoryProvider<RealtimeNotificationService>.value(
          value: realtimeNotificationService),
        RepositoryProvider<EnhancedAuthService>.value(
          value: enhancedAuthService),
        RepositoryProvider<UserVerificationService>.value(
          value: userVerificationService),
        RepositoryProvider<LoyaltyProgramService>.value(
          value: loyaltyProgramService),
        RepositoryProvider<ReferralProgramService>.value(
          value: referralProgramService),
        RepositoryProvider<RatingReviewService>.value(
          value: ratingReviewService),
        RepositoryProvider<LobbyRepository>.value(
          value: lobbyRepository),
        RepositoryProvider<ForumService>.value(
          value: forumService),
        RepositoryProvider<PropertyRequestRepository>.value(
          value: propertyRequestRepository),
        RepositoryProvider<PropertyRequestService>.value(
          value: propertyRequestService),
        RepositoryProvider<ForumActivityTracker>.value(
          value: forumActivityTracker),
        RepositoryProvider<UserAchievementsService>.value(
          value: userAchievementsService),
        RepositoryProvider<ExternalServicesManager>.value(
          value: externalServicesManager),
        // مزود مستودع التحليلات
        RepositoryProvider<AnalyticsRepository>.value(
          value: analyticsRepository),
        // مزود خدمة التحليلات
        RepositoryProvider<AnalyticsService>.value(
          value: analyticsService),
        // مزود خدمة التحسين الشاملة
        RepositoryProvider<AppOptimizationService>.value(
          value: appOptimizationService),
        // مزودات النظام الجديد
        RepositoryProvider<ProjectRepositoryImpl>.value(
          value: projectRepository),
        RepositoryProvider<ClientInteractionRepositoryImpl>.value(
          value: clientInteractionRepository),

      ],
      child: MyApp(
        getAllEstates: getAllEstates,
        getPaginatedEstates: getPaginatedEstates,
        createEstate: createEstateNew,
        createEstateNew: createEstateNew,
        estateRepository: estateRepository,
        updateEstate: updateEstate,
        deleteEstate: deleteEstate,
        copyEstate: copyEstate,
        rememberMe: rememberMe)));
}

class MyApp extends StatelessWidget {
  final GetAllEstates getAllEstates;
  final GetPaginatedEstates getPaginatedEstates;
  final CreateEstateNew createEstate;
  final CreateEstateNew createEstateNew;
  final EstateRepository estateRepository;
  final usecases.UpdateEstate updateEstate;
  final DeleteEstate deleteEstate;
  final CopyEstate copyEstate;
  final bool rememberMe;

  const MyApp({
    super.key,
    required this.getAllEstates,
    required this.getPaginatedEstates,
    required this.createEstate,
    required this.createEstateNew,
    required this.estateRepository,
    required this.updateEstate,
    required this.deleteEstate,
    required this.copyEstate,
    required this.rememberMe,
  });

  @override
  Widget build(BuildContext context) {
    // التأكد من إزالة الشريط الأسود عند كل إعادة بناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      SystemUIService.instance.hideSystemUI();
    });

    return MultiBlocProvider(
      providers: [
        RepositoryProvider<UserRepository>(
          create: (_) => UserRepositoryImpl(
            firestore: FirebaseFirestore.instance,
            storage: FirebaseStorage.instance)),



        BlocProvider<EstateBloc>(
          create: (_) => EstateBloc(
            getAllEstates: getAllEstates,
            getPaginatedEstates: getPaginatedEstates,
            createEstate: createEstate,
            updateEstate: updateEstate,
            deleteEstate: deleteEstate,
            copyEstate: copyEstate)),
        BlocProvider<AuthBloc>(
          create: (_) => AuthBloc()),
        BlocProvider<ImprovedAdBloc>(
          create: (_) => ImprovedAdBloc(
            createEstate: createEstateNew,
            estateRepository: estateRepository)),

        // إضافة مزود المصادقة (يجب أن يكون قبل مزود المنتدى)
        ChangeNotifierProvider<app_auth.AuthProvider>(
          create: (context) => app_auth.AuthProvider()),
        // إضافة مزود المنتدى
        ChangeNotifierProvider<ForumProvider>(
          create: (context) => ForumProvider(
            forumService: RepositoryProvider.of<ForumService>(context),
            activityTracker: RepositoryProvider.of<ForumActivityTracker>(context),
            authProvider: Provider.of<app_auth.AuthProvider>(context, listen: false))),
        // إضافة مزود طلبات العقارات
        ChangeNotifierProvider<PropertyRequestProvider>(
          create: (context) => PropertyRequestProvider(
            service: RepositoryProvider.of<PropertyRequestService>(context))),
        // إضافة مزود الثيم
        ChangeNotifierProvider<ThemeProvider>(
          create: (context) => ThemeProvider()..initialize()),
        // إضافة مزودات النظام الجديد
        ChangeNotifierProvider<ProjectProvider>(
          create: (context) => ProjectProvider(
            projectRepository: RepositoryProvider.of<ProjectRepositoryImpl>(context))),
        ChangeNotifierProvider<ClientInteractionProvider>(
          create: (context) => ClientInteractionProvider(
            interactionRepository: RepositoryProvider.of<ClientInteractionRepositoryImpl>(context))),
      ],
      child: DoubleTapSystemUIDetector(
        enabled: false,
        child: Consumer<ThemeProvider>(
          builder: (context, themeProvider, child) {
            return MaterialApp(
              debugShowCheckedModeBanner: false,
              title: PlanConstants.appName,
              theme: themeProvider.lightTheme.copyWith(
                // تطبيق خط Cairo كخط افتراضي لكامل التطبيق
                textTheme: GoogleFonts.cairoTextTheme(
                  themeProvider.lightTheme.textTheme)),
              darkTheme: themeProvider.darkTheme.copyWith(
                textTheme: GoogleFonts.cairoTextTheme(
                  themeProvider.darkTheme.textTheme)),
              themeMode: themeProvider.themeMode,
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('ar'),
            Locale('en'),
          ],
          locale: const Locale('ar'),
          builder: (context, child) {
            // تطبيق اتجاه النص من اليمين إلى اليسار
            final mediaQuery = MediaQuery.of(context);

            // الحصول على ارتفاع شريط الحالة للمساحة الآمنة
            final statusBarHeight = mediaQuery.padding.top;
            final bottomPadding = mediaQuery.padding.bottom;

            return Directionality(
              textDirection: TextDirection.rtl,
              child: MediaQuery(
                // الحفاظ على المساحة الآمنة للمحتوى مع إخفاء الأشرطة
                data: mediaQuery.copyWith(
                  padding: EdgeInsets.only(
                    top: statusBarHeight,
                    bottom: bottomPadding),
                  viewPadding: EdgeInsets.only(
                    top: statusBarHeight,
                    bottom: bottomPadding),
                  viewInsets: EdgeInsets.zero),
                child: child!));
              },
              initialRoute: AppRoutes.splash,
              routes: AppRoutes.getRoutes());
          }),
      ),
    );
  }
}
