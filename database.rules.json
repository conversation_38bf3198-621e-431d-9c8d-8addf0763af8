{
  "rules": {
    ".read": false,
    ".write": false,
    
    // قواعد المستخدمين
    "users": {
      "$userId": {
        ".read": "auth != null && (auth.uid == $userId || root.child('users').child(auth.uid).child('role').val() == 'admin')",
        ".write": "auth != null && auth.uid == $userId",
        ".validate": "newData.hasChildren(['name', 'email', 'type', 'createdAt'])",
        
        "name": {
          ".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 100"
        },
        "email": {
          ".validate": "newData.isString() && newData.val().matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/)"
        },
        "type": {
          ".validate": "newData.isNumber() && newData.val() >= 0 && newData.val() <= 4"
        },
        "role": {
          ".validate": "newData.isString() && (newData.val() == 'user' || newData.val() == 'admin' || newData.val() == 'moderator')"
        },
        "isVerified": {
          ".validate": "newData.isBoolean()"
        },
        "createdAt": {
          ".validate": "newData.isNumber()"
        },
        "lastActive": {
          ".validate": "newData.isNumber()"
        }
      }
    },
    
    // قواعد الإشعارات المباشرة
    "notifications": {
      "$userId": {
        ".read": "auth != null && auth.uid == $userId",
        ".write": false,
        
        "$notificationId": {
          ".write": "auth != null && (auth.uid == $userId || root.child('users').child(auth.uid).child('role').val() == 'admin')",
          ".validate": "newData.hasChildren(['type', 'title', 'body', 'timestamp', 'isRead'])",
          
          "type": {
            ".validate": "newData.isString() && newData.val().length > 0"
          },
          "title": {
            ".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 100"
          },
          "body": {
            ".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 500"
          },
          "timestamp": {
            ".validate": "newData.isNumber()"
          },
          "isRead": {
            ".validate": "newData.isBoolean()"
          },
          "senderId": {
            ".validate": "newData.isString() && newData.val().length > 0"
          },
          "senderName": {
            ".validate": "newData.isString() && newData.val().length > 0"
          }
        }
      }
    },
    
    // قواعد الحالة المباشرة للمستخدمين
    "presence": {
      "$userId": {
        ".read": "auth != null",
        ".write": "auth != null && auth.uid == $userId",
        ".validate": "newData.hasChildren(['isOnline', 'lastSeen'])",
        
        "isOnline": {
          ".validate": "newData.isBoolean()"
        },
        "lastSeen": {
          ".validate": "newData.isNumber()"
        },
        "currentActivity": {
          ".validate": "newData.isString()"
        }
      }
    },
    
    // قواعد المحادثات المباشرة
    "chat_rooms": {
      "$roomId": {
        ".read": "auth != null && (root.child('chat_rooms').child($roomId).child('participants').child(auth.uid).exists() || root.child('users').child(auth.uid).child('role').val() == 'admin')",
        ".write": "auth != null && root.child('chat_rooms').child($roomId).child('participants').child(auth.uid).exists()",
        
        "participants": {
          "$participantId": {
            ".validate": "newData.isBoolean()"
          }
        },
        
        "messages": {
          "$messageId": {
            ".write": "auth != null && root.child('chat_rooms').child($roomId).child('participants').child(auth.uid).exists()",
            ".validate": "newData.hasChildren(['senderId', 'content', 'timestamp', 'type'])",
            
            "senderId": {
              ".validate": "newData.isString() && newData.val() == auth.uid"
            },
            "content": {
              ".validate": "newData.isString() && newData.val().length > 0 && newData.val().length <= 1000"
            },
            "timestamp": {
              ".validate": "newData.isNumber()"
            },
            "type": {
              ".validate": "newData.isString() && (newData.val() == 'text' || newData.val() == 'image' || newData.val() == 'file')"
            },
            "isEdited": {
              ".validate": "newData.isBoolean()"
            },
            "editedAt": {
              ".validate": "newData.isNumber()"
            }
          }
        },
        
        "lastMessage": {
          ".validate": "newData.hasChildren(['content', 'timestamp', 'senderId'])"
        },
        
        "createdAt": {
          ".validate": "newData.isNumber()"
        },
        
        "updatedAt": {
          ".validate": "newData.isNumber()"
        }
      }
    },
    
    // قواعد الإحصائيات المباشرة
    "analytics": {
      ".read": "auth != null && root.child('users').child(auth.uid).child('role').val() == 'admin'",
      ".write": "auth != null && root.child('users').child(auth.uid).child('role').val() == 'admin'",
      
      "daily_stats": {
        "$date": {
          ".validate": "newData.hasChildren(['activeUsers', 'newRegistrations', 'totalViews'])"
        }
      },
      
      "user_activity": {
        "$userId": {
          ".read": "auth != null && (auth.uid == $userId || root.child('users').child(auth.uid).child('role').val() == 'admin')",
          ".write": "auth != null && auth.uid == $userId"
        }
      }
    },
    
    // قواعد النسخ الاحتياطية
    "backups": {
      ".read": "auth != null && root.child('users').child(auth.uid).child('role').val() == 'admin'",
      ".write": "auth != null && root.child('users').child(auth.uid).child('role').val() == 'admin'",
      
      "$backupId": {
        ".validate": "newData.hasChildren(['timestamp', 'type', 'status'])",
        
        "timestamp": {
          ".validate": "newData.isNumber()"
        },
        "type": {
          ".validate": "newData.isString() && (newData.val() == 'full' || newData.val() == 'incremental')"
        },
        "status": {
          ".validate": "newData.isString() && (newData.val() == 'pending' || newData.val() == 'completed' || newData.val() == 'failed')"
        }
      }
    },
    
    // قواعد مراقبة الأمان
    "security_logs": {
      ".read": "auth != null && root.child('users').child(auth.uid).child('role').val() == 'admin'",
      ".write": "auth != null && root.child('users').child(auth.uid).child('role').val() == 'admin'",
      
      "$logId": {
        ".validate": "newData.hasChildren(['userId', 'action', 'timestamp', 'ipAddress'])",
        
        "userId": {
          ".validate": "newData.isString()"
        },
        "action": {
          ".validate": "newData.isString() && newData.val().length > 0"
        },
        "timestamp": {
          ".validate": "newData.isNumber()"
        },
        "ipAddress": {
          ".validate": "newData.isString()"
        },
        "userAgent": {
          ".validate": "newData.isString()"
        },
        "success": {
          ".validate": "newData.isBoolean()"
        }
      }
    }
  }
}
