import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

/// حالات المشروع
enum ProjectStatus {
  planning,     // قيد التخطيط
  active,       // قيد التنفيذ
  onHold,       // معلق
  completed,    // مكتمل
  cancelled,    // ملغي
}

/// أنواع المشاريع
enum ProjectType {
  residential,  // سكني
  commercial,   // تجاري
  industrial,   // صناعي
  mixed,        // مختلط
  infrastructure, // بنية تحتية
}

/// أولوية المشروع
enum ProjectPriority {
  low,          // منخفضة
  medium,       // متوسطة
  high,         // عالية
  urgent,       // عاجلة
}

/// نموذج المشروع العقاري
class Project extends Equatable {
  /// معرف المشروع
  final String id;
  
  /// اسم المشروع
  final String name;
  
  /// وصف المشروع
  final String description;
  
  /// نوع المشروع
  final ProjectType type;
  
  /// حالة المشروع
  final ProjectStatus status;
  
  /// أولوية المشروع
  final ProjectPriority priority;
  
  /// الموقع
  final String location;
  
  /// معرف الشركة المالكة
  final String companyId;
  
  /// معرف مدير المشروع
  final String managerId;
  
  /// اسم مدير المشروع
  final String managerName;
  
  /// الميزانية الإجمالية
  final double budget;
  
  /// الميزانية المستخدمة
  final double spentBudget;
  
  /// تاريخ البداية
  final DateTime? startDate;
  
  /// تاريخ الانتهاء المتوقع
  final DateTime? endDate;
  
  /// تاريخ الانتهاء الفعلي
  final DateTime? actualEndDate;
  
  /// نسبة الإنجاز (0-100)
  final double progress;
  
  /// عدد أعضاء الفريق
  final int teamSize;
  
  /// قائمة معرفات أعضاء الفريق
  final List<String> memberIds;
  
  /// عدد المهام الإجمالي
  final int tasksCount;
  
  /// عدد المهام المكتملة
  final int completedTasks;
  
  /// قائمة معرفات المهام
  final List<String> taskIds;
  
  /// قائمة معرفات الوثائق
  final List<String> documentIds;
  
  /// قائمة معرفات المعالم
  final List<String> milestoneIds;
  
  /// قائمة معرفات العملاء المرتبطين
  final List<String> clientIds;
  
  /// قائمة معرفات العقارات المرتبطة
  final List<String> estateIds;
  
  /// هل المشروع عاجل
  final bool isUrgent;
  
  /// العلامات
  final List<String> tags;
  
  /// ملاحظات
  final String? notes;
  
  /// تاريخ الإنشاء
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث
  final DateTime updatedAt;
  
  /// معلومات إضافية
  final Map<String, dynamic>? additionalInfo;

  const Project({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.status,
    required this.priority,
    required this.location,
    required this.companyId,
    required this.managerId,
    required this.managerName,
    required this.budget,
    this.spentBudget = 0.0,
    this.startDate,
    this.endDate,
    this.actualEndDate,
    this.progress = 0.0,
    this.teamSize = 0,
    this.memberIds = const [],
    this.tasksCount = 0,
    this.completedTasks = 0,
    this.taskIds = const [],
    this.documentIds = const [],
    this.milestoneIds = const [],
    this.clientIds = const [],
    this.estateIds = const [],
    this.isUrgent = false,
    this.tags = const [],
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.additionalInfo,
  });

  /// إنشاء نسخة معدلة من المشروع
  Project copyWith({
    String? id,
    String? name,
    String? description,
    ProjectType? type,
    ProjectStatus? status,
    ProjectPriority? priority,
    String? location,
    String? companyId,
    String? managerId,
    String? managerName,
    double? budget,
    double? spentBudget,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? actualEndDate,
    double? progress,
    int? teamSize,
    List<String>? memberIds,
    int? tasksCount,
    int? completedTasks,
    List<String>? taskIds,
    List<String>? documentIds,
    List<String>? milestoneIds,
    List<String>? clientIds,
    List<String>? estateIds,
    bool? isUrgent,
    List<String>? tags,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalInfo,
  }) {
    return Project(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      location: location ?? this.location,
      companyId: companyId ?? this.companyId,
      managerId: managerId ?? this.managerId,
      managerName: managerName ?? this.managerName,
      budget: budget ?? this.budget,
      spentBudget: spentBudget ?? this.spentBudget,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      actualEndDate: actualEndDate ?? this.actualEndDate,
      progress: progress ?? this.progress,
      teamSize: teamSize ?? this.teamSize,
      memberIds: memberIds ?? this.memberIds,
      tasksCount: tasksCount ?? this.tasksCount,
      completedTasks: completedTasks ?? this.completedTasks,
      taskIds: taskIds ?? this.taskIds,
      documentIds: documentIds ?? this.documentIds,
      milestoneIds: milestoneIds ?? this.milestoneIds,
      clientIds: clientIds ?? this.clientIds,
      estateIds: estateIds ?? this.estateIds,
      isUrgent: isUrgent ?? this.isUrgent,
      tags: tags ?? this.tags,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalInfo: additionalInfo ?? this.additionalInfo,
    );
  }

  /// تحويل المشروع إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'priority': priority.toString().split('.').last,
      'location': location,
      'companyId': companyId,
      'managerId': managerId,
      'managerName': managerName,
      'budget': budget,
      'spentBudget': spentBudget,
      'startDate': startDate != null ? Timestamp.fromDate(startDate!) : null,
      'endDate': endDate != null ? Timestamp.fromDate(endDate!) : null,
      'actualEndDate': actualEndDate != null ? Timestamp.fromDate(actualEndDate!) : null,
      'progress': progress,
      'teamSize': teamSize,
      'memberIds': memberIds,
      'tasksCount': tasksCount,
      'completedTasks': completedTasks,
      'taskIds': taskIds,
      'documentIds': documentIds,
      'milestoneIds': milestoneIds,
      'clientIds': clientIds,
      'estateIds': estateIds,
      'isUrgent': isUrgent,
      'tags': tags,
      'notes': notes,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'additionalInfo': additionalInfo,
    };
  }

  /// إنشاء مشروع من Map
  factory Project.fromMap(Map<String, dynamic> map) {
    return Project(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      type: ProjectType.values.firstWhere(
        (e) => e.toString().split('.').last == map['type'],
        orElse: () => ProjectType.residential,
      ),
      status: ProjectStatus.values.firstWhere(
        (e) => e.toString().split('.').last == map['status'],
        orElse: () => ProjectStatus.planning,
      ),
      priority: ProjectPriority.values.firstWhere(
        (e) => e.toString().split('.').last == map['priority'],
        orElse: () => ProjectPriority.medium,
      ),
      location: map['location'] ?? '',
      companyId: map['companyId'] ?? '',
      managerId: map['managerId'] ?? '',
      managerName: map['managerName'] ?? '',
      budget: (map['budget'] ?? 0.0).toDouble(),
      spentBudget: (map['spentBudget'] ?? 0.0).toDouble(),
      startDate: map['startDate'] is Timestamp 
          ? (map['startDate'] as Timestamp).toDate() 
          : null,
      endDate: map['endDate'] is Timestamp 
          ? (map['endDate'] as Timestamp).toDate() 
          : null,
      actualEndDate: map['actualEndDate'] is Timestamp 
          ? (map['actualEndDate'] as Timestamp).toDate() 
          : null,
      progress: (map['progress'] ?? 0.0).toDouble(),
      teamSize: map['teamSize'] ?? 0,
      memberIds: List<String>.from(map['memberIds'] ?? []),
      tasksCount: map['tasksCount'] ?? 0,
      completedTasks: map['completedTasks'] ?? 0,
      taskIds: List<String>.from(map['taskIds'] ?? []),
      documentIds: List<String>.from(map['documentIds'] ?? []),
      milestoneIds: List<String>.from(map['milestoneIds'] ?? []),
      clientIds: List<String>.from(map['clientIds'] ?? []),
      estateIds: List<String>.from(map['estateIds'] ?? []),
      isUrgent: map['isUrgent'] ?? false,
      tags: List<String>.from(map['tags'] ?? []),
      notes: map['notes'],
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt'] as Timestamp).toDate() 
          : DateTime.now(),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt'] as Timestamp).toDate() 
          : DateTime.now(),
      additionalInfo: map['additionalInfo'],
    );
  }

  @override
  List<Object?> get props => [
    id, name, description, type, status, priority, location,
    companyId, managerId, managerName, budget, spentBudget,
    startDate, endDate, actualEndDate, progress, teamSize,
    memberIds, tasksCount, completedTasks, taskIds, documentIds,
    milestoneIds, clientIds, estateIds, isUrgent, tags, notes,
    createdAt, updatedAt, additionalInfo,
  ];
}
