import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../domain/models/forum/user_statistics_model.dart';

/// خدمة مستويات المستخدم في المنتدى
class UserLevelService {
  /// مرجع Firestore
  final FirebaseFirestore _firestore;

  /// مجموعة إحصائيات المستخدمين
  final CollectionReference _userStatsCollection;

  /// Constructor
  UserLevelService({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance,
        _userStatsCollection = (firestore ?? FirebaseFirestore.instance)
            .collection('forum_user_statistics');

  /// الحصول على إحصائيات المستخدم
  Future<UserStatisticsModel> getUserStatistics(String userId) async {
    try {
      final doc = await _userStatsCollection.doc(userId).get();
      if (doc.exists) {
        return UserStatisticsModel.fromFirestore(doc);
      } else {
        // إنشاء إحصائيات جديدة للمستخدم إذا لم تكن موجودة
        final newStats = UserStatisticsModel.empty(userId);
        await _userStatsCollection.doc(userId).set(newStats.toMap());
        return newStats;
      }
    } catch (e) {
      throw Exception('فشل في الحصول على إحصائيات المستخدم: $e');
    }
  }

  /// إضافة نقاط للمستخدم
  Future<UserStatisticsModel> addPoints(String userId, int points, String reason) async {
    try {
      final userStats = await getUserStatistics(userId);
      
      // حساب النقاط الجديدة والمستوى
      final newPoints = userStats.points + points;
      final newLevel = _calculateLevel(newPoints);
      final nextLevelPoints = _calculateNextLevelPoints(newLevel);
      
      // تحديث الإحصائيات
      final updatedStats = userStats.copyWith(
        points: newPoints,
        level: newLevel,
        nextLevelPoints: nextLevelPoints,
        lastActivityDate: DateTime.now());
      
      // حفظ التغييرات
      await _userStatsCollection.doc(userId).update(updatedStats.toMap());
      

      
      // التحقق من الإنجازات الجديدة
      await _checkAchievements(userId, updatedStats);
      
      return updatedStats;
    } catch (e) {
      throw Exception('فشل في إضافة النقاط: $e');
    }
  }



  /// حساب المستوى بناءً على النقاط
  int _calculateLevel(int points) {
    // صيغة حساب المستوى: المستوى = جذر(النقاط / 100) + 1
    return (points / 100).sqrt().floor() + 1;
  }

  /// حساب النقاط المطلوبة للمستوى التالي
  int _calculateNextLevelPoints(int currentLevel) {
    // النقاط المطلوبة للمستوى التالي = (المستوى التالي - 1)² × 100
    return ((currentLevel + 1 - 1) * (currentLevel + 1 - 1)) * 100;
  }

  /// تحديث معدل النشاط
  Future<void> updateActivityRate(String userId) async {
    try {
      final userStats = await getUserStatistics(userId);
      
      // حساب معدلات النشاط
      final now = DateTime.now();
      final lastActivity = userStats.lastActivityDate ?? now;
      
      // معدل النشاط اليومي: عدد أيام النشاط في آخر 7 أيام / 7
      final dailyRate = _calculateDailyActivityRate(userId);
      
      // معدل النشاط الأسبوعي: عدد أسابيع النشاط في آخر 4 أسابيع / 4
      final weeklyRate = _calculateWeeklyActivityRate(userId);
      
      // معدل النشاط الشهري: عدد أشهر النشاط في آخر 3 أشهر / 3
      final monthlyRate = _calculateMonthlyActivityRate(userId);
      
      // تحديث أيام النشاط المتتالية
      int streakDays = userStats.streakDays;
      if (now.difference(lastActivity).inDays <= 1) {
        // إذا كان آخر نشاط في نفس اليوم أو اليوم السابق
        streakDays++;
      } else {
        // إعادة تعيين أيام النشاط المتتالية
        streakDays = 1;
      }
      
      // تحديث الإحصائيات
      final updatedStats = userStats.copyWith(
        lastActivityDate: now,
        streakDays: streakDays,
        dailyActivityRate: dailyRate,
        weeklyActivityRate: weeklyRate,
        monthlyActivityRate: monthlyRate);
      
      // حفظ التغييرات
      await _userStatsCollection.doc(userId).update(updatedStats.toMap());
      
      // التحقق من إنجازات النشاط
      await _checkActivityAchievements(userId, updatedStats);
    } catch (e) {
      throw Exception('فشل في تحديث معدل النشاط: $e');
    }
  }

  /// حساب معدل النشاط اليومي
  Future<double> _calculateDailyActivityRate(String userId) async {
    try {
      // الحصول على سجل النشاط في آخر 7 أيام
      final now = DateTime.now();
      final sevenDaysAgo = now.subtract(const Duration(days: 7));
      
      final activeDays = await _firestore
          .collection('forum_activity_log')
          .where('userId', isEqualTo: userId)
          .where('timestamp', isGreaterThanOrEqualTo: sevenDaysAgo)
          .get();
      
      // حساب عدد الأيام الفريدة التي كان فيها نشاط
      final uniqueDays = _countUniqueDays(activeDays.docs);
      
      // حساب المعدل
      return uniqueDays / 7;
    } catch (e) {
      print('فشل في حساب معدل النشاط اليومي: $e');
      return 0.0;
    }
  }

  /// حساب معدل النشاط الأسبوعي
  Future<double> _calculateWeeklyActivityRate(String userId) async {
    try {
      // الحصول على سجل النشاط في آخر 4 أسابيع
      final now = DateTime.now();
      final fourWeeksAgo = now.subtract(const Duration(days: 28));
      
      final activeWeeks = await _firestore
          .collection('forum_activity_log')
          .where('userId', isEqualTo: userId)
          .where('timestamp', isGreaterThanOrEqualTo: fourWeeksAgo)
          .get();
      
      // حساب عدد الأسابيع الفريدة التي كان فيها نشاط
      final uniqueWeeks = _countUniqueWeeks(activeWeeks.docs);
      
      // حساب المعدل
      return uniqueWeeks / 4;
    } catch (e) {
      print('فشل في حساب معدل النشاط الأسبوعي: $e');
      return 0.0;
    }
  }

  /// حساب معدل النشاط الشهري
  Future<double> _calculateMonthlyActivityRate(String userId) async {
    try {
      // الحصول على سجل النشاط في آخر 3 أشهر
      final now = DateTime.now();
      final threeMonthsAgo = DateTime(now.year, now.month - 3, now.day);
      
      final activeMonths = await _firestore
          .collection('forum_activity_log')
          .where('userId', isEqualTo: userId)
          .where('timestamp', isGreaterThanOrEqualTo: threeMonthsAgo)
          .get();
      
      // حساب عدد الأشهر الفريدة التي كان فيها نشاط
      final uniqueMonths = _countUniqueMonths(activeMonths.docs);
      
      // حساب المعدل
      return uniqueMonths / 3;
    } catch (e) {
      print('فشل في حساب معدل النشاط الشهري: $e');
      return 0.0;
    }
  }

  /// عد الأيام الفريدة من سجلات النشاط
  int _countUniqueDays(List<DocumentSnapshot> activityDocs) {
    final uniqueDays = <String>{};
    
    for (final doc in activityDocs) {
      final data = doc.data() as Map<String, dynamic>?;
      if (data != null && data['timestamp'] != null) {
        final timestamp = (data['timestamp'] as Timestamp).toDate();
        final dayKey = '${timestamp.year}-${timestamp.month}-${timestamp.day}';
        uniqueDays.add(dayKey);
      }
    }
    
    return uniqueDays.length;
  }

  /// عد الأسابيع الفريدة من سجلات النشاط
  int _countUniqueWeeks(List<DocumentSnapshot> activityDocs) {
    final uniqueWeeks = <String>{};
    
    for (final doc in activityDocs) {
      final data = doc.data() as Map<String, dynamic>?;
      if (data != null && data['timestamp'] != null) {
        final timestamp = (data['timestamp'] as Timestamp).toDate();
        final weekYear = _getWeekYear(timestamp);
        final weekKey = '${weekYear.year}-${weekYear.week}';
        uniqueWeeks.add(weekKey);
      }
    }
    
    return uniqueWeeks.length;
  }

  /// عد الأشهر الفريدة من سجلات النشاط
  int _countUniqueMonths(List<DocumentSnapshot> activityDocs) {
    final uniqueMonths = <String>{};
    
    for (final doc in activityDocs) {
      final data = doc.data() as Map<String, dynamic>?;
      if (data != null && data['timestamp'] != null) {
        final timestamp = (data['timestamp'] as Timestamp).toDate();
        final monthKey = '${timestamp.year}-${timestamp.month}';
        uniqueMonths.add(monthKey);
      }
    }
    
    return uniqueMonths.length;
  }

  /// الحصول على رقم الأسبوع والسنة من تاريخ
  _WeekYear _getWeekYear(DateTime date) {
    // حساب رقم الأسبوع في السنة
    final firstDayOfYear = DateTime(date.year, 1, 1);
    final dayOfYear = date.difference(firstDayOfYear).inDays;
    final weekNumber = ((dayOfYear + firstDayOfYear.weekday - 1) / 7).ceil();
    
    return _WeekYear(date.year, weekNumber);
  }

  /// التحقق من الإنجازات
  Future<void> _checkAchievements(String userId, UserStatisticsModel stats) async {
    try {
      // التحقق من إنجازات المستوى
      await _checkLevelAchievements(userId, stats);
      
      // التحقق من إنجازات المشاركات
      await _checkPostsAchievements(userId, stats);
      
      // التحقق من إنجازات المواضيع
      await _checkTopicsAchievements(userId, stats);
      
      // التحقق من إنجازات الإعجابات
      await _checkLikesAchievements(userId, stats);
    } catch (e) {
      print('فشل في التحقق من الإنجازات: $e');
    }
  }

  /// التحقق من إنجازات المستوى
  Future<void> _checkLevelAchievements(String userId, UserStatisticsModel stats) async {
    // قائمة إنجازات المستوى
    final levelAchievements = [
      _createAchievement(
        'level_5',
        'مستوى 5',
        'الوصول إلى المستوى 5',
        'level',
        stats.level >= 5 ? stats.level : 5,
        5,
        stats.level >= 5),
      _createAchievement(
        'level_10',
        'مستوى 10',
        'الوصول إلى المستوى 10',
        'level',
        stats.level >= 10 ? stats.level : 10,
        10,
        stats.level >= 10),
      _createAchievement(
        'level_20',
        'مستوى 20',
        'الوصول إلى المستوى 20',
        'level',
        stats.level >= 20 ? stats.level : 20,
        20,
        stats.level >= 20),
      _createAchievement(
        'level_50',
        'مستوى 50',
        'الوصول إلى المستوى 50',
        'level',
        stats.level >= 50 ? stats.level : 50,
        50,
        stats.level >= 50),
      _createAchievement(
        'level_100',
        'مستوى 100',
        'الوصول إلى المستوى 100',
        'level',
        stats.level >= 100 ? stats.level : 100,
        100,
        stats.level >= 100),
    ];
    
    // تحديث الإنجازات
    await _updateAchievements(userId, levelAchievements);
  }

  /// التحقق من إنجازات المشاركات
  Future<void> _checkPostsAchievements(String userId, UserStatisticsModel stats) async {
    // قائمة إنجازات المشاركات
    final postsAchievements = [
      _createAchievement(
        'posts_10',
        '10 مشاركات',
        'كتابة 10 مشاركات',
        'posts',
        stats.postsCount,
        10,
        stats.postsCount >= 10),
      _createAchievement(
        'posts_50',
        '50 مشاركة',
        'كتابة 50 مشاركة',
        'posts',
        stats.postsCount,
        50,
        stats.postsCount >= 50),
      _createAchievement(
        'posts_100',
        '100 مشاركة',
        'كتابة 100 مشاركة',
        'posts',
        stats.postsCount,
        100,
        stats.postsCount >= 100),
      _createAchievement(
        'posts_500',
        '500 مشاركة',
        'كتابة 500 مشاركة',
        'posts',
        stats.postsCount,
        500,
        stats.postsCount >= 500),
      _createAchievement(
        'posts_1000',
        '1000 مشاركة',
        'كتابة 1000 مشاركة',
        'posts',
        stats.postsCount,
        1000,
        stats.postsCount >= 1000),
    ];
    
    // تحديث الإنجازات
    await _updateAchievements(userId, postsAchievements);
  }

  /// التحقق من إنجازات المواضيع
  Future<void> _checkTopicsAchievements(String userId, UserStatisticsModel stats) async {
    // قائمة إنجازات المواضيع
    final topicsAchievements = [
      _createAchievement(
        'topics_5',
        '5 مواضيع',
        'إنشاء 5 مواضيع',
        'topics',
        stats.topicsCount,
        5,
        stats.topicsCount >= 5),
      _createAchievement(
        'topics_10',
        '10 مواضيع',
        'إنشاء 10 مواضيع',
        'topics',
        stats.topicsCount,
        10,
        stats.topicsCount >= 10),
      _createAchievement(
        'topics_25',
        '25 موضوع',
        'إنشاء 25 موضوع',
        'topics',
        stats.topicsCount,
        25,
        stats.topicsCount >= 25),
      _createAchievement(
        'topics_50',
        '50 موضوع',
        'إنشاء 50 موضوع',
        'topics',
        stats.topicsCount,
        50,
        stats.topicsCount >= 50),
      _createAchievement(
        'topics_100',
        '100 موضوع',
        'إنشاء 100 موضوع',
        'topics',
        stats.topicsCount,
        100,
        stats.topicsCount >= 100),
    ];
    
    // تحديث الإنجازات
    await _updateAchievements(userId, topicsAchievements);
  }

  /// التحقق من إنجازات الإعجابات
  Future<void> _checkLikesAchievements(String userId, UserStatisticsModel stats) async {
    // قائمة إنجازات الإعجابات
    final likesAchievements = [
      _createAchievement(
        'likes_received_10',
        '10 إعجابات',
        'الحصول على 10 إعجابات',
        'likes',
        stats.receivedLikesCount,
        10,
        stats.receivedLikesCount >= 10),
      _createAchievement(
        'likes_received_50',
        '50 إعجاب',
        'الحصول على 50 إعجاب',
        'likes',
        stats.receivedLikesCount,
        50,
        stats.receivedLikesCount >= 50),
      _createAchievement(
        'likes_received_100',
        '100 إعجاب',
        'الحصول على 100 إعجاب',
        'likes',
        stats.receivedLikesCount,
        100,
        stats.receivedLikesCount >= 100),
      _createAchievement(
        'likes_received_500',
        '500 إعجاب',
        'الحصول على 500 إعجاب',
        'likes',
        stats.receivedLikesCount,
        500,
        stats.receivedLikesCount >= 500),
      _createAchievement(
        'likes_received_1000',
        '1000 إعجاب',
        'الحصول على 1000 إعجاب',
        'likes',
        stats.receivedLikesCount,
        1000,
        stats.receivedLikesCount >= 1000),
    ];
    
    // تحديث الإنجازات
    await _updateAchievements(userId, likesAchievements);
  }

  /// التحقق من إنجازات النشاط
  Future<void> _checkActivityAchievements(String userId, UserStatisticsModel stats) async {
    // قائمة إنجازات النشاط
    final activityAchievements = [
      _createAchievement(
        'streak_7',
        'نشاط 7 أيام',
        'الحفاظ على النشاط لمدة 7 أيام متتالية',
        'streak',
        stats.streakDays,
        7,
        stats.streakDays >= 7),
      _createAchievement(
        'streak_30',
        'نشاط 30 يوم',
        'الحفاظ على النشاط لمدة 30 يوم متتالي',
        'streak',
        stats.streakDays,
        30,
        stats.streakDays >= 30),
      _createAchievement(
        'streak_90',
        'نشاط 90 يوم',
        'الحفاظ على النشاط لمدة 90 يوم متتالي',
        'streak',
        stats.streakDays,
        90,
        stats.streakDays >= 90),
      _createAchievement(
        'streak_180',
        'نشاط 180 يوم',
        'الحفاظ على النشاط لمدة 180 يوم متتالي',
        'streak',
        stats.streakDays,
        180,
        stats.streakDays >= 180),
      _createAchievement(
        'streak_365',
        'نشاط سنة كاملة',
        'الحفاظ على النشاط لمدة 365 يوم متتالي',
        'streak',
        stats.streakDays,
        365,
        stats.streakDays >= 365),
    ];
    
    // تحديث الإنجازات
    await _updateAchievements(userId, activityAchievements);
  }

  /// إنشاء إنجاز
  AchievementModel _createAchievement(
    String id,
    String name,
    String description,
    String type,
    int value,
    int maxValue,
    bool isCompleted) {
    return AchievementModel(
      id: id,
      name: name,
      description: description,
      type: type,
      earnedAt: isCompleted ? DateTime.now() : DateTime(2000),
      value: value > maxValue ? maxValue : value,
      maxValue: maxValue,
      isCompleted: isCompleted);
  }

  /// تحديث الإنجازات
  Future<void> _updateAchievements(String userId, List<AchievementModel> achievements) async {
    try {
      final userStats = await getUserStatistics(userId);
      final currentAchievements = userStats.achievements;
      
      // دمج الإنجازات الحالية مع الإنجازات الجديدة
      final updatedAchievements = <AchievementModel>[];
      
      // إضافة الإنجازات الحالية التي ليست في الإنجازات الجديدة
      for (final achievement in currentAchievements) {
        final existingIndex = achievements.indexWhere((a) => a.id == achievement.id);
        if (existingIndex == -1) {
          updatedAchievements.add(achievement);
        }
      }
      
      // إضافة الإنجازات الجديدة أو تحديث الإنجازات الحالية
      for (final achievement in achievements) {
        final existingIndex = currentAchievements.indexWhere((a) => a.id == achievement.id);
        if (existingIndex == -1) {
          // إنجاز جديد
          updatedAchievements.add(achievement);
          
          // إذا كان الإنجاز مكتمل، إضافة نقاط مكافأة
          if (achievement.isCompleted) {
            await addPoints(userId, 50, 'إكمال إنجاز: ${achievement.name}');
          }
        } else {
          // تحديث إنجاز موجود
          final existingAchievement = currentAchievements[existingIndex];
          
          if (!existingAchievement.isCompleted && achievement.isCompleted) {
            // إذا تم إكمال الإنجاز حديثاً، إضافة نقاط مكافأة
            await addPoints(userId, 50, 'إكمال إنجاز: ${achievement.name}');
            updatedAchievements.add(achievement);
          } else {
            // تحديث قيمة الإنجاز
            updatedAchievements.add(existingAchievement.copyWith(
              value: achievement.value,
              isCompleted: achievement.isCompleted,
              earnedAt: achievement.isCompleted && !existingAchievement.isCompleted
                  ? DateTime.now()
                  : existingAchievement.earnedAt));
          }
        }
      }
      
      // تحديث إحصائيات المستخدم
      final updatedStats = userStats.copyWith(
        achievements: updatedAchievements);
      
      // حفظ التغييرات
      await _userStatsCollection.doc(userId).update(updatedStats.toMap());
    } catch (e) {
      print('فشل في تحديث الإنجازات: $e');
    }
  }
}

/// فئة مساعدة لتخزين رقم الأسبوع والسنة
class _WeekYear {
  final int year;
  final int week;
  
  _WeekYear(this.year, this.week);
}
