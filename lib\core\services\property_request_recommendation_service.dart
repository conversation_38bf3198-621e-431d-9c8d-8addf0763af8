// lib/core/services/property_request_recommendation_service.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

import '../../domain/models/property_request/property_request_model.dart';
import '../../domain/models/estate/estate_model.dart';

/// خدمة توصيات طلبات العقارات
class PropertyRequestRecommendationService {
  /// مثيل Firestore
  final FirebaseFirestore _firestore;

  /// مثيل Firebase Auth
  final FirebaseAuth _auth;

  /// إنشاء خدمة توصيات طلبات العقارات
  PropertyRequestRecommendationService({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance;

  /// الحصول على طلبات العقارات الموصى بها للمستخدم الحالي
  Future<List<PropertyRequestModel>> getRecommendedRequests({int limit = 10}) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return [];
      }

      // الحصول على العقارات التي يملكها المستخدم
      final userEstatesSnapshot = await _firestore
          .collection('estates')
          .where('userId', isEqualTo: user.uid)
          .where('status', isEqualTo: 'approved')
          .get();

      final userEstates = userEstatesSnapshot.docs
          .map((doc) => EstateModel.fromFirestore(doc))
          .toList();

      if (userEstates.isEmpty) {
        // إذا لم يكن لدى المستخدم عقارات، قم بإرجاع أحدث الطلبات النشطة
        final requestsSnapshot = await _firestore
            .collection('propertyRequests')
            .where('status', isEqualTo: RequestStatus.active.name)
            .orderBy('createdAt', descending: true)
            .limit(limit)
            .get();

        return requestsSnapshot.docs
            .map((doc) => PropertyRequestModel.fromFirestore(doc))
            .toList();
      }

      // استخراج أنواع العقارات التي يملكها المستخدم
      final userPropertyTypes = userEstates
          .map((estate) => estate.propertyType)
          .toSet()
          .toList();

      // استخراج المناطق التي توجد فيها عقارات المستخدم
      final userAreas = userEstates
          .map((estate) => estate.area)
          .toSet()
          .toList();

      // الحصول على الطلبات النشطة التي تتطابق مع أنواع العقارات التي يملكها المستخدم
      final matchingTypeRequestsSnapshot = await _firestore
          .collection('propertyRequests')
          .where('status', isEqualTo: RequestStatus.active.name)
          .where('propertyType', whereIn: userPropertyTypes)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      final matchingTypeRequests = matchingTypeRequestsSnapshot.docs
          .map((doc) => PropertyRequestModel.fromFirestore(doc))
          .toList();

      // الحصول على الطلبات النشطة التي تتطابق مع المناطق التي توجد فيها عقارات المستخدم
      final matchingAreaRequestsSnapshot = await _firestore
          .collection('propertyRequests')
          .where('status', isEqualTo: RequestStatus.active.name)
          .where('preferredAreas', arrayContainsAny: userAreas)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      final matchingAreaRequests = matchingAreaRequestsSnapshot.docs
          .map((doc) => PropertyRequestModel.fromFirestore(doc))
          .where((request) => !matchingTypeRequests.any((r) => r.id == request.id))
          .toList();

      // دمج النتائج وإزالة التكرارات
      final combinedRequests = [...matchingTypeRequests, ...matchingAreaRequests];
      
      // إذا كان عدد النتائج أقل من الحد المطلوب، قم بإضافة أحدث الطلبات النشطة
      if (combinedRequests.length < limit) {
        final remainingLimit = limit - combinedRequests.length;
        
        final existingIds = combinedRequests.map((request) => request.id).toSet();
        
        final additionalRequestsSnapshot = await _firestore
            .collection('propertyRequests')
            .where('status', isEqualTo: RequestStatus.active.name)
            .orderBy('createdAt', descending: true)
            .limit(limit * 2) // نحصل على عدد أكبر ثم نقوم بالفلترة
            .get();
            
        final additionalRequests = additionalRequestsSnapshot.docs
            .map((doc) => PropertyRequestModel.fromFirestore(doc))
            .where((request) => !existingIds.contains(request.id))
            .take(remainingLimit)
            .toList();
            
        combinedRequests.addAll(additionalRequests);
      }
      
      return combinedRequests;
    } catch (e) {
      debugPrint('خطأ في الحصول على طلبات العقارات الموصى بها: $e');
      return [];
    }
  }

  /// الحصول على العقارات الموصى بها لطلب عقار
  Future<List<EstateModel>> getRecommendedEstatesForRequest(
      PropertyRequestModel request, {int limit = 10}) async {
    try {
      // البحث عن العقارات التي تتطابق مع نوع العقار المطلوب
      final matchingTypeEstatesSnapshot = await _firestore
          .collection('estates')
          .where('status', isEqualTo: 'approved')
          .where('propertyType', isEqualTo: request.propertyType)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      final matchingTypeEstates = matchingTypeEstatesSnapshot.docs
          .map((doc) => EstateModel.fromFirestore(doc))
          .toList();

      // البحث عن العقارات التي تتطابق مع المناطق المفضلة
      List<EstateModel> matchingAreaEstates = [];
      
      if (request.preferredAreas.isNotEmpty) {
        final matchingAreaEstatesSnapshot = await _firestore
            .collection('estates')
            .where('status', isEqualTo: 'approved')
            .where('area', whereIn: request.preferredAreas)
            .orderBy('createdAt', descending: true)
            .limit(limit)
            .get();

        matchingAreaEstates = matchingAreaEstatesSnapshot.docs
            .map((doc) => EstateModel.fromFirestore(doc))
            .where((estate) => !matchingTypeEstates.any((e) => e.id == estate.id))
            .toList();
      }

      // البحث عن العقارات التي تتطابق مع نطاق السعر
      List<EstateModel> matchingPriceEstates = [];
      
      if (request.minPrice > 0 || request.maxPrice > 0) {
        Query<Map<String, dynamic>> query = _firestore
            .collection('estates')
            .where('status', isEqualTo: 'approved');
            
        if (request.minPrice > 0) {
          query = query.where('price', isGreaterThanOrEqualTo: request.minPrice);
        }
        
        if (request.maxPrice > 0) {
          query = query.where('price', isLessThanOrEqualTo: request.maxPrice);
        }
        
        final matchingPriceEstatesSnapshot = await query
            .orderBy('price')
            .limit(limit)
            .get();

        matchingPriceEstates = matchingPriceEstatesSnapshot.docs
            .map((doc) => EstateModel.fromFirestore(doc))
            .where((estate) => 
                !matchingTypeEstates.any((e) => e.id == estate.id) &&
                !matchingAreaEstates.any((e) => e.id == estate.id))
            .toList();
      }

      // دمج النتائج وإزالة التكرارات
      final combinedEstates = [
        ...matchingTypeEstates,
        ...matchingAreaEstates,
        ...matchingPriceEstates,
      ];
      
      // إذا كان عدد النتائج أقل من الحد المطلوب، قم بإضافة أحدث العقارات
      if (combinedEstates.length < limit) {
        final remainingLimit = limit - combinedEstates.length;
        
        final existingIds = combinedEstates.map((estate) => estate.id).toSet();
        
        final additionalEstatesSnapshot = await _firestore
            .collection('estates')
            .where('status', isEqualTo: 'approved')
            .orderBy('createdAt', descending: true)
            .limit(limit * 2) // نحصل على عدد أكبر ثم نقوم بالفلترة
            .get();
            
        final additionalEstates = additionalEstatesSnapshot.docs
            .map((doc) => EstateModel.fromFirestore(doc))
            .where((estate) => !existingIds.contains(estate.id))
            .take(remainingLimit)
            .toList();
            
        combinedEstates.addAll(additionalEstates);
      }
      
      return combinedEstates;
    } catch (e) {
      debugPrint('خطأ في الحصول على العقارات الموصى بها لطلب عقار: $e');
      return [];
    }
  }

  /// حساب درجة التطابق بين طلب عقار وعقار
  double calculateMatchScore(PropertyRequestModel request, EstateModel estate) {
    double score = 0;
    
    // التطابق في نوع العقار (30%)
    if (request.propertyType == estate.propertyType) {
      score += 30;
    }
    
    // التطابق في المنطقة (25%)
    if (request.preferredAreas.contains(estate.area)) {
      score += 25;
    }
    
    // التطابق في نطاق السعر (20%)
    if (request.minPrice <= estate.price && 
        (request.maxPrice == 0 || estate.price <= request.maxPrice)) {
      score += 20;
    } else if (request.maxPrice > 0 && estate.price <= request.maxPrice * 1.1) {
      // إذا كان السعر أعلى بنسبة 10% فقط
      score += 10;
    }
    
    // التطابق في عدد الغرف (15%)
    if (request.minRooms <= estate.rooms && 
        (request.maxRooms == 0 || estate.rooms <= request.maxRooms)) {
      score += 15;
    }
    
    // التطابق في المساحة (10%)
    if (request.minArea <= estate.area_size && 
        (request.maxArea == 0 || estate.area_size <= request.maxArea)) {
      score += 10;
    }
    
    return score;
  }
}
