import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

/// أنواع المكافآت
enum RewardType {
  /// خصم على الإعلانات
  adDiscount,
  
  /// خصم على الباقات
  packageDiscount,
  
  /// إعلان مجاني
  freeAd,
  
  /// ترقية مجانية للإعلان
  freeUpgrade,
  
  /// خدمة مميزة
  premiumService,
  
  /// منتج مادي
  physicalProduct,
  
  /// رصيد نقدي
  cashCredit,
  
  /// نقاط إضافية
  bonusPoints,
  
  /// خصم خاص
  specialDiscount,
  
  /// عضوية VIP
  vipMembership,
}

/// حالة المكافأة
enum RewardStatus {
  /// متاحة للاستبدال
  available,
  
  /// غير متاحة (نفدت الكمية)
  outOfStock,
  
  /// معطلة مؤقتاً
  disabled,
  
  /// منتهية الصلاحية
  expired,
}

/// نموذج المكافأة
class RewardModel {
  /// معرف المكافأة
  final String id;
  
  /// اسم المكافأة
  final String name;
  
  /// وصف المكافأة
  final String description;
  
  /// نوع المكافأة
  final RewardType type;
  
  /// عدد النقاط المطلوبة للاستبدال
  final int pointsRequired;
  
  /// قيمة المكافأة (للخصومات والرصيد النقدي)
  final double value;
  
  /// رابط صورة المكافأة
  final String imageUrl;
  
  /// لون المكافأة
  final Color color;
  
  /// أيقونة المكافأة
  final IconData icon;
  
  /// الكمية المتاحة (-1 للكمية غير المحدودة)
  final int availableQuantity;
  
  /// عدد مرات الاستبدال المسموحة لكل مستخدم (-1 للعدد غير المحدود)
  final int maxRedemptionsPerUser;
  
  /// تاريخ انتهاء صلاحية المكافأة
  final DateTime? expiryDate;
  
  /// حالة المكافأة
  final RewardStatus status;
  
  /// شروط الاستبدال
  final String? terms;
  
  /// رمز الكوبون (للخصومات)
  final String? couponCode;
  
  /// مدة صلاحية الكوبون بالأيام
  final int? couponValidityDays;
  
  /// الحد الأدنى للمبلغ لتطبيق الخصم
  final double? minimumAmount;
  
  /// تاريخ الإنشاء
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث
  final DateTime updatedAt;
  
  /// هل المكافأة متاحة للاستبدال
  bool get isAvailable {
    if (status != RewardStatus.available) return false;
    if (expiryDate != null && DateTime.now().isAfter(expiryDate!)) return false;
    if (availableQuantity == 0) return false;
    return true;
  }
  
  /// هل المكافأة منتهية الصلاحية
  bool get isExpired {
    return expiryDate != null && DateTime.now().isAfter(expiryDate!);
  }
  
  /// الحصول على نص نوع المكافأة
  String get typeText {
    switch (type) {
      case RewardType.adDiscount:
        return 'خصم على الإعلانات';
      case RewardType.packageDiscount:
        return 'خصم على الباقات';
      case RewardType.freeAd:
        return 'إعلان مجاني';
      case RewardType.freeUpgrade:
        return 'ترقية مجانية';
      case RewardType.premiumService:
        return 'خدمة مميزة';
      case RewardType.physicalProduct:
        return 'منتج مادي';
      case RewardType.cashCredit:
        return 'رصيد نقدي';
      case RewardType.bonusPoints:
        return 'نقاط إضافية';
      case RewardType.specialDiscount:
        return 'خصم خاص';
      case RewardType.vipMembership:
        return 'عضوية VIP';
    }
  }
  
  /// الحصول على نص حالة المكافأة
  String get statusText {
    switch (status) {
      case RewardStatus.available:
        return 'متاحة';
      case RewardStatus.outOfStock:
        return 'نفدت الكمية';
      case RewardStatus.disabled:
        return 'معطلة';
      case RewardStatus.expired:
        return 'منتهية الصلاحية';
    }
  }
  
  RewardModel({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.pointsRequired,
    required this.value,
    required this.imageUrl,
    required this.color,
    required this.icon,
    required this.availableQuantity,
    required this.maxRedemptionsPerUser,
    this.expiryDate,
    required this.status,
    this.terms,
    this.couponCode,
    this.couponValidityDays,
    this.minimumAmount,
    required this.createdAt,
    required this.updatedAt,
  });
  
  /// إنشاء نسخة معدلة من المكافأة
  RewardModel copyWith({
    String? id,
    String? name,
    String? description,
    RewardType? type,
    int? pointsRequired,
    double? value,
    String? imageUrl,
    Color? color,
    IconData? icon,
    int? availableQuantity,
    int? maxRedemptionsPerUser,
    DateTime? expiryDate,
    RewardStatus? status,
    String? terms,
    String? couponCode,
    int? couponValidityDays,
    double? minimumAmount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RewardModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      pointsRequired: pointsRequired ?? this.pointsRequired,
      value: value ?? this.value,
      imageUrl: imageUrl ?? this.imageUrl,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      availableQuantity: availableQuantity ?? this.availableQuantity,
      maxRedemptionsPerUser: maxRedemptionsPerUser ?? this.maxRedemptionsPerUser,
      expiryDate: expiryDate ?? this.expiryDate,
      status: status ?? this.status,
      terms: terms ?? this.terms,
      couponCode: couponCode ?? this.couponCode,
      couponValidityDays: couponValidityDays ?? this.couponValidityDays,
      minimumAmount: minimumAmount ?? this.minimumAmount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
  
  /// تحويل المكافأة إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.index,
      'pointsRequired': pointsRequired,
      'value': value,
      'imageUrl': imageUrl,
      'colorValue': color.value,
      'iconCodePoint': icon.codePoint,
      'iconFontFamily': icon.fontFamily,
      'iconFontPackage': icon.fontPackage,
      'availableQuantity': availableQuantity,
      'maxRedemptionsPerUser': maxRedemptionsPerUser,
      'expiryDate': expiryDate?.millisecondsSinceEpoch,
      'status': status.index,
      'terms': terms,
      'couponCode': couponCode,
      'couponValidityDays': couponValidityDays,
      'minimumAmount': minimumAmount,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }
  
  /// إنشاء مكافأة من خريطة
  factory RewardModel.fromMap(Map<String, dynamic> map) {
    return RewardModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      type: RewardType.values[map['type'] ?? 0],
      pointsRequired: map['pointsRequired'] ?? 0,
      value: (map['value'] ?? 0.0).toDouble(),
      imageUrl: map['imageUrl'] ?? '',
      color: Color(map['colorValue'] ?? 0xFF4CAF50),
      icon: IconData(
        map['iconCodePoint'] ?? Icons.card_giftcard.codePoint,
        fontFamily: map['iconFontFamily'],
        fontPackage: map['iconFontPackage'],
      ),
      availableQuantity: map['availableQuantity'] ?? -1,
      maxRedemptionsPerUser: map['maxRedemptionsPerUser'] ?? -1,
      expiryDate: map['expiryDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['expiryDate'])
          : null,
      status: RewardStatus.values[map['status'] ?? 0],
      terms: map['terms'],
      couponCode: map['couponCode'],
      couponValidityDays: map['couponValidityDays'],
      minimumAmount: map['minimumAmount']?.toDouble(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(
        map['createdAt'] ?? DateTime.now().millisecondsSinceEpoch,
      ),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(
        map['updatedAt'] ?? DateTime.now().millisecondsSinceEpoch,
      ),
    );
  }

  /// تحويل المكافأة إلى Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'type': type.index,
      'pointsRequired': pointsRequired,
      'value': value,
      'imageUrl': imageUrl,
      'colorValue': color.value,
      'iconCodePoint': icon.codePoint,
      'iconFontFamily': icon.fontFamily,
      'iconFontPackage': icon.fontPackage,
      'availableQuantity': availableQuantity,
      'maxRedemptionsPerUser': maxRedemptionsPerUser,
      'expiryDate': expiryDate != null ? Timestamp.fromDate(expiryDate!) : null,
      'status': status.index,
      'terms': terms,
      'couponCode': couponCode,
      'couponValidityDays': couponValidityDays,
      'minimumAmount': minimumAmount,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  /// إنشاء مكافأة من Firestore
  factory RewardModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return RewardModel(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      type: RewardType.values[data['type'] ?? 0],
      pointsRequired: data['pointsRequired'] ?? 0,
      value: (data['value'] ?? 0.0).toDouble(),
      imageUrl: data['imageUrl'] ?? '',
      color: Color(data['colorValue'] ?? 0xFF4CAF50),
      icon: IconData(
        data['iconCodePoint'] ?? Icons.card_giftcard.codePoint,
        fontFamily: data['iconFontFamily'],
        fontPackage: data['iconFontPackage'],
      ),
      availableQuantity: data['availableQuantity'] ?? -1,
      maxRedemptionsPerUser: data['maxRedemptionsPerUser'] ?? -1,
      expiryDate: data['expiryDate'] != null
          ? (data['expiryDate'] as Timestamp).toDate()
          : null,
      status: RewardStatus.values[data['status'] ?? 0],
      terms: data['terms'],
      couponCode: data['couponCode'],
      couponValidityDays: data['couponValidityDays'],
      minimumAmount: data['minimumAmount']?.toDouble(),
      createdAt: data['createdAt'] != null
          ? (data['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }
}
